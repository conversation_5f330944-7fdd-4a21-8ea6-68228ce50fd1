(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5450],{53111:function(e,t,o){Promise.resolve().then(o.t.bind(o,81749,23)),Promise.resolve().then(o.t.bind(o,25250,23)),Promise.resolve().then(o.bind(o,97753)),Promise.resolve().then(o.bind(o,57250)),Promise.resolve().then(o.bind(o,4353)),Promise.resolve().then(o.t.bind(o,46282,23)),Promise.resolve().then(o.t.bind(o,59927,23)),Promise.resolve().then(o.bind(o,80257)),Promise.resolve().then(o.bind(o,73071)),Promise.resolve().then(o.bind(o,21005)),Promise.resolve().then(o.t.bind(o,25323,23)),Promise.resolve().then(o.t.bind(o,98633,23)),Promise.resolve().then(o.t.bind(o,26155,23)),Promise.resolve().then(o.t.bind(o,78846,23))},47907:function(e,t,o){"use strict";var n=o(15313);o.o(n,"usePathname")&&o.d(t,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(t,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(t,{useSearchParams:function(){return n.useSearchParams}})},97753:function(e,t,o){"use strict";o.r(t);var n=o(16480),i=o.n(n),r=o(2265),a=o(12865),l=o(57437);let s=r.forwardRef((e,t)=>{let{bsPrefix:o,fluid:n=!1,as:r="div",className:s,...p}=e,d=(0,a.vE)(o,"container");return(0,l.jsx)(r,{ref:t,...p,className:i()(s,n?"".concat(d).concat("string"==typeof n?"-".concat(n):"-fluid"):d)})});s.displayName="Container",t.default=s},12865:function(e,t,o){"use strict";o.d(t,{SC:function(){return d},pi:function(){return s},vE:function(){return l},zG:function(){return p}});var n=o(2265);o(57437);let i=n.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:r,Provider:a}=i;function l(e,t){let{prefixes:o}=(0,n.useContext)(i);return e||o[t]||t}function s(){let{breakpoints:e}=(0,n.useContext)(i);return e}function p(){let{minBreakpoint:e}=(0,n.useContext)(i);return e}function d(){let{dir:e}=(0,n.useContext)(i);return"rtl"===e}},4353:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return u}});var n=o(57437);o(2265);var i=o(97753),r=o(8792),a=o(62806),l=o(21768),s=o(47907),p=o(61292),d=o.n(p);function u(e){let{data:t,variant:o}=e,p=(0,s.useRouter)();return(0,n.jsx)("section",{className:d().ctaContainer,children:"downloadOurBrand"!==o?(0,n.jsxs)(i.default,{className:d().ctaWrapper,children:[(0,n.jsx)(l.Z,{richTextValue:null==t?void 0:t.ctaTitle,headingType:"h2",className:d().ctaHeading}),(0,n.jsx)("div",{className:d().ctaBtn,children:(0,n.jsx)(a.Z,{label:null==t?void 0:t.ctaButtonText,className:d().btn,onClick:()=>{if("scrollToContactForm"===o){let e=document.getElementById("contact-us-form");e&&e.scrollIntoView({behavior:"smooth"})}else p.push("".concat(null==t?void 0:t.ctaLink))}})})]}):(0,n.jsxs)(i.default,{className:d().ctaWrapper,children:[(0,n.jsx)(l.Z,{richTextValue:null==t?void 0:t.ctaTitle,headingType:"h3",className:d().ctaHeading}),(0,n.jsx)(r.default,{href:null==t?void 0:t.ctaLink,target:"_blank",typeof:"application/pdf",prefetch:!1,download:!0,className:d().downloadLinkWrapper,children:(0,n.jsx)("div",{className:d().downloadLink,children:null==t?void 0:t.ctaButtonText})})]})})}},80257:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return l}});var n=o(57437),i=o(20703),r=o(97073),a=o(2265);function l(e){var t,o,l,s,p,d,u,x,c,b,m,k;let{src:v,width:f,height:h,fill:F=!1,alt:_="image alt text",loading:g="lazy",useThumbnail:y=!1,className:S,style:C}=e,L=(0,r.Z)({query:"(max-width: 576px)"}),P=(0,r.Z)({query:"(max-width: 768px)"});(0,r.Z)({query:"(max-width: 1600px)"});let[w,T]=(0,a.useState)(!1);if((0,a.useEffect)(()=>{T(!0)},[]),!w)return null;let B=y?(null==v?void 0:null===(o=v.format)||void 0===o?void 0:null===(t=o.small)||void 0===t?void 0:t.url)||(null==v?void 0:null===(s=v.formats)||void 0===s?void 0:null===(l=s.small)||void 0===l?void 0:l.url)||(null==v?void 0:v.url):L?(null==v?void 0:null===(d=v.format)||void 0===d?void 0:null===(p=d.small)||void 0===p?void 0:p.url)||(null==v?void 0:null===(x=v.formats)||void 0===x?void 0:null===(u=x.small)||void 0===u?void 0:u.url)||(null==v?void 0:v.url):P&&((null==v?void 0:null===(b=v.format)||void 0===b?void 0:null===(c=b.medium)||void 0===c?void 0:c.url)||(null==v?void 0:null===(k=v.formats)||void 0===k?void 0:null===(m=k.medium)||void 0===m?void 0:m.url))||(null==v?void 0:v.url);return(0,n.jsx)(i.default,{unoptimized:!0,src:B,alt:_,width:f,height:h,fill:F,loading:g,className:S,style:C})}},73071:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return d}});var n=o(57437),i=o(50425),r=o.n(i),a=o(21768),l=o(80480),s=o(41396),p=o(97753);function d(e){var t,o,i;let{data:d,playButtonUrl:u,fromCloudConsulting:x=!1}=e,c=x?r().fromCloud:"";return(0,n.jsxs)(p.default,{fluid:!0,className:(0,s.Z)(r().container,c),children:[(0,n.jsxs)("div",{className:r().content,children:[(0,n.jsx)(a.Z,{headingType:"h2",title:null==d?void 0:d.title,className:(0,s.Z)(r().title,c)}),(null==d?void 0:d.subtitle)&&(0,n.jsx)("div",{className:(0,s.Z)(r().subtitle,c),dangerouslySetInnerHTML:{__html:null==d?void 0:d.subtitle}})]}),(0,n.jsx)("div",{className:r().videoContainer,children:(0,n.jsx)(l.Z,{embedLink:null==d?void 0:d.youtube_video_embed_link,playButtonUrl:u,thumbnailUrl:null==d?void 0:null===(i=d.video_thumbnail_image)||void 0===i?void 0:null===(o=i.data)||void 0===o?void 0:null===(t=o.attributes)||void 0===t?void 0:t.url})})]})}},97073:function(e,t,o){"use strict";var n=o(2265);let i=void 0===window.matchMedia?()=>!1:e=>{let{query:t}=e,[o,i]=(0,n.useState)(!1),r=(0,n.useRef)(window.matchMedia(t)),a=(0,n.useCallback)(e=>{i(e.matches)},[i]);return(0,n.useEffect)(()=>{let e=r.current;return i(e.matches),e.addListener(a),()=>e.removeListener(a)},[i,a]),o};t.Z=i},61292:function(e){e.exports={variables:'"@styles/variables.module.css"',colorBlack:"#000000",bodyHeadingS:"20px",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorWhite:"#FFFFFF",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md-769":"769px","breakpoint-sm-550":"550px","breakpoint-sm-320":"320px","breakpoint-sm":"576px",ctaContainer:"Cta_ctaContainer___sfgZ",ctaWrapper:"Cta_ctaWrapper__TkOMF",ctaHeading:"Cta_ctaHeading___2l6Z",btn:"Cta_btn__Fsqyb",downloadLinkWrapper:"Cta_downloadLinkWrapper__dMcXu",downloadLink:"Cta_downloadLink__7uzCi"}},59927:function(e){e.exports={variables:'"@styles/variables.module.css"',fontWeight600:"600",gray300:"#F3F3F3",fontWeight400:"400",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":"1440px","breakpoint-md":"768px","breakpoint-sm":"576px",containerHeroSection:"HeroSectionPodcasts_containerHeroSection__acT3D",content:"HeroSectionPodcasts_content__55lfJ",title:"HeroSectionPodcasts_title__M3_BM",description:"HeroSectionPodcasts_description__Mm8Zi",heroImageContainer:"HeroSectionPodcasts_heroImageContainer__9HwFX",heroImage:"HeroSectionPodcasts_heroImage__TCDZZ"}},50425:function(e){e.exports={breakpoints:'"@styles/breakpoints.module.css"',variables:'"@styles/variables.module.css"',"breakpoint-sm-450":"450px","breakpoint-md":"768px","breakpoint-sm":"576px","breakpoint-xl":"1200px","breakpoint-xl-1440":"1440px",colorBlack:"#000000",colorWhite:"#FFFFFF",container:"LatestEpisode_container__d874G",title:"LatestEpisode_title__loucB",subtitle:"LatestEpisode_subtitle__URgp9",videoContainer:"LatestEpisode_videoContainer__CwsdM",fromCloud:"LatestEpisode_fromCloud__YouVh"}},78846:function(e){e.exports={variables:'"@styles/variables.module.css"',gray300:"#F3F3F3",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":"1440px","breakpoint-xl":"1200px","breakpoint-lg":"992px","breakpoint-md":"768px","breakpoint-sm":"576px",containerPodcastsLinks:"PodcastsLinks_containerPodcastsLinks__H6H4q",title:"PodcastsLinks_title__6fRMJ",linksWrapper:"PodcastsLinks_linksWrapper__qf1T4"}},26155:function(e){e.exports={"breakpoint-xs":"0","breakpoint-sm-195":"195px","breakpoint-sm-270":"270px","breakpoint-sm-200":"200px","breakpoint-sm-320":"320px","breakpoint-sm-326":"326px","breakpoint-sm-390":"390px","breakpoint-sm-367":"367px","breakpoint-sm-365":"365px","breakpoint-sm-340":"340px","breakpoint-sm-350":"350px","breakpoint-sm-370":"370px","breakpoint-sm-380":"380px","breakpoint-sm-424":"424px","breakpoint-sm-427":"427px","breakpoint-sm-420":"420px","breakpoint-sm-430":"430px","breakpoint-sm-450":"450px","breakpoint-sm-460":"460px","breakpoint-sm-484":"484px","breakpoint-sm-480":"480px","breakpoint-sm-532":"532px","breakpoint-sm-550":"550px","breakpoint-sm":"576px","breakpoint-md-579":"579px","breakpoint-md-585":"585px","breakpoint-md-767":"767px","breakpoint-md":"768px","breakpoint-md-769":"769px","breakpoint-md-820":"820px","breakpoint-md-850":"850px","breakpoint-lg-901":"901px","breakpoint-lg":"992px","breakpoint-lg-991px":"991px","breakpoint-xl-1024":"1024px","breakpoint-xl-1051":"1051px","breakpoint-xl-1208":"1208px","breakpoint-xl-1023":"1023px","breakpoint-xl-1199":"1199px","breakpoint-xl-1188":"1188px","breakpoint-xl":"1200px","breakpoint-xl-1365":"1365px","breakpoint-xl-1366":"1366px","breakpoint-xl-1309":"1309px","breakpoint-xl-1400":"1400px","breakpoint-xl-1439":"1439px","breakpoint-xl-1440":"1440px","breakpoint-xl-1405":"1405px","breakpoint-xl-1406":"1406px","breakpoint-xl-1600":"1600px","breakpoint-xl-1800":"1800px","breakpoint-xl-2000":"2000px","breakpoint-xl-2100":"2100px","breakpoint-xl-2442":"2442px","breakpoint-xl-2559":"2559px","breakpoint-xl-2560":"2560px"}},98633:function(e){e.exports={brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",gray100:"#FCFCFC",gray200:"#F8F8F8",gray300:"#F3F3F3",gray400:"#E4E4E4",gray500:"#CDCDCD",gray600:"#B1B1B1",gray700:"#808080",gray800:"#646464",gray900:"#3A3A3A",error:"#FF6D60",success:"#23A881",grayBorder:"#8C8B8B",link:"#0075FF",grayBlueFonts:"#262531",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",halfSpace:"4px",oneSpace:"8px",twoSpace:"16px",threeSpace:"24px",fourSpace:"32px",fiveSpace:"40px",sixSpace:"48px",eightSpace:"64px",tenSpace:"80px",fifteenSpace:"120px",twentyFiveSpace:"200px",h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",bodyHeadingXL:"56px",bodyHeadingL:"24px",bodyHeadingM:"21px",bodyHeadingS:"20px",bodyHeadingXS:"18px",bodyHeadingXSS:"16px",buttonLabelXLargeFontSize:"26px",buttonLabelLargeFontSize:"20px",buttonLabelMediumFontSize:"16px",buttonLabelSmallFontSize:"14px",bodyTextXLarge:"26px",bodyTextLarge:"22px",bodyTextMedium:"20px",bodyTextSmall:"18px",bodyTextXSmall:"16px",bodyTextXXSmall:"14px",bodyTextXXXSSmall:"8px",bodyLinkXXLarge:"26px",bodyLinkXLarge:"22px",bodyLinkLarge:"19px",bodyLinkMedium:"18px",bodyLinkSmall:"17px",bodyLinkXSmall:"16px",bodyLinkXXSmall:"15px",fontWeight100:"100",fontWeight200:"200",fontWeight300:"300",fontWeight400:"400",fontWeight500:"500",fontWeight600:"600",fontWeight700:"700",fontWeight800:"800",fontWeight900:"900"}},16480:function(e,t){"use strict";var o;!/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/function(){var n={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var o=arguments[t];o&&(e=r(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var o in e)n.call(e,o)&&e[o]&&(t=r(t,o));return t}(o)))}return e}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0!==(o=(function(){return i}).apply(t,[]))&&(e.exports=o)}()}},function(e){e.O(0,[5250,1607,843,7250,1005,2971,8069,1744],function(){return e(e.s=53111)}),_N_E=e.O()}]);