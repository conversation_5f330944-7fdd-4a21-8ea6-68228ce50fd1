3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","chatbot-development","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","chatbot-development","d"],{"children":["__PAGE__?{\"blogDetails\":\"chatbot-development\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","chatbot-development","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
12:Ta69,<p>Chatbots. They’ve been around for quite a while but only recently, (2016 onwards) they’ve became popularized and mainstream, with brands and enterprises engaging in chatbot development in order to reach customers with better efficiency and cost-effectiveness.</p><p>Enterprises today, build and deploy chatbots to not only assist but also automate its customer support. For e.g., KLM Royal Dutch Airlines handled an upwards of 16,000 interactions on a weekly basis and in 6 months, <a href="https://www.convinceandconvert.com/digital-marketing/6-critical-chatbot-statistics-for-2018/" target="_blank" rel="noopener">the Blue Bot sent out almost 2 million messages to more than 500,000 customers</a>. Talk about scalability.</p><p>Surveys show that <a href="https://www.convinceandconvert.com/digital-marketing/6-critical-chatbot-statistics-for-2018/" target="_blank" rel="noopener">37% of Americans would prefer to use a chatbot to get a swift answer</a>, in an urgent situation. Additionally, 64% of Americans feel that the 24-hour availability of chatbots is the best feature with 55% appreciating the instant response and instant communication.</p><p><img src="https://cdn.marutitech.com/how_to_plan_chatbot_development_at_an_enterprise_level_min_f0b1e3d4bb.jpg" alt="how-to-plan-chatbot-development-at-an-enterprise-level-min" srcset="https://cdn.marutitech.com/thumbnail_how_to_plan_chatbot_development_at_an_enterprise_level_min_f0b1e3d4bb.jpg 220w,https://cdn.marutitech.com/small_how_to_plan_chatbot_development_at_an_enterprise_level_min_f0b1e3d4bb.jpg 500w,https://cdn.marutitech.com/medium_how_to_plan_chatbot_development_at_an_enterprise_level_min_f0b1e3d4bb.jpg 750w,https://cdn.marutitech.com/large_how_to_plan_chatbot_development_at_an_enterprise_level_min_f0b1e3d4bb.jpg 1000w," sizes="100vw"></p><p>Statistics from HubSpot show that 48% of consumers would connect with a company through live chat than any other means of contact, and 55% of consumers are more interested in interacting with a business or store by using a messaging app to solve a problem.</p><p>Additional stats when it comes to business profitability show that 47% of consumers would purchase through a chatbot and millennials (26 to 36-year-olds) are prepared to spend up to £481.15 on a business transaction through a bot.</p><p>So far, enterprises that have adopted chatbots have done so by creating and using them in silos. Although this approach may work for businesses that need to automate a handful of tasks, it doesn’t exactly align with the high-end needs of an enterprise – scalability, agility, and cost-effectiveness across a smorgasbord of functions.</p>13:T162b,<p>When it comes to enterprises, chatbots should be readily available and accessible across a myriad of channels and integrated with internal business systems with Customer Relationship Management (CRM) and Supply Chain Management (SCM) systems being top priority.</p><p>When coming up with a bot development strategy, enterprises have several options. A single task bot is not a feasible option for enterprises that need an automated workflow coupled with the integration of internal and external ecosystems and application of natural language processing.</p><p><a href="https://marutitech.com/complete-guide-bot-frameworks/" target="_blank" rel="noopener">Chatbot frameworks</a> assist programmers with structures with which they can build individual chatbots. However, these frameworks are merely just a collection of a set of tools and services. The frameworks apply to a fixed set of use cases and can be used to assemble and deploy a single-task bot which, at the end of the day, lacks the end-to-end development and ongoing management capabilities.</p><p>Frameworks tend to be useful if the use case is small, however, for an enterprise where the overall requirements and scope are more demanding – this is where a chatbot platform comes into the picture.</p><p>When it comes to <a href="https://marutitech.com/chatbots-work-guide-chatbot-architecture/" target="_blank" rel="noopener">chatbot architecture</a>, these are the following requirements that enterprises should make certain of when it comes to their chatbot development platform –</p><p><img src="https://cdn.marutitech.com/Guide-to-a-Successful-RPA-Implementation-in-2019-2.jpg" alt="chatbot development project"></p><h3>&nbsp;1. Multiple types of chatbots executing multiple tasks</h3><p>This functionality is imperative for enterprises as it allows them to track and streamline multiple functions at once. Ideally, the enterprise should have to ability to deploy a chatbot that works on a single task along with creating and deploying a multi-purpose chatbot that communicates with multiple systems and completes a variety of tasks within each of them.</p><p>The chatbot development platform should offer pre-built and ready to deploy bots which address certain use cases (e.g., lead generation, customer support etc.) along with the ability to customize them to suit your business needs so as to handle multiple different workflows and processes pertaining to different customer interactions and your business offerings (e.g., a lead generation bot that also answers customer’s queries and replies with answers in a FAQ, document or website).</p><h3>&nbsp;2. Multiple&nbsp;Channel support</h3><p>Enterprises should look for chatbot development platforms where the bots can be deployed to the website, mobile apps, or the channel of its choice with the user interface that is customized for each channel, be it SMS, e-mail or social media. To add on to that, the bots should have the ability to interact with corporate tools like Slack, Telegram, Skype, etc.</p><h3>&nbsp;3. Natural Language Processing and Speech Support</h3><p>Training the chatbot is yet another important consideration when it comes to the scalability of the bot. Does your chatbot development platform incorporate <a href="https://marutitech.com/how-is-natural-language-processing-applied-in-business/" target="_blank" rel="noopener">Natural Language Processing (NLP)</a> training? Can the bots maintain accurate interactions and conversations using text and/or speech? A chatbot platform that provides NLP and speech support tends to provide the best results when it comes to understanding user intent and replying with relevant content post-assessment.</p><h3>&nbsp;4. Deploying Intelligent Chatbots through the platform</h3><p>The platform should have <a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">intelligent chatbots</a> that understand, recollect and continuously learn from data and information that is garnered from each customer interaction. This also includes the need to maintain the context of a customer request during interaction and using Machine Learning to develop further and perfect its natural language processing capabilities.</p><h3>&nbsp;5. Ability to bridge with the platform</h3><p>Does the platform have the ability to share messages between users, bots, and cross-functional systems? This would include sharing messages that are stored between users, bots, and systems whole automatically logging as well as success and failure categorization of messages. This provides a comprehensive and crystal-clear picture of the functionality of the chatbot development platform and subsequently, the bot.</p><h3>6. Building the chatbot</h3><p>The platform should have an intuitive, web-based tool for designing, building and customizing the chatbot based on bot’s use-cases, tasks and the channels where it is deployed. It should also have the option to restart the process of <a href="https://chatbotsmagazine.com/how-to-develop-a-chatbot-from-scratch-62bed1adab8c" target="_blank" rel="noopener">developing the bot from scratch</a> or reuse developed components along with testing the chatbot build throughout the development cycle.</p><h3>&nbsp;7. Industry Experience and Domain Knowledge</h3><p>Identify and engage with the right technology and platform providers that have considerable industry experience and domain knowledge.</p><p>Enterprises need to factor in and truly determine what chatbot development platform or relevant framework will augment and facilitate speed, scalability, and flexibility in order to support their customers and employees.</p>14:T11ba,<h3><img src="https://cdn.marutitech.com/Guide-to-a-Successful-RPA-Implementation-in-2019-3.jpg" alt="chatbot development project"></h3><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Chatbots do not solely depend on technology but also on the content</strong></span></h3><p>The end goal with the chatbot is to achieve high-quality customer experience and service staff assistance. The noticeable element of chatbots is obviously the technology. However, content plays an important part in its success as well. Creating knowledge assets is a noteworthy investment, but in the shadow of high-end technology components like Artificial Intelligence (AI), the important role of content creation and curation often gets overshadowed.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Content reuse and repurposing</strong></span></h3><p>Most of the content that chatbots require is already being created and used extensively by customer service teams, be it in telephone conversations, in web chats, online or social media messaging or in emails. Repurposing this existing content is in a format that is understood by chatbots and can be served by them upon request triggering.</p><p>The goal should be to generate the content once and reuse after that. Accomplishing this needs close links between whoever is curating the content which can be your service teams and the digital marketing department.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Analyse customer contact/touchpoint to create relevant content</strong></span></h3><p>The commencing point is to build content that will have the maximum effect – where content can tone down the motive for calling, and the volume of calls is higher. To understand why people are connecting with your business needs a deeper analysis that can be made effective by assessing who is calling – for what, when and why, breaking down the overall customer journey, testing of contacts across channels and research activities like customer surveys, online as well as offline conversations and e-mail content.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Create a channel pyramid and flip it in order to migrate</strong></span></h3><p>While creating a channel pyramid, at the topmost part would be the chatbots.&nbsp;In the beginning, these would resolve only a small portion of overall queries, acting as a preliminary step or an online IVR and directing customers to the digital teams who are more suitable to manage the interactions.</p><p>The digital teams will cover web chat and messaging platforms as the center layer, assisting the migration to the digital conversation which is lower in cost as compared to voice at the absolute bottom of the pyramid which entails maximum cost and higher volume channel.</p><p>Using all the analysis carried out and the generation of applicable online content, we can then flip the pyramid with chatbots dealing with more and more complex queries – final objective being that they will manage the majority of the cases shortly.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Trim down risks by piloting and involving practiced agents</strong></span></h3><p>Chatbots are not readily developed technology tools, so the risk of a deprived experience is something one must take into consideration.&nbsp;A few forms of AI can be tested with the internal teams rather than direct exposure to the customers or prospects. It would be prudent to locate these trials in an innovation hub with the added support of the contact center.&nbsp;This contact centers will have the precise mix of skill-sets to make it workable and later try in real-time.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>How to make a chatbot with a chatbot development company?</strong></span></h3><p>Before initiating any project, it’s essential to characterize goals, the roadmap along with the ways to quantify success.</p><p>At Maruti Techlabs, being a <a href="https://marutitech.com/" target="_blank" rel="noopener">chatbot development company</a>, we use agile methodologies, and our procedures are influenced by Kanban, XP, and Scrum. We customize our processes for the different projects and customers that we work with.</p><p>Our processes are collaborative, transparent, user-centered and iterative. Being agile assists us to reduce overall project risks, handle change and maximize customer value.</p>15:T14b6,<p>Every project starts with a discovery phase and is pursued by iterative development cycles. Each sprint or cycle is of 2 to 4 weeks.</p><ul><li><strong>Discovery</strong></li></ul><p>The discovery phase is undertaken at the commencement of the chatbot development project. It consists largely of requirements collection workshops, stakeholder interviews and analyzing key end-user needs. The backlog is the prime output of this phase with recognized requirements written as ‘user stories’. Identifying the use-case and type they’re looking for (sequential bot or NLP based) helps in listing out the various intents and actions that are to be carried out the chatbot. Discovering the requirements from the end user viewpoint leads the project team to explore the product features with depth.</p><p>For example, selecting the precise messaging platform (Facebook Messenger, Slack or your Website) is the key point of the discovery phase. Ultimately the preference should be focused as per your target audience, and you need to meet them where they are.</p><p>We use precise tools to analyze the requirements and host the backlog for issue tracking. A product member from the client’s side is in charge of prioritizing the backlog stages. The backlog is a project execution document and can be revised at any time all through the project.</p><ul><li><strong>Plan</strong></li></ul><p>Before we get to the bot’s personality, it is important to create user journeys, and conversation design flows that empower the process for our clients. This could be seen as the counterpart of user journey mapping along with wireframes for visual UI projects.</p><p><strong>For conversational flow ask yourself these questions:</strong></p><ul><li>What are the queries the bot has to solve or execute to steer the visitor to the desired endpoint?</li><li>What details will the visitor have to provide to get the best possible reply?</li><li>What are all the steps to reach that final objective?</li></ul><p>The number of conversations or interactions required in order to reach out and satisfy the user’s purpose is critical and significant. Even though there are a myriad of techniques that can help achieve the bot’s personality, the best and the easiest way is to conceptualize and develop the character of the <a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">AI chatbot</a> such that it was an actual person doing their job by responding to user queries complete with their own set of quirks.</p><p>Coming back to the sprint, at the opening of every sprint, we conduct a ‘Sprint Planning’ session. The entire team attends the Sprint Planning meeting, and it is where we go through the utmost priority stories in the backlog, state them in detail, plan the tasks and assess them. Every user story is given an approval criteria to be considered as completed.</p><p>We assess and analyze how many points can be accomplished in a sprint by utilizing the velocity metric while deploying techniques to average the precedent performance across sprints and enhance the precision of our approximation with time.</p><p><img src="https://cdn.marutitech.com/Guide-to-a-Successful-RPA-Implementation-in-2019-4.jpg" alt="chatbot development project"></p><ul><li><strong>Build</strong></li></ul><p>We intend to build and deliver a working product which is ready to use by actual users. The thought is to then iterate on this product, accumulating more features until we start off to meet the objectives we set out right at the initial stages.</p><p>Starting with interior functionality and then delving into chatbot personality, the bot scripts are coded and developed cumulatively.</p><p>Conversation design incorporates training the natural language processor to be aware of your users intent and then amplifying the same as more user data becomes obtainable.</p><ul><li><strong>Testing and Review</strong></li></ul><p>If you are developing a bot for a customer, it’s vital to build UATs: User Acceptance Tests. It’s a conversation set that characterizes the&nbsp;conversation flow. Write diverse conversations: one that thrives where the bot is handling the false user input and one when the user isn’t discussing in regards to the bot’s use-case.&nbsp;Ensure that all conversations are practical!</p><p>At the end of every sprint, we conduct a demo. We run across all the created user stories and try to display its implemented process. The product owner who is from the client’s team reviews and decides whether to allow the implementation on the basis of the determined criteria.</p><p>If the user story is 100% accepted, it is given the status as done.</p><ul><li><strong>Technology</strong></li></ul><p>We’re continuously improving our technology offerings and have developed a <a href="https://wotnot.io" target="_blank" rel="noopener">chatbot platform</a> that meets the needs of cross-domain customers. Additionally, we also offer:</p><ul><li>Support for key messenger apps and SMS</li><li>A robust engine that allows for multifaceted conversation workflows</li><li>NLP integration</li><li>RESTful API for swift integration with any of the required web services</li><li>A completely managed solution that we can host and sustain for you</li></ul>16:T6a0,<p><a href="https://wotnot.io/" target="_blank" rel="noopener">Chatbots</a> are not exactly different from other applications; you have multiple integrations that back the application, with the involvement of all the diverse dynamics. Given that a chatbot is needed to engage rapidly with an end-user, it requires being clear whether the information offered by the different integrations, are critical for the conversation or can be deferred until a later moment. Consistency in the integrations through APIs not only assists the agility but also helps in creating perfect conversations.</p><p>Customers today are more insistent than ever, with higher expectations and lower tolerance. A chatbot solution can assist you in meeting those expectations, however, every enterprise doesn’t have the time or resources to come up with a solution that is tailored to their core business – trained and deployable in just a couple weeks time.</p><p>At Maruti Techlabs, we offer end-to-end chatbot development services and assist enterprises &amp; brands in streamlining the way they interact with customers. We deploy highly intelligent, sophisticated and scalable chatbot solutions across multiple domains such as Finance, E-Commerce, Real Estate and Insurance to name a few.</p><p>Being well-versed in AI, NLP and ML, we provide bot solutions, customized to your requirements, across multiple different channels (Facebook, Twitter, Slack, Telegram, Website or your enterprise’s Intranet). <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener">Get in touch</a> with us today to deploy bots that fit your business specific needs while matching your brand’s voice and tone.</p>17:T5ef,<p><i>Understanding the Chatbot need-</i></p><p>Websites play a big role in the conversion of potential customers to sales. Businesses have realized that with the addition of chatbots to their webpages, visitors stay engaged for much longer, thereby greatly improving these conversion rates.</p><p>Chatbots have also been acknowledged as an excellent resource for collecting and sharing relevant information. Furthermore, the automation of simple business processes without sacrificing human resources makes this a very economical way of generating online value.</p><p>But, before one goes ahead and starts <a href="https://marutitech.com/14-powerful-chatbot-platforms/" target="_blank" rel="noopener">building a chatbot</a>, it is important to know what the primary purpose of that bot would be –</p><ul><li>Answering FAQs?</li><li>Providing product information?</li><li>Booking appointments?</li></ul><p>The applications are endless.</p><p>So for our guide, given that we have worked so closely with the <a href="https://www.qsrmagazine.com/outside-insights/chatbots-restaurants-redefining-customer-experience" target="_blank" rel="noopener">food &amp; beverage industry</a>, we have chosen a rather fun application for a chatbot: <i>Ordering a Burger</i>.&nbsp;</p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">However, before we rush off to it, take a few minutes to familiarize yourself with the jargon we will use while building a Dialogflow chatbot.</span></p>18:T1c4d,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;"><i>Learn the jargon/slang associated with building a chatbot on Dialogflow and start sounding like a true pro.</i></span></p><p><strong>Dialogflow </strong>– Dialogflow is a Google-owned framework that enables users to develop human-computer interaction technologies that can support <a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;">Natural Language Processing (NLP)</span></a>. Basically, it lets you make Digital Programs that interact with end users through natural languages. Therefore, you could even say that Dialogflow enables the creation of <i>Conversational User Experience Platforms (</i><a href="https://www.cmswire.com/digital-experience/what-is-conversational-user-experience-ux/" target="_blank" rel="noopener"><i>CUXP</i></a><i>).</i></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">With Dialogflow chatbot, you get the ability to ‘one-click integrate’ with most of the popular messaging platforms such as Facebook, Twitter, Instagram, etc.</span> In this guide, we will be using its ‘Web Demo’ feature to simulate how a basic form of this integration would appear.</p><p>‘<strong>User</strong>‘ – A user is any human being who uses the chatbot technology. They can play any role: owning the chatbot, <a href="https://marutitech.com/nlp-whatsapp-chatbot-dialogflow/" target="_blank" rel="noopener"><span style="color:#f05443;">developing the bot</span></a>, or interacting with the same. As long as they are human, they are termed ‘user’.&nbsp;</p><p>Their exact role is often clear from context so rest assured, you won’t be confused!</p><p><strong>Text/Voice</strong> – These are the modes used to communicate the input or the output. The <a href="https://marutitech.com/banking-need-digital-voice-assistant/" target="_blank" rel="noopener"><span style="color:#f05443;">user interacts with the bot through text or through voice</span></a>. Text would be anything that is typed into the chatbot window and voice would be any message spoken into the chatbot window.</p><p>Different chatbots support different inputs/outputs. Though it is very common to use text since it does away with issues of microphone access, noisy surroundings, diction issues, etc., it is becoming increasingly popular for bots to support both.</p><p><strong>Agent </strong>– An agent is merely another term used to refer to the chatbot. Sometimes people say ‘agent’ when referring to the processing module within the application that enables discussions with the chatbot. And sometimes, it is another way to refer to the bot since it functions ‘like a support agent’. The context will always be clear enough for you know what they mean.</p><p>While using Dialogflow, you will find that many people start off by asking you to ‘name the agent.’ This just means giving your chatbot a name, so even in this context, its one and the same.</p><p><strong>Expressions </strong>– Expressions/Training Phrases are the dialogues/utterances that people say when they interact with a bot. They represent a user’s desire and are often in the form of a question. For example –&nbsp;</p><p><i>“Is the store open?”</i></p><p><i>“Do you serve vegetarian?”</i></p><p><i>“Where is my order?”</i></p><p>One of the first rules to accept when working with Expressions in Chatbot Development is that, the same thing, can and will be said in different ways.</p><p>See our three dialogues above? Let’s rephrase them:</p><p><i>“What are your store timings?”</i></p><p><i>“Do you only serve non-veg?”</i></p><p><i>“My order is late.”</i></p><p>Different people say the same things in different ways. It is, therefore, very important to predict/collate a set of Expressions (often referred to as FAQs), when you are training your chatbot to answer them. These FAQs will be laying the groundwork when you start developing your bot.</p><p><strong>Intent –</strong> ‘Intents’ are how a chatbot understands Expressions.&nbsp;</p><p>We just saw how varied Expressions can be while still meaning the same thing. This <i>meaning </i>is termed as an <i>Intent</i>, wherein we extract what the user i<i>ntends </i>to say through his/her Expression.&nbsp;</p><p>It is the simple process of grouping expressions into their one meaning, thereby making it easier to program.</p><p>Let’s determine an Intent from the following Expressions in the following example:</p><p><i>“Are you closed on Sundays?”</i></p><p><i>“What time do you open?”</i></p><p><i>“What are your store timings?”</i></p><p>All these Expressions want to know about the Store Timings. The Intent can therefore be, ‘<i>Store Timings</i>‘.</p><p>By using Intents you don’t have to teach your chatbot how to respond to every Expression. Instead, you can just categorise Expressions into Intents that the bot can easily tackle. It is a lot simpler for both the developer and the chatbot this way.</p><p>Ultimately, Intents determine the bot’s responses.</p><p><strong>Responses: </strong>This is the chatbot’s output that is aimed at satisfying the user’s intent.</p><p>For example, if the Expressions trigger the Intent ‘Store Timings’, the chatbot can respond saying,&nbsp;</p><p><i>“The store is open everyday from 10:00 hrs to 23:00 hrs, except Sundays.”</i></p><p>The most accurate responses occur when a proper range of expressions have been correctly grouped into Intents. Accurate and simple responses are important traits for a good chatbot.</p><p><strong>Entities</strong>: ‘Entities’ are Dialogflow’s mechanism for identifying and extracting useful data from natural language inputs.&nbsp;</p><p>An Intent limits the bot to the scope of the user input. Entities enable it to extract specific pieces of information from your users. This can be anything from burger toppings to appointment dates. Basically, if there is any important data you want to get from the user, you will use a corresponding entity.&nbsp;</p><p><strong>Actions &amp; Parameters:</strong> These too, are Dialogflow mechanisms. They serve as a method to identify/annotate the keywords/values in the training phrases by connecting them with Entities.</p><p>They also provide the prompts that extract information from the user. For example:</p><p>“When do you want an appointment?”</p><p>“What toppings would you like?”</p><p>Actions allow the developer to work with code; but we will make our BurgerBot without it, so relax!</p><p><strong>Annotate: </strong>The ability of Dialogflow to recognize and link keywords/values between Parameters, Expressions and Entities.</p><p><i>Let’s finally put everything we learned into action and make our own ‘BurgerBot’. Here’s the guide!</i></p><figure class="image"><a href="https://marutitech.com/case-study/covid-awareness-whatsapp-chatbot/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/large_covid_19_chatbot_8d3bcead3a_04023fdabf.png" alt="how a chatbot reduced the burden "></a></figure>19:T5125,<h3><strong>Level 1 – </strong><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;"><strong>Develop a Dialogflow chatbot</strong></span></h3><p><strong>Step 1: Getting set up with a DialogFlow Account.</strong></p><ol><li>Go to <a href="https://dialogflow.com/" target="_blank" rel="noopener"><span style="color:#f05443;">https://dialogflow.com/</span></a></li><li>Click ‘Go to console’ in the top right corner.</li><li>Login with a Gmail account when prompted.</li></ol><p><img src="https://cdn.marutitech.com/c4c77fc4-picture1.png" alt="develop a chatbot using dialogflow" srcset="https://cdn.marutitech.com/c4c77fc4-picture1.png 1640w, https://cdn.marutitech.com/c4c77fc4-picture1-768x221.png 768w, https://cdn.marutitech.com/c4c77fc4-picture1-1500x431.png 1500w, https://cdn.marutitech.com/c4c77fc4-picture1-705x202.png 705w, https://cdn.marutitech.com/c4c77fc4-picture1-450x129.png 450w" sizes="(max-width: 1640px) 100vw, 1640px" width="1640"></p><p><strong>Step 2: Creating an Agent&nbsp;</strong></p><ol><li>Start off by clicking ‘Create Agent’ in the column menu to your left.</li><li>Give your Bot a name! We’re going to call ours a ‘BurgerBot’.</li><li>Be sure to select your time zone and language as required.</li><li>Click ‘Create’.</li></ol><p><img src="https://cdn.marutitech.com/09ed434b-picture2.png" alt="create a chatbot using dialogflow"></p><p><img src="https://cdn.marutitech.com/f56a2753-picture3.png" alt="create a chatbot using dialogflow" srcset="https://cdn.marutitech.com/f56a2753-picture3.png 1380w, https://cdn.marutitech.com/f56a2753-picture3-768x199.png 768w, https://cdn.marutitech.com/f56a2753-picture3-705x182.png 705w, https://cdn.marutitech.com/f56a2753-picture3-450x116.png 450w" sizes="(max-width: 1380px) 100vw, 1380px" width="1380"></p><p>Congratulations! You have created your first agent. Once the system recognizes it, you will see how massively your left column menu expands.</p><p>Let’s use some of these features and develop our BurgerBot.</p><h3><strong>Level 2 – Bot Development</strong></h3><p><strong>Step 1: Checking out the Preset Intents</strong></p><p>Dialogflow provides basic presets like a Default Welcome Intent and a Default Fallback Intent.</p><p>This is just telling the bot what to do when welcoming someone or when the bot doesn’t know the answer to their question. Click on ‘Default Welcome Intent’.</p><p><img src="https://cdn.marutitech.com/055583c1-picture4.png" alt="creating a chatbot using dialogflow" srcset="https://cdn.marutitech.com/055583c1-picture4.png 977w, https://cdn.marutitech.com/055583c1-picture4-768x568.png 768w, https://cdn.marutitech.com/055583c1-picture4-705x521.png 705w, https://cdn.marutitech.com/055583c1-picture4-450x333.png 450w" sizes="(max-width: 977px) 100vw, 977px" width="977"></p><p>Scroll to the ‘Training phrases’ section. Here you will see a set of conversation starter Expressions that a user might say to our BurgerBot. Note that all of these convey the same message, and are therefore categorized under one Intent: ‘Default Welcome Intent’.</p><p><img src="https://cdn.marutitech.com/389de515-picture5.png" alt="build a bot using dialogflow" srcset="https://cdn.marutitech.com/389de515-picture5.png 953w, https://cdn.marutitech.com/389de515-picture5-768x662.png 768w, https://cdn.marutitech.com/389de515-picture5-705x608.png 705w, https://cdn.marutitech.com/389de515-picture5-450x388.png 450w" sizes="(max-width: 953px) 100vw, 953px" width="953"></p><p>The expressions seems to cover pretty much all the ways a user might start the conversation, so we don’t need to adjust anything here. Let’s test this out:</p><p>In the top right section you can test how the BurgerBot performs. Type a conversation starter, like Hey or Hi or Hello.</p><p><img src="https://cdn.marutitech.com/1d199435-picture6.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/1d199435-picture6.png 511w, https://cdn.marutitech.com/1d199435-picture6-450x566.png 450w" sizes="(max-width: 511px) 100vw, 511px" width="511"></p><p>The BurgerBot is alive! Try other expressions again and you’ll see that it picks a response at random. Let’s check out these responses and make our first edit!</p><p><strong>Step 2: Creating a custom response under Default Welcome Intent</strong></p><p>Scroll down to the ‘Responses’ section. Here you can see the different responses that our BurgerBot picked randomly when we entered an expression.&nbsp;</p><p><img src="https://cdn.marutitech.com/f3003311-picture7.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/f3003311-picture7.png 955w, https://cdn.marutitech.com/f3003311-picture7-768x495.png 768w, https://cdn.marutitech.com/f3003311-picture7-705x455.png 705w, https://cdn.marutitech.com/f3003311-picture7-450x290.png 450w" sizes="(max-width: 955px) 100vw, 955px" width="955"></p><p>We are going to create a special welcoming response that suits our restaurant: Patty Palace.</p><p><i>“Hello! Welcome to Patty Palace. My name is BurgerBot.”</i></p><p>Add this text below the existing responses. We can simply delete the other generic responses since we don’t need them anymore. To delete, simply press the ‘trashcan’ button to the right of every response.</p><p>But our response is not complete yet. Let’s finish it by adding a second line. To add a new line, Click ‘Add Responses’ and select ‘Text Response’.</p><p>Here’s an example of what you can do with it.</p><p><img src="https://cdn.marutitech.com/c46f3e7b-picture8.png" alt="chatbot with dialogflow" srcset="https://cdn.marutitech.com/c46f3e7b-picture8.png 959w, https://cdn.marutitech.com/c46f3e7b-picture8-768x667.png 768w, https://cdn.marutitech.com/c46f3e7b-picture8-705x612.png 705w, https://cdn.marutitech.com/c46f3e7b-picture8-450x391.png 450w" sizes="(max-width: 959px) 100vw, 959px" width="959"></p><p>And here is how it would look:</p><p><img src="https://cdn.marutitech.com/3469f689-picture9.png" alt="chatbot using dialogflow" srcset="https://cdn.marutitech.com/3469f689-picture9.png 508w, https://cdn.marutitech.com/3469f689-picture9-450x501.png 450w" sizes="(max-width: 508px) 100vw, 508px" width="508"></p><p>You can add as many lines as you want, but be sure to simulate a friendly, human agent-like experience for your users.&nbsp;</p><p><strong>CAUTION:&nbsp;</strong>Never forget to <strong>Save</strong></p><p>Never forget to click the ‘Save’ button at the top. Your changes <strong>will not take effect </strong>if you have not selected ‘Save’. Always look for these icons that give you the green signal to go ahead:</p><p><img src="https://cdn.marutitech.com/7abe12ee-picture10.png" alt=""> &nbsp;<img src="https://cdn.marutitech.com/e86b72a0-picture11.png" alt=""></p><p>Great Job! We have set base for our BurgerBot to welcome users. Let’s proceed to equip our bot with more helpful skills.</p><p><strong>Step 3: Creating New Intents</strong></p><p>Let’s develop our BurgerBot to assist users with some common queries:</p><p><i>“What are your delivery timings?”</i></p><p><i>“Is there anything new?”</i></p><p><i>“I’d like to order a burger”</i></p><p>We’ll create Intents for each of these question-types, then feed-in the appropriate Expressions &amp; Responses.&nbsp;</p><p>To create a new Intent, simply click the ‘+’ next to the ‘Intents’ button in the left menu.</p><p>Be organised when naming an Intent so that it is easy for you to recognise later.&nbsp;</p><p><strong>Points to remember:</strong></p><ul><li>Add a variety of Expressions</li><li>Group Expressions correctly under well-defined Intents</li><li>Keep Responses precise</li><li>Always click ‘Save’.</li></ul><p>Here’s an example of what you can do with your first two intents:</p><p><img src="https://cdn.marutitech.com/84dc1d87-picture12.png" alt="build chatbot with dialogflow" srcset="https://cdn.marutitech.com/84dc1d87-picture12.png 1015w, https://cdn.marutitech.com/84dc1d87-picture12-768x602.png 768w, https://cdn.marutitech.com/84dc1d87-picture12-705x552.png 705w, https://cdn.marutitech.com/84dc1d87-picture12-450x352.png 450w" sizes="(max-width: 1015px) 100vw, 1015px" width="1015"></p><p style="text-align:center;">Expressions – New Intent&nbsp;</p><p><img src="https://cdn.marutitech.com/7bc3a27e-picture13.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/7bc3a27e-picture13.png 918w, https://cdn.marutitech.com/7bc3a27e-picture13-768x561.png 768w, https://cdn.marutitech.com/7bc3a27e-picture13-705x515.png 705w, https://cdn.marutitech.com/7bc3a27e-picture13-450x328.png 450w" sizes="(max-width: 918px) 100vw, 918px" width="918"></p><p style="text-align:center;">Response – New Intent&nbsp;</p><p><img src="https://cdn.marutitech.com/b2b30f49-picture14.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/b2b30f49-picture14.png 919w, https://cdn.marutitech.com/b2b30f49-picture14-768x633.png 768w, https://cdn.marutitech.com/b2b30f49-picture14-705x581.png 705w, https://cdn.marutitech.com/b2b30f49-picture14-450x371.png 450w" sizes="(max-width: 919px) 100vw, 919px" width="919"></p><p style="text-align:center;">Expressions – New Intent&nbsp;</p><p><img src="https://cdn.marutitech.com/7c145585-picture15.png" alt="building a chatbot using dialogflow" srcset="https://cdn.marutitech.com/7c145585-picture15.png 837w, https://cdn.marutitech.com/7c145585-picture15-768x423.png 768w, https://cdn.marutitech.com/7c145585-picture15-705x388.png 705w, https://cdn.marutitech.com/7c145585-picture15-450x248.png 450w" sizes="(max-width: 837px) 100vw, 837px" width="837"></p><p style="text-align:center;">Response – New Intent&nbsp;</p><p>Great job! Let’s kick this up a notch.</p><h3><strong>Level 3: Entities, Actions &amp; Parameters</strong></h3><p>Let’s make one more Intent so that BurgerBot can start taking orders.</p><p><strong>Step 1: Creating Entities</strong></p><ol><li>Click the ‘+’ next to the ‘Entities’ button in the left menu.</li><li>Enter the values of the Burger Buns and Toppings separately</li><li>Be sure to add appropriate synonyms</li></ol><p><img src="https://cdn.marutitech.com/ae3cd7c7-picture36.png" alt="building a bot using dialogflow" srcset="https://cdn.marutitech.com/ae3cd7c7-picture36.png 1149w, https://cdn.marutitech.com/ae3cd7c7-picture36-768x188.png 768w, https://cdn.marutitech.com/ae3cd7c7-picture36-705x173.png 705w, https://cdn.marutitech.com/ae3cd7c7-picture36-450x110.png 450w" sizes="(max-width: 1149px) 100vw, 1149px" width="1149"></p><p><img src="https://cdn.marutitech.com/d15b2638-picture17.png" alt="bot using dialogflow" srcset="https://cdn.marutitech.com/d15b2638-picture17.png 1135w, https://cdn.marutitech.com/d15b2638-picture17-768x344.png 768w, https://cdn.marutitech.com/d15b2638-picture17-705x316.png 705w, https://cdn.marutitech.com/d15b2638-picture17-450x202.png 450w" sizes="(max-width: 1135px) 100vw, 1135px" width="1135"></p><p><img src="https://cdn.marutitech.com/6d371214-picture18.png" alt="chatbot using dialogflow" srcset="https://cdn.marutitech.com/6d371214-picture18.png 1147w, https://cdn.marutitech.com/6d371214-picture18-768x233.png 768w, https://cdn.marutitech.com/6d371214-picture18-705x214.png 705w, https://cdn.marutitech.com/6d371214-picture18-450x137.png 450w" sizes="(max-width: 1147px) 100vw, 1147px" width="1147"></p><p><strong>Step 2: Creating the Intent</strong></p><ol><li>Name the new Intent ‘Placing Orders’</li><li>Scroll down to add parameters first</li></ol><p><strong>Step 3: Actions &amp; Parameters</strong></p><ol><li>Name the Parameters</li><li>Enter the ‘Entity’ that you created, starting with the ‘@’ symbol</li><li>Enter corresponding ‘Value’, starting with the ‘$’ sign</li><li>Check the ‘Required’ box to enable ‘Prompts’</li></ol><p><img src="https://cdn.marutitech.com/f8ed98b7-picture19.png" alt="bot with dialogflow" srcset="https://cdn.marutitech.com/f8ed98b7-picture19.png 1122w, https://cdn.marutitech.com/f8ed98b7-picture19-768x329.png 768w, https://cdn.marutitech.com/f8ed98b7-picture19-705x302.png 705w, https://cdn.marutitech.com/f8ed98b7-picture19-450x193.png 450w" sizes="(max-width: 1122px) 100vw, 1122px" width="1122"></p><ol><li>Add prompt messages like shown below</li></ol><p><img src="https://cdn.marutitech.com/486d8856-picture20.png" alt="bot using dialogflow" srcset="https://cdn.marutitech.com/486d8856-picture20.png 734w, https://cdn.marutitech.com/486d8856-picture20-705x409.png 705w, https://cdn.marutitech.com/486d8856-picture20-450x261.png 450w" sizes="(max-width: 734px) 100vw, 734px" width="734"></p><p><img src="https://cdn.marutitech.com/d9fa0680-picture21.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/d9fa0680-picture21.png 735w, https://cdn.marutitech.com/d9fa0680-picture21-705x414.png 705w, https://cdn.marutitech.com/d9fa0680-picture21-450x264.png 450w" sizes="(max-width: 735px) 100vw, 735px" width="735"></p><p><strong>Step 4: Adding Expressions</strong></p><ol><li>Proceed to add the training phrases</li><li>Notice automatic colour coded annotation</li><li>Manually annotate (if required) by right clicking the phrases and assigning the entities</li></ol><p><img src="https://cdn.marutitech.com/5a9a3e67-picture22.png" alt="bot building with dialogflow" srcset="https://cdn.marutitech.com/5a9a3e67-picture22.png 1040w, https://cdn.marutitech.com/5a9a3e67-picture22-768x527.png 768w, https://cdn.marutitech.com/5a9a3e67-picture22-705x483.png 705w, https://cdn.marutitech.com/5a9a3e67-picture22-450x309.png 450w" sizes="(max-width: 1040px) 100vw, 1040px" width="1040"></p><p><strong>Step 5: Adding the Response</strong></p><ol><li>Draft a concluding response.</li><li>Include the ‘$value’ in the message so that it can copy useful information from the Parameters. Refer to the image below.</li><li>Toggle on the Intent as ‘end of conversation’.</li></ol><p><img src="https://cdn.marutitech.com/4b61ab29-picture23.png" alt="chatbot with dialogflow" srcset="https://cdn.marutitech.com/4b61ab29-picture23.png 1091w, https://cdn.marutitech.com/4b61ab29-picture23-768x322.png 768w, https://cdn.marutitech.com/4b61ab29-picture23-705x295.png 705w, https://cdn.marutitech.com/4b61ab29-picture23-450x188.png 450w" sizes="(max-width: 1091px) 100vw, 1091px" width="1091"></p><h3><strong>Level 4: Integration</strong></h3><p>Actual chatbot deployment on platforms, like your websites, etc. is a complicated procedure that required publishing the bot. But we can still get an idea of how the chatbot would appear when functional. Here’s how:</p><ol><li>Navigate to the ‘Integration’ section in the left column</li><li>Toggle ‘Web Demo’ On, then click it to enter</li></ol><p><img src="https://cdn.marutitech.com/07357e62-picture24.png" alt="dialogflow chatbot" srcset="https://cdn.marutitech.com/07357e62-picture24.png 750w, https://cdn.marutitech.com/07357e62-picture24-705x395.png 705w, https://cdn.marutitech.com/07357e62-picture24-450x252.png 450w" sizes="(max-width: 750px) 100vw, 750px" width="750"></p><p style="text-align:center;">Click the URL</p><p><img src="https://cdn.marutitech.com/0cddd144-picture25.png" alt="" srcset="https://cdn.marutitech.com/0cddd144-picture25.png 921w, https://cdn.marutitech.com/0cddd144-picture25-768x590.png 768w, https://cdn.marutitech.com/0cddd144-picture25-705x542.png 705w, https://cdn.marutitech.com/0cddd144-picture25-450x346.png 450w" sizes="(max-width: 921px) 100vw, 921px" width="921"></p><p style="text-align:center;">Start Interacting!</p><p>Here’s how skilled our BurgerBot has gotten:</p><p><img src="https://cdn.marutitech.com/7ce75506-picture26.png" alt="" srcset="https://cdn.marutitech.com/7ce75506-picture26.png 603w, https://cdn.marutitech.com/7ce75506-picture26-532x705.png 532w, https://cdn.marutitech.com/7ce75506-picture26-450x596.png 450w" sizes="(max-width: 603px) 100vw, 603px" width="603"></p><p><img src="https://cdn.marutitech.com/2676a1c2-picture27.png" alt="" srcset="https://cdn.marutitech.com/2676a1c2-picture27.png 607w, https://cdn.marutitech.com/2676a1c2-picture27-450x480.png 450w" sizes="(max-width: 607px) 100vw, 607px" width="607"></p><p><img src="https://cdn.marutitech.com/bd6dd774-picture28.png" alt="" srcset="https://cdn.marutitech.com/bd6dd774-picture28.png 605w, https://cdn.marutitech.com/bd6dd774-picture28-450x482.png 450w" sizes="(max-width: 605px) 100vw, 605px" width="605"></p><p><img src="https://cdn.marutitech.com/644bc4a2-picture29.png" alt="" srcset="https://cdn.marutitech.com/644bc4a2-picture29.png 603w, https://cdn.marutitech.com/644bc4a2-picture29-450x361.png 450w" sizes="(max-width: 603px) 100vw, 603px" width="603"></p><h3><strong>Level 5: Training &amp; Fallbacks</strong></h3><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Let’s get back to the Dialogflow chatbot.</span> It is important to keep training the chatbot to improve its accuracy, fix errors and accommodate fallbacks. Remember that this is a smart bot; it uses machine learning to improve itself. It constantly learns based on it’s interactions &amp; training.</p><p><strong>Step 1: Training</strong></p><ol><li>Navigate to the ‘Training’ section in the left menu.</li><li>Select one of the rows of data. Each row is a conversation.</li></ol><p><img src="https://cdn.marutitech.com/43f0c529-picture30.png" alt="" srcset="https://cdn.marutitech.com/43f0c529-picture30.png 1073w, https://cdn.marutitech.com/43f0c529-picture30-768x586.png 768w, https://cdn.marutitech.com/43f0c529-picture30-705x538.png 705w, https://cdn.marutitech.com/43f0c529-picture30-450x343.png 450w" sizes="(max-width: 1073px) 100vw, 1073px" width="1073"></p><ol><li>Click the conversation to view the session window.</li><li>Study the session. If an Intent has been mismatched, then right click and correct it.</li><li>Double check before approving. Incorrect approving will only teach the bot to make more mistakes.</li><li>Check this example where our BurgerBot misread the Intent when the expression was <i>“There is no non veg burger”</i>, and how we corrected it to a Fallback Intent.</li></ol><p><img src="https://cdn.marutitech.com/28e0193d-picture31.png" alt="" srcset="https://cdn.marutitech.com/28e0193d-picture31.png 1084w, https://cdn.marutitech.com/28e0193d-picture31-768x204.png 768w, https://cdn.marutitech.com/28e0193d-picture31-705x187.png 705w, https://cdn.marutitech.com/28e0193d-picture31-450x120.png 450w" sizes="(max-width: 1084px) 100vw, 1084px" width="1084"></p><p style="text-align:center;">Error</p><p><img src="https://cdn.marutitech.com/339703c5-picture32.png" alt="" srcset="https://cdn.marutitech.com/339703c5-picture32.png 1304w, https://cdn.marutitech.com/339703c5-picture32-768x323.png 768w, https://cdn.marutitech.com/339703c5-picture32-705x297.png 705w, https://cdn.marutitech.com/339703c5-picture32-450x189.png 450w" sizes="(max-width: 1304px) 100vw, 1304px" width="1304"></p><p style="text-align:center;">Trained</p><p><strong>Step 2: Fallback</strong></p><p>What happens when a chatbot doesn’t know the answer to a question?&nbsp;</p><p>For example –&nbsp;</p><p><i>“When will Patty Palace serve non-veg burgers?”</i></p><p><i>“When do you start midnight deliveries?”</i></p><p>To tackle such questions, create a response in the Default Fallback Intent to adjust expectations with the user.</p><p><img src="https://cdn.marutitech.com/37340b67-picture33.png" alt="" srcset="https://cdn.marutitech.com/37340b67-picture33.png 1041w, https://cdn.marutitech.com/37340b67-picture33-768x392.png 768w, https://cdn.marutitech.com/37340b67-picture33-705x360.png 705w, https://cdn.marutitech.com/37340b67-picture33-450x230.png 450w" sizes="(max-width: 1041px) 100vw, 1041px" width="1041"></p><p>Remember, all this data is being collected in sessions. So when you have enough expressions from users asking about new things, you can collate them to continue adding Intents.</p><p><img src="https://cdn.marutitech.com/9b798035-picture34.png" alt=""></p><p><strong>Step 3: Building new skills</strong></p><ol><li>Check your BurgerBot’s conversation history by selecting ‘History’ in the left column.</li><li>Collate questions that triggered fallback responses</li><li>Repeat steps learnt before to create new intents.</li><li>Continue adding to and training your bot.</li></ol><p><img src="https://cdn.marutitech.com/28cb247b-picture35.png" alt="" srcset="https://cdn.marutitech.com/28cb247b-picture35.png 603w, https://cdn.marutitech.com/28cb247b-picture35-450x513.png 450w" sizes="(max-width: 603px) 100vw, 603px" width="603"></p><p><i>Congratulations on completing the guide! You can now show off your very own BurgerBot!</i></p>1a:T1095,<p>Natural Language Processing is a based on <a href="https://marutitech.com/top-8-deep-learning-frameworks/" target="_blank" rel="noopener">deep learning</a> that enables computers to acquire meaning from inputs given by users. In the context of bots, it assesses the intent of the input from the users and then creates responses based on contextual analysis similar to a human being.</p><p>Say you have a chatbot for customer support, it is very likely that users will try to ask questions that go beyond the bot’s scope and throw it off. This can be resolved by having default responses in place, however, it isn’t exactly possible to predict the kind of questions a user may ask or the manner in which they will be raised.</p><p>When it comes to Natural Language Processing, developers can train the bot on multiple interactions and conversations it will go through as well as providing multiple examples of content it will come in contact with as that tends to give it a much wider basis with which it can further assess and interpret queries more effectively.</p><p>So, while training the bot sounds like a very tedious process, the results are very much worth it. <a href="https://www.finextra.com/newsarticle/30513/rbs-gives-ai-a-helping-hand-with-hybrid-bots" target="_blank" rel="noopener">Royal Bank of Scotland uses NLP in their chatbots</a>&nbsp;to enhance customer experience through text analysis to interpret the trends from the customer feedback in multiple forms like surveys, call center discussions, complaints or emails. It helps them identify the root cause of the customer’s dissatisfaction and help them improve their services according to that.</p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/<EMAIL>" alt="NLP based chatbot" srcset="https://cdn.marutitech.com/<EMAIL> 2421w, https://cdn.marutitech.com/<EMAIL> 768w, https://cdn.marutitech.com/<EMAIL> 1500w, https://cdn.marutitech.com/<EMAIL> 705w, https://cdn.marutitech.com/<EMAIL> 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><p><strong>Best NLP Approach</strong></p><p>The best approach towards NLP that is a blend of Machine Learning and Fundamental Meaning for maximizing the outcomes. Machine Learning only is at the core of many NLP platforms, however, the amalgamation of fundamental meaning and Machine Learning helps to make efficient NLP based chatbots. Machine Language is used to train the bots which leads it to continuous learning for natural language processing (NLP) and <a href="https://marutitech.com/advantages-of-natural-language-generation/" target="_blank" rel="noopener">natural language generation (NLG)</a>. Both ML and FM has its own benefits and shortcomings as well. Best features of both the approaches are ideal for resolving the real-world business problems.</p><p>Here’s what an NLP based bot entails &nbsp;–</p><ol><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Lesser false positive outcomes through accurate interpretation</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Identify user input failures and resolve conflicts using statistical modeling</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Use comprehensive communication for user responses</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Learn faster to address the development gaps</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Achieve natural language capability through lesser training data inputs</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Ability to re-purpose&nbsp;the input training data for future learnings</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Provide simple corrective actions for the false positives</span></li></ol><p><img src="https://cdn.marutitech.com/1_Mtech-1.png" alt="nlp-based-chatbots"></p>1b:T1163,<p>NLP engines extensively use Machine Learning to parse user input in order to take out the necessary entities and understand user intent. NLP based <a href="https://wotnot.io/" target="_blank" rel="noopener">chatbots</a> can parse multiple user intents to minimize the failures.</p><p><strong>Intent Recognition –</strong></p><p>User inputs through a chatbot are broken and compiled into a user intent through few words. For e.g., “search for a pizza corner in Seattle which offers deep dish margherita”.</p><p>NLP analyses complete sentence through the understanding of the meaning of the words, positioning, conjugation, plurality, and many other factors that human speech can have. Thus, it breaks down the complete sentence or a paragraph to a simpler one like – search for pizza to begin with followed by other search factors from the speech to better understand the intent of the user.</p><p>This attribute also facilitates <a href="https://marutitech.com/nlp-contract-management-analysis/" target="_blank" rel="noopener">NLP contract management and analysis</a>.</p><p><strong>Dealing with Entity –</strong></p><p>Entities can be fields, data or words related to date, time, place, location, description, a synonym of a word, a person, an item, a number or anything that specifies an object. The chatbots are able to identify words from users, matches the available entities or collects additional entities of needed to complete a task.</p><p><strong>Capitalization of Nouns –</strong></p><p>NLP enabled chatbots remove capitalization from the common nouns and recognize the proper nouns from speech/user input.</p><p><strong>Expansion &amp; Transfer of vocabulary –</strong></p><p>NLP enables bots to continuously add new synonyms and uses Machine Learning to expand chatbot vocabulary while also transfer vocabulary from one bot to the next.</p><p><strong>Tense of the Verbs –</strong></p><p>AI chatbots understand different tense and conjugation of the verbs through the tenses.</p><p><strong>Contractions –</strong></p><p>Bots with NLP can expand the contractions and simplify the tasks removing apostrophes in between the words.</p><p>Other than these, there are many capabilities that NLP enabled bots possesses, such as – document analysis, machine translations, distinguish contents and more.</p><p>NLP engines rely on the following elements in order to process queries –</p><ul><li><strong>Intent</strong> – The central concept of constructing a <a href="https://marutitech.com/trends-need-to-know-about-conversational-marketing/" target="_blank" rel="noopener"><span style="color:#f05443;">conversational</span></a> user interface and it is identified as the task a user wants to achieve or the problem statement a user is looking to solve.</li><li><strong>Utterance – </strong>The various different instances of sentences that a user may give as input to the chatbot as when they are referring to an intent.</li><li><strong>Entity</strong>. They include all characteristics and details pertinent to the user’s intent. This can range from location, date, time, etc.</li><li><strong>Context</strong>. This helps in saving and share different parameters over the entirety of the user’s session.</li><li><strong>Session</strong>. This essentially covers the start and end points of a user’s conversation.</li></ul><p>There are many NLP engines available in the market right from <a href="https://dialogflow.com/" target="_blank" rel="noopener">Google’s Dialogflow</a> (previously known as API.ai), <a href="https://wit.ai/" target="_blank" rel="noopener">Wit.ai</a>, <a href="https://www.ibm.com/watson/services/conversation/" target="_blank" rel="noopener">Watson Conversation Service</a>, <a href="https://aws.amazon.com/lex/" target="_blank" rel="noopener">Lex</a> and more. Some services provide an all in one solution while some focus on resolving one single issue.</p><p><img src="https://cdn.marutitech.com/3_Mtech.png" alt="nlp-based-chatbot"></p><p>At its core, the crux of natural language processing lies in understanding input and translating it into language that can be understood between computers. To extract intents, parameters and the main context from utterances and transform it into a piece of <a href="https://marutitech.com/big-data-analysis-structured-unstructured-data/" target="_blank" rel="noopener">structured data</a> while also calling APIs is the job of NLP engines.</p>1c:T12bd,<p>There are many <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">different types of chatbots</a> created for various purposes like FAQ, customer service, virtual assistance and much more. Chatbots without NLP rely majorly on pre-fed static information &amp; are naturally less equipped to handle human languages that have variations in emotions, intent, and sentiments to express each specific query.</p><p>Let’s check out the reasons that your chatbot should have NLP in it –</p><p><strong>1.Overcoming the challenges of language variations –</strong></p><p>The problem with the approach of pre-fed static content is that languages have an infinite number of variations in expressing a specific statement. There are uncountable ways a user can produce a statement to express an emotion. Researchers have worked long and hard to make the systems interpret the language of a human being.</p><p><span style="font-family:Arial;">Through </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Arial;">Natural Language Processing implementation</span></a><span style="font-family:Arial;">, it is possible to make a connection between the incoming text from a human being and the system-generated response.</span> This response can be anything starting from a simple answer to a query, action based on customer request or store any information from the customer to the system database. NLP can differentiate between the different type of requests generated by a human being and thereby enhance customer experience substantially.</p><ul><li>NLP based chatbots are smart to understand the language semantics, text structures, and speech phrases. Therefore, it empowers you to analyze a vast amount of unstructured data and make sense.</li><li>NLP is capable of understanding the morphemes across languages which makes a bot more capable of understanding different nuances.</li><li>NLP gives chatbots the ability to understand and interpret slangs and learn abbreviation continuously like a human being while also understanding various emotions through sentiment analysis.</li></ul><p><strong>2.Shift in focus on more important tasks</strong></p><p>Generally many different roles &amp; resources are deployed in order to make an organization function, however, that entails repetition of manual tasks across different verticals like customer service, human resources, catalog management or invoice processing. <a href="https://marutitech.com/artificial-intelligence-for-customer-service-2/" target="_blank" rel="noopener">NLP based chatbots reduce the human efforts in operations like customer service</a> or invoice processing dramatically so that these operations require fewer resources with increased employee efficiency.</p><p>Now, employees can focus on mission critical tasks and tasks that impact the business positively in a far more creative manner as opposed to losing time on tedious repeated tasks every day. You can use NLP based chatbots for internal use as well especially for Human Resources and IT Helpdesk.</p><p><strong>3.Increased profitability due to reduced cost</strong></p><p>Costing is the essential aspect for any business to grow and increase profitability. NLP based chatbots can significantly assist in cutting down costs associated with manpower and other resources entangled in repetitive tasks as well as costs on customer retention, while&nbsp;improving efficiency and streamlining workflows.</p><p><strong>4.Higher efficient systems lead to customer satisfaction</strong></p><p>Millennials today want an instant response and instant solutions for their queries. NLP helps chatbots understand, analyze and prioritize the questions according to the complexity &amp; this enables bots to respond to customer queries faster than a human being. Faster responses help in building customer trust and subsequently, more business.</p><p>You’ll experience an increased customer retention rate after using chatbots. It reduces the effort and cost of acquiring a new customer each time by increasing loyalty of the existing ones. Chatbots give the customers the time and attention they want to make them feel important and happy.</p><p><strong>5.Market Research and Analysis for making impactful business decisions</strong></p><p>You can get or generate a considerable amount of versatile and unstructured content just from social media. NLP helps in structuring the unstructured content and draw meaning from it. You can easily understand the meaning or idea behind the customer reviews, inputs, comments or queries. You can get a glimpse at how the user is feeling about your services or your brand.</p>1d:T524,<p><img src="https://cdn.marutitech.com/2_Mtech.png" alt="nlp-based-chatbot"></p><p>NLP based chatbots can help enhance your business processes and elevate customer experience to the next level while also increasing overall growth and profitability. It provides technological advantages to stay competitive in the market-saving time, effort and costs that further leads to increased customer satisfaction and increased engagements in your business.</p><p>Although NLP, NLU and NLG isn’t exactly at par with human language comprehension, given its subtleties and contextual reliance; <a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">an intelligent chatbot</a> can imitate that level of understanding and analysis fairly well. Within semi restricted contexts, a bot can execute quite well when it comes to assessing the user’s objective &amp; accomplish required tasks in the form of a self-service interaction.</p><p>At the end of the day, with NLP based chatbots, the result is significant when it comes to cutting down on operational costs for customer support through immediate responses with zero down time, round the clock and consistent execution from an “employee” that is new for an extremely short time frame and already well-versed in multiple languages.</p>1e:Tc2c,<p>“The first impression is the last impression,” they say. It also holds true for customer service. The first touchpoint between your prospect and your business defines whether they will turn into a customer or not. To perfect the first impression and the impressions after that, businesses today are turning to chatbot development platforms.</p><p><i>Hey there! This blog is almost about&nbsp;</i><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>3800+ words</strong></i></span><i>&nbsp;long and may take&nbsp;</i><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>~14 mins</strong></i></span><i>&nbsp;to go through the whole thing. We understand that you might not have that much time.</i></p><p><i>This is precisely why we made a&nbsp;</i><span style="font-family:Poppins, sans-serif;font-size:16px;"><i><strong>short video</strong></i></span><i>&nbsp;on the topic. It is less than 2 mins, and summarizes&nbsp;</i><span style="font-family:Poppins, sans-serif;font-size:16px;"><i><strong>14 Most Powerful Platforms to Build a Chatbot.</strong></i></span><i><strong>&nbsp;</strong>We hope this helps you learn more and save your time. Cheers!</i></p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/zCVpiIsfOno" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div><p>Today, excellent customer service is the defining factor for customers choosing your service over your competitors. Being more advanced than a live chat tool, bots address your customers’ queries instantly across channels, without the need for a support agent. Chatbots, owing to their benefits, have become a necessity for businesses to offer impeccable customer service.</p><p>The adoption of chatbots accelerated in the last few years when Facebook opened up its developer platform and explored the possibility of chatbots through their Messenger app.</p><p>One such no-code <a href="https://www.hoory.com/products/chatbot-builder" target="_blank" rel="noopener">AI Chatbot Builder</a> that facilitates seamless conversation between your platform and the user is Hoory. It offers the convenience of creating custom interactions from scratch and optimizing customer engagement.</p><p>A study from Grand View Research states that the <a href="https://www.grandviewresearch.com/press-release/global-chatbot-market#:~:text=The%20global%20chatbot%20market%20is,to%20substantially%20reduce%20operating%20costs." target="_blank" rel="noopener">bot economy will total to $1.25 billion by 2025</a>, while Gartner predicts that 85% of businesses will have some sort of chatbot automation implemented by 2020. With Covid-19 bringing the world to a standstill in March 2020, and businesses looking to cut costs with automation – that <a href="https://www.gartner.com/imagesrv/summits/docs/na/customer-360/C360_2011_brochure_FINAL.pdf" target="_blank" rel="noopener">Gartner prediction</a> is more than likely to come true.&nbsp;</p>1f:Tc91,<p>Getting started with chatbots can be very overwhelming. There are multiple aspects of <a href="https://wotnot.io/blog/how-to-build-a-chatbot-from-a-template-a-step-by-step-guide/?utm_source=Internal%20Link&amp;utm_medium=Blog&amp;utm_campaign=14CB" target="_blank" rel="noopener">how to build a chatbot</a>, such as strategy, conversational flow, technology, tools, process, reporting, and more.</p><p>Before you get to building a chatbot, you need to identify –</p><ul><li>What problem are you trying to solve? This becomes your use case.</li><li>How much time are you/your team currently spending on this problem? This helps you define your ROI later.</li><li>Could you automate a 100% of the process with a bot, or do you need human intervention? This helps you identify if you need the platform to have a chatbot to human handover functionality.</li><li>Do you need the chatbot to push/pull data from a 3rd party system? This will help you narrow down to platforms with ready integrations.</li></ul><p>By closely assessing your processes, understanding your business goals, the chatbot’s objectives, and designing the chatbot conversation flow to handle input/output efficiently, will help you in your journey of building a bot.</p><p>There are mainly three different types of bots that you can build, including –</p><ul><li><strong>Rule-Based Chatbots</strong></li></ul><p>Rule-based bots work on a predefined conversation flow that allows the bot to flow logically based on the user’s inputs/choices. The users navigate through the conversation flow by clicking on buttons, menus, carousels and answering questions.</p><p>Rule-based bots are easier to build, and are more comfortable for users to navigate through. Users cannot ask their own questions, but can only enter details when the bot asks for it (contact details, details pertaining to the use case and more).</p><ul><li><strong>AI Chatbots</strong></li></ul><p>AI chatbots make use of <a href="https://marutitech.com/how-is-natural-language-processing-applied-in-business/" target="_blank" rel="noopener">natural language processing</a> to understand sentence structure and then process that information, while consecutively getting better at answering the question at hand over time.</p><p>Put simply; AI chatbots first understand what the intent behind the customer’s question is, and come back with a relevant and contextual answer, instead of relying on a predetermined output text designed by a human.</p><ul><li><strong>Hybrid Chatbots</strong></li></ul><p>As the name suggests, the hybrid <a href="https://marutitech.com/benefits-chatbot/" target="_blank" rel="noopener">chatbot uses</a> the best of rule based and AI, along with live chat functionality to provide a superior customer experience. To be able to build a chatbot, you would need to –</p><ol><li>Determine the exact tone and personality of the chatbot based on your respective business and use case.</li><li>Include a human element to the chatbot to ensure comfortable and fluent conversations.</li><li>The scripting data you use should reflect your target audience as the conversation design’s success will largely depend on the context and user intent.</li></ol>20:T6f99,<p>With a myriad of chatbot platforms out there, we’ve narrowed down to a list of 14 best chatbot building platforms out there. The list below goes into detail on their features, pros, cons, pricing details, and if you require any technical expertise for building a chatbot for your business.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. </strong></span><a href="https://wotnot.io/?utm_source=internal_link&amp;utm_medium=Blog&amp;utm_campaign=14CB" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>WotNot</strong></span></a></h3><p>WotNot is the best chatbot development platform that helps you build <a href="https://marutitech.com/make-intelligent-chatbot/?utm_source=internal_link&amp;utm_medium=Blog&amp;utm_campaign=14CB" target="_blank" rel="noopener"><span style="color:#f05443;">intelligent chatbots</span></a>, and offer the full range of conversational marketing solutions for more than 16 industries.&nbsp;</p><p>With a <a href="https://wotnot.io/bot-builder/?utm_source=internal_link&amp;utm_medium=Blog&amp;utm_campaign=14CB" target="_blank" rel="noopener"><span style="color:#f05443;">no-code chatbot builder</span></a>, you can easily build bots using the drag and drop interface, from scratch, or use any of their pre-existing templates to quickly customize, and go live.&nbsp;</p><p>WotNot offers the best of both worlds – a chatbot and a live chat tool to scale sales and support, with human intervention, when needed. If you’re in a rush to build your bot, and go live ASAP – WotNot is the platform for you.&nbsp;</p><p>&nbsp;<strong> <img src="https://cdn.marutitech.com/0253bb7f-wotnot.png" alt="Chatbot development platform - WotNot" srcset="https://cdn.marutitech.com/0253bb7f-wotnot.png 1000w, https://cdn.marutitech.com/0253bb7f-wotnot-768x344.png 768w, https://cdn.marutitech.com/0253bb7f-wotnot-705x316.png 705w, https://cdn.marutitech.com/0253bb7f-wotnot-450x202.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></strong></p><p><strong>Features</strong></p><ul><li>No-code bot builder enables you to build bots instantly with simple drag and drop interface</li><li>The <a href="https://wotnot.io/human-handover/?utm_source=internal_link&amp;utm_medium=Blog&amp;utm_campaign=14CB" target="_blank" rel="noopener"><span style="color:#f05443;"><i>chatbot to human handover</i></span></a> feature allows a human agent to participate in the conversation, whenever required</li><li>Chatbot Analytics for a bird’s eye view of the bot’s performance through KPIs such as top countries, top intents, average conversation time, and more</li><li>Chatbot conversations are saved in the backend, and the transcripts can be emailed to sales and support team in real time&nbsp;</li></ul><p><strong>Pros</strong></p><ul><li>Supports multiple channels from websites, Messenger, WhatsApp, SMS to Mobile apps</li><li>Unlimited conversations and messages</li><li>Seamless integrations with Salesforce, Shopify, Zoho, WordPress, Slack, Dialogflow, IBM Watson and many more</li></ul><p><strong>Cons</strong></p><ul><li>Bot limit is up to 10 bots/account</li></ul><p><strong>Pricing</strong></p><p>There is a 14-day free trial for users to explore and test the platform. WotNot offers a flat pricing plan with access to all features at $99/month or $949 per year.&nbsp;</p><p><strong>Expert Tip</strong></p><p>Leverage the expertise of their conversation design team to build your bot for you, as WotNot offers a fully managed done-for-you service. Make sure you keep a close eye on chatbot analytics to uncover insights, and split A/B test chatbot flows to increase conversions.</p><figure class="image"><a href="https://marutitech.com/case-study/appointment-booking-chatbot-for-hospital/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/c48a18b5_artboard_2_495b72619d.png" alt="chatbots benefits "></a></figure><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Intercom</strong></span></h3><p>Intercom provides a range of products in the customer support space. They provide custom chatbots for use cases around sales, marketing, and support. These bot can also be integrated with e-commerce and social platforms, and have live chat options.</p><p><img src="https://cdn.marutitech.com/2ba14912-intercom2.png" alt="Chatbot development platform - Intercom" srcset="https://cdn.marutitech.com/2ba14912-intercom2.png 1000w, https://cdn.marutitech.com/2ba14912-intercom2-768x304.png 768w, https://cdn.marutitech.com/2ba14912-intercom2-705x279.png 705w, https://cdn.marutitech.com/2ba14912-intercom2-450x178.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Allows you to design a bot quickly without any coding</li><li>Engage with every qualified lead proactively by starting conversations using advanced targeting</li></ul><p><strong>Pros</strong></p><ul><li>You can integrate conversations from social media channels into a CRM</li><li>High-quality, personalized help at scale</li></ul><p><strong>Cons</strong></p><ul><li>No free version available</li><li>Complex UI makes it difficult to build a bot</li></ul><p><strong>Pricing</strong></p><p>Plans are starting from $499/month which includes 10 seats. You are required to pay more if you have a high volume of conversations.&nbsp;</p><p><strong>Expert Tip</strong></p><p>Leverage Intercom to scale conversational experiences to every customer without overwhelming your teams.&nbsp;</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Drift Chatbot</strong></span></p><p>Drift primarily started off in the live chat space, and got into chatbots fairly recently. Their offering is more specific to a chatbot that books meetings for sales teams. The bot facilitates conversations with leads and qualifies website visitors without using any forms. It also identifies the right sales representative, and schedules a meeting on their calendar.</p><p>Drift's chatbot has garnered a lot of positive reviews over the years due to its exceptional performance. <a href="https://wotnot.io/blog/drift-review/" target="_blank" rel="noopener"><span style="color:#f05443;">Drift reviews</span></a> give you a better understanding of how the platform has helped businesses improve their lead qualification and meeting booking process.</p><p><img src="https://cdn.marutitech.com/cd3ad4d6-drift.png" alt="Chatbot development platform - Drift chatbot" srcset="https://cdn.marutitech.com/cd3ad4d6-drift.png 1000w, https://cdn.marutitech.com/cd3ad4d6-drift-768x413.png 768w, https://cdn.marutitech.com/cd3ad4d6-drift-705x379.png 705w, https://cdn.marutitech.com/cd3ad4d6-drift-450x242.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Engages people immediately on the website – making it more likely for interested people to share their contact information</li><li>Chatbot and livechat go hand in hand</li></ul><p><strong>Pros</strong></p><ul><li>Wide range of integrations</li><li>Allows for real-time conversations</li><li>Answer questions quickly with <i>Drift Automation</i></li></ul><p><strong>Cons</strong></p><ul><li>The mobile app does not do a fine job of clarifying conversations through push notifications</li><li>The pricing model is expensive</li></ul><p><strong>Pricing</strong></p><p>The free pricing plan only covers the live chat. The paid plan starts at $400/month (billed annually) which covers chatbot and livechat.&nbsp;</p><p><strong>Expert Tip</strong></p><p>Make use of Drift’s playbooks to build a bot that helps you book more meetings, and generate more pipeline for your business.</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Landbot.io</strong></span></p><p>An intuitive tool, Lanbot.io, allows you to build rule-based bots and AI-powered bots to seamlessly interact with your prospective customers and generate high-quality dialogues. Landbot also allows human agents to jump into the conversation mid-way and take control from the chatbot in real-time.</p><p><img src="https://cdn.marutitech.com/26646d3f-lanbot2.png" alt="Chatbot development platform - landbot" srcset="https://cdn.marutitech.com/26646d3f-lanbot2.png 1000w, https://cdn.marutitech.com/26646d3f-lanbot2-768x413.png 768w, https://cdn.marutitech.com/26646d3f-lanbot2-705x379.png 705w, https://cdn.marutitech.com/26646d3f-lanbot2-450x242.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Offers a drag-and-drop interface to create a chatbot quickly</li><li>Allows you to initiate dialog flows, test and analyze your chatbots without any code, and also integrate it with other online apps and tools</li><li>Personalize your chatbots with brand elements</li></ul><p><strong>Pros</strong></p><ul><li>Availability of a free version</li><li>Easy to use</li><li>Several integrations available</li><li>Create chatbots for multiple platforms</li></ul><p><strong>Cons</strong></p><ul><li>Integrations are available only in the paid plan</li><li>Limit on the number of conversations</li></ul><p><strong>Pricing</strong></p><p>There are free, paid, and custom plans available. Paid plans start at 30€/month and 100€/month.</p><p><strong>Expert Tip</strong></p><p>If you’re an independent business owner, or a small business, then Landbot is best suited for your needs. Be sure to go through their blogs as well as content to better understand how you can create engaging, and memorable customer experiences</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>LivePerson</strong></span></p><p>LivePerson is an excellent platform that helps you comfortably build, deploy, and optimize AI-powered chatbots. One of LivePerson’s highlights is that it enables you to leverage advanced analytics for continual optimization and real-time intent detection.</p><p><img src="https://cdn.marutitech.com/9c71262b-liveperson.png" alt="Chatbot development platform - liveperson" srcset="https://cdn.marutitech.com/9c71262b-liveperson.png 1000w, https://cdn.marutitech.com/9c71262b-liveperson-768x373.png 768w, https://cdn.marutitech.com/9c71262b-liveperson-705x343.png 705w, https://cdn.marutitech.com/9c71262b-liveperson-450x219.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Allows you to develop custom pre-written statements to send in chat</li><li>Intuitive for users and new employees</li><li>Features such as hyperlinks, canned responses, etc. help in offering a better customer experience</li></ul><p><strong>Pros</strong></p><ul><li>Ease of use</li><li>Flexibility in communication</li><li>Convenient and rich live chat features</li></ul><p><strong>Cons</strong></p><ul><li>Reporting is a little challenging to figure out</li><li>The program gets slow when there is a lot of data</li><li>No free trial</li></ul><p><strong>Pricing</strong></p><p>The pricing of the platform is based on the scope of automation and the extensiveness of messaging channels. You can book a demo or get more information from their website.</p><p><strong>Expert Tip</strong></p><p>Make sure that your use cases and scope of work is mapped out thoroughly in order to get the most value out of the solution.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Bold360</strong></span></h3><p>Bold360 is one of the most popular bot solutions that leverages <a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener">natural language processing services</a> to help customer support agents be more efficient, and take over conversations or transition directly from the chatbot to agents.</p><p><img src="https://cdn.marutitech.com/7c8f17b1-bold360.png" alt="Chatbot development platform -bold360" srcset="https://cdn.marutitech.com/7c8f17b1-bold360.png 1000w, https://cdn.marutitech.com/7c8f17b1-bold360-768x387.png 768w, https://cdn.marutitech.com/7c8f17b1-bold360-705x355.png 705w, https://cdn.marutitech.com/7c8f17b1-bold360-450x227.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Patented NLP technology that can understand customers’ intent without needing any keyword matching</li><li>Various customer engagement tools, internal network systems for HR &amp; IT, APIs and SDKs</li></ul><p><strong>Pros</strong></p><ul><li>Robust platform with a large number of features</li><li>Tightly integrated live agent</li><li>Hassle-free and quick human handoff</li></ul><p><strong>Cons</strong></p><ul><li>The platform is not visually appealing</li><li>Haphazard pricing strategy</li><li>Outdated UI/UX</li></ul><p><strong>Pricing</strong></p><p>Pricing information is not available online. To get a custom quote, you will need to contact them directly.</p><p><strong>Expert Tip</strong></p><p>Use the platform to scale your <a href="https://marutitech.com/trends-need-to-know-about-conversational-marketing/" target="_blank" rel="noopener"><span style="color:#f05443;">conversational marketing</span></a> to new digital channels, including chatbots, messaging, and your mobile app in over 40 languages.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Octane AI</strong></span></h3><p>Octane AI is mainly useful if you are looking to integrate a chatbot with a Shopify store via Facebook Messenger. The platform allows you to answer customer questions automatically, send receipts as well as shipping information, and help customers find their preferred products.</p><p><img src="https://cdn.marutitech.com/5fed812c-octaneai.png" alt="Chatbot development platform -Octane AI" srcset="https://cdn.marutitech.com/5fed812c-octaneai.png 1000w, https://cdn.marutitech.com/5fed812c-octaneai-768x350.png 768w, https://cdn.marutitech.com/5fed812c-octaneai-705x321.png 705w, https://cdn.marutitech.com/5fed812c-octaneai-450x205.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Automated workflows and easy FAQs handling</li><li>Analytics interface to get into the nitty-gritties of customer behavior</li><li>Quick-start templates, surveys, and notifications, along with voice, image, and video support</li></ul><p><strong>Pros</strong></p><ul><li>The platform offers a wide range of integrations in addition to Slack, Nexmo, Salesforce, Facebook Messenger, and PayPal</li><li>Notification support for abandoned cart and shipping information</li></ul><p><strong>Cons</strong></p><ul><li>Complex interface and UX takes time getting used to</li><li>Limited to Messenger only</li></ul><p><strong>Pricing</strong></p><p>14-day free trial available. Plans starting at $9/month (basic) and at $209/month (pro)</p><p><strong>Expert Tip&nbsp;</strong></p><p>You can create conversational Messenger ads to rope in customers quickly.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>8. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Flow XO</strong></span></h3><p>If you’re looking to build bots without any kind of coding, then FlowXO is another option to choose from. You can build and deploy bots across multiple platforms, while integrating them with other 3rd party platforms as well.</p><p><img src="https://cdn.marutitech.com/7966e31b-flowxo.png" alt="Chatbot development platform - flowxo" srcset="https://cdn.marutitech.com/7966e31b-flowxo.png 1000w, https://cdn.marutitech.com/7966e31b-flowxo-768x297.png 768w, https://cdn.marutitech.com/7966e31b-flowxo-705x273.png 705w, https://cdn.marutitech.com/7966e31b-flowxo-450x174.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Integration with a myriad of 3rd party tools</li><li>Drag and drop editor</li><li>Multi channel support</li></ul><p><strong>Pros</strong></p><ul><li>Free trial available&nbsp;</li><li>No technical expertise needed</li></ul><p><strong>Cons</strong></p><ul><li>Lack of a good technical documentation</li></ul><p><strong>Pricing</strong></p><p>The free version is limited to just 500 interactions. You can sign up for the paid plan at $19/month (5000 interactions). You can also add 25,000 additional interactions for $25/month along with 5 more bots at $10/month.</p><p><strong>Expert Tip</strong></p><p>Make sure you fully test out your bot using their in-built simulator before going live. This will help you spot errors in the conversation flow quickly, and create a water-tight conversational experience for your users.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>9. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>ManyChat</strong></span></h3><p>ManyChat’s bots can be built and deployed on Messenger for use cases on sales, marketing, and customer service. The benefit here is that you also get to broadcast content to your subscribers on Facebook at once via Messenger.</p><p><img src="https://cdn.marutitech.com/6190cd10-manychat.png" alt="Chatbot development platform - manychat" srcset="https://cdn.marutitech.com/6190cd10-manychat.png 1000w, https://cdn.marutitech.com/6190cd10-manychat-768x345.png 768w, https://cdn.marutitech.com/6190cd10-manychat-705x317.png 705w, https://cdn.marutitech.com/6190cd10-manychat-450x202.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Facebook Messenger marketing tools to engage with your audience</li><li>No code drag-and-drop bot builder</li><li>Messenger broadcasting for better engagement</li></ul><p><strong>Pros</strong></p><ul><li>Integrations with Stripe, Zapier, Shopify and others</li><li>Multiple tutorials for easier onboarding</li><li>Ready to use templates</li></ul><p><strong>Cons</strong></p><ul><li>Restricted to Facebook Messenger only</li></ul><p><strong>Pricing</strong></p><p>Both free and paid plans available. Paid plan is fairly standard, with one starting at $10/month for 500 subscribers, and another at $145/month for 25,000 subscribers.</p><p><strong>Expert Tip</strong></p><p>If you wish to make the process of bot-building hassle-free and straightforward, automate your audience engagement on Messenger based on triggers.<strong>&nbsp;</strong></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>10. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Botsify</strong></span></h3><p>Botsify offers a fairly easy to use bot builder to create bots for websites, Messenger and even Slack with ready to use templates. Like other platforms, you can seamlessly handover the chat from a bot to a human agent with Botsify as well.</p><p><img src="https://cdn.marutitech.com/776729a6-botsify.png" alt="Chatbot development platform - botsify" srcset="https://cdn.marutitech.com/776729a6-botsify.png 1000w, https://cdn.marutitech.com/776729a6-botsify-768x406.png 768w, https://cdn.marutitech.com/776729a6-botsify-710x375.png 710w, https://cdn.marutitech.com/776729a6-botsify-705x372.png 705w, https://cdn.marutitech.com/776729a6-botsify-450x238.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Multi channel support</li><li>Chatbot to human handoff available</li><li>Create <a href="https://marutitech.com/conversational-interfaces-will-replace-web-forms/" target="_blank" rel="noopener"><span style="color:#f05443;">conversational forms</span></a></li></ul><p><strong>Pros</strong></p><ul><li>Integrates with multiple 3rd party tools</li></ul><p><strong>Cons</strong></p><ul><li>There is a steep learning curve on how to use the platform</li></ul><p><strong>Pricing</strong></p><p>They have a 14 day free trial, followed by a standard plan of $50/month where we do everything by yourself. If you are looking for a fully managed service, the plan starts at $300/month.&nbsp;</p><p><strong>Expert Tip</strong></p><p>Make sure you integrate the chatbot with Slack or Google Sheets to better manage leads generated by the bot while taking full advantage of conversational forms.</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>11. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Chatfuel</strong></span></p><p>Chatfuel is yet another chatbot platform that is limited to just Facebook Messenger. You can leverage NLP to identify intents and utterances, and subsequently share predefined answers. Chatfuel’s key feature is that it stores the users data in the database, which allows you to get back in touch with them in the future, as you see fit.</p><p><img src="https://cdn.marutitech.com/7c9b7bf1-chatfuel.png" alt="Chatbot development platform - chatfuel" srcset="https://cdn.marutitech.com/7c9b7bf1-chatfuel.png 1000w, https://cdn.marutitech.com/7c9b7bf1-chatfuel-768x280.png 768w, https://cdn.marutitech.com/7c9b7bf1-chatfuel-705x257.png 705w, https://cdn.marutitech.com/7c9b7bf1-chatfuel-450x164.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Action and Activity Management</li><li>Chatbot Analytics and 3rd Party Integration</li></ul><p><strong>Pros</strong></p><ul><li>Supports 50 languages</li></ul><p><strong>Cons</strong></p><ul><li>Poor documentation process</li></ul><p><strong>Pricing</strong></p><p>The free version of the platform allows you access to all the features for up to 50 users. The Pro plan starts at $15/month, while the Premium Plan starts at $300/month. The latter comes with unlimited bots for upto 30,000 users.</p><p><strong>Expert Tip</strong></p><p>Use the network extractor to map keywords that your users would relate to for a particular intent and trigger actions seamlessly.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>12. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Pandorabots</strong></span></h3><p>An excellent AI-based chatbot platform, Pandorabots offers comprehensive solutions for full turnkey chatbot development. Known as one of the oldest and largest chats hosting services worldwide, it is a multilingual chatbot.</p><p>This is one of those platforms that requires a level of coding expertise. If you have an engineering team, then they can pretty much whip up a custom bot with endless possibilities, as the multilingual platform is pretty flexible. Pandorabots is one of the oldest platforms on this list.</p><p><img src="https://cdn.marutitech.com/23ab4836-pandorabots.png" alt="Chatbot development platform - pandorabots" srcset="https://cdn.marutitech.com/23ab4836-pandorabots.png 1000w, https://cdn.marutitech.com/23ab4836-pandorabots-768x354.png 768w, https://cdn.marutitech.com/23ab4836-pandorabots-705x325.png 705w, https://cdn.marutitech.com/23ab4836-pandorabots-450x207.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Completely voice-enabled</li><li>Multilingual support</li><li>Multichannel support</li></ul><p><strong>Pros</strong></p><ul><li>Availability of RESTful APIs</li><li>Allows you to understand the context and download your code</li></ul><p><strong>Cons</strong></p><ul><li>Requires coding expertise</li><li>There are limited features in the free version</li></ul><p><strong>Pricing</strong></p><p>You may either use a free version or go for paid ones. The cost to build a chatbot in the latter case is $19/month for the developer version, and $199/month for the pro version.&nbsp;</p><p><strong>Expert Tip</strong></p><p>Make sure that you settle on what features are paramount to your use case, before making a decision on the paid plan.</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>13. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>BotsCrew</strong></span></p><p>BotsCrew chatbot platform is a fairly popular choice for SMBs and SMEs as they too provide a managed service. The platform also allows you to build the bot yourself, if you choose to do so.&nbsp;</p><p>The platform currently offers multilingual bots with native integrations with FB Messenger and website widget. You can connect other platforms like WhatsApp, Twitter, Telegram, etc. on-demand. The bot you create will live on multiple platforms with no need to duplicate it.</p><p>The BotsCrew chatbot platform pricing starts at $600.00 per month, but the price can vary based on the integrations, features, and customization that you would like to have. The setup fee usually starts from $3K.</p><p><img src="https://cdn.marutitech.com/7c3c0f31-botscreww.png" alt="Chatbot development platform - botscrew" srcset="https://cdn.marutitech.com/7c3c0f31-botscreww.png 1000w, https://cdn.marutitech.com/7c3c0f31-botscreww-768x356.png 768w, https://cdn.marutitech.com/7c3c0f31-botscreww-705x326.png 705w, https://cdn.marutitech.com/7c3c0f31-botscreww-450x208.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><h4>&nbsp;</h4><p><strong>Features</strong></p><ul><li>Code-free chatbot development</li><li>Intuitive and easy to use platform</li><li>Omnichannel support</li></ul><p><strong>Pros</strong></p><ul><li>Conversation design as a service</li><li>Robust maintenance &amp; support</li></ul><p><strong>Cons</strong></p><ul><li>There is no mobile app support</li><li>Limited integrations</li></ul><p><strong>Pricing</strong></p><p>The pricing of the platform mainly depends on the complexity of the project. They do not have a free version, while the paid plans start at $600/month.</p><p><strong>Expert Tip</strong></p><p>Opt for building a bot around a use case, where you need to deploy it across multiple channels. This will help you take full advantage of Botscrew’s omnichannel capabilities.</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>14. </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Aivo</strong></span></p><p>Aivo’s bots offer robust customer service and gives you the ability to respond to customers in real-time, through text as well as voice. Bots can be programmed under different rules and conditions across channels to reply appropriately.&nbsp;</p><p>Aivo’s AgentBot pricing starts at $240 per month, which includes 1,000 monthly sessions. Additional sessions cost $26 per 100. It also comes with a free 30-day trial.</p><p><img src="https://cdn.marutitech.com/8ec37092-aivo.png" alt="Chatbot development platform -aivo" srcset="https://cdn.marutitech.com/8ec37092-aivo.png 1000w, https://cdn.marutitech.com/8ec37092-aivo-768x324.png 768w, https://cdn.marutitech.com/8ec37092-aivo-705x298.png 705w, https://cdn.marutitech.com/8ec37092-aivo-450x190.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p><strong>Features</strong></p><ul><li>Ability to reply via voice functionality</li><li>Offers detailed analytics through the business intelligence tool</li><li>Customer service available in more than 50 languages</li></ul><p><strong>Pros</strong></p><ul><li>Support across multiple channels</li><li>Integrations with Salesforce, Zapier, Zendesk, and more</li></ul><p><strong>Cons</strong></p><ul><li>No free version available</li></ul><p><strong>Pricing</strong></p><p>Free demo available. The paid version starts at $240/month which covers around 1,000 sessions. You need to pay an additional $26 for 100 more sessions.</p><p><strong>Expert</strong> <strong>Tip</strong></p><p>As the platform gathers unanswered questions, you can understand what your customers want and train the bot accordingly.</p>21:Te01,<p>Now that we’ve listed all the platforms, we have listed a few additional points to consider, in order to help you make your evaluation of finding the best chatbot development tool easy.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Identify your Use Cases</strong></span></h3><p>The first questions that you need to consider here are – <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">why do you need a chatbot</a>, and what is the use case for using the chatbot.</p><p>A thorough understanding of your use case can help you determine what exactly you want out of your chatbot. As the platforms differ in features, pricing, and integrations, and all other factors considered, the chatbots will also vary significantly between a B2B or B2C use case.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Integrations</strong></span></h3><p>It is vital to have the right chatbot integrations in place to get the finest results out of your chatbot platform. Remember, you’re not only automating answers, but also actions. You want to be able to log into Salesforce or <a href="http://www.hubspot.com/products/crm/live-chat" target="_blank" rel="noopener">Hubspot</a> and see the leads generated by the chatbot with full context of the conversation. This is going to help you jump into stage 2 of the discussion with your prospects instead of spending time qualifying the,&nbsp;</p><p>Ensure that the platform you choose allows your current <span style="color:hsl(0,0%,0%);">marketing tech stack</span> to integrate seamlessly with your existing workflows.</p><p><span style="font-family:;">Additionally, consider integrating your chatbot with Robotic Process Automation (RPA) tools, especially in industries such as </span><a href="https://marutitech.com/rpa-in-hr/" target="_blank" rel="noopener"><span style="font-family:;">HR</span></a><span style="font-family:;">, Finance, Healthcare, and Customer Service.</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Natural Language &amp; AI Capabilities</strong></span></h3><p>The conversation is one of the most critical components that make chatbots so intriguing for the customers.&nbsp;</p><p>You don’t necessarily need to start off with an NLP based bot, if you’re deploying a bot for the first time. However, consider a platform which supports NLP and has AI capabilities for you to expand your use case and chatbot’s capabilities down the line.&nbsp;</p><p>The chatbot platform should have the ability to be trained on various intents, entities, utterances and responses, in order to maintain context, reply with the right answer and execute a task seamlessly.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Training</strong></span></h3><p>This is one of the most critical aspects when it comes to selecting a chatbot platform is its capacity to train the chatbot to make it smarter. Organizations need a human-independent chatbot solution, that supports continuous learning and gets smarter with each conversation using machine learning and semantic modeling.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Pricing</strong></span></h3><p>Today, most of the chatbot platforms use a combination of a pay-per-call, monthly license fee, and pay-per-performance pricing models. You need to go with a chatbot pricing plan that is predictive, guarantees savings and allows you to pay according to your achieved or non-achieved goals.</p>22:T643,<p>Whether you’re choosing a chatbot platform independently or an agency for <a href="https://marutitech.com/services/interactive-experience/chatbot-development/" target="_blank" rel="noopener"><span style="color:#f05443;">chatbot development services</span></a>, you ultimately need to look at all the pros and cons, your use case(s), carry out additional research, and then make a decision. The 14 chatbot platforms listed above, are leading the chatbot space for quite a while now.&nbsp;</p><p>Like we stated earlier, chatbots have become more of a necessity than a good-to-have luxury for businesses. In today’s technologically advanced business environment, chatbots help your business stay accessible round the clock, without you having to invest heavily in hiring extra customer support reps.&nbsp;</p><p>At Maruti Techlabs, we’ve been developing <a href="https://marutitech.com/custom-chatbots/" target="_blank" rel="noopener">custom chatbots</a> for our clients over the last 5 years. Having worked with early stage startups, SMBs and Enterprises across 16 industries, our team of conversation designers and bot developers are known for tailoring natural chatbot conversations that give your business a human touch.&nbsp;</p><p>We build a chatbot, keeping in mind the specific needs and wants of your audience. <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener"><span style="color:#f05443;">Book a free consultation</span></a> with our team today, and we’d be happy to help you map out use cases that help you automate your processes with conversational AI.</p>2:[null,["$","$L11",null,{"blogData":{"data":[{"id":198,"attributes":{"createdAt":"2022-09-14T11:28:55.151Z","updatedAt":"2025-06-16T10:42:10.965Z","publishedAt":"2022-09-15T05:27:04.469Z","title":"How to plan Chatbot Development at an Enterprise Level?","description":"Discover the key factors and requirements to deploy the chatbot platform at the enterprise level.","type":"Bot Development","slug":"chatbot-development","content":[{"id":13754,"title":null,"description":"$12","twitter_link":null,"twitter_link_text":null},{"id":13755,"title":"How should modern enterprises go about chatbot development?","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13756,"title":"Key Requirements for Chatbot Development Success","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13757,"title":"How does the chatbot development process work?","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13758,"title":"Conclusion","description":"$16","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":390,"attributes":{"name":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","alternativeText":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","caption":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","width":1000,"height":563,"formats":{"small":{"name":"small_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":11.86,"sizeInBytes":11858,"url":"https://cdn.marutitech.com//small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"thumbnail":{"name":"thumbnail_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":3.9,"sizeInBytes":3898,"url":"https://cdn.marutitech.com//thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"medium":{"name":"medium_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":22.48,"sizeInBytes":22483,"url":"https://cdn.marutitech.com//medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"}},"hash":"Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","size":34.77,"url":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:16.903Z","updatedAt":"2024-12-16T11:45:16.903Z"}}},"audio_file":{"data":null},"suggestions":{"id":1964,"blogs":{"data":[{"id":199,"attributes":{"createdAt":"2022-09-14T11:28:55.824Z","updatedAt":"2025-06-16T10:42:11.086Z","publishedAt":"2022-09-15T05:24:08.631Z","title":"Dialogflow Chatbot: Step-By-Step Guide To Building One","description":"Building chatbots can be stressful. Learn how to build a chatbot using dialogflow with step-by-step instructions.","type":"Bot Development","slug":"build-a-chatbot-using-dialogflow","content":[{"id":13759,"title":null,"description":"<p><span style=\"background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;\">In this article, you will learn how to build your Dialogflow chatbot through simple, step-by-step instructions. Here’s the overview:</span></p>","twitter_link":null,"twitter_link_text":null},{"id":13760,"title":"Contents: ","description":"<p>– The Context</p><p>– The ‘Dictionary’</p><p>– The Guide</p><ul><li><strong>Level 1 – Getting Started</strong></li><li><strong>Level 2 – Bot Development</strong></li><li><strong>Level 3 – Entities, Actions &amp; Parameters</strong></li><li><strong>Level 4 – Integration</strong></li><li><strong>Level 5 – Training &amp; Fallbacks</strong></li></ul><p>– Moving Ahead</p>","twitter_link":null,"twitter_link_text":null},{"id":13761,"title":"The Context:","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13762,"title":"The ‘Dictionary’","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13763,"title":"The Guide:","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13764,"title":"Moving Ahead","description":"<p>There is a lot more to building a Dialogflow chatbot that cannot be covered in these 5 levels. The production bots of today employ various methods of deployment, customisation, and even coding.&nbsp;</p><p>At <a href=\"https://marutitech.com/contact-us/\" target=\"_blank\" rel=\"noopener\">Maruti Techlabs</a>, we have built interactive chatbots on Dialogflow for a series of use cases ranging from hospitality, healthcare, finance, real estate and more.&nbsp;</p><p>Regardless of your industry or scale of business, if you are looking to embrace bots as a part of your digital initiative, be sure to take a look at our <a href=\"https://marutitech.com/services/interactive-experience/chatbot-development/\" target=\"_blank\" rel=\"noopener\">Bot Development services</a>, drop us a <NAME_EMAIL> and see how we can create and deliver conversational experiences for you!</p>","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":388,"attributes":{"name":"47af80a0-dialogflow.png","alternativeText":"47af80a0-dialogflow.png","caption":"47af80a0-dialogflow.png","width":1000,"height":563,"formats":{"thumbnail":{"name":"thumbnail_47af80a0-dialogflow.png","hash":"thumbnail_47af80a0_dialogflow_3e8a2f9584","ext":".png","mime":"image/png","path":null,"width":245,"height":138,"size":72.92,"sizeInBytes":72922,"url":"https://cdn.marutitech.com//thumbnail_47af80a0_dialogflow_3e8a2f9584.png"},"small":{"name":"small_47af80a0-dialogflow.png","hash":"small_47af80a0_dialogflow_3e8a2f9584","ext":".png","mime":"image/png","path":null,"width":500,"height":282,"size":256.86,"sizeInBytes":256859,"url":"https://cdn.marutitech.com//small_47af80a0_dialogflow_3e8a2f9584.png"},"medium":{"name":"medium_47af80a0-dialogflow.png","hash":"medium_47af80a0_dialogflow_3e8a2f9584","ext":".png","mime":"image/png","path":null,"width":750,"height":422,"size":540.97,"sizeInBytes":540965,"url":"https://cdn.marutitech.com//medium_47af80a0_dialogflow_3e8a2f9584.png"}},"hash":"47af80a0_dialogflow_3e8a2f9584","ext":".png","mime":"image/png","size":172.92,"url":"https://cdn.marutitech.com//47af80a0_dialogflow_3e8a2f9584.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:11.298Z","updatedAt":"2024-12-16T11:45:11.298Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":201,"attributes":{"createdAt":"2022-09-14T11:28:56.499Z","updatedAt":"2025-06-16T10:42:11.331Z","publishedAt":"2022-09-15T06:04:22.120Z","title":"What is NLP? Why does your business need an NLP based chatbot?","description":"Understand the basics of NLP and how it can be used to create an NLP-based chatbot for your business.","type":"Bot Development","slug":"nlp-based-chatbot","content":[{"id":13771,"title":null,"description":"<p>With chatbots becoming more and more prevalent over the last couple years, they have gone on to serve multiple different use cases across industries in the form of scripted &amp; linear conversations with a predetermined output. Although that has served the purpose with multiple use cases, today, with the advent of <a href=\"https://marutitech.com/artificial-intelligence-and-machine-learning/\" target=\"_blank\" rel=\"noopener\">AI and Machine Learning</a>, it has become imperative for businesses to develop and deploy an NLP based chatbot that assesses, analyzes and communicates with its users just like a human in order to offer an unparalleled experience.&nbsp;<strong>&nbsp;</strong></p>","twitter_link":null,"twitter_link_text":null},{"id":13772,"title":"What is Natural Language Processing (NLP)?","description":"$1a","twitter_link":null,"twitter_link_text":null},{"id":13773,"title":"What can NLP Engines do?","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13774,"title":"What can chatbots with NLP do to your business?","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13775,"title":"Conclusion","description":"$1d","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":506,"attributes":{"name":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","alternativeText":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","caption":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","width":9170,"height":3840,"formats":{"thumbnail":{"name":"thumbnail_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"thumbnail_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":103,"size":5.89,"sizeInBytes":5886,"url":"https://cdn.marutitech.com//thumbnail_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"large":{"name":"large_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"large_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":419,"size":43.67,"sizeInBytes":43667,"url":"https://cdn.marutitech.com//large_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"small":{"name":"small_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"small_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":209,"size":17.03,"sizeInBytes":17030,"url":"https://cdn.marutitech.com//small_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"medium":{"name":"medium_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"medium_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":314,"size":29.52,"sizeInBytes":29524,"url":"https://cdn.marutitech.com//medium_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"}},"hash":"nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","size":785.07,"url":"https://cdn.marutitech.com//nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:36.013Z","updatedAt":"2024-12-16T11:53:36.013Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":208,"attributes":{"createdAt":"2022-09-14T11:28:58.573Z","updatedAt":"2025-06-16T10:42:12.293Z","publishedAt":"2022-09-15T05:34:00.256Z","title":"The 14 Best Chatbot Builder Platforms [2025 Update]","description":"Everything you need to know about the 14 most powerful platform for building custom chatbot for your business.","type":"Bot Development","slug":"14-powerful-chatbot-platforms","content":[{"id":13829,"title":null,"description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":13830,"title":"Building a Chatbot – Defining Use Cases, Requirements and Types of Chatbot","description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":13831,"title":"14 Most Powerful Chatbot Development Platforms To Build A Chatbot For Your Business","description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13832,"title":"How To Choose The Right Chatbot Platform?","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13833,"title":"To Conclude","description":"$22","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":391,"attributes":{"name":"b7e51129-14cb-2.png","alternativeText":"b7e51129-14cb-2.png","caption":"b7e51129-14cb-2.png","width":1000,"height":500,"formats":{"thumbnail":{"name":"thumbnail_b7e51129-14cb-2.png","hash":"thumbnail_b7e51129_14cb_2_8819d2694a","ext":".png","mime":"image/png","path":null,"width":245,"height":123,"size":15.77,"sizeInBytes":15774,"url":"https://cdn.marutitech.com//thumbnail_b7e51129_14cb_2_8819d2694a.png"},"small":{"name":"small_b7e51129-14cb-2.png","hash":"small_b7e51129_14cb_2_8819d2694a","ext":".png","mime":"image/png","path":null,"width":500,"height":250,"size":43.31,"sizeInBytes":43307,"url":"https://cdn.marutitech.com//small_b7e51129_14cb_2_8819d2694a.png"},"medium":{"name":"medium_b7e51129-14cb-2.png","hash":"medium_b7e51129_14cb_2_8819d2694a","ext":".png","mime":"image/png","path":null,"width":750,"height":375,"size":76.96,"sizeInBytes":76959,"url":"https://cdn.marutitech.com//medium_b7e51129_14cb_2_8819d2694a.png"}},"hash":"b7e51129_14cb_2_8819d2694a","ext":".png","mime":"image/png","size":17.93,"url":"https://cdn.marutitech.com//b7e51129_14cb_2_8819d2694a.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:21.472Z","updatedAt":"2024-12-16T11:45:21.472Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1964,"title":"How a WhatsApp Chatbot helped UKHealth address COVID-19 related concerns","link":"https://marutitech.com/case-study/covid-awareness-whatsapp-chatbot/","cover_image":{"data":{"id":404,"attributes":{"name":"6 (4).png","alternativeText":"6 (4).png","caption":"6 (4).png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_6 (4).png","hash":"thumbnail_6_4_73ef872d4d","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":17.76,"sizeInBytes":17759,"url":"https://cdn.marutitech.com//thumbnail_6_4_73ef872d4d.png"},"small":{"name":"small_6 (4).png","hash":"small_6_4_73ef872d4d","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":65.02,"sizeInBytes":65022,"url":"https://cdn.marutitech.com//small_6_4_73ef872d4d.png"},"medium":{"name":"medium_6 (4).png","hash":"medium_6_4_73ef872d4d","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":149.29,"sizeInBytes":149289,"url":"https://cdn.marutitech.com//medium_6_4_73ef872d4d.png"},"large":{"name":"large_6 (4).png","hash":"large_6_4_73ef872d4d","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":271.03,"sizeInBytes":271033,"url":"https://cdn.marutitech.com//large_6_4_73ef872d4d.png"}},"hash":"6_4_73ef872d4d","ext":".png","mime":"image/png","size":91.3,"url":"https://cdn.marutitech.com//6_4_73ef872d4d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:00.951Z","updatedAt":"2024-12-16T11:46:00.951Z"}}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]},"seo":{"id":2194,"title":"How to plan Chatbot Development at an Enterprise Level?","description":"Looking to deploy chatbots for your business? Here's a complete breakdown on how to go about executing your enterprise chatbot development project.","type":"article","url":"https://marutitech.com/chatbot-development/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":390,"attributes":{"name":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","alternativeText":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","caption":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","width":1000,"height":563,"formats":{"small":{"name":"small_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":11.86,"sizeInBytes":11858,"url":"https://cdn.marutitech.com//small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"thumbnail":{"name":"thumbnail_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":3.9,"sizeInBytes":3898,"url":"https://cdn.marutitech.com//thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"medium":{"name":"medium_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":22.48,"sizeInBytes":22483,"url":"https://cdn.marutitech.com//medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"}},"hash":"Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","size":34.77,"url":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:16.903Z","updatedAt":"2024-12-16T11:45:16.903Z"}}}},"image":{"data":{"id":390,"attributes":{"name":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","alternativeText":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","caption":"Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","width":1000,"height":563,"formats":{"small":{"name":"small_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":11.86,"sizeInBytes":11858,"url":"https://cdn.marutitech.com//small_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"thumbnail":{"name":"thumbnail_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":3.9,"sizeInBytes":3898,"url":"https://cdn.marutitech.com//thumbnail_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"},"medium":{"name":"medium_Guide-to-a-Successful-RPA-Implementation-in-2019.jpg","hash":"medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":22.48,"sizeInBytes":22483,"url":"https://cdn.marutitech.com//medium_Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"}},"hash":"Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6","ext":".jpg","mime":"image/jpeg","size":34.77,"url":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:16.903Z","updatedAt":"2024-12-16T11:45:16.903Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
23:T608,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/chatbot-development/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/chatbot-development/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/chatbot-development/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/chatbot-development/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/chatbot-development/#webpage","url":"https://marutitech.com/chatbot-development/","inLanguage":"en-US","name":"How to plan Chatbot Development at an Enterprise Level?","isPartOf":{"@id":"https://marutitech.com/chatbot-development/#website"},"about":{"@id":"https://marutitech.com/chatbot-development/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/chatbot-development/#primaryimage","url":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/chatbot-development/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Looking to deploy chatbots for your business? Here's a complete breakdown on how to go about executing your enterprise chatbot development project."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"How to plan Chatbot Development at an Enterprise Level?"}],["$","meta","3",{"name":"description","content":"Looking to deploy chatbots for your business? Here's a complete breakdown on how to go about executing your enterprise chatbot development project."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$23"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/chatbot-development/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"How to plan Chatbot Development at an Enterprise Level?"}],["$","meta","9",{"property":"og:description","content":"Looking to deploy chatbots for your business? Here's a complete breakdown on how to go about executing your enterprise chatbot development project."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/chatbot-development/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"How to plan Chatbot Development at an Enterprise Level?"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"How to plan Chatbot Development at an Enterprise Level?"}],["$","meta","19",{"name":"twitter:description","content":"Looking to deploy chatbots for your business? Here's a complete breakdown on how to go about executing your enterprise chatbot development project."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//Guide_to_a_Successful_RPA_Implementation_in_2019_a58ee77db6.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
