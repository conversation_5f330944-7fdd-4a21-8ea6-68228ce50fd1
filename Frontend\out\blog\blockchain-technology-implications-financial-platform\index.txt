3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","blockchain-technology-implications-financial-platform","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","blockchain-technology-implications-financial-platform","d"],{"children":["__PAGE__?{\"blogDetails\":\"blockchain-technology-implications-financial-platform\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","blockchain-technology-implications-financial-platform","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:T786,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/blockchain-technology-implications-financial-platform/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#webpage","url":"https://marutitech.com/blockchain-technology-implications-financial-platform/","inLanguage":"en-US","name":"Blockchain Technology and its Implications on the Financial Platform","isPartOf":{"@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#website"},"about":{"@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#primaryimage","url":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/blockchain-technology-implications-financial-platform/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"The Blockchain technology is revolutionizing the financial sectors. Many banks have started adopting the blockchain technology for offering credit."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Blockchain Technology and its Implications on the Financial Platform"}],["$","meta","3",{"name":"description","content":"The Blockchain technology is revolutionizing the financial sectors. Many banks have started adopting the blockchain technology for offering credit."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$11"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/blockchain-technology-implications-financial-platform/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"Blockchain Technology and its Implications on the Financial Platform"}],["$","meta","9",{"property":"og:description","content":"The Blockchain technology is revolutionizing the financial sectors. Many banks have started adopting the blockchain technology for offering credit."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/blockchain-technology-implications-financial-platform/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"Blockchain Technology and its Implications on the Financial Platform"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"Blockchain Technology and its Implications on the Financial Platform"}],["$","meta","19",{"name":"twitter:description","content":"The Blockchain technology is revolutionizing the financial sectors. Many banks have started adopting the blockchain technology for offering credit."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
12:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
13:T9a3,<p><img src="https://cdn.marutitech.com/Bitcoin-Blockchain.png" alt="Bitcoin in blockchain" srcset="https://cdn.marutitech.com/Bitcoin-Blockchain.png 1165w, https://cdn.marutitech.com/Bitcoin-Blockchain-768x350.png 768w, https://cdn.marutitech.com/Bitcoin-Blockchain-705x321.png 705w, https://cdn.marutitech.com/Bitcoin-Blockchain-450x205.png 450w" sizes="(max-width: 1165px) 100vw, 1165px" width="1165"></p><p>Blockchain technology led to the innovation of Bitcoin – a digital currency that is accepted by many for making payments. This new revolutionary technology can be used for all transactions where the value is involved. Money, property, goods and services are examples where value-based transactions. Unlike our bill which is regulated by the central authority, Bitcoin is unregulated and validated by the users who pay for goods and services.</p><p>Blockchain stores information in blocks and it is shared by everyone who is connected to the block. When each user joins the network he/she is connected to the block for executing transactions. Thus, the Bitcoin blockchain will have all the past details of its usage at any of the point that is available for public access. This also eliminates the need for a central system to hold and validate data.</p><p>Conventional banking record all the transactions pertaining to the corresponding account and it is held private meaning as it is accessible only to the account holders. Blockchains, in contrast, records all transactions of all users and sends a copy of the transaction to whosoever is connected to the network. The distributed database system is an open electronic ledger and helps reduce complexities that are involved in all business transactions. As it is a transparent system, it will be useful to agencies and government institutions involved in the registration process. In addition, it can even be implied to &nbsp;medical systems and voting systems. &nbsp;It may even help in identifying the ownership of precious artworks and antiques.</p><p>Traditional payments systems do not have the flexibility to detail out the history of ownership of particular period. Though not impossible to retrieve information, but if blockchain technology can provide an efficient payment and network system with more secured encrypted transactions, why not we replace. This technology can save you lot of money as the existing payment systems fail to address the cost reduction in the financial industry.</p>14:T91d,<p>As we have seen in our earlier blog on how fintech is disrupting the banking sector, now with the rise of blockchain – a technology that has the potential to further disrupt the financial industry that we use every day for our business transactions. Let us see the impacts it may create in the financial sectors.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Decentralized system that enhances payment mechanism</span></h3><p>As for the traditional payment system where contracts are fulfilled when certain criteria are met and validated by the central authority. In blockchain transactions, the contracts are stored in public ledger surpassing the middlemen to complete the agreement between the two parties smartly in no time.</p><p>Blockchains technology has the potential to gear up the real-time payment mechanism that can provide cost-effective techniques for many financial intermediaries who still look upon to the central authority for clearing and settlement. Distributed ledger mechanism leads to inordinate delay in settling transactions and customer dissatisfaction which could be used as a driving force to address the new technology solutions which can better solve these problems.</p><p>Let us understand how the payment system works in blockchains. For instance, if A wants to send money to B, the transaction is made as a block and sends the information to the earlier participants who were connected with the same money for validating their transactions for approval. Once the deal is approved the money is transferred to B and gets recorded in the ledger sequentially.</p><p>There is no central authority for clearing the transactions. This is the most significant advantage in blockchain as the mechanism is decentralized.</p><p>Centralized payment systems are prone to risk as the attacker needs to concentrate on the single entry point. In distributed ledger, one needs to access all networks simultaneously that are connected to the particular transaction that is impossible to succeed. As such these records hold undisputable proof of identity and ownership that prevents fraud and counterfeiting. Blockchain works on a trusted platform where the parties that validate the transactions are called miners. As the data are interconnected transaction gets verified automatically.</p>15:T14be,<h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Bitcoin in trading arena</strong></span></h3><p><img src="https://cdn.marutitech.com/Blockchain-in-Trading.png" alt="Blockchain in Trading" srcset="https://cdn.marutitech.com/Blockchain-in-Trading.png 1920w, https://cdn.marutitech.com/Blockchain-in-Trading-768x354.png 768w, https://cdn.marutitech.com/Blockchain-in-Trading-1500x691.png 1500w, https://cdn.marutitech.com/Blockchain-in-Trading-705x325.png 705w, https://cdn.marutitech.com/Blockchain-in-Trading-450x207.png 450w" sizes="(max-width: 1920px) 100vw, 1920px" width="1920"></p><p style="text-align:center;"><i>Ref – www.quoinex.com</i></p><p>“As blockchain technology continues to redefine not only how the exchange sector operates, but the global financial economy as a whole, Nasdaq aims to be at the center of this watershed development.” –<a href="https://www.ft.com/content/764aed26-198a-11e5-8201-cbdb03d71480" target="_blank" rel="noopener"> Bob Greifeld, Chief Executive of NASDAQ</a>.</p><p>Blockchain technology has powerful mechanisms to transform the trading platform into an efficient manner. Bookkeeping, a record of ownership and may even boost backend of the trading sectors where hefty costs are involved in maintaining and validating the records of all transactions.</p><p>One of the critical aspects of trading is asset management which is at present manually done and slow involving various steps like reconciling, tallying with similar records, etc. are cumbersome. As a result, every transaction goes through such tedious procedures that create complexities and inefficiencies in managing the whole setup. This leads to significant errors in the process.</p><p>With blockchain technology, there will be no need to maintain the same records of brokers, dealers, intermediaries, clearing and settlement houses. The distributed ledger provides the parties with a transaction to have access to the same data at the same time. Additionally, there will be a smoother, transparent and most importantly better monitoring of regulation and auditing which will be managed effectively.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Insurance sectors</strong></span></h3><p><img src="https://cdn.marutitech.com/Blockchain-in-Insurance.jpg" alt="Blockchain in Insurance" srcset="https://cdn.marutitech.com/Blockchain-in-Insurance.jpg 1000w, https://cdn.marutitech.com/Blockchain-in-Insurance-768x449.jpg 768w, https://cdn.marutitech.com/Blockchain-in-Insurance-705x412.jpg 705w, https://cdn.marutitech.com/Blockchain-in-Insurance-450x263.jpg 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>Insurance is typically a binding contract between the two parties. The contract involves series of steps like paying premiums, filing a claim, investigation, and final settlement of claims.</p><p>Blockchain may change the way insurance businesses deal with their clients. They may well replace legal contracts with a smart contract by embedding the distributed ledger mechanism. Digitally enabled settlements in blockchain create transparency, efficiency and responsive in managing the claims of the customers. Potentially these chained contracts help reduce fake claims.</p><p>Insurance contracts are most difficult to understand as it uses a different language to address the legal terms, blockchain can be adaptive and penetrate any line of business, and it is on the move to make radical changes in the traditional way of working.</p><p>Another essential contribution blockchain can make into the insurance sector is helping them to settle life insurance files that are unclaimed. The blockchain registry will help connect the dots when the beneficiary is unknown or difficult to trace. Hence the technology while retaining the privacy improves the security concerns and helps the rightful claimant to access the funds when the policy matures.</p><p>Blockchains can streamline the<a href="http://www.investopedia.com/terms/s/subrogation.asp" target="_blank" rel="noopener"> subrogate</a> claims where the insurance company fulfills the loss suffered by the insured in the first place and reclaims the loss from the third party (fault-party). These claims are difficult to validate due to the fear of fraud. With a powerful technology such as blockchain, fraudulent claims can be reduced to the minimum, and it will be also easier to know whether the insurer of the faulty party has a ‘<a href="http://www.investopedia.com/terms/w/waiver-of-subrogation.asp" target="_blank" rel="noopener">waiver of subrogation</a>’.</p><p>Waiver means the insurance company may not be able to seek reimbursement; in that case, the company may refuse to compensate the insured. The insured and the insurance company may be benefitted as the blockchain registry holds transparency and a better auditability in all aspects.</p><p>Mutual trust between both parties is what makes the insurance company sells its policies in the market. If blockchain can boost trust and faith in the insurance company, it may create a plan in devising the technology in its various lines of business and products. And take the lead in creating more transparency in its operations resulting in increased numbers of people enrolling in the policy.</p>16:T4f3,<p>Access to credit facilities is the key driving factor for prosperity. Availing credit to buy a home, business start-ups are critical and more importantly the credit ratings that affect buying a loan. According to the consumer finance<a href="https://files.consumerfinance.gov/f/201505_cfpb_data-point-credit-invisibles.pdf" target="_blank" rel="noopener"> report</a>, about 11 percent of the adult population in the United States are credit-invisible and over 8.3 percent of the adult population were treated unscorable. That is approximately 45 million Americans are deprived of a loan from registered financial companies forcing them to access loans from unsecured and dangerous parties working the black market.</p><p>This scenario might change with embedding the blockchain platform in the credit industries. The database in blockchain registry might give you a comprehensive credit rating that gives the opportunity to new borrowers to buy loans which may help flourish the financial industry. Credit rating, risk assessment, verification, and identification might come under the blockchain platform to help identify fraud and expand credit facilities across the nation. Blockchain open ledger system may also help also cross-border credit scoring system.</p>17:T4f5,<p>Fintech and blockchain add core value to the business. The duo if implemented properly in the business helps assure data integrity, render financial contracts more transparently and automate many laborious records with the technology that can trace the source of the ownership. Take for example how blockchain can implement in a departmental store.</p><p>Many times customers return defective products back to the E-commerce site, and it is even found true that sometimes the whole batch of products received at the store are defective. In this situation, if the products are still in the store, it is easy to replace them. But the worse scenario is when these are distributed to local retailers and it is impossible to trace each and every product that is defective. Blockchain with its open ledger system may provide the solution to these problems and help the store to trace the location of every product at ease.</p><p>Technologies that help quickly recover from such embarrassments or image loss due to the negligence on the part of the manufacturer. Blockchain can be devised into any line of business that needs traceability across its supply chains. Time saved in such situations is worth a lot of money considering the impact it will have on the market.</p>18:T671,<p>Bankers provide a safe place to keep our money, facilitate payments in our business transactions. They hold the cash effectively to run their business in lending it to institutions and individuals at profit. They effectively provide a market to do business. They provide the platform for global trade and help transfer money to the clients as an alternative to physical cash.&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</p><p><img src="https://cdn.marutitech.com/Blockchain-disrupting-the-banking-industry.jpeg" alt="Blockchain disrupting the banking industry" srcset="https://cdn.marutitech.com/Blockchain-disrupting-the-banking-industry.jpeg 620w, https://cdn.marutitech.com/Blockchain-disrupting-the-banking-industry-450x290.jpeg 450w" sizes="(max-width: 620px) 100vw, 620px" width="620"></p><p>Bitcoin crypto-currency that is already in the market – an alternative to the money we use is increasingly becoming popular. Though accepted only by few businesses, Bitcoin has the potential to replace traditional currencies. However, it is unlikely to dislodge it overnight and it is also difficult to predict how much longer it may take for it to make a strong impact on the economy. But the underlying technology is what the banks are keen to adopt and how they can implement blockchain into their existing system is to be seen.&nbsp;&nbsp;</p><p>Cryptocurrency may or may not succeed in the coming times. But if it does than banks should be considering issuing something like a digital currency to combat the threats. Banks cannot ignore the fact that digital age can transform or disrupt any business that has not opened their doors to innovations.</p>19:Tc1b,<p>Blockchains can be used in different scenarios in the business that deals with value, asset, agreements, record keeping or data storage, contracts and cryptocurrencies.</p><p>Blockchain startup Symbiont, a company based in New York is using blockchain technology to develop smart contracts to be used in the trading platform. As discussed earlier how blockchains can transform trading sector (link it to the Blockchain in Trading Arena if possible), Symbiont helps in converting complex financial instruments to understandable language onto a distributed ledger.</p><p>From crop to cup,<a href="https://www.bext360.com/" target="_blank" rel="noopener"> Bext360</a> uses blockchain technology built by<a href="https://www.stellar.org/how-it-works/powered-by-stellar/" target="_blank" rel="noopener"> Stellar</a> to assert and assign codes to quality coffee cherries. The machine assorting the cherries assigns tokens to each quality and tracks the product across its lifespan. Coffee supply chains are greatly benefitted by blockchain technology that is built to suit their business.</p><p><a href="http://www3.weforum.org/docs/WEF_GAC15_Technological_Tipping_Points_report_2015.pdf#page=24" target="_blank" rel="noopener">By 2027, 10 percent of global gross domestic product (GDP) is expected to be stored on blockchain technology</a>. Currently, the total worth of bitcoin in the blockchain is around $20 billion, or about 0.025% of global GDP of around $80 trillion. And since there are only 21 million bitcoins that are going to be produced, their value is only going to keep increasing. Bitcoin had started in 2010 when it was valued at only $0.008, and now in 2017, 1 Bitcoin equals $7,415.86.</p><p>As we have seen in the past, with the revolution of the internet, Fintech innovations blossomed and we see the same trendsetter on the rise with the innovation of blockchain technology, all thanks to Satoshi Nakamoto the name often found on the internet credited with the creation of bitcoin.</p><p>Some of the wallets that are powered by blockchain technology are given below for your reference. To find out more details about the wallets pursue the link given on each wallet.</p><p><a href="https://xapo.com/" target="_blank" rel="noopener">Xapo</a> is a company based in Switzerland that provides a bitcoin wallet with a bitcoin-based debit card. It also provides access to the cold storage vault.<a href="https://www.ledger.com/" target="_blank" rel="noopener"> Ledger</a> is a Cryptocurrency hardware wallet that safeguards crypto assets for individuals and companies.<a href="https://trezor.io/" target="_blank" rel="noopener"> Trezor</a> is a Bitcoin hardware wallet to store and secure your bitcoins. It also provides a variety of alternative digital currencies with a single device.</p><p>Leaders who see blockchain technology as an opportunity and not as a threat will lead the business organization to competitive advantage, efficiency in carrying out the business and overcome security threats. Blockchain can well become the potential game changer in the financial industry.</p>1a:T4e91,<p>In the last two articles, we went over the <a href="https://marutitech.com/artificial-intelligence-in-insurance/" target="_blank" rel="noopener">key challenges faced by the Insurance industry</a> while assessing how AI can assist the Insurance industry in <a href="https://marutitech.com/ai-insurance-implementation-challenges-solutions/" target="_blank" rel="noopener">fraud detection and claims management</a>. This article looks at the confluence of AI, Blockchain and IoT for effective claims managements and fraud detection in the Insurance space.</p><p>Over the years, for Insurance companies, detecting multiple frauds during the <a href="https://marutitech.com/machine-learning-in-insurance-claims/" target="_blank" rel="noopener">insurance claims management process</a> has been a very taxing process coupled with the typical challenges and unpredictable patterns.</p><p>To gain illicit favors from an Insurance company, some individuals may try to be inventive and commit illegal activities under the name of insurance cover. It mainly includes pretended incidents, exaggerated presentation of fake damages, false cause of accidents and more.</p><p>It is, therefore, a vital practice to build detection models that maintain a perfect balance between loss prevention savings and investment of false alert detection. Artificial Intelligence, in the most practical way possible, helps in improving the scenario for the Insurance industry.</p><p><img src="https://cdn.marutitech.com/1_Mtech_1_9be676af9f.png" alt="1_Mtech (1).png" srcset="https://cdn.marutitech.com/thumbnail_1_Mtech_1_9be676af9f.png 47w,https://cdn.marutitech.com/small_1_Mtech_1_9be676af9f.png 149w,https://cdn.marutitech.com/medium_1_Mtech_1_9be676af9f.png 224w,https://cdn.marutitech.com/large_1_Mtech_1_9be676af9f.png 299w," sizes="100vw"></p><p>For instance, the use of<a href="https://marutitech.com/artificial-intelligence-and-machine-learning/" target="_blank" rel="noopener"> machine learning and AI</a> contributes to the following:</p><ul><li>&nbsp;&nbsp;&nbsp;The technology’s smart, case-specific analytics model improves predictive accuracy</li><li>&nbsp;&nbsp;&nbsp;Minimizes the enormous impact of false alerts and the resultant loss</li><li>&nbsp;&nbsp;&nbsp;Intelligently processes various data sets to sense misleading or false claims</li></ul><p>Here, we aim to examine a little deeper into different possible situations within the Insurance world and how AI’s superior predictive performance and learning ability come to assist in issue resolutions.</p><p>Potential areas to address with AI: &nbsp;</p><ol><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Misrepresentation of incidents</strong>: Includes malpractices from the customer’s end like twisting the context of cover provided, holding accountable the nature of events instead of the irresponsible activities and/or blatant failure to take pre-explained safety measures.</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Soliciting excessive cover</strong>: In this scenario, insured individual attempts to cover-up the situation that was not covered in the policy such as driving under influence, reckless acts, and irresponsible behaviors, or illegal activities.</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Over-exaggerating the aftermath of incident</strong>: Customers solicit excessive favors by exaggerating the impact of the event and request the remittance for fake losses or increase cost against the damage incurred.</span></li></ol><p>The insurance industry grapples with many more intense challenges pertaining to fraudulent claims such as:</p><ul><li>&nbsp;&nbsp;&nbsp;Unpleasant impact on customer retention due to delayed payouts or tedious investigation</li><li>&nbsp;&nbsp;&nbsp;Diminished profitability from inconsiderate payouts</li><li>&nbsp;&nbsp;&nbsp;Indirect encouragement to delinquent behaviors from other policyholders</li><li>&nbsp;&nbsp;&nbsp;Compromised process efficiency due to deceit and high premium costs</li></ul><p><a href="https://www.fbi.gov/stats-services/publications/insurance-fraud" target="_blank" rel="noopener">FBI reveals that over 700 insurance companies in the USA</a> receive over $1 trillion annually in premiums, with the estimate of total cost of insurance fraud being more than $40 billion annually.</p><p>This goes to indicate how urgent it is to develop an intellectual capability to recognize potential frauds with higher accuracy and clear, clean cover claims rapidly.</p><p><img src="https://cdn.marutitech.com/2_Mtech_1_b155e2d5f5.png" alt="2_Mtech (1).png" srcset="https://cdn.marutitech.com/thumbnail_2_Mtech_1_b155e2d5f5.png 56w,https://cdn.marutitech.com/small_2_Mtech_1_b155e2d5f5.png 180w,https://cdn.marutitech.com/medium_2_Mtech_1_b155e2d5f5.png 270w,https://cdn.marutitech.com/large_2_Mtech_1_b155e2d5f5.png 360w," sizes="100vw"></p><h3><strong>Why depend on Machine Learning for Fraud Detection?</strong></h3><p>The traditional fraud detection techniques are limited in its reach and effect. Some of the traditional practices are –</p><ul><li>&nbsp;&nbsp;&nbsp;Heuristics for fraud indicators that help make decisions on fraud</li><li>&nbsp;&nbsp;&nbsp;Defining specific rules that determine the need for further investigation</li><li>&nbsp;&nbsp;&nbsp;The examination of scores and claims value to check the need for investigation</li></ul><h3>Limitations of conventional techniques –</h3><ul><li>&nbsp;&nbsp;&nbsp;Involves a lot of manual efforts for determining fraud indicators and requires insurers to set and recalibrate thresholds periodically</li><li>&nbsp;&nbsp;&nbsp;Contains a limited set of known parameters of heuristics and excludes many other attributes having the potential to influence the fraud detection process</li><li>&nbsp;&nbsp;&nbsp;Offers a restrictive understanding of given scenario based on limited parameters and context</li><li>&nbsp;&nbsp;&nbsp;Lacks the typical model of the fraud investigation</li><li>&nbsp;&nbsp;&nbsp;The model has to be adapted to changing behavior and feedback from investigations</li></ul><p>To meet these challenges of manual techniques, insurers have started turning to machine learning where the goal is to supply entire data sets to the AI algorithm without the relevance of different elements. <span style="font-family:Arial;">Utilizing </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">custom AI software development</span></a><span style="font-family:Arial;"> and machine learning, the system can develop an organized model based on identified frauds, which can then be leveraged to make wise decisions.</span></p><h3><strong>Blockchain and Insurance</strong></h3><p>Despite its touch-and-go start, Blockchain has been used by several industries on an experimental basis. When it comes to the InsurTech space, start-up companies especially have embarked on giving Blockchain models an optimistic go.&nbsp;Here’s what Blockchain is capable of doing for the Insurance giants:</p><ol><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Creation of the new models </strong>Companies have started using Blockchain for a variety of verticals. For e.g., there is a new kind of travel insurance where travelers can insure their flight and, upon experiencing an official delay beyond a certain threshold, use the Ethereum-based cryptocurrency to enable instant payout.In 2015, InsurETH start-up blazed the trail in Ethereum-supported flight delay insurance and within 12 months, </span><a href="https://etherisc.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">Etherisc</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> became another start-up to develop a similar Blockchain-based product.&nbsp;In 2017, </span><a href="https://www.coindesk.com/axa-using-ethereums-blockchain-new-flight-insurance-product/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">AXA, the French insurance giant</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> started offering users a version of the blockchain-based solution, as well.Even though this is a small start, the impact is enormous nonetheless. Start-ups are the first ones to take the lead in this space and it is fairly evident that Blockchain technology is equipped to accelerate and sophisticate insurance process.</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Blockchain-enabled smart contracts </strong>Smart contracts aids in underwriting and claims management. If a person wishes to purchase a health policy and want to negotiate on premium rate, Blockchain can come up with the best possible solution. The person can give access to his health-related data including lifestyle habits, age, eating choices, exercise routine, employment type, and past and current medical records.This entire information will be uploaded and encrypted onto the insurer’s Blockchain which processes all the data for calculating a premium. The rate will be modified on a quarterly basis based on rules set in the smart contract and thus, Blockchain smartens health insurance premium process for both insurers and customers.</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Automating Claims Process </strong>According to </span><a href="https://www.pwc.com/gx/en/insurance/assets/blockchain-a-catalyst.pdf" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">the PWC report</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> on benefit analysis, claims management will be at the top with the lowest barriers to implementation. Blockchain-based claims will process much faster than what brokers and insurers are engaged in currently by eliminating multiple manual verifications, duplications, and delay, ensuring easy availability of all the relevant data.According to a </span><a href="https://www2.deloitte.com/content/dam/Deloitte/us/Documents/financial-services/us-fsi-blockchain-in-insurance-ebook.pdf" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">Deloitte report</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">, with all of the customer’s health and medical information consolidated through Blockchain encryption, the process of life insurance, underwriting and applications will be accelerated to real-time completion.Although the revolutionary implementation of Blockchain will require existing insurers to adjust their processes and systems and invest significantly, forward-thinking newcomers believe that it will disrupt the insurance system for better.</span></li></ol><h4><strong>Going from predictable behavior to actual behavior</strong></h4><p>The next decade of insurance market ought to adapt to the change that involves a tremendous shift from likely behavior to actual behavior of individuals when it comes to determining policy price. The move from the proxy to source data will redefine customer experience as well.</p><h4><strong>Wearables for better health plans</strong></h4><p>Consumers are also willing to support data analysis and accuracy with facial and biometric data. Troubadour Research and Consulting finds that almost half of consumers send data from their wearables to insurers for health insurance. Two recent start-ups <a href="https://biobeats.com/" target="_blank" rel="noopener">BioBeats</a> and <a href="https://fitsense.io/" target="_blank" rel="noopener">Fitsense</a> are handling wearables data of health insurance to personalize employee health plans.</p><h4><strong>AI interfaces for coverage personalization and customer onboarding</strong></h4><p>The three critical ways for AI technology to enhance insurance cover purchase experience are:</p><p><strong>Chatbots</strong>: To truly personalize the conversation, chatbots can use advanced image recognition and social data.&nbsp;A survey by Accenture reveals that 68% of respondents depend on <a href="https://wotnot.io/insurance-chatbot/" target="_blank" rel="noopener">insurance bots</a> in some segment of their business. Lemonade’s AI Jim and <a href="https://www.geico.com/web-and-mobile/mobile-apps/virtual-assistant/" target="_blank" rel="noopener">Geico’s Kate</a> assist in settling claims whereas, the chatbot <a href="https://www.next-insurance.com/" target="_blank" rel="noopener">Next</a> sells commercial insurance to personal trainers via Facebook Messenger.</p><p><strong>Platforms</strong>: Custom platforms can automate personal identity verification and accelerate authentication for policy quotes. The life insurance start-up Lapetus started offering life insurance to people <a href="https://www.smh.com.au/money/super-and-funds/a-selfie-could-become-the-new-way-to-obtain-life-insurance-20170616-gwsl2m.html" target="_blank" rel="noopener">using merely a selfie</a>. Lapetus can use facial analysis to determine risk scores without tedious medical process. The company follows SMILe (smoker indication and lifestyle estimation) approach to measure the effect of lifestyle habits like smoking cigarettes on lifespan.</p><p><strong>Carriers</strong>: Through machine learning, customers can have customized coverage and receive fully online app-based insurance purchase experience.&nbsp;Customer delight is central to successful e-commerce. <a href="https://www.the-digital-insurer.com/dia/allianz1-insurance-customisation-in-real-time/" target="_blank" rel="noopener">Allianz1</a> is a web interface in the Italian marketplace that provides most personalized experience to customers by allowing them to custom-make and mix their own insurance covers based on Allianz thirteen distinct business lines.</p><p><img src="https://cdn.marutitech.com/3_Mtech_506671b219.png" alt="3_Mtech.png" srcset="https://cdn.marutitech.com/thumbnail_3_Mtech_506671b219.png 126w,https://cdn.marutitech.com/small_3_Mtech_506671b219.png 402w,https://cdn.marutitech.com/medium_3_Mtech_506671b219.png 603w,https://cdn.marutitech.com/large_3_Mtech_506671b219.png 804w," sizes="100vw"></p><h3><strong>AI’s potential to settle claims faster and curb fraud cases</strong></h3><p>As covered above, customer satisfaction in the insurance industry depends on speed, experience, and efficiency. AI improves customer satisfaction by:</p><ol><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Accelerating claim settlement</strong>: The rate at which claims are settled will be equivalent to customer delight. The time taken to pass claims will be an essential key to increasing customer retention as well.</span><br><br><span style="font-family:Raleway, sans-serif;font-size:16px;">AI definitely offers a competitive advantage and enhances performance metrics by increasing claim settlement speed. Lemonade’s AI Jim was incredible in settling a claim in merely three seconds. </span><a href="https://www.jdpower.com/business/press-releases/2019-us-property-claims-satisfaction-study" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">JD Power and Associates</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> survey indicates how customers care a lot about the time to settle the claim.</span><br><br><span style="font-family:Raleway, sans-serif;font-size:16px;">AI is set to transform the insurance market ever so drastically. An Accenture survey back in 2017, found that </span><a href="https://newsroom.accenture.com/news/artificial-intelligence-set-to-transform-insurance-industry-but-integration-challenges-remain-according-to-accenture-report.htm" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">79% of insurance executives carry a positive belief</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> that AI will revolutionize the information and interactions flowing between insurers and customers.</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Controlling fraudulent activities</strong>: The significant decrease in fraudulent cases with intelligent solutions will bring terrific benefits to insurance companies in the long run.</span><br><br><span style="font-family:Raleway, sans-serif;font-size:16px;">To enable digital information flow between insurers and hospital in China, </span><a href="https://www.scmp.com/business/companies/article/2102395/chinas-first-online-only-insurance-agency-zhong-draws-spotlight" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">Zhong An</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> leverages AI power to process a substantial mass of paper information of policyholders. On top of this, they use </span><a href="https://marutitech.com/machine-learning-fraud-detection/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">machine learning model to detect frauds</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">, process hard copies and digitize information.&nbsp;Fraud detection is one substantial area where AI tech is rapidly adopted in the insurance domain.&nbsp;</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>The inevitable human touch:&nbsp;</strong>Despite the dominant role of modern technologies like AI, IoT, and Blockchain, it is important to cherish the pivotal role insurance agents play in the process. When consumers decide to protect their valuables, they do care to ensure they have a trustworthy human advisor to support and shepherd them down the path.</span></li></ol><p>A <span style="color:hsl(0,0%,0%);">survey published on Bizreport</span> comes across the following facts:</p><ul><li>&nbsp;&nbsp;&nbsp;60% of consumers resist interacting with AI for the fear that the technology might deny them an Insurance cover that a human agent might offer</li><li>&nbsp;&nbsp;&nbsp;72% of them said that they feel uncomfortable purchasing Insurance through a <a href="https://wotnot.io/" target="_blank" rel="noopener"><span style="color:#f05443;">chatbot</span></a></li><li>&nbsp;&nbsp;&nbsp;Nearly half of them emphasized on purchasing cover through human experience</li></ul><h3><strong>Conclusion</strong></h3><p>Looking at the modern-day scenario, we can firmly conclude that in coming years, the confluence of Artificial Intelligence, IoT and Blockchain is going to make the Insurance industry automated, frictionless and highly controlled. Despite of being a newcomer, the way insurance companies have already begun embracing technologies; it is clear that Blockchain-based solutions are likely to be explored more in future to build custom products.</p><p>The process of buying insurance and filing a claim with a few right clicks is a compelling idea with boundless opportunities. As <a href="https://www.cbsnews.com/news/digital-disruption-is-rocking-the-insurance-world/" target="_blank" rel="noopener">Michael LaRocca</a>, CEO of State Auto Financial (STFC) had once suggested for fellow insurance executives, it is time we have to be ready for unexpected changes and feel its power, and those who lag behind in adopting it, may regress and lose their business.</p><p>It’s an exciting time for those working in the Insurance &amp; InsurTech space. It is equally thrilling to see how <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">technologies are cleverly disrupting the current traditional dynamics of the insurance system</a>, making it more convenient, transparent and intelligent for both companies as well as consumers.</p>1b:Td53,<p>In the last blog – <a href="https://marutitech.com/benefits-of-blockchain/" target="_blank" rel="noopener">“What is blockchain and understand its key benefits”</a> we mentioned some of the noteworthy examples of <a href="https://marutitech.com/blockchain-technology-implications-financial-platform/" target="_blank" rel="noopener">blockchain in finance and banking sector</a>. It solves the expensive and insecure financial transaction systems by introducing new forms of digital interactions using the concept of distributed ledger. Below are some of the applications in finance and banking sector:</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Currency exchange and Payments</strong></span></h3><p>The most prevalent use case of blockchain is the transfer of virtual currency – bitcoin. It reduces the risk of inflation or collapse of currency as it is detached from country or institution. Money transfer using blockchain requires neither fees nor need of transaction verification by a central authority. R3 in association with world’s largest banks is developing a bank-to-bank global transaction system with help of Microsoft’s Azure-based Blockchain-as-a-Service.</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Peer-to-Peer Lending</strong></span></h3><p>P2P lending does not rely on a central authority (financial institution) and allows the borrower to define their own amount, interest rates and other parameters. Thus it becomes perfect use case for blockchain due to its ability to create public decentralized ledgers. A peer-to-peer system build using blockchain creates parity between lender and borrower and makes it easy to compare rates of return.</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Micropayments</strong></span></h3><p>There is a minimum cost for every transaction done using fiat currencies. Due to associated costs, small payments for goods and services are often expensive. Blockchain alleviates this associated cost while sending small payments. The lack of transfer fees is largely due to the decentralized public ledger used by cryptocurrencies that records financial transactions without the need for a third party to check and verify. It also reduces settlement and accounting fraud risk.</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Digital identity</strong></span></h3><p><img src="https://cdn.marutitech.com/Digital-Identity-e1480416666608.jpg" alt="Digital identification using Blockchain"></p><p style="text-align:center;">Digital identification using Blockchain</p><p>As more businesses and institutions begin to adopt blockchain, we would require digital identity to validate the trillions of transactions a second. A Money over internet protocol (MoIP) will form a foundation for payments structure and will enable machine-to-machine commerce. Thus it will enhance the scalability of payment transactions. If Digital Identities are recorded on blockchain protocol, then individual things can have authorization to transact. Over time, smart objects can be added, leading the way for Internet of things (IoT) also. This is where the most likely breakthrough will come before payments, as companies such as Blockstack are developing ideas in this space fast.</p>1c:T1739,<h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Legal and Insurance</strong></span></h3><p>Legal and insurance companies are using ‘smart contracts’ – a computerized transaction protocol that executes the terms of a contract. <a href="https://www.selachii.co.uk/smart-contacts.html" target="_blank" rel="noopener">London law firm Selachii</a> have already announced plans to launch digitized agreements based on blockchain technology. Blockchain-based smart contracts streamline and automate the processes that are faster, secure and cheaper. Tech startup <a href="https://tierion.com/" target="_blank" rel="noopener">Tierion</a>, works on this concept to issue digital receipts, create an audit trail, secure customer data and collect and store IoT data. The technology can improve the accuracy and speedup the way in which insurers operate. Administrative resources can be deployed elsewhere, with documentation logged and shared via the chain; rather than reams and reams of paper. It also opens up the possibility of ‘instant insurance policies’ without the need of manual intervention.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Energy sector</strong></span></h3><p>Blockchain technology can infuse transparency and renew trust between consumers and energy suppliers. Challenges such as price changes, tariff and billing can be solved by blockchain. Quite simply, integration of the Blockchain protocol would enable smarter metering systems, and this, in turn, will enhance the efficiency with which the end user pays for their energy. <a href="https://www.invirohub.com/" target="_blank" rel="noopener">Invirohub</a> aims at developing smart electricity, water and gas solutions to empower people with accurate detailed real-time information. It can be used to form a globally connected network of energy transfer to achieve smart energy usage, smart grids and energy credits.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Agriculture</strong></span></h3><p>Transparent transactions and smart contracts by blockchain are beneficial for farmers and &nbsp;consumers. It allows farmers to negotiate better price throughout the supply chain and giving the consumers knowledge about the source and journey of products. Using blockchain farmers can capture real-time data that will help them plan their spaces more effectively and maximize the success rate of their harvests. <a href="https://www.skuchain.com/" target="_blank" rel="noopener">Skuchain</a> applies the cryptographic principles developed using blockchain to secure and visualize the global supply chain. It is building a system of next generation identifiers in the form of both barcodes and RFID tags to digitally secure the transfer of goods across the entire global economy.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Healthcare</strong></span></h3><p>A community of people, including hospitals, doctors, patients, and insurance companies, could be part of the overall blockchain and reduce fraud in healthcare payments. When a data requires authorization by multiple people digitally signed blockchain can be used. Blockchain would regulate the availability and maintain the privacy of health records. According to Bruce Broussard, CEO of Humana ‘<a href="https://www.linkedin.com/pulse/blockchain-transformational-technology-health-care-bruce-broussard?trk=vsrp_people_res_infl_post_title" target="_blank" rel="noopener">blockchain will become the next big healthcare technology innovation</a>’, because it particularly relates to payments and payer contracts. For example, in a situation when a health plan and patient are dealing with a contract, the blockchain can automatically verify and authorize information and the contractual processes. In the future, blockchain can help in building a shared infrastructure by connecting users of wellness app, their medical records etc on the same network.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Retail and supply chain</strong></span></h3><p>Apart from B2B, blockchain also impacts B2C industries such as retail and supply chain which are involved in selling consumer goods and services. Individual ownership and attribution are at risk on the internet. It allows the sellers to establish ownership of their products on a decentralized platform. It also ensures transparency in the sourcing and transportation of goods, eliminates the risk of fraudulent transactions, ensures verification of provenance and finding stolen merchandise.</p><p><img src="https://cdn.marutitech.com/Asset-transfer-using-Blockchain-e1480416710195.jpg" alt="Asset transfer-using blockchain"></p><p>Asset transfer using blockchain; Reference- Deloitte University Press</p><p><a href="https://www.blockverify.io/" target="_blank" rel="noopener">BlockVerify</a> aims at improving supply chains using blockchain based anti-counterfeit solution. It records the transaction for pharmaceuticals, diamonds, electronics in a distributed digital ledger. Thus system guarantees authentic products by working with manufacturers to verify and certify the goods.</p><p>Blockchain has the power to revolutionize the finance and banking industry. But it can have huge implications on other sectors too. It improves the integrity and confidentiality of data, thus enhancing the quality of service. Some of the unconventional use cases are <a href="https://www.ibtimes.co.uk/rwe-slock-it-electric-cars-using-ethereum-wallets-can-recharge-by-induction-traffic-lights-1545220" target="_blank" rel="noopener">charging an electric car</a>, sharing electricity, voting, and <a href="https://ujomusic.com/" target="_blank" rel="noopener">music streaming</a>. With its guarantee of giving secure and faster transactions, blockchain promises to be a key element in the digital world.</p>1d:T867,<p>Blockchain: something we’ve been hearing about a lot these days. What started out with the popularity of Bitcoin, soon seeped into mainstream business applications. Blockchain has overcome the infancy phase and businesses today are ready to utilize it in its full-fledged form – beyond its obvious uses for <a href="https://marutitech.com/blockchain-uses/" target="_blank" rel="noopener">financial institutions</a>.</p><p>While cryptocurrencies were the first favorite uses of the blockchain, the technology is revolutionizing all leading industries today. <a href="https://techcrunch.com/2017/04/26/spotify-acquires-blockchain-startup-mediachain-to-solve-musics-attribution-problem/" target="_blank" rel="noopener">Spotify acquired the blockchain startup</a> Mediachain Labs to develop solutions based on decentralized databases to connect artists and licensing agreements with the multitude of tracks on the platform.</p><p>Another instance of blockchain’s widespread applications is Warranteer. This is a blockchain application that enables customers to access information on the products they purchased and get prompt customer service in cases of any malfunction.</p><p>It has become apparent that blockchain has broken shackles and entered mainstream business operations across industries to prove its usability.</p><p><img src="https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1.jpg" alt="all about Blockchain" srcset="https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1.jpg 1000w, https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1-768x1787.jpg 768w, https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1-645x1500.jpg 645w, https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1-303x705.jpg 303w, https://cdn.marutitech.com/da218ed5-blockchain-benefits-drawbacks-and-everything-you-need-to-know-min-1-429x999.jpg 429w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p>1e:T8df,<p>To explain how blockchain works, let’s assume the example of its most common application- <a href="https://en.wikipedia.org/wiki/Bitcoin" target="_blank" rel="noopener">Bitcoin</a>. Bitcoin is a digital currency, similar to any other national currency. To keep track of how many bitcoins each person owns, blockchain uses a ledger system- a file that maintains and tracks all transactions.</p><p>The ledger file is not stored on a central server – bringing us to the USP of blockchain – that it is a decentralized system. The ledger file is distributed across the world and stored on private computers that are all used to store data and perform computations.</p><p>When X wants to send 5 bitcoins to Y, they will broadcast a message to all nodes (personal computers) saying that X’s bitcoins should be reduced by 5 and Y’s increased by 5. Now, each computer will get this information and update their copy of X and Y’s bitcoin balances.</p><p>Blockchain allows everyone to see everyone else’s transactions and does not force you to trust a person or entity. Instead, special mathematical functions and codes are implemented to enforce security and reliability.</p><p>Each wallet is protected with a cryptographic method that uses a unique and distinct pair of connected keys: a public and a private key.</p><p>Each of these features of the blockchain technology brings us to a specific advantage of using it.</p><p><img src="https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2.jpg" alt="Blockchain-Benefits-Drawbacks-and-Everything-You-Need-to-Know_2" srcset="https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2.jpg 1484w, https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2-768x906.jpg 768w, https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2-1271x1500.jpg 1271w, https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2-597x705.jpg 597w, https://cdn.marutitech.com/da9366dd-blockchain-benefits-drawbacks-and-everything-you-need-to-know_2-450x531.jpg 450w" sizes="(max-width: 1484px) 100vw, 1484px" width="1484"></p>1f:Te04,<p>The blockchain is nothing short of a game-changing technology for anyone who chooses to use and master it. Let’s discuss the benefits of blockchain-</p><ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Transparency</strong> –</span> Blockchain makes transaction histories more transparent than they ever were. Because it is a type of a distributed ledger, all nodes in the network share a copy of the documentation.&nbsp; The data on a blockchain ledger is easily accessible for everyone to view. If a transaction history changes, everyone in the network can see the change and the updated record. Therefore, all information about currency exchange is available to everyone.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Security</strong> –</span> Blockchain is better than any other record-keeping system when it comes to security, by all standards. The shared documentation of transactions can only be updated and/or modified with consensus on a blockchain network. Only if everyone or a majority of nodes agree to update a record, the information is edited. Moreover, when a transaction is approved, it is encrypted and connected with the previous transaction. Therefore, no one person or party has the potential to alter a record. Blockchain is decentralized, and so, no one reserves the right to update records by their free will. Any industry that has a critical need to protect sensitive data such as governments, healthcare, financial services, etc., can use blockchain to enforce stringent security.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Efficiency</strong> –</span> With traditional, paperwork processes, completing a transaction is exhausting as it needs third-party mediation and is prone to human errors. Blockchain can streamline and discipline these legacy methods and remove the risk of mistakes, making trading more efficient and faster. Since there is only one ledger, parties don’t have to maintain multiple documents, a fact that leads to much less clutter. And, when everyone has access to the same information, establishing trust is easier. Without any need for intermediaries, settlements can be made smooth and effortless, too.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Traceability</strong> –</span> In complex supply chains, it is hard to trace products back to their origins. But, with blockchain, the exchanges of goods are recorded, so you get an audit trail to learn where a particular asset came from. You also get to know every stop the product made on its journey &amp; this level of traceability of products can help verify the authenticity and prevent frauds.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Auditability</strong> –</span> Another aspect of the point mentioned above is auditability. As each transaction is recorded for its complete lifetime in blockchain, there is an audit trail that already exists for you to see and check the authenticity of your asset.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Cost reduction</strong> –</span> As blockchain eliminates the need for third-parties and middlemen, it saves enormous costs for businesses. Given that you can trust the trading partner, you don’t need anyone else to establish the rules and policies of exchange. The cost and effort spent on documentation and its revisions are also saved as everyone gets to view a single immutable version of the ledger.</li></ul>20:Tb22,<p>Each coin has a flip side. Blockchain is a notch above its infancy today, and there are some drawbacks with the technology that needs to be handled before it can be widely used for everyday transactions.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:16px;"><strong>Scalability</strong> –</span> Blockchain’s application Bitcoin is massively popular. However, it can only handle seven transactions per second, where Hyprledger can handle 10,000 and Visa 24,000. The practical use of blockchain gets a bit hard to imagine with the issue of scalability in view. Each participant node needs to verify and approve a transaction, and so one Bitcoin exchange can take up to several hours.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Storage</strong> –</span> Since blockchain databases are stored indefinitely on all network nodes, the issue of storage surfaces. With the increasing number of transactions, the size of the database will only expand, and there is no way personal computers can store unlimited data which just gets appended. To put this in perspective, the Ethereum blockchain is increasing at the speed of 55 GB/year.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Privacy</strong> –</span> Data on a public blockchain is encrypted and anonymous, but lies in the hands of all nodes in the network. So, everyone in the network has rightful access to this data. There is a possibility someone could track down the identity of a person in the network through transactional data, just as web trackers and cookies are used by businesses normally. This proves that blockchain is not 100 percent secure, unfortunately.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Regulations</strong> –</span> Regulatory regimes in the financial arena are a challenge for blockchain’s implementation. Blockchain applications will have to lay down the process of pinpointing the culprit in case a fraud takes place, which is a bit of a challenge. Other regulatory aspects of blockchain technology will need to be laid down first in order to facilitate its broad adoption.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Security</strong> –</span> Satoshi Nakamoto highlighted the ‘51% attack’ when he launched Bitcoin. The attack can be simply put like this – if 51% of the nodes in a network lie, the lie will have to be accepted as truth. Therefore, everyone in the network will have to continually have a watch on it to perceive any unwanted influence.</li></ul><p>As the blockchain technology nears its widespread adoption, these challenges may get resolved over time. For its sweet advantages, developers and blockchain enthusiasts will surely find a way out of these bumps on the way!</p>21:T993,<h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Public Blockchain</strong></span></h3><p>A public blockchain, a fully decentralized platform where anyone can read and send transactions. The valid transactions are included in the ledger. Public blockchains are secured by cryptoeconomics, a combination of economic incentives and cryptographic verification. The degree of influence in the consensus process is proportional to the quantity of economic resources brought in the system.</p><ul><li><a href="https://www.ethereum.org/" target="_blank" rel="noopener"><span style="color:#f05443;">Ethereum</span></a>, Provider of a decentralized platform and programming language that helps running smart contracts and allows developers to publish distributed applications.</li><li>Blockstream – Provider of blockchain technology, focused on extending capabilities of cryptography and distributed systems. Their vision is to form an ecosystem for solving problems in financial systems related to fraud, counterfeiting, accountability and transparency.</li></ul><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Private Blockchain</strong></span></h3><p>In a private blockchain write permissions are kept centralized to one organization. In this system the access and permissions are tightly controlled and rights to modify are restricted to the central authority. This could be a concept with huge interest from FIs and large companies. A proprietary system built on private blockchain will reduce the transactional cost and increase validation efficiency.</p><ul><li>Eris Industries – Provider of multi-network blockchain client. It is a controllable, smart contract-enabled, proof-of-stake based blockchain design.</li><li>Blockstack – Developers can use APIs by blockstack.js to authenticate the user, fetching and storing application data.</li><li><a href="http://www.multichain.com/" target="_blank" rel="noopener"><span style="color:#f05443;">MultiChain</span></a> – Provides an open source distributed database for financial transactions.</li><li><a href="https://chain.com/" target="_blank" rel="noopener"><span style="color:#f05443;">Chain Inc</span></a><span style="color:#f05443;">.</span> – Similar to Multichain, it’s an enterprise grade blockchain infrastructure that enables organizations to build better financial services from the ground up.</li></ul>22:T5f7,<p>The future looks bright for blockchain. Here are a few promising applications of the technology-</p><ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>DLT-based governments</strong> –</span> The Distributed Ledger Technology is not a fad. Dubai has gone all in to replace all government systems with the ones backed up by DLT by 2020. The transition from a paper-based system to DLT seems the logical next-thing for government institutions.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>A collaboration of blockchains</strong> –</span> While there may be different blockchain networks operating in a single organization, aimed toward different business purposes, actual benefits for the customer can only be realized when these networks can be made to collaborate in an open standard.</li><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Transparency for industries</strong> –</span> Blockchain promises that all transactions are for everyone to view and that any change can only be made when it is verified by all nodes in a network. Therefore, blockchain will help industries introduce transparency in operations- at the least.</li></ul><p>As blockchain rises far and above our perceived challenges, it will be put together with the Internet of Things to create trust between parties, reduce the risk of tampering, lower costs by removing intermediaries, and accelerate the pace of settlements from days to almost instantaneous.</p>2:[null,["$","$L12",null,{"blogData":{"data":[{"id":194,"attributes":{"createdAt":"2022-09-14T11:28:54.107Z","updatedAt":"2025-06-16T10:42:10.486Z","publishedAt":"2022-09-15T05:13:55.337Z","title":"Blockchain Technology and its Implications on the Financial Platform","description":"Learn more about blockchain technology and its implications on a financial platform. ","type":"Block Chain","slug":"blockchain-technology-implications-financial-platform","content":[{"id":13729,"title":null,"description":"<p>In simple terms, blockchain is a technology that helps keep a record of currency transactions in a detailed manner. These transactions are decentralized without a central record keeping. So, that each player has the record of their transactions. Each transaction is stored in blocks and has detailed information on the currency before it reaches to your wallet. These operations are recorded and distributed in a public ledger for everyone to see and access. It is like if you receive a dollar, that block contains all the notes of the usage of that dollar written on it.</p>","twitter_link":null,"twitter_link_text":null},{"id":13730,"title":"Blockchain and Bitcoin","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13731,"title":"Blockchain in the Financial Industry","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13732,"title":null,"description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13733,"title":"Blockchains for Financial Institutions Offering Credit","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13734,"title":"Fintech and Blockchain duo transforming financial industry","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13735,"title":"Blockchain disrupting in banking sectors","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13736,"title":"Implementing Blockchain in Fintech industry","description":"$19","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":502,"attributes":{"name":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","alternativeText":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","caption":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","width":7048,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":140,"size":4.07,"sizeInBytes":4069,"url":"https://cdn.marutitech.com//thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"medium":{"name":"medium_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":428,"size":19.02,"sizeInBytes":19023,"url":"https://cdn.marutitech.com//medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"large":{"name":"large_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":571,"size":29.08,"sizeInBytes":29077,"url":"https://cdn.marutitech.com//large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"small":{"name":"small_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":285,"size":10.66,"sizeInBytes":10664,"url":"https://cdn.marutitech.com//small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"}},"hash":"businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","size":406.1,"url":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:13.281Z","updatedAt":"2024-12-16T11:53:13.281Z"}}},"audio_file":{"data":null},"suggestions":{"id":1960,"blogs":{"data":[{"id":157,"attributes":{"createdAt":"2022-09-13T11:53:27.367Z","updatedAt":"2025-06-16T10:42:05.827Z","publishedAt":"2022-09-13T12:39:31.253Z","title":"How effective are AI, Blockchain & IoT in Insurance Claims Management?","description":"Learn how AI, Blockchain & IoT play important roles in insurance claim management.","type":"Artificial Intelligence and Machine Learning","slug":"ai-blockchain-and-iot-in-claims-management-process","content":[{"id":13479,"title":null,"description":"$1a","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":478,"attributes":{"name":"hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","alternativeText":"AI, Blockchain & IoT in Insurance Claims Management","caption":"hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","width":5000,"height":3333,"formats":{"thumbnail":{"name":"thumbnail_hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","hash":"thumbnail_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad","ext":".jpg","mime":"image/jpeg","path":null,"width":234,"height":156,"size":7.96,"sizeInBytes":7963,"url":"https://cdn.marutitech.com//thumbnail_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad.jpg"},"small":{"name":"small_hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","hash":"small_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":333,"size":25.27,"sizeInBytes":25268,"url":"https://cdn.marutitech.com//small_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad.jpg"},"large":{"name":"large_hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","hash":"large_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":666,"size":76.6,"sizeInBytes":76597,"url":"https://cdn.marutitech.com//large_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad.jpg"},"medium":{"name":"medium_hand-medical-glove-pointing-virtual-screen-medical-technology (1).jpg","hash":"medium_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":500,"size":48.95,"sizeInBytes":48946,"url":"https://cdn.marutitech.com//medium_hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad.jpg"}},"hash":"hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad","ext":".jpg","mime":"image/jpeg","size":1011.4,"url":"https://cdn.marutitech.com//hand_medical_glove_pointing_virtual_screen_medical_technology_1_4b32a74bad.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:51:12.108Z","updatedAt":"2024-12-16T11:51:12.108Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":195,"attributes":{"createdAt":"2022-09-14T11:28:54.462Z","updatedAt":"2025-06-16T10:42:10.591Z","publishedAt":"2022-09-15T05:16:14.771Z","title":"Exploring Impact to Beyond the World of Finance and Banking","description":"Here are examples of how blockchain is used in various industries as an evolving technology. ","type":"Block Chain","slug":"blockchain-uses","content":[{"id":13737,"title":null,"description":"<p>Blockchain offers an alternative to a trusted intermediary in case of contract signing, money transfer and payments leading to significant saving in time and money. It is a form of database build on the concept of distributed ledger which records the transactions in chronological fashion.</p><p><img src=\"https://cdn.marutitech.com/Payment-process.jpg\" alt=\"Payment process - Banking vs Blockchain\"></p><p style=\"text-align:center;\">Payment process – Banking vs Blockchain</p>","twitter_link":null,"twitter_link_text":null},{"id":13738,"title":"Applications of Blockchain in finance and banking","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13739,"title":"Applications of Blockchain in other sectors","description":"$1c","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":503,"attributes":{"name":"trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","alternativeText":"trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","caption":"trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","width":8048,"height":4024,"formats":{"small":{"name":"small_trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"small_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":250,"size":15.28,"sizeInBytes":15277,"url":"https://cdn.marutitech.com//small_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535.jpg"},"thumbnail":{"name":"thumbnail_trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"thumbnail_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":122,"size":4.91,"sizeInBytes":4905,"url":"https://cdn.marutitech.com//thumbnail_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535.jpg"},"medium":{"name":"medium_trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"medium_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":375,"size":27.75,"sizeInBytes":27748,"url":"https://cdn.marutitech.com//medium_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535.jpg"},"large":{"name":"large_trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"large_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":500,"size":42.85,"sizeInBytes":42852,"url":"https://cdn.marutitech.com//large_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535.jpg"}},"hash":"trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535","ext":".jpg","mime":"image/jpeg","size":738.34,"url":"https://cdn.marutitech.com//trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_5d3fe98535.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:19.352Z","updatedAt":"2024-12-16T11:53:19.352Z"}}},"authors":{"data":[{"id":10,"attributes":{"createdAt":"2022-09-02T07:15:26.748Z","updatedAt":"2025-06-16T10:42:34.224Z","publishedAt":"2022-09-02T07:15:28.070Z","name":"Harsh Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Harsh is the Engineering Team Lead at Maruti Techlabs. He oversees product delivery and user adoption for early-stage startup clients and helps them ship products faster using no-code tools.</span></p>","slug":"harsh-makadia","linkedin_link":"https://www.linkedin.com/in/harsh-makadia/","twitter_link":"https://twitter.com/MakadiaHarsh","image":{"data":[{"id":525,"attributes":{"name":"11.jpg","alternativeText":"11.jpg","caption":"11.jpg","width":1080,"height":1080,"formats":{"thumbnail":{"name":"thumbnail_11.jpg","hash":"thumbnail_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.72,"sizeInBytes":4720,"url":"https://cdn.marutitech.com//thumbnail_11_6b68ffb856.jpg"},"medium":{"name":"medium_11.jpg","hash":"medium_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":56.66,"sizeInBytes":56658,"url":"https://cdn.marutitech.com//medium_11_6b68ffb856.jpg"},"small":{"name":"small_11.jpg","hash":"small_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":30.36,"sizeInBytes":30364,"url":"https://cdn.marutitech.com//small_11_6b68ffb856.jpg"},"large":{"name":"large_11.jpg","hash":"large_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":87,"sizeInBytes":86998,"url":"https://cdn.marutitech.com//large_11_6b68ffb856.jpg"}},"hash":"11_6b68ffb856","ext":".jpg","mime":"image/jpeg","size":88.78,"url":"https://cdn.marutitech.com//11_6b68ffb856.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:59.670Z","updatedAt":"2024-12-16T11:54:59.670Z"}}]}}}]}}},{"id":200,"attributes":{"createdAt":"2022-09-14T11:28:56.044Z","updatedAt":"2025-06-16T10:42:11.214Z","publishedAt":"2022-09-15T05:17:57.707Z","title":"Blockchain - Benefits, Drawbacks and Everything You Need to Know","description":"Explore the blockchain and its benefits as an ever-changing technology with promising applications. ","type":"Block Chain","slug":"benefits-of-blockchain","content":[{"id":13765,"title":null,"description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13766,"title":"How does blockchain work?","description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":13767,"title":"Benefits of Blockchain","description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":13768,"title":"Drawbacks of Blockchain","description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13769,"title":"Public vs Private Blockchains","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13770,"title":"The Way Ahead: Some Predictions for Blockchain","description":"$22","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":505,"attributes":{"name":"blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","alternativeText":"blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","caption":"blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","width":7048,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"thumbnail_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":140,"size":4.45,"sizeInBytes":4453,"url":"https://cdn.marutitech.com//thumbnail_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a.jpg"},"small":{"name":"small_blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"small_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":285,"size":13.27,"sizeInBytes":13269,"url":"https://cdn.marutitech.com//small_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a.jpg"},"medium":{"name":"medium_blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"medium_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":428,"size":23.28,"sizeInBytes":23279,"url":"https://cdn.marutitech.com//medium_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a.jpg"},"large":{"name":"large_blockchain-network-connecting-trade-trading-crypto-currency-coins-bitcoin-exchanges-invest-metaverse-stocks (1).jpg","hash":"large_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":571,"size":35.76,"sizeInBytes":35758,"url":"https://cdn.marutitech.com//large_blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a.jpg"}},"hash":"blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a","ext":".jpg","mime":"image/jpeg","size":525.95,"url":"https://cdn.marutitech.com//blockchain_network_connecting_trade_trading_crypto_currency_coins_bitcoin_exchanges_invest_metaverse_stocks_1_8dc3337a7a.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:29.913Z","updatedAt":"2024-12-16T11:53:29.913Z"}}},"authors":{"data":[{"id":10,"attributes":{"createdAt":"2022-09-02T07:15:26.748Z","updatedAt":"2025-06-16T10:42:34.224Z","publishedAt":"2022-09-02T07:15:28.070Z","name":"Harsh Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Harsh is the Engineering Team Lead at Maruti Techlabs. He oversees product delivery and user adoption for early-stage startup clients and helps them ship products faster using no-code tools.</span></p>","slug":"harsh-makadia","linkedin_link":"https://www.linkedin.com/in/harsh-makadia/","twitter_link":"https://twitter.com/MakadiaHarsh","image":{"data":[{"id":525,"attributes":{"name":"11.jpg","alternativeText":"11.jpg","caption":"11.jpg","width":1080,"height":1080,"formats":{"thumbnail":{"name":"thumbnail_11.jpg","hash":"thumbnail_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.72,"sizeInBytes":4720,"url":"https://cdn.marutitech.com//thumbnail_11_6b68ffb856.jpg"},"medium":{"name":"medium_11.jpg","hash":"medium_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":56.66,"sizeInBytes":56658,"url":"https://cdn.marutitech.com//medium_11_6b68ffb856.jpg"},"small":{"name":"small_11.jpg","hash":"small_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":30.36,"sizeInBytes":30364,"url":"https://cdn.marutitech.com//small_11_6b68ffb856.jpg"},"large":{"name":"large_11.jpg","hash":"large_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":87,"sizeInBytes":86998,"url":"https://cdn.marutitech.com//large_11_6b68ffb856.jpg"}},"hash":"11_6b68ffb856","ext":".jpg","mime":"image/jpeg","size":88.78,"url":"https://cdn.marutitech.com//11_6b68ffb856.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:59.670Z","updatedAt":"2024-12-16T11:54:59.670Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1960,"title":"Machine Learning Model Accelerates Healthcare Record Processing by 87%","link":"https://marutitech.com/case-study/medical-record-processing-using-nlp/","cover_image":{"data":{"id":402,"attributes":{"name":"2 (13).png","alternativeText":"2 (13).png","caption":"2 (13).png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_2 (13).png","hash":"thumbnail_2_13_7118a99116","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":21,"sizeInBytes":21002,"url":"https://cdn.marutitech.com//thumbnail_2_13_7118a99116.png"},"small":{"name":"small_2 (13).png","hash":"small_2_13_7118a99116","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":73.7,"sizeInBytes":73702,"url":"https://cdn.marutitech.com//small_2_13_7118a99116.png"},"large":{"name":"large_2 (13).png","hash":"large_2_13_7118a99116","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":292.75,"sizeInBytes":292746,"url":"https://cdn.marutitech.com//large_2_13_7118a99116.png"},"medium":{"name":"medium_2 (13).png","hash":"medium_2_13_7118a99116","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":163.79,"sizeInBytes":163790,"url":"https://cdn.marutitech.com//medium_2_13_7118a99116.png"}},"hash":"2_13_7118a99116","ext":".png","mime":"image/png","size":93.1,"url":"https://cdn.marutitech.com//2_13_7118a99116.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:54.644Z","updatedAt":"2024-12-16T11:45:54.644Z"}}}},"authors":{"data":[{"id":10,"attributes":{"createdAt":"2022-09-02T07:15:26.748Z","updatedAt":"2025-06-16T10:42:34.224Z","publishedAt":"2022-09-02T07:15:28.070Z","name":"Harsh Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Harsh is the Engineering Team Lead at Maruti Techlabs. He oversees product delivery and user adoption for early-stage startup clients and helps them ship products faster using no-code tools.</span></p>","slug":"harsh-makadia","linkedin_link":"https://www.linkedin.com/in/harsh-makadia/","twitter_link":"https://twitter.com/MakadiaHarsh","image":{"data":[{"id":525,"attributes":{"name":"11.jpg","alternativeText":"11.jpg","caption":"11.jpg","width":1080,"height":1080,"formats":{"thumbnail":{"name":"thumbnail_11.jpg","hash":"thumbnail_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.72,"sizeInBytes":4720,"url":"https://cdn.marutitech.com//thumbnail_11_6b68ffb856.jpg"},"medium":{"name":"medium_11.jpg","hash":"medium_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":56.66,"sizeInBytes":56658,"url":"https://cdn.marutitech.com//medium_11_6b68ffb856.jpg"},"small":{"name":"small_11.jpg","hash":"small_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":30.36,"sizeInBytes":30364,"url":"https://cdn.marutitech.com//small_11_6b68ffb856.jpg"},"large":{"name":"large_11.jpg","hash":"large_11_6b68ffb856","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":87,"sizeInBytes":86998,"url":"https://cdn.marutitech.com//large_11_6b68ffb856.jpg"}},"hash":"11_6b68ffb856","ext":".jpg","mime":"image/jpeg","size":88.78,"url":"https://cdn.marutitech.com//11_6b68ffb856.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:59.670Z","updatedAt":"2024-12-16T11:54:59.670Z"}}]}}}]},"seo":{"id":2190,"title":"Blockchain Technology and its Implications on the Financial Platform","description":"The Blockchain technology is revolutionizing the financial sectors. Many banks have started adopting the blockchain technology for offering credit.","type":"article","url":"https://marutitech.com/blockchain-technology-implications-financial-platform/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":502,"attributes":{"name":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","alternativeText":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","caption":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","width":7048,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":140,"size":4.07,"sizeInBytes":4069,"url":"https://cdn.marutitech.com//thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"medium":{"name":"medium_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":428,"size":19.02,"sizeInBytes":19023,"url":"https://cdn.marutitech.com//medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"large":{"name":"large_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":571,"size":29.08,"sizeInBytes":29077,"url":"https://cdn.marutitech.com//large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"small":{"name":"small_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":285,"size":10.66,"sizeInBytes":10664,"url":"https://cdn.marutitech.com//small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"}},"hash":"businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","size":406.1,"url":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:13.281Z","updatedAt":"2024-12-16T11:53:13.281Z"}}}},"image":{"data":{"id":502,"attributes":{"name":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","alternativeText":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","caption":"businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","width":7048,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":140,"size":4.07,"sizeInBytes":4069,"url":"https://cdn.marutitech.com//thumbnail_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"medium":{"name":"medium_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":428,"size":19.02,"sizeInBytes":19023,"url":"https://cdn.marutitech.com//medium_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"large":{"name":"large_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":571,"size":29.08,"sizeInBytes":29077,"url":"https://cdn.marutitech.com//large_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"},"small":{"name":"small_businessman-explore-metaverse-technology-with-blockchain-network-connecting (1).jpg","hash":"small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":285,"size":10.66,"sizeInBytes":10664,"url":"https://cdn.marutitech.com//small_businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg"}},"hash":"businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12","ext":".jpg","mime":"image/jpeg","size":406.1,"url":"https://cdn.marutitech.com//businessman_explore_metaverse_technology_with_blockchain_network_connecting_1_eb2fb2db12.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:13.281Z","updatedAt":"2024-12-16T11:53:13.281Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
