exports.id=6934,exports.ids=[6934],exports.modules={98638:(e,a,t)=>{Promise.resolve().then(t.bind(t,33126))},72829:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=async()=>{let e="user_ip_location",a=localStorage.getItem(e);if(a)try{return JSON.parse(a)}catch(e){console.warn("Failed to parse cached IP data:",e)}try{let a=await fetch("https://api.ipify.org?format=json");if(!a.ok)throw Error(`IP fetch error! Status: ${a.status}`);let t=(await a.json()).ip||"",r=await fetch(`https://ipapi.co/${t}/json/`);if(!r.ok)throw Error(`Location fetch error! Status: ${r.status}`);let n=await r.json(),s={ipAddress:t,location:{city:n.city||"",country:n.country_name||"",country_code:n.country_code||""}};return localStorage.setItem(e,JSON.stringify(s)),s}catch(e){return console.error("Error fetching IP or location:",e),{ipAddress:"",location:{city:"",country:"",country_code:""}}}}},33126:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>W});var r=t(95344),n=t(3729),s=t(8428),o=t(89410),i=t(11276),l=t.n(i),c=t(18924),d=t(72885),_=t.n(d),u=t(88070),m=t.n(u);function p({sectionIndex:e,sectionQuestions:a,sectionData:t,sectionError:s,handleData:o,handleError:i}){let l=(0,c.Z)({query:`(max-width: ${_()["breakpoint-xl-1024"]})`}),[d,u]=(0,n.useState)(()=>null!==localStorage.getItem("subAnswer")?JSON.parse(localStorage.getItem("subAnswer")):[[],[,,,].fill(null)]),p=(e,a)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),a())};function h(e,a,t,r,n){let s=[...d];s[0][e]=a,s[1][e]=t;let l=0;for(let e=0;e<d[1].length;e++)null!==s[1][e]&&(l+=33.33);localStorage.setItem("subAnswer",JSON.stringify(s)),u(s),i(r,n,!1),o(r,n,s[0].join(","),Math.round(l))}return r.jsx(r.Fragment,{children:a.map((n,c)=>(0,r.jsxs)("div",{className:m().container,children:[(0,r.jsxs)("div",{className:m().question_container,children:[(0,r.jsxs)("div",{className:s[c]?`${m().question_number} ${m().error_message}`:m().question_number,children:[n.number<10?"0":"",n.number,"."]}),r.jsx("div",{className:m().question_name,children:n.name})]}),"mcq"===n.type?r.jsx("div",{className:m().mcqs_container,children:0!==n.sub_question.length?r.jsx(r.Fragment,{children:function(e,a,t){let n=e.sub_question,s=e.answers,o=[],i=0;for(let e=0;e<n.length;e++){let l=[r.jsx("div",{className:m().sub_question_name,children:n[e].name},e)];for(let o=0;o<n[e].value;o++){if(i>=s.length){console.warn(`Answer index ${i} exceeds answers array length ${s.length}`);break}let o=s[i],c=(0,r.jsxs)("label",{className:d[1][e]===o.id?` ${m().mcq} ${m().selected_mcq}`:m().mcq,htmlFor:o.id,tabIndex:0,onKeyDown:r=>p(r,()=>{h(e,o.name,Number(o.id),a,t)}),children:[r.jsx("input",{type:"radio",id:o.id,name:n[e].name,"data-name":o.name,value:o.id,onChange:r=>{h(e,r.target.dataset.name,Number(r.target.value),a,t)}}),o.name]},o.id);i+=1,l.push(c)}o.push(l)}return o}(n,e,c)}):r.jsx(r.Fragment,{children:n.answers.map((a,s)=>(0,r.jsxs)("label",{className:t[c][1]===a.value?` ${m().mcq} ${m().selected_mcq}`:m().mcq,htmlFor:a.id,tabIndex:0,onKeyDown:t=>p(t,()=>{i(e,c,!1),o(e,c,a.name,Number(a.value))}),children:[r.jsx("input",{type:"radio",id:a.id,name:n.name,value:a.value,onChange:t=>{i(e,c,!1),o(e,c,a.name,Number(t.target.value))}}),a.name]},s))})}):r.jsx(r.Fragment,{children:l?(0,r.jsxs)("div",{className:m().number_wrapper,children:[r.jsx("div",{className:m().draggable_container_tablet,children:n.answers.map((a,s)=>(0,r.jsxs)("label",{className:t[c][1]===a.value?` ${m().number} ${m().selected_number}`:m().number,htmlFor:a.id,tabIndex:0,onKeyDown:t=>p(t,()=>{i(e,c,!1),o(e,c,a.name,Number(a.value))}),children:[r.jsx("input",{type:"radio",id:a.id,name:n.name,value:a.value,onChange:t=>{i(e,c,!1),o(e,c,a.name,Number(t.target.value))}}),s+1]},s))}),(0,r.jsxs)("div",{className:m().number_label,children:[r.jsx("span",{children:n.answers[0].name}),r.jsx("span",{children:n.answers[n.answers.length-1].name})]})]}):(0,r.jsxs)("div",{className:m().draggable_container,children:[r.jsx("input",{type:"range",id:"range",name:"range",min:"0",max:"100",step:"25",className:m().draggable_input,value:t[c][1],onChange:t=>{let r;i(e,c,!1),o(e,c,(r=Math.round(t.target.value/25))>=a[c].answers.length?(console.warn(`Answer index ${r} exceeds answers array length ${a[c].answers.length}`),""):a[c].answers[r].name,Number(t.target.value))},style:{background:`linear-gradient(to right, #30AD43 0%, #30AD43 ${t[c][1]}%, #ccc ${t[c][1]}%, #ccc 100%)`}}),r.jsx("div",{className:m().draggable_wrapper,children:n.answers.map((a,n)=>r.jsx("div",{className:t[c][1]===a.value?`${m().draggable_label} ${m().selected_draggable_label}`:m().draggable_label,onClick:t=>o(e,c,a.name,Number(a.value)),onKeyDown:t=>p(t,()=>{o(e,c,a.name,Number(a.value))}),tabIndex:0,role:"button","aria-pressed":t[c][1]===a.value,children:a.name},n))})]})})]},c))})}var h=t(81473),b=t(55846),g=t(6297),f=t(18502),y=t.n(f);let v=["Strategy & Leadership","Data Readiness & Infrastructure","Talent & Skills","Execution & Monitoring","Impact Evaluation"],x=({visibleCount:e,onStepClick:a})=>{let[t,s]=(0,n.useState)(1);return(0,n.useEffect)(()=>{t<e+1&&s(e+1)},[e,t]),r.jsx("div",{className:y().container,children:v.map((n,o)=>{let i=o+1,l=i<=e+1,c=i===t;return(0,r.jsxs)("div",{className:y().stepWrapper,onClick:()=>{i<=e+1&&(s(i),a(i))},children:[r.jsx("div",{className:`
                ${y().circle}
                ${l?y().active_circle:""}
                ${c?y().selected_circle:""}
              `,children:i}),r.jsx("p",{className:`
                ${y().label}
                ${l?y().active_text:""}
                ${c?y().selected_text:""}
              `,children:n}),o<v.length-1&&r.jsx("div",{className:`${y().line} ${i<e+1?y().active:""}`})]},o)})})};var w=t(82678),N=t(5894),A=t.n(N);let j=({percentage:e,tag_list:a,tag_color:t})=>{let n=Math.max(0,Math.min(e,100)),{tag:s,color:o}=n<=30?{tag:a[0].name,color:t[0]}:n<=60?{tag:a[1].name,color:t[1]}:n<=85?{tag:a[2].name,color:t[2]}:{tag:a[3].name,color:t[3]},i=[{limit:30,color:t[0],label:"0–30"},{limit:60,color:t[1],label:"31–60"},{limit:85,color:t[2],label:"61–85"},{limit:100,color:t[3],label:"86–100"}];return(0,r.jsxs)("div",{className:A().gauge_container,children:[r.jsx(w.ZP,{type:"semicircle",arc:{width:.2,padding:.005,cornerRadius:5,subArcs:i.map(e=>({limit:e.limit,color:e.color,showTick:!0,label:e.label}))},pointer:{color:"#000000",baseColor:"#ffff",length:.7,width:12},className:A().gauge,value:n,labels:{valueLabel:{formatTextValue:()=>""}}}),(0,r.jsxs)("div",{className:A().result_text,style:{backgroundColor:o},children:[s,": ",Math.round(n),"%"]})]})},I=({percentage:e=50,radius:a=20,strokeWidth:t=7})=>{let n=a-t/2,s=2*n*Math.PI;return(0,r.jsxs)("svg",{height:2*a,width:2*a,children:[r.jsx("defs",{children:(0,r.jsxs)("linearGradient",{id:"gradient",x1:"0",y1:"0",x2:"1",y2:"0.15",children:[r.jsx("stop",{offset:"0%",stopColor:"#febe10"}),r.jsx("stop",{offset:"30.56%",stopColor:"#f47a37"}),r.jsx("stop",{offset:"53.47%",stopColor:"#f05443"}),r.jsx("stop",{offset:"75.75%",stopColor:"#d91a5f"}),r.jsx("stop",{offset:"100%",stopColor:"#b41f5e"})]})}),r.jsx("circle",{stroke:"#ddd",fill:"transparent",strokeWidth:t,r:n,cx:a,cy:a}),r.jsx("circle",{stroke:"url(#gradient)",fill:"transparent",strokeWidth:t,strokeDasharray:s,strokeDashoffset:s-e/100*s,strokeLinecap:"round",r:n,cx:a,cy:a,transform:`rotate(-90 ${a} ${a})`,style:{transition:"stroke-dashoffset 0.5s ease"}})]})};var k=t(60646),C=t(22281),S=t(26356),F=t.n(S),R=t(85551),$=t(72767),T=t.n($);t(22745);var L=t(55494);function q({formData:e,source:a="AIReadiness",handleResult:t,handleVisibleSection:s}){let{title:i,instructions:l,consent_statement:c,LinkedInButton_title:d,button:_,formFields:{fieldNameFor_FirstName:u,fieldNameFor_LastName:m,fieldNameFor_EmailAddress:p,fieldNameFor_CompanyName:g,fieldNameFor_PhoneNumber:f,fieldNameFor_HowCanWeHelpYou:y}}=e,[v,x]=(0,n.useState)(!1),w=(0,L.Z)(),{values:N,errors:A,errorMessages:j,handleChange:I,handleSubmitAIReadiness:S}=(0,R.Z)({firstName:"",lastName:"",emailAddress:"",phoneNumber:"",howDidYouHearAboutUs:"",companyName:"",howCanWeHelpYou:"",consent:!1},{firstName:{empty:!1},lastName:{empty:!1},emailAddress:{empty:!1,invalid:!1},phoneNumber:{empty:!1,invalid:!1},consent:{empty:!1}},"default",a),$=async e=>{e.preventDefault(),x(!0);try{let{data:e,newResult:a}=t();await S(e,a,s)}catch(e){console.error("Form submission failed:",e)}finally{x(!1)}};return r.jsx(k.default,{fluid:!0,children:(0,r.jsxs)("div",{className:F().formWrapper,children:[r.jsx(h.Z,{title:"Contact Details",headingType:"h2",className:F().heading}),(0,r.jsxs)("form",{className:F().form,onSubmit:$,children:[r.jsx("div",{className:F().formFields,children:r.jsx("div",{className:F().personalDetailsWrapper,children:r.jsx("div",{className:(0,C.Z)(F().row,F().firstRow),children:(0,r.jsxs)("div",{className:(0,C.Z)(F().row,F().nameFields),children:[(0,r.jsxs)("div",{className:(0,C.Z)(F().nameAndInputWrapper),children:[(0,r.jsxs)("label",{htmlFor:"firstName",className:A.firstName.empty?F().errorLabel:F().formLabel,children:[u,"*"]}),r.jsx("input",{className:A.firstName.empty?`${F().errorInput} ${F().formInput}`:`${F().formInput}`,type:"text",id:"firstName",name:"firstName",maxLength:50,value:N.firstName,onChange:e=>I(e?.target),onBlur:e=>I(e?.target)})]}),(0,r.jsxs)("div",{className:(0,C.Z)(F().nameAndInputWrapper),children:[(0,r.jsxs)("label",{htmlFor:"lastName",className:A.lastName.empty?F().errorLabel:F().formLabel,children:[m,"*"]}),r.jsx("input",{className:A.lastName.empty?`${F().errorInput} ${F().formInput}`:`${F().formInput}`,type:"text",id:"lastName",name:"lastName",maxLength:50,value:N.lastName,onChange:e=>I(e?.target),onBlur:e=>I(e?.target)})]}),(0,r.jsxs)("div",{className:F().nameAndInputWrapper,children:[(0,r.jsxs)("label",{htmlFor:"emailAddress",className:A.emailAddress.empty||A.emailAddress.invalid?F().errorLabel:F().formLabel,children:[p,"*"]}),r.jsx("input",{className:A.emailAddress.empty?`${F().errorInput} ${F().formInput}`:`${F().formInput}`,type:"text",id:"emailAddress",name:"emailAddress",maxLength:50,value:N.emailAddress,onChange:e=>I(e?.target),onBlur:e=>I(e?.target)})]}),(0,r.jsxs)("div",{className:F().nameAndInputWrapper,children:[(0,r.jsxs)("label",{htmlFor:"phoneNumber",className:A.phoneNumber.empty||A.phoneNumber.invalid?F().errorLabel:F().formLabel,children:[f,"*"]}),r.jsx("div",{className:F().phoneInputWrapper,children:r.jsx(T(),{inputProps:{id:"phoneNumber"},placeholder:"",inputClass:A.phoneNumber.empty||A.phoneNumber.invalid?`${F().errorInput} ${F().formInputPhone}`:F().formInputPhone,buttonClass:A.phoneNumber.empty||A.phoneNumber.invalid?`${F().errorInput} ${F().formInputPhone_dial_icon}`:F().formInputPhone_dial_icon,dropdownClass:F().ph_number_countries_dropdown,preferredCountries:["us","gb","sg","de","sa","in","nl","au","be","my"],country:w||"us",value:N.phoneNumber,onChange:e=>I({value:e,name:"phoneNumber"}),onBlur:e=>I(e?.target)})})]}),(0,r.jsxs)("div",{className:(0,C.Z)(F().nameAndInputWrapper,F().companyNameWrapper),children:[r.jsx("label",{htmlFor:"companyName",className:F().formLabel,children:g}),r.jsx("input",{className:F().formInput,type:"text",id:"companyName",name:"companyName",maxLength:50,value:N.companyName,onChange:e=>I(e?.target)})]})]})})})}),r.jsx("div",{className:F().consentRow,children:(0,r.jsxs)("label",{className:A.consent.empty?`${F().errorLabel_consentText} ${F().consentText}`:F().consentText,htmlFor:"consent",onClick:()=>{I({name:"consent",type:"checkbox",value:"",checked:!N.consent})},children:[r.jsx("input",{type:"checkbox",id:"consent",name:"consent",checked:N.consent}),r.jsx("span",{children:c})]})}),(0,r.jsxs)("div",{className:F().submitButtonRow,children:[v?r.jsx("div",{className:F().container_spinner,children:r.jsx("div",{className:F().spinner})}):r.jsx(b.Z,{type:"submit",className:F().result_button,label:"Check my AI Readiness Score"}),d&&(0,r.jsxs)("a",{className:F().linkedInButton,href:"#",children:[d,r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/linkedin_c13ca9a536.png",width:32,height:32,alt:"LinkedIn Logo"})]})]})]}),(0,r.jsxs)("div",{className:F().errorMessages,children:[r.jsx("div",{children:j.empty&&j.empty}),r.jsx("div",{children:j.invalid&&j.invalid})]})]})})}function W({body:e,formData:a}){let[t,i]=(0,n.useState)(null),[d,_]=(0,n.useState)(null),[u,m]=(0,n.useState)(null),[f,y]=(0,n.useState)(null),[v,w]=(0,n.useState)([]),N=(0,s.useRouter)(),A=(0,c.Z)({query:"(max-width: 700px)"});(0,n.useEffect)(()=>{let a=e?.ai_readiness_components?.data.map(e=>e.attributes.heading.toLowerCase().replaceAll(" ","-").replaceAll("&","and"))||[];function t(){}return a.push("result"),w(a),S(),window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),(0,n.useEffect)(()=>{v.length},[u,v]);let k=e?.tag_list,C=["#FF5656","#FF8888","#84BD32","#30AD43"];function S(){let a=[],t=[],r=0,n=null;if(null!==localStorage.getItem("result")&&(n=JSON.parse(localStorage.getItem("result"))),null!==localStorage.getItem("data")&&null!==localStorage.getItem("error"))a=JSON.parse(localStorage.getItem("data")),t=JSON.parse(localStorage.getItem("error"));else for(let r=0;r<e?.ai_readiness_components?.data.length;r++){let n=[],s=[];for(let a=0;a<e?.ai_readiness_components?.data[r]?.attributes?.question.length;a++)e?.ai_readiness_components?.data[r]?.attributes?.question[a].type==="mcq"?(n.push([null,null]),s.push(null)):(n.push([e?.ai_readiness_components?.data[r]?.attributes?.question[a].answers[0].name,0]),s.push(!1));a[r]=n,t[r]=s}null!==localStorage.getItem("visibleSection")&&(r=JSON.parse(localStorage.getItem("visibleSection"))),i(a),_(t),m(r),y(n)}function F(e,a,r,n){let s=[...t];s[e][a][0]=r,s[e][a][1]=n,localStorage.setItem("data",JSON.stringify(s)),i(s)}function R(e,a,t){let r=[...d];r[e][a]=t,localStorage.setItem("error",JSON.stringify(r)),_(r)}function $(e){-1===e&&(e=0),localStorage.setItem("visibleSection",JSON.stringify(e)),m(e)}function T(){$(u-1)}function L(){if(!(d[u].includes(null)||d[u].includes(!0)))return!0;{let e=[...d];for(let a=0;a<e[u].length;a++)null===e[u][a]&&(e[u][a]=!0);return localStorage.setItem("error",JSON.stringify(e)),_(e),!1}}function W(){if(L()){let a={};a.final=0;for(let r=0;r<t.length;r++){let n=e?.ai_readiness_components?.data[r].attributes.section_weight,s=0;for(let e=0;e<t[r].length;e++)s+=t[r][e][1];a[r]=Math.round(s/t[r].length),a.final=a.final+a[r]*n}return a.final=Math.round(a.final),console.log("data",t),console.log("result",a),localStorage.setItem("result",JSON.stringify(a)),y(a),{data:t,newResult:a}}}function B(){let e={...f};delete e.final;let a=0,t=100;for(let r in e)e[r]<t&&(t=e[r],a=Number(r));return a}return(0,r.jsxs)(r.Fragment,{children:[t&&u<t.length&&r.jsx(g.Z,{heroData:e?.hero_section,variant:"ai-readiness"}),t&&(0,r.jsxs)("div",{className:l().container,id:e?.hero_section?.button_link,children:[u<t.length&&r.jsx(r.Fragment,{children:A?r.jsx(r.Fragment,{children:(0,r.jsxs)("div",{className:l().button_wrapper_mobile,children:[u>0&&u<t.length&&r.jsx("button",{onClick:T,children:r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/black_chevron_left_5adc2eb9de.svg",alt:"previous section",width:25,height:25})}),u+1,"/0",v.length-1,u<t.length-1&&r.jsx("button",{onClick:()=>{L()&&$(u+1)},children:r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/black_chevron_left_6d81dc24e5.svg",alt:"next section",width:25,height:25})})]})}):r.jsx(x,{visibleCount:u,onStepClick:e=>$(e-1)})}),e?.ai_readiness_components?.data.map((e,n)=>r.jsxs("div",{className:u===n?l().section_wrapper:l().hidden,children:[r.jsx("div",{className:l().heading,children:r.jsxs("h2",{children:[n+1,". ",e?.attributes?.heading]})}),u!==t.length&&r.jsx(p,{sectionIndex:n,sectionQuestions:e?.attributes.question,sectionData:t[n],sectionError:d[n],handleData:F,handleError:R}),r.jsx("span",{id:"error",children:u<t.length&&d[u].includes(!0)&&r.jsx("div",{className:l().error_message,children:"Please fill all the required fields."})}),u===t.length-1&&r.jsx(q,{formData:a,handleResult:W,handleVisibleSection:$}),r.jsxs("div",{className:l().button_wrapper,children:[u>0&&u<t.length&&r.jsx("button",{onClick:T,children:r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/chevron_left_7f3e8fa9d6.svg",alt:"previous section",width:50,height:50})}),u<t.length-1&&r.jsx("button",{onClick:()=>{L()&&$(u+1)},children:r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/chevron_right_0f9e1dff3c.svg",alt:"next section",width:50,height:50})})]})]},n)),u===t.length&&f&&(0,r.jsxs)("div",{className:l().result_section,children:[r.jsx("div",{className:l().button_wrapper,children:(0,r.jsxs)(b.Z,{className:l().restart_button,onClick:function(){localStorage.removeItem("data"),localStorage.removeItem("error"),localStorage.removeItem("visibleSection"),localStorage.removeItem("result"),localStorage.removeItem("subAnswer"),window.location.href="/ai-readiness-audit#"+e?.ai_readiness_components?.data[0].attributes.heading.toLowerCase().replaceAll(" ","-").replaceAll("&","and"),S()},children:[r.jsx(o.default,{src:"https://dev-cdn.marutitech.com/restart_button_831deeb022.svg",alt:"restart assessment",width:24,height:24}),e?.restart_button?.title]})}),r.jsx("div",{className:l().heading,children:(0,r.jsxs)("h2",{children:[e?.tag?.title,":"," ",(0,r.jsxs)("span",{style:{color:C[function(e){let a="",t=0;for(let r=0;r<k.length;r++)if(f[e]<=k[r].value){a=k[r].name,t=r;break}return[a,t]}(B())[1]]},children:[e?.ai_readiness_components?.data[B()]?.attributes?.heading," ",": ",f[B()],"%"]})]})}),r.jsx("div",{className:l().gauge_wrapper,children:r.jsx(j,{percentage:f.final,tag_list:k,tag_color:C})}),r.jsx("div",{className:l().tags,children:k.map((e,a)=>(0,r.jsxs)("span",{style:{color:C[a]},children:[0===a?`<${k[a].value}`:a===k.length-1?`>${k[a-1].value}`:`${k[a-1].value+1} - ${k[a].value}`,"%: ",k[a].name,a<k.length-1?" | ":""]},a))}),r.jsx("div",{className:l().description,dangerouslySetInnerHTML:{__html:e?.tag?.description}}),r.jsx("div",{children:r.jsx(b.Z,{className:l().consultation_button,label:e?.consultation_button?.title,type:"button",onClick:()=>{N.push(e?.consultation_button?.link)}})}),r.jsx(h.Z,{title:e?.score_heading,headingType:"h2",className:l().heading}),r.jsx("div",{className:l().score_cards_wrapper,children:e?.ai_readiness_components?.data.map((e,a)=>r.jsxs("div",{className:l().score_cards,children:[r.jsxs("div",{style:{display:"flex",gap:"20px",alignItems:"center"},children:[r.jsx(I,{percentage:f[a]}),f[a],"%"]}),r.jsx("div",{children:r.jsx("b",{children:e?.attributes?.heading})})]},a))})]})]})]})}},6297:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r.default});var r=t(93261)},85551:(e,a,t)=>{"use strict";t.d(a,{Z:()=>o});var r=t(72829);let n=async()=>{try{let e=document.referrer||"",a=new URLSearchParams(window.location.search),t=a.get("utm_medium")||"",r=a.get("utm_source")||"",n=a.get("utm_campaign")||"",s="";if(window.clarity)try{s=window.clarity("get","userId")}catch(e){console.error("Error fetching Clarity ID:",e)}let o="";try{o=globalThis.gaGlobal?.vid.match(/\d+\.\d+$/)?.[0]||""}catch(e){console.error("Error fetching GA4 Client ID:",e)}return{clarity:s,utm_medium:t,utm_source:r,utm_campaign:n,referrer:e,ga_client_id:o}}catch(e){return console.error("Error fetching user tracking data:",e),{clarity:"",utm_medium:"",utm_source:"",utm_campaign:"",referrer:"",ga_client_id:""}}};var s=t(3729);function o(e,a,t="default",o,i=""){let[l,c]=(0,s.useState)(e),[d,_]=(0,s.useState)(a),[u,m]=(0,s.useState)({empty:"",invalid:""}),p="caseStudy"===t?["firstName","emailAddress","phoneNumber"]:["firstName","lastName","emailAddress","phoneNumber","consent"],h=e=>{let a={...u};p.some(a=>e[a]?.empty)?a.empty="Please fill the highlighted fields":a.empty="",e.emailAddress?.invalid&&e.phoneNumber?.invalid?a.invalid="Please enter valid Email ID and Phone Number":e.emailAddress?.invalid?a.invalid="Please enter a valid Email ID":e.phoneNumber?.invalid?a.invalid="Please enter a valid Phone Number":a.invalid="",m(a)},b=(e,t)=>{let r={...d};t?("emailAddress"!==e||/\S+@\S+\.\S+/.test(t))&&("phoneNumber"!==e||/.{6,}/.test(t))?r[e]=a[e]:r[e]={empty:!1,invalid:!0}:r[e]={empty:!0,invalid:!1},_(r),h(r)},g=()=>{let e={...d};return p.forEach(a=>{l[a]||(e[a]={empty:!0,invalid:!1})}),_(e),h(e),!Object.values(e).some(e=>e.empty||e.invalid)},f=async t=>{if(t.preventDefault(),g()){try{let e=await (0,r.Z)(),a=await n(),t={firstName:l.firstName||"",lastName:l.lastName||"",emailAddress:l.emailAddress||"",phoneNumber:l.phoneNumber||"",howDidYouHearAboutUs:l.howDidYouHearAboutUs||"",companyName:l.companyName||"",howCanWeHelpYou:l.howCanWeHelpYou||"",utm_campaign:a.utm_campaign||"",utm_medium:a.utm_medium||"",utm_source:a.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:a.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:o||"",clarity:a.clarity||"",url:window.location.href||"",referrer:a.referrer||"",consent:l.consent||!1},s=await fetch("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev/contact-us",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(t)});s.ok?(("CaseStudy"===o||"eBooks"===o||"whitePapers"===o)&&i&&window.open(i,"_blank"),window.location.href="/thank-you/"):console.error("Error submitting form:",await s.json())}catch(e){console.error("Error in form submission:",e)}c(e),_(a)}},y=async(t,s,i)=>{if(g()){try{let e=await (0,r.Z)(),a=await n(),c={firstName:l.firstName||"",lastName:l.lastName||"",emailAddress:l.emailAddress||"",phoneNumber:l.phoneNumber||"",companyName:l.companyName||"",utm_campaign:a.utm_campaign||"",utm_medium:a.utm_medium||"",utm_source:a.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:a.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:o||"",clarity:a.clarity||"",url:window.location.href||"",referrer:a.referrer||"",consent:l.consent||!1,do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_:t[0][0][0]||"",how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_:t[0][1][0]||"",do_you_have_budget_allocated_for_your_ai_project_:t[0][2][0]||"",do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_:t[1][0][0]||"",which_of_the_below_db_tools_do_you_currently_use_:t[1][1][0]||"",is_the_relevant_data_for_the_ai_project_available_and_accessible_:t[1][2][0]||"",do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__:t[1][3][0]||"",how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib:t[1][4][0]||"",does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_:t[2][0][0]||"",do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_:t[3][0][0]||"",do_you_have_risk_management_strategies_in_place_for_the_ai_project_:t[3][1][0]||"",do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions:t[4][0][0]||"",strategy___leadership:s[0]||"",data_readiness___infrastructure:s[1]||"",talent___skills:s[2]||"",execution___monitoring:s[3]||"",impact_evaliation:s[4]||"",average_of_all_score:s.final||""},d=await fetch("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev/ai-readiness",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(c)});d.ok?i(t.length):console.error("Error submitting form:",await d.json())}catch(e){console.error("Error in form submission:",e)}c(e),_(a)}};return{values:l,errors:d,errorMessages:u,handleChange:({name:e,value:a,type:t="",checked:r=!1})=>{a="checkbox"===t?r:a;let n={...l};"firstName"===e||"lastName"===e?n[e]=a.replace(/[^a-zA-Z0-9 ]/g,"").trimStart():"emailAddress"===e?n[e]=a.replace(" ",""):n[e]=a,c(n),e in d&&b(e,a)},handleBlur:({name:e,value:a})=>{let t={...l};"string"==typeof a&&(t[e]=a.trim()),c(t),e in d&&b(e,a)},handleSubmit:f,handleSubmitAIReadiness:y}}},55494:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});var r=t(3729),n=t(72829);function s(){let[e,a]=(0,r.useState)("");return(0,r.useEffect)(()=>{(async()=>{try{let e=await (0,n.Z)();a(e?.location?.country_code?.toLowerCase()||"")}catch(e){a("us")}})()},[]),e}},11276:(e,a,t)=>{var r=t(24640),n=t(70048);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,colorWhite:""+r.colorWhite,colorBlack:""+r.colorBlack,gray300:""+r.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":""+n["breakpoint-sm"],"breakpoint-xl":""+n["breakpoint-xl"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-xl-1440":""+n["breakpoint-xl-1440"],container:"AIReadinessBody_container__tGdLN",step_container:"AIReadinessBody_step_container__gW60D",hidden:"AIReadinessBody_hidden__QgV9b",section_wrapper:"AIReadinessBody_section_wrapper__Zm43X",heading:"AIReadinessBody_heading__L0Jtf",error_message:"AIReadinessBody_error_message__g_WNZ",button_wrapper_mobile:"AIReadinessBody_button_wrapper_mobile__S0aI1",button_wrapper:"AIReadinessBody_button_wrapper__P9uSU",result_button:"AIReadinessBody_result_button__pwAt1",tags:"AIReadinessBody_tags__eraaj",result_section:"AIReadinessBody_result_section__sI57L",restart_button:"AIReadinessBody_restart_button__ngofd",consultation_button:"AIReadinessBody_consultation_button__CtKyT",description:"AIReadinessBody_description__g__hx",score_cards_wrapper:"AIReadinessBody_score_cards_wrapper__Q1Vk_",score_cards:"AIReadinessBody_score_cards__tIR6g",gauge_wrapper:"AIReadinessBody_gauge_wrapper__cUmfs"}},26356:(e,a,t)=>{var r=t(24640),n=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+r.colorBlack,colorWhite:""+r.colorWhite,gray:""+r.gray,brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,gray300:""+r.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm-450":""+n["breakpoint-sm-450"],"breakpoint-md":""+n["breakpoint-md"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-xl-1400":""+n["breakpoint-xl-1400"],heading:"AIReadinessForm_heading__zDVKs",formWrapper:"AIReadinessForm_formWrapper__gZIkR",form:"AIReadinessForm_form__uu90e",formFields:"AIReadinessForm_formFields__WEfzC",personalDetailsWrapper:"AIReadinessForm_personalDetailsWrapper__MA09e",row:"AIReadinessForm_row__0FoYJ",nameAndInputWrapper:"AIReadinessForm_nameAndInputWrapper__Jkgg7",firstRow:"AIReadinessForm_firstRow__4fvbq",formLabel:"AIReadinessForm_formLabel__Sz516",formInput:"AIReadinessForm_formInput__LiTq7",phoneInputWrapper:"AIReadinessForm_phoneInputWrapper__uxK7a",formInputPhone:"AIReadinessForm_formInputPhone__Eq6EL",formInputPhone_dial_icon:"AIReadinessForm_formInputPhone_dial_icon__H1ZPl",ph_number_countries_dropdown:"AIReadinessForm_ph_number_countries_dropdown__5VtDb",formInputForHowCanWeHelpYou:"AIReadinessForm_formInputForHowCanWeHelpYou__7etT_",consentRow:"AIReadinessForm_consentRow__YAlpQ",consentText:"AIReadinessForm_consentText__jcT47",submitButtonRow:"AIReadinessForm_submitButtonRow__H9FgZ",submitButton:"AIReadinessForm_submitButton__lgj0Z",linkedInButton:"AIReadinessForm_linkedInButton__UlLiV",errorInput:"AIReadinessForm_errorInput__jFI3x",errorMessages:"AIReadinessForm_errorMessages__lMe0y",errorLabel:"AIReadinessForm_errorLabel__Dj5XK",errorLabel_consentText:"AIReadinessForm_errorLabel_consentText__xUfN3",container_spinner:"AIReadinessForm_container_spinner__hGhLh",spinner:"AIReadinessForm_spinner__YaMdM",spin:"AIReadinessForm_spin__WCWUD",result_button:"AIReadinessForm_result_button__M_E4e"}},18502:(e,a,t)=>{var r=t(24640),n=t(70048);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,colorWhite:""+r.colorWhite,colorBlack:""+r.colorBlack,gray300:""+r.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md-820":""+n["breakpoint-md-820"],container:"AIReadinessStep_container__tmP3g",stepWrapper:"AIReadinessStep_stepWrapper__rRQUI",circle:"AIReadinessStep_circle__1cIeN",label:"AIReadinessStep_label__04lGp",line:"AIReadinessStep_line__NFSN3",active_circle:"AIReadinessStep_active_circle__6Ersp",active_text:"AIReadinessStep_active_text__zOSsX",active:"AIReadinessStep_active__ODLL_"}},88070:(e,a,t)=>{var r=t(24640),n=t(70048);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,gray300:""+r.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm-450":""+n["breakpoint-sm-450"],"breakpoint-xl":""+n["breakpoint-xl"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-md":""+n["breakpoint-md"],container:"QuestionAndAnswers_container__vG2qU",question_container:"QuestionAndAnswers_question_container__EtWck",question_number:"QuestionAndAnswers_question_number__7um4F",sub_question_name:"QuestionAndAnswers_sub_question_name__dvCuz",question_name:"QuestionAndAnswers_question_name__YK_Qn",error_message:"QuestionAndAnswers_error_message__gZobY",mcqs_container:"QuestionAndAnswers_mcqs_container__bbuHx",selected_mcq:"QuestionAndAnswers_selected_mcq__esm12",mcq:"QuestionAndAnswers_mcq__th1Qm",draggable_container:"QuestionAndAnswers_draggable_container__rxl5V",draggable_input:"QuestionAndAnswers_draggable_input__vA3MW",draggable_wrapper:"QuestionAndAnswers_draggable_wrapper__g618z",draggable_label:"QuestionAndAnswers_draggable_label__7V9Md",selected_draggable_label:"QuestionAndAnswers_selected_draggable_label__Z5WuC",number_wrapper:"QuestionAndAnswers_number_wrapper__wYtxa",draggable_container_tablet:"QuestionAndAnswers_draggable_container_tablet__4FnSm",number:"QuestionAndAnswers_number__nXewR",number_label:"QuestionAndAnswers_number_label__g9Yhw",selected_number:"QuestionAndAnswers_selected_number__aNKCd"}},5894:(e,a,t)=>{var r=t(24640),n=t(70048);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,gray300:""+r.gray300,colorWhite:""+r.colorWhite,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":""+n["breakpoint-sm"],"breakpoint-xl":""+n["breakpoint-xl"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-md":""+n["breakpoint-md"],gauge_container:"RatingGuage_gauge_container__mXDVv",result_text:"RatingGuage_result_text__AdeSw",gauge:"RatingGuage_gauge__Co87l"}},36197:(e,a,t)=>{"use strict";t.d(a,{Z:()=>o});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\AIReadinessBody\AIReadinessBody.tsx`),{__esModule:n,$$typeof:s}=r,o=r.default},49256:(e,a,t)=>{"use strict";t.d(a,{Z:()=>n});var r=t(25036);function n({data:e}){return r.jsx(r.Fragment,{children:e?.schema&&r.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e?.schema)}})})}},35992:(e,a,t)=>{"use strict";function r(e){return{title:e?.title||"AI & ML Solutions | Software Development Partner | Maruti Techlabs",description:e?.description||"Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.",alternates:{canonical:e?.url||"https://marutitech.com/"},keywords:e?.keywords?.keyword||"AI, ML, software development, custom software solutions, automation, business growth",openGraph:{title:e?.title,description:e?.description,type:e?.type||"website",url:e?.url,locale:e?.locale||"en_US",siteName:e?.site_name||"Maruti Techlabs",images:e?.image?.data?.attributes?.url?[{url:e.image.data.attributes.url,alt:e.title||"Maruti Techlabs Logo"}]:[]},twitter:{card:"summary_large_image",title:e?.title,description:e?.description,images:e?.image?.data?.attributes?.url?[e.image.data.attributes.url]:[],creator:"@MarutiTech"},other:{"application/ld+json":JSON.stringify(function(e){let a=e?.url||"https://marutitech.com/",t=e?.title||"Maruti Techlabs",r=e?.description||"Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.";return{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":`${a}#organization`,name:"Maruti Techlabs",url:"https://marutitech.com/",sameAs:[]},{"@type":"WebSite","@id":`${a}#website`,url:"https://marutitech.com/",name:"Maruti Techlabs",publisher:{"@id":`${a}#organization`},potentialAction:{"@type":"SearchAction",target:`${a}?s={search_term_string}`,"query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":`${a}#webpage`,url:a,inLanguage:"en-US",name:t,isPartOf:{"@id":`${a}#website`},about:{"@id":`${a}#organization`},image:{"@type":"ImageObject","@id":`${a}#primaryimage`,url:e?.image?.data?.attributes?.url||"",width:631,height:417,caption:"home-hero-image"},primaryImageOfPage:{"@id":`${a}#primaryimage`},datePublished:"2019-03-19T05:53:21+00:00",dateModified:"2020-11-02T08:06:30+00:00",description:r}]}}(e))}}}t.d(a,{Z:()=>r})}};