exports.id=5236,exports.ids=[5236],exports.modules={87335:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(95344);t(3729);var o=t(60646),n=t(56506),i=t(55846),s=t(81473),l=t(8428),c=t(70849),d=t.n(c);function p({data:e,variant:r}){let t=(0,l.useRouter)();return a.jsx("section",{className:d().ctaContaine<PERSON>,children:"downloadOurBrand"!==r?(0,a.jsxs)(o.default,{className:d().ctaWrapper,children:[a.jsx(s.Z,{richTextValue:e?.ctaTitle,headingType:"h2",className:d().ctaHeading}),a.jsx("div",{className:d().ctaBtn,children:a.jsx(i.Z,{label:e?.ctaButtonText,className:d().btn,onClick:()=>{if("scrollToContactForm"===r){let e=document.getElementById("contact-us-form");e&&e.scrollIntoView({behavior:"smooth"})}else t.push(`${e?.ctaLink}`)}})})]}):(0,a.jsxs)(o.default,{className:d().ctaWrapper,children:[a.jsx(s.Z,{richTextValue:e?.ctaTitle,headingType:"h3",className:d().ctaHeading}),a.jsx(n.default,{href:e?.ctaLink,target:"_blank",typeof:"application/pdf",prefetch:!1,download:!0,className:d().downloadLinkWrapper,children:a.jsx("div",{className:d().downloadLink,children:e?.ctaButtonText})})]})})}},94531:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var a=t(95344),o=t(3729),n=t(60646),i=t(60544),s=t.n(i),l=t(81473),c=t(89410),d=t(18924),p=t(72885),h=t.n(p);function b({data:e}){let[r,t]=(0,o.useState)(0),i=(0,o.useRef)(null),[p,b]=(0,o.useState)(!1),_=(0,o.useRef)(!1),u=(0,o.useRef)(0),m=(0,o.useRef)(0),C=(0,d.Z)({query:`(max-width: ${h()["breakpoint-sm-450"]})`}),k=e?.tab[r];(0,o.useEffect)(()=>{let e=()=>{let e=i.current;e&&b(e.scrollWidth>e.clientWidth)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[e?.tab]),(0,o.useEffect)(()=>{if(!C)return;let e=i.current,a=()=>{let a=e.querySelectorAll(`.${s().tech_tabs_wrapper}`),o=-1,n=1/0;a.forEach((r,t)=>{let a=r.getBoundingClientRect(),i=e.getBoundingClientRect();a.right>i.left&&a.left<i.right&&a.left<n&&(n=a.left,o=t)}),-1!==o&&o!==r&&t(o)};return e.addEventListener("scroll",a,{passive:!0}),()=>e.removeEventListener("scroll",a)},[r,C]);let x=e=>{let a=i.current.querySelectorAll(`.${s().tech_tabs}`),o="next"===e?r+1:r-1;a[o]&&(C||t(o),a[o].scrollIntoView({behavior:"smooth",inline:"start",block:"nearest"}))};return(0,a.jsxs)(n.default,{fluid:!0,className:s().main_container,children:[a.jsx("div",{className:s().inner_container,children:a.jsx(l.Z,{headingType:"h2",title:e?.title,position:"center",className:s().ourServiceTitle})}),(0,a.jsxs)("div",{className:s().scrollWrapper,children:[p&&a.jsx("button",{className:s().scrollButton,onClick:()=>x("prev"),children:a.jsx(c.default,{src:"https://dev-cdn.marutitech.com/arrow_left_571ede1db4.svg",alt:"Scroll Left",width:40,height:40})}),a.jsx("section",{ref:i,className:s().scrollContainer,onMouseDown:e=>{_.current=!0,u.current=e.pageX-i.current.offsetLeft,m.current=i.current.scrollLeft,i.current.style.cursor="grabbing"},onMouseMove:e=>{if(!_.current)return;e.preventDefault();let r=(e.pageX-i.current.offsetLeft-u.current)*2;i.current.scrollLeft=m.current-r},onMouseUp:()=>{_.current=!1,i.current.style.cursor="grab"},onMouseLeave:()=>{_.current=!1,i.current.style.cursor="grab"},children:a.jsx("div",{className:s().scrollContent,children:e?.tab?.map((e,o)=>a.jsx("div",{className:s().tech_tabs_wrapper,children:a.jsx("div",{className:`${s().tech_tabs} ${r===o?s().activeTab:""}`,onClick:()=>t(o),id:o,role:"button",tabIndex:0,children:e?.tab_title})},e?.id))})}),p&&a.jsx("button",{className:s().scrollButton,onClick:()=>x("next"),children:a.jsx(c.default,{src:"https://dev-cdn.marutitech.com/arrow_right_39f0ac1581.svg",alt:"Scroll Right",width:40,height:40})})]}),a.jsx("div",{className:s().tabContent,children:k?.logo_url?.map(e=>a.jsxs("div",{className:s().box,children:[a.jsx("div",{className:s().iconContainer,children:a.jsx("img",{src:e?.url,alt:e?.title,className:s().iconImage})}),a.jsx("div",{className:s().iconTitle,children:e?.title})]},e?.title))},r)]})}},43590:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(95344),o=t(3729),n=t.n(o),i=t(81473),s=t(18924),l=t(48062),c=t.n(l);function d({data:e}){let r=(0,s.Z)({query:"(max-width: 585px)"}),t=(0,s.Z)({query:"(max-width: 867px)"});return a.jsx("section",{className:c().sectionWrapper,children:a.jsx("div",{className:c().whyChooseMTLWrapper,children:(0,a.jsxs)("div",{className:c().whyChooseMTLContentArea,children:[(0,a.jsxs)("div",{className:c().headingArea,children:[a.jsx(i.Z,{headingType:"h2",title:e?.title,position:"center"}),a.jsx("div",{className:c().subHeading,dangerouslySetInnerHTML:{__html:e?.subtitle}})]}),a.jsx("div",{className:c().pointsWrapper,children:e?.whyChooseMtlCards?.map((e,o)=>a.jsxs(n().Fragment,{children:[a.jsx("div",{className:c().pointCard,children:a.jsxs("div",{className:c().pointCardContent,children:[a.jsx(i.Z,{headingType:"h3",richTextValue:e?.cardTitle,className:c().pointCardTitle}),a.jsx("div",{className:c().pointCardDescription,dangerouslySetInnerHTML:{__html:e?.cardDescription}})]})}),!r&&(t?o%2==0&&a.jsx("div",{className:c().dividerLine}):2!==o&&5!==o&&a.jsx("div",{className:c().dividerLine}))]},e?.id))})]})})})}},70849:(e,r,t)=>{var a=t(24640),o=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+a.colorBlack,bodyHeadingS:""+a.bodyHeadingS,brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,colorWhite:""+a.colorWhite,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md-769":""+o["breakpoint-md-769"],"breakpoint-sm-550":""+o["breakpoint-sm-550"],"breakpoint-sm-320":""+o["breakpoint-sm-320"],"breakpoint-sm":""+o["breakpoint-sm"],ctaContainer:"Cta_ctaContainer___sfgZ",ctaWrapper:"Cta_ctaWrapper__TkOMF",ctaHeading:"Cta_ctaHeading___2l6Z",btn:"Cta_btn__Fsqyb",downloadLinkWrapper:"Cta_downloadLinkWrapper__dMcXu",downloadLink:"Cta_downloadLink__7uzCi"}},60544:(e,r,t)=>{var a=t(24640),o=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+a.colorBlack,colorWhite:""+a.colorWhite,fifteenSpace:""+a.fifteenSpace,grayBorder:""+a.grayBorder,brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,bodyTextXXXSSmall:""+a.bodyTextXXXSSmall,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":""+o["breakpoint-sm"],"breakpoint-md":""+o["breakpoint-md"],"breakpoint-xl-2000":""+o["breakpoint-xl-2000"],"breakpoint-xl-1800":""+o["breakpoint-xl-1800"],"breakpoint-sm-450":""+o["breakpoint-sm-450"],"breakpoint-sm-550":""+o["breakpoint-sm-550"],"breakpoint-sm-320":""+o["breakpoint-sm-320"],"breakpoint-xl-1024":""+o["breakpoint-xl-1024"],"breakpoint-xl-1440":""+o["breakpoint-xl-1440"],"breakpoint-lg":""+o["breakpoint-lg"],"breakpoint-lg-991px":""+o["breakpoint-lg-991px"],"breakpoint-xl-2560":""+o["breakpoint-xl-2560"],ourServiceTitle:"TechStack_ourServiceTitle__FBakW",main_container:"TechStack_main_container__o_z_3",inner_container:"TechStack_inner_container__WRXIB",scrollContainer:"TechStack_scrollContainer__GYYGG",scrollContent:"TechStack_scrollContent__gbtYq",scrollWrapper:"TechStack_scrollWrapper__Rx_hy",scrollButton:"TechStack_scrollButton__yN4QG",tech_tabs_wrapper:"TechStack_tech_tabs_wrapper__OnOkf",tech_tabs:"TechStack_tech_tabs__xJesy",activeTab:"TechStack_activeTab__hyB3m",iconContainer:"TechStack_iconContainer__7D2GX",iconImage:"TechStack_iconImage__Uo24C",iconTitle:"TechStack_iconTitle__v8lck",tabContent:"TechStack_tabContent__czr_k",box:"TechStack_box__5O_Iu"}},48062:(e,r,t)=>{var a=t(24640),o=t(70048);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,colorBlack:""+a.colorBlack,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+o["breakpoint-md"],"breakpoint-md-585":""+o["breakpoint-md-585"],"breakpoint-sm-550":""+o["breakpoint-sm-550"],"breakpoint-xl-1188":""+o["breakpoint-xl-1188"],"breakpoint-sm":""+o["breakpoint-sm"],sectionWrapper:"WhyChooseMTL_sectionWrapper__fzz3k",whyChooseMTLContentArea:"WhyChooseMTL_whyChooseMTLContentArea__pZfeM",headingArea:"WhyChooseMTL_headingArea__6ygBn",subHeading:"WhyChooseMTL_subHeading__IIQKc",pointsWrapper:"WhyChooseMTL_pointsWrapper__p3gVK",pointCard:"WhyChooseMTL_pointCard__cKnPL",dividerLine:"WhyChooseMTL_dividerLine__hu3Pc",cardBackground:"WhyChooseMTL_cardBackground__wP8DQ",pointCardContent:"WhyChooseMTL_pointCardContent__1uqAo",pointCardTitle:"WhyChooseMTL_pointCardTitle__EifLs",pointCardDescription:"WhyChooseMTL_pointCardDescription__m3wyE"}}};