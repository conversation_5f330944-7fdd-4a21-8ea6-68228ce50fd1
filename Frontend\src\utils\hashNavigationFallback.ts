/**
 * Fallback utility for hash navigation in static sites
 * This provides additional methods to handle edge cases where the main hook might fail
 */

/**
 * Force scroll to element by hash with aggressive retry logic
 * @param hash - The hash to scroll to (without #)
 * @param maxRetries - Maximum number of retries
 */
export function forceScrollToHash(hash: string, maxRetries: number = 10): Promise<boolean> {
  return new Promise((resolve) => {
    let retryCount = 0;
    
    const attemptScroll = () => {
      if (typeof window === 'undefined') {
        resolve(false);
        return;
      }

      const element = document.getElementById(hash);
      
      if (element) {
        // Check if element is visible
        const rect = element.getBoundingClientRect();
        if (rect.height > 0 && rect.width > 0) {
          element.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
          resolve(true);
          return;
        }
      }
      
      retryCount++;
      if (retryCount < maxRetries) {
        // Use exponential backoff with jitter
        const delay = Math.min(1000, 100 * Math.pow(2, retryCount)) + Math.random() * 100;
        setTimeout(attemptScroll, delay);
      } else {
        // Final attempt with alternative selectors
        const fallbackElement = 
          document.querySelector(`[id="${hash}"]`) ||
          document.querySelector(`[data-id="${hash}"]`) ||
          document.querySelector(`[name="${hash}"]`) ||
          document.querySelector(`[id*="${hash}"]`);
          
        if (fallbackElement) {
          (fallbackElement as HTMLElement).scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
          resolve(true);
        } else {
          resolve(false);
        }
      }
    };
    
    attemptScroll();
  });
}

/**
 * Initialize hash navigation with comprehensive fallback strategies
 * This can be called from pages that need extra reliability
 */
export function initializeHashNavigation(): void {
  if (typeof window === 'undefined') return;

  const handleHashNavigation = async () => {
    const hash = window.location.hash.substring(1);
    if (!hash) return;

    // Try the standard approach first
    const element = document.getElementById(hash);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
      return;
    }

    // If that fails, use the aggressive retry approach
    const success = await forceScrollToHash(hash);
    
    if (!success) {
      console.warn(`Hash navigation failed for: #${hash}`);
      
      // Last resort: try to find any element with similar text content
      const allElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, [id], [data-id]');
      const matchingElement = Array.from(allElements).find(el => {
        const text = el.textContent?.toLowerCase().replace(/\s+/g, '-');
        const id = el.id?.toLowerCase();
        const dataId = el.getAttribute('data-id')?.toLowerCase();
        
        return text?.includes(hash.toLowerCase()) || 
               id?.includes(hash.toLowerCase()) ||
               dataId?.includes(hash.toLowerCase());
      });
      
      if (matchingElement) {
        (matchingElement as HTMLElement).scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };

  // Multiple initialization strategies
  const strategies = [
    () => handleHashNavigation(), // Immediate
    () => setTimeout(handleHashNavigation, 100), // Quick delay
    () => setTimeout(handleHashNavigation, 500), // Medium delay
    () => setTimeout(handleHashNavigation, 1000), // Long delay
  ];

  // Execute all strategies
  strategies.forEach(strategy => strategy());

  // Also listen for hash changes
  window.addEventListener('hashchange', handleHashNavigation);
  
  // Listen for page load events
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handleHashNavigation);
  }
  
  window.addEventListener('load', handleHashNavigation);
}

/**
 * Manual hash navigation function that can be called programmatically
 * @param sectionId - The ID of the section to navigate to
 */
export async function navigateToSection(sectionId: string): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  
  // Update the hash
  window.location.hash = '#' + sectionId;
  
  // Force scroll to the element
  return await forceScrollToHash(sectionId);
}
