"use strict";exports.id=2713,exports.ids=[2713],exports.modules={98453:(e,t,o)=>{o.d(t,{Z:()=>M});var n,r=o(34132),l=o.n(r),a=o(92801),i=o(42714),s=o(19751),d=o(83209);function u(e){if((!n&&0!==n||e)&&i.default){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),n=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return n}var c=o(3729),f=o(68342),p=o(83524),m=o(42887),g=o(26400),y=o(96857),v=o(67378),b=o(70136),N=o(95344);let h=c.forwardRef(({className:e,bsPrefix:t,as:o="div",...n},r)=>(t=(0,b.vE)(t,"modal-body"),(0,N.jsx)(o,{ref:r,className:l()(e,t),...n})));h.displayName="ModalBody";var w=o(51562);let x=c.forwardRef(({bsPrefix:e,className:t,contentClassName:o,centered:n,size:r,fullscreen:a,children:i,scrollable:s,...d},u)=>{e=(0,b.vE)(e,"modal");let c=`${e}-dialog`,f="string"==typeof a?`${e}-fullscreen-${a}`:`${e}-fullscreen`;return(0,N.jsx)("div",{...d,ref:u,className:l()(c,t,r&&`${e}-${r}`,n&&`${c}-centered`,s&&`${c}-scrollable`,a&&f),children:(0,N.jsx)("div",{className:l()(`${e}-content`,o),children:i})})});x.displayName="ModalDialog";let E=x,O=c.forwardRef(({className:e,bsPrefix:t,as:o="div",...n},r)=>(t=(0,b.vE)(t,"modal-footer"),(0,N.jsx)(o,{ref:r,className:l()(e,t),...n})));O.displayName="ModalFooter";var D=o(80620);let R=c.forwardRef(({bsPrefix:e,className:t,closeLabel:o="Close",closeButton:n=!1,...r},a)=>(e=(0,b.vE)(e,"modal-header"),(0,N.jsx)(D.Z,{ref:a,...r,className:l()(t,e),closeLabel:o,closeButton:n})));R.displayName="ModalHeader";let Z=(0,o(80232).Z)("h4"),j=c.forwardRef(({className:e,bsPrefix:t,as:o=Z,...n},r)=>(t=(0,b.vE)(t,"modal-title"),(0,N.jsx)(o,{ref:r,className:l()(e,t),...n})));function I(e){return(0,N.jsx)(v.Z,{...e,timeout:null})}function S(e){return(0,N.jsx)(v.Z,{...e,timeout:null})}j.displayName="ModalTitle";let T=c.forwardRef(({bsPrefix:e,className:t,style:o,dialogClassName:n,contentClassName:r,children:v,dialogAs:h=E,"data-bs-theme":x,"aria-labelledby":O,"aria-describedby":D,"aria-label":R,show:Z=!1,animation:j=!0,backdrop:T=!0,keyboard:M=!0,onEscapeKeyDown:$,onShow:k,onHide:B,container:F,autoFocus:C=!0,enforceFocus:A=!0,restoreFocus:H=!0,restoreFocusOptions:U,onEntered:P,onExit:z,onExiting:L,onEnter:W,onEntering:_,onExited:K,backdropClassName:q,manager:G,...J},Q)=>{let[V,X]=(0,c.useState)({}),[Y,ee]=(0,c.useState)(!1),et=(0,c.useRef)(!1),eo=(0,c.useRef)(!1),en=(0,c.useRef)(null),[er,el]=(0,c.useState)(null),ea=(0,p.Z)(Q,el),ei=(0,f.Z)(B),es=(0,b.SC)();e=(0,b.vE)(e,"modal");let ed=(0,c.useMemo)(()=>({onHide:ei}),[ei]);function eu(){return G||(0,y.t)({isRTL:es})}function ec(e){if(!i.default)return;let t=eu().getScrollbarWidth()>0,o=e.scrollHeight>(0,s.default)(e).documentElement.clientHeight;X({paddingRight:t&&!o?u():void 0,paddingLeft:!t&&o?u():void 0})}let ef=(0,f.Z)(()=>{er&&ec(er.dialog)});!function(e){let t=function(e){let t=(0,c.useRef)(e);return t.current=e,t}(e);(0,c.useEffect)(()=>()=>t.current(),[])}(()=>{(0,d.Z)(window,"resize",ef),null==en.current||en.current()});let ep=()=>{et.current=!0},em=e=>{et.current&&er&&e.target===er.dialog&&(eo.current=!0),et.current=!1},eg=()=>{ee(!0),en.current=(0,m.Z)(er.dialog,()=>{ee(!1)})},ey=e=>{e.target===e.currentTarget&&eg()},ev=e=>{if("static"===T){ey(e);return}if(eo.current||e.target!==e.currentTarget){eo.current=!1;return}null==B||B()},eb=(0,c.useCallback)(t=>(0,N.jsx)("div",{...t,className:l()(`${e}-backdrop`,q,!j&&"show")}),[j,q,e]),eN={...o,...V};return eN.display="block",(0,N.jsx)(w.Z.Provider,{value:ed,children:(0,N.jsx)(g.Z,{show:Z,ref:ea,backdrop:T,container:F,keyboard:!0,autoFocus:C,enforceFocus:A,restoreFocus:H,restoreFocusOptions:U,onEscapeKeyDown:e=>{M?null==$||$(e):(e.preventDefault(),"static"===T&&eg())},onShow:k,onHide:B,onEnter:(e,t)=>{e&&ec(e),null==W||W(e,t)},onEntering:(e,t)=>{null==_||_(e,t),(0,a.ZP)(window,"resize",ef)},onEntered:P,onExit:e=>{null==en.current||en.current(),null==z||z(e)},onExiting:L,onExited:e=>{e&&(e.style.display=""),null==K||K(e),(0,d.Z)(window,"resize",ef)},manager:eu(),transition:j?I:void 0,backdropTransition:j?S:void 0,renderBackdrop:eb,renderDialog:o=>(0,N.jsx)("div",{role:"dialog",...o,style:eN,className:l()(t,e,Y&&`${e}-static`,!j&&"show"),onClick:T?ev:void 0,onMouseUp:em,"data-bs-theme":x,"aria-label":R,"aria-labelledby":O,"aria-describedby":D,children:(0,N.jsx)(h,{...J,onMouseDown:ep,className:n,contentClassName:r,children:v})})})})});T.displayName="Modal";let M=Object.assign(T,{Body:h,Header:R,Title:j,Footer:O,Dialog:E,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},70117:(e,t,o)=>{o.d(t,{Z:()=>r});let n={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function r(e={}){let t,o,l,a,i;let s=!1,d=!0,u=0;function c(){if(l||s||!d)return;o.emit("autoScroll:play");let e=o.internalEngine(),{ownerWindow:n}=e;u=n.setTimeout(()=>{e.scrollBody=function(e){let{location:n,target:r,scrollTarget:l,index:a,indexPrevious:i,limit:{reachedMin:s,reachedMax:d,constrain:u},options:{loop:c}}=e,p="forward"===t.direction?-1:1,m=()=>h,g=0,y=0,v=n.get(),b=0,N=!1,h={direction:()=>y,duration:()=>-1,velocity:()=>g,settled:()=>N,seek:function(){g=p*t.speed,v+=g,n.add(g),r.set(n),y=Math.sign(v-b),b=v;let e=l.byDistance(0,!1).index;a.get()!==e&&(i.set(a.get()),a.set(e),o.emit("select"));let m="forward"===t.direction?s(n.get()):d(n.get());if(!c&&m){N=!0;let e=u(n.get());n.set(e),r.set(n),f()}return h},useBaseFriction:m,useBaseDuration:m,useFriction:m,useDuration:m};return h}(e),e.animation.start()},a),s=!0}function f(){if(l||!s)return;o.emit("autoScroll:stop");let e=o.internalEngine(),{ownerWindow:t}=e;e.scrollBody=i,t.clearTimeout(u),u=0,s=!1}function p(){d&&c(),o.off("settle",p)}function m(){o.on("settle",p)}return{name:"autoScroll",options:e,init:function(s,u){o=s;let{mergeOptions:p,optionsAtMedia:g}=u,y=p(n,r.globalOptions);if(t=g(p(y,e)),o.scrollSnapList().length<=1)return;a=t.startDelay,l=!1,i=o.internalEngine().scrollBody;let{eventStore:v}=o.internalEngine(),b=o.rootNode(),N=t.rootNode&&t.rootNode(b)||b,h=o.containerNode();o.on("pointerDown",f),t.stopOnInteraction||o.on("pointerUp",m),t.stopOnMouseEnter&&(v.add(N,"mouseenter",()=>{d=!1,f()}),t.stopOnInteraction||v.add(N,"mouseleave",()=>{d=!0,c()})),t.stopOnFocusIn&&(v.add(h,"focusin",()=>{f(),o.scrollTo(o.selectedScrollSnap(),!0)}),t.stopOnInteraction||v.add(h,"focusout",c)),t.playOnInit&&c()},destroy:function(){o.off("pointerDown",f).off("pointerUp",m).off("settle",p),f(),l=!0,s=!1},play:function(e){void 0!==e&&(a=e),d=!0,c()},stop:function(){s&&f()},reset:function(){s&&(f(),m())},isPlaying:function(){return s}}}r.globalOptions=void 0}};