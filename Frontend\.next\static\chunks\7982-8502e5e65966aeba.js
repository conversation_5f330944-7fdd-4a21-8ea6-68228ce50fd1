"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7982],{64561:function(e,t,n){n.d(t,{Z:function(){return M}});var o,r=n(16480),l=n.n(r),a=n(59390),i=n(97550),s=n(69275),c=n(44990);function d(e){if((!o&&0!==o||e)&&i.Z){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),o=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return o}var u=n(43756),f=n(45832),p=n(1564),m=n(40343),g=n(82562),y=n(2265),v=n(70133),b=n(46579),h=n(83534),N=n(12865),w=n(57437);let E=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r="div",...a}=e;return o=(0,N.vE)(o,"modal-body"),(0,w.jsx)(r,{ref:t,className:l()(n,o),...a})});E.displayName="ModalBody";var x=n(14272);let Z=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,contentClassName:r,centered:a,size:i,fullscreen:s,children:c,scrollable:d,...u}=e;n=(0,N.vE)(n,"modal");let f="".concat(n,"-dialog"),p="string"==typeof s?"".concat(n,"-fullscreen-").concat(s):"".concat(n,"-fullscreen");return(0,w.jsx)("div",{...u,ref:t,className:l()(f,o,i&&"".concat(n,"-").concat(i),a&&"".concat(f,"-centered"),d&&"".concat(f,"-scrollable"),s&&p),children:(0,w.jsx)("div",{className:l()("".concat(n,"-content"),r),children:c})})});Z.displayName="ModalDialog";let O=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r="div",...a}=e;return o=(0,N.vE)(o,"modal-footer"),(0,w.jsx)(r,{ref:t,className:l()(n,o),...a})});O.displayName="ModalFooter";var D=n(94241);let R=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,closeLabel:r="Close",closeButton:a=!1,...i}=e;return n=(0,N.vE)(n,"modal-header"),(0,w.jsx)(D.Z,{ref:t,...i,className:l()(o,n),closeLabel:r,closeButton:a})});R.displayName="ModalHeader";let j=(0,n(89764).Z)("h4"),k=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r=j,...a}=e;return o=(0,N.vE)(o,"modal-title"),(0,w.jsx)(r,{ref:t,className:l()(n,o),...a})});function I(e){return(0,w.jsx)(h.Z,{...e,timeout:null})}function T(e){return(0,w.jsx)(h.Z,{...e,timeout:null})}k.displayName="ModalTitle";let S=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,style:r,dialogClassName:h,contentClassName:E,children:O,dialogAs:D=Z,"data-bs-theme":R,"aria-labelledby":j,"aria-describedby":k,"aria-label":S,show:M=!1,animation:B=!0,backdrop:C=!0,keyboard:F=!0,onEscapeKeyDown:_,onShow:A,onHide:H,container:U,autoFocus:P=!0,enforceFocus:z=!0,restoreFocus:L=!0,restoreFocusOptions:W,onEntered:K,onExit:q,onExiting:G,onEnter:J,onEntering:Q,onExited:V,backdropClassName:X,manager:Y,...$}=e,[ee,et]=(0,y.useState)({}),[en,eo]=(0,y.useState)(!1),er=(0,y.useRef)(!1),el=(0,y.useRef)(!1),ea=(0,y.useRef)(null),[ei,es]=(0,u.Z)(),ec=(0,p.Z)(t,es),ed=(0,f.Z)(H),eu=(0,N.SC)();n=(0,N.vE)(n,"modal");let ef=(0,y.useMemo)(()=>({onHide:ed}),[ed]);function ep(){return Y||(0,b.t)({isRTL:eu})}function em(e){if(!i.Z)return;let t=ep().getScrollbarWidth()>0,n=e.scrollHeight>(0,s.Z)(e).documentElement.clientHeight;et({paddingRight:t&&!n?d():void 0,paddingLeft:!t&&n?d():void 0})}let eg=(0,f.Z)(()=>{ei&&em(ei.dialog)});(0,m.Z)(()=>{(0,c.Z)(window,"resize",eg),null==ea.current||ea.current()});let ey=()=>{er.current=!0},ev=e=>{er.current&&ei&&e.target===ei.dialog&&(el.current=!0),er.current=!1},eb=()=>{eo(!0),ea.current=(0,g.Z)(ei.dialog,()=>{eo(!1)})},eh=e=>{e.target===e.currentTarget&&eb()},eN=e=>{if("static"===C){eh(e);return}if(el.current||e.target!==e.currentTarget){el.current=!1;return}null==H||H()},ew=(0,y.useCallback)(e=>(0,w.jsx)("div",{...e,className:l()("".concat(n,"-backdrop"),X,!B&&"show")}),[B,X,n]),eE={...r,...ee};return eE.display="block",(0,w.jsx)(x.Z.Provider,{value:ef,children:(0,w.jsx)(v.Z,{show:M,ref:ec,backdrop:C,container:U,keyboard:!0,autoFocus:P,enforceFocus:z,restoreFocus:L,restoreFocusOptions:W,onEscapeKeyDown:e=>{F?null==_||_(e):(e.preventDefault(),"static"===C&&eb())},onShow:A,onHide:H,onEnter:(e,t)=>{e&&em(e),null==J||J(e,t)},onEntering:(e,t)=>{null==Q||Q(e,t),(0,a.ZP)(window,"resize",eg)},onEntered:K,onExit:e=>{null==ea.current||ea.current(),null==q||q(e)},onExiting:G,onExited:e=>{e&&(e.style.display=""),null==V||V(e),(0,c.Z)(window,"resize",eg)},manager:ep(),transition:B?I:void 0,backdropTransition:B?T:void 0,renderBackdrop:ew,renderDialog:e=>(0,w.jsx)("div",{role:"dialog",...e,style:eE,className:l()(o,n,en&&"".concat(n,"-static"),!B&&"show"),onClick:C?eN:void 0,onMouseUp:ev,"data-bs-theme":R,"aria-label":S,"aria-labelledby":j,"aria-describedby":k,children:(0,w.jsx)(D,{...$,onMouseDown:ey,className:h,contentClassName:E,children:O})})})})});S.displayName="Modal";var M=Object.assign(S,{Body:E,Header:R,Title:k,Footer:O,Dialog:Z,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},22543:function(e,t,n){n.d(t,{Z:function(){return r}});let o={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function r(){let e,t,n,l,a,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=!1,c=!0,d=0;function u(){if(n||s||!c)return;t.emit("autoScroll:play");let o=t.internalEngine(),{ownerWindow:r}=o;d=r.setTimeout(()=>{o.scrollBody=function(n){let{location:o,target:r,scrollTarget:l,index:a,indexPrevious:i,limit:{reachedMin:s,reachedMax:c,constrain:d},options:{loop:u}}=n,p="forward"===e.direction?-1:1,m=()=>N,g=0,y=0,v=o.get(),b=0,h=!1,N={direction:()=>y,duration:()=>-1,velocity:()=>g,settled:()=>h,seek:function(){g=p*e.speed,v+=g,o.add(g),r.set(o),y=Math.sign(v-b),b=v;let n=l.byDistance(0,!1).index;a.get()!==n&&(i.set(a.get()),a.set(n),t.emit("select"));let m="forward"===e.direction?s(o.get()):c(o.get());if(!u&&m){h=!0;let e=d(o.get());o.set(e),r.set(o),f()}return N},useBaseFriction:m,useBaseDuration:m,useFriction:m,useDuration:m};return N}(o),o.animation.start()},l),s=!0}function f(){if(n||!s)return;t.emit("autoScroll:stop");let e=t.internalEngine(),{ownerWindow:o}=e;e.scrollBody=a,o.clearTimeout(d),d=0,s=!1}function p(){c&&u(),t.off("settle",p)}function m(){t.on("settle",p)}return{name:"autoScroll",options:i,init:function(s,d){t=s;let{mergeOptions:p,optionsAtMedia:g}=d,y=p(o,r.globalOptions);if(e=g(p(y,i)),t.scrollSnapList().length<=1)return;l=e.startDelay,n=!1,a=t.internalEngine().scrollBody;let{eventStore:v}=t.internalEngine(),b=t.rootNode(),h=e.rootNode&&e.rootNode(b)||b,N=t.containerNode();t.on("pointerDown",f),e.stopOnInteraction||t.on("pointerUp",m),e.stopOnMouseEnter&&(v.add(h,"mouseenter",()=>{c=!1,f()}),e.stopOnInteraction||v.add(h,"mouseleave",()=>{c=!0,u()})),e.stopOnFocusIn&&(v.add(N,"focusin",()=>{f(),t.scrollTo(t.selectedScrollSnap(),!0)}),e.stopOnInteraction||v.add(N,"focusout",u)),e.playOnInit&&u()},destroy:function(){t.off("pointerDown",f).off("pointerUp",m).off("settle",p),f(),n=!0,s=!1},play:function(e){void 0!==e&&(l=e),c=!0,u()},stop:function(){s&&f()},reset:function(){s&&(f(),m())},isPlaying:function(){return s}}}r.globalOptions=void 0}}]);