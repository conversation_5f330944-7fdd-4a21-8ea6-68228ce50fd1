"use strict";exports.id=2522,exports.ids=[2522],exports.modules={2522:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(3729);function u(e){return"[object Object]"===Object.prototype.toString.call(e)||Array.isArray(e)}function c(e,t){let r=Object.keys(e),n=Object.keys(t);return r.length===n.length&&JSON.stringify(Object.keys(e.breakpoints||{}))===JSON.stringify(Object.keys(t.breakpoints||{}))&&r.every(r=>{let n=e[r],s=t[r];return"function"==typeof n?`${n}`==`${s}`:u(n)&&u(s)?c(n,s):n===s})}function s(e){return e.concat().sort((e,t)=>e.name>t.name?1:-1).map(e=>e.options)}function o(e={},t=[]){let r=(0,n.useRef)(e),u=(0,n.useRef)(t),[o,i]=(0,n.useState)(),[f,a]=(0,n.useState)(),l=(0,n.useCallback)(()=>{o&&o.reInit(r.current,u.current)},[o]);return(0,n.useEffect)(()=>{i(void 0)},[f,i]),(0,n.useEffect)(()=>{c(r.current,e)||(r.current=e,l())},[e,l]),(0,n.useEffect)(()=>{!function(e,t){if(e.length!==t.length)return!1;let r=s(e),n=s(t);return r.every((e,t)=>c(e,n[t]))}(u.current,t)&&(u.current=t,l())},[t,l]),[a,o]}o.globalOptions=void 0}};