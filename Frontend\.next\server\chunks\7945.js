exports.id=7945,exports.ids=[7945],exports.modules={58404:(e,o,s)=>{"use strict";s.d(o,{H:()=>r,Y:()=>l});var t=s(95344),i=s(3729);let l=e=>{let[o,s]=(0,i.useState)(0),[t,l]=(0,i.useState)([]),r=(0,i.useCallback)(o=>{e&&e.scrollTo(o)},[e]),_=(0,i.useCallback)(e=>{l(e.scrollSnapList())},[]),n=(0,i.useCallback)(e=>{s(e.selectedScrollSnap())},[]);return(0,i.useEffect)(()=>{e&&(_(e),n(e),e.on("reInit",_).on("reInit",n).on("select",n))},[e,_,n]),{selectedIndex:o,scrollSnaps:t,onDotButtonClick:r}},r=e=>{let{children:o,...s}=e;return t.jsx("button",{type:"button",...s,"aria-label":"carousel-button",children:o})}},59086:(e,o,s)=>{"use strict";s.r(o),s.d(o,{default:()=>j});var t=s(95344),i=s(3729),l=s.n(i),r=s(60646),_=s(89410),n=s(31905),a=s(36067),c=s(2522),m=s(76846),d=s(18924),b=s(72885),u=s.n(b),h=s(58404),p=s(74354),H=s.n(p),x=s(56506),v=s(81473),g=s(22281),f=s(25609);function j(e){let{slides:o,varaint:s,resourcesSlide:b}=e,p=o?.data?.attributes?.hero_section,[j,S]=(0,i.useState)(!1),[N,k]=(0,c.Z)({loop:!0},[(0,a.Z)(),(0,n.Z)({delay:6e3,stopOnInteraction:!0})]);(0,i.useEffect)(()=>{k&&S(!0)},[k]);let{selectedIndex:y,scrollSnaps:$,onDotButtonClick:E}=(0,h.Y)(k),w=(0,d.Z)({query:`(max-width: ${u()["breakpoint-sm"]})`}),C="primary"===s?p[0]?.image?.data?.attributes?.formats?.thumbnail?.url:"";return(0,t.jsxs)(t.Fragment,{children:["primary"===s&&!j&&(0,t.jsxs)("div",{className:H().embla_placeholder,children:[t.jsx(_.default,{src:C,alt:"Blur Placeholder",fill:!0,priority:!0,loading:"eager",className:(0,g.Z)(H().embla__slide_image,"blur")}),t.jsx("div",{className:H().inner_container_blur,children:(0,t.jsxs)("div",{className:H().section_items,children:[(0,t.jsxs)("div",{className:`${H().section_top}`,children:[t.jsx(v.Z,{title:p[0]?.title_description?.title,headingType:"h1",className:H().carousel_title}),t.jsx("div",{className:H().homepage__desc,dangerouslySetInnerHTML:{__html:p[0]?.title_description?.description}}),t.jsx(x.default,{href:`${p[0]?.link_url}`,children:t.jsx(m.Z,{variant:"medium"})})]}),t.jsx("div",{className:H().bottom_section_controls})]})})]}),"primary"===s&&t.jsx("section",{className:H().embla,style:{display:j?"block":"none"},children:t.jsx("div",{className:H().embla__viewport,ref:N,children:t.jsx("div",{className:H().embla__container,children:k&&p?.map((e,o)=>t.jsx(l().Fragment,{children:w?t.jsxs("div",{className:H().embla__slide_mobile,children:[t.jsx(f.Z,{src:e?.image?.data?.attributes,width:576,height:245,loading:"eager",className:H().thumbnail_image}),t.jsxs("div",{className:H().main_section_mobile,children:[t.jsxs("div",{className:H().section_items_mobile,children:[t.jsxs("div",{className:H().section_top_mobile,children:[t.jsx(v.Z,{title:e?.title_description?.title,headingType:"h1",className:H().carousel_title_mobile}),t.jsx("div",{className:H().homepage__desc_mobile,dangerouslySetInnerHTML:{__html:e?.title_description?.description}})]}),e?.open_link_in_new_tab===!0?t.jsx(x.default,{href:`${e?.link_url}`,className:H().circular_button_mobile,target:"_blank",children:t.jsx(m.Z,{variant:"medium"})}):t.jsx(x.default,{href:`${e?.link_url}`,className:H().circular_button_mobile,children:t.jsx(m.Z,{variant:"medium"})})]}),t.jsxs("div",{className:H().bottom_section_controls_mobile,children:[t.jsxs("div",{className:H().service_title,children:[e?.banner_name,e?.service_name&&` / ${e?.service_name}`]}),t.jsx("div",{className:H().embla__controls,children:t.jsx("div",{className:H().embla__dots,children:$.map((e,o)=>t.jsx(h.H,{onClick:()=>E(o),className:o===y?`${H().embla__dot_mobile} ${H().embla__dot_selected_mobile}`:H().embla__dot_mobile},o))})})]})]})]},o):t.jsxs(r.default,{fluid:!0,className:H().embla__slide,children:[t.jsx(f.Z,{src:e?.image?.data?.attributes,fill:!0,loading:"eager",className:H().embla__slide_image}),t.jsxs("div",{className:H().inner_container,children:[t.jsx("div",{className:H().section_items,children:t.jsxs("div",{className:`${H().section_top} ${o===y?H().section_top_active:""}`,children:[t.jsx(v.Z,{title:e?.title_description?.title,headingType:"h1",className:H().carousel_title}),t.jsx("div",{className:H().homepage__desc,dangerouslySetInnerHTML:{__html:e?.title_description?.description}}),e?.open_link_in_new_tab===!0?t.jsx(x.default,{href:`${e?.link_url}`,className:H().circular_button,target:"_blank",children:t.jsx(m.Z,{variant:"medium"})}):t.jsx(x.default,{href:`${e?.link_url}`,className:H().circular_button,children:t.jsx(m.Z,{variant:"medium"})})]})}),t.jsxs("div",{className:H().bottom_section_controls,children:[t.jsxs("div",{className:H().service_title,children:[e?.banner_name,e?.service_name&&` / ${e?.service_name}`]}),t.jsx("div",{className:H().embla__controls,children:t.jsx("div",{className:H().embla__dots,children:$.map((e,o)=>t.jsx(h.H,{onClick:()=>E(o),className:o===y?`${H().embla__dot} ${H().embla__dot_selected}`:H().embla__dot},o))})})]})]})]},o)},o))})})}),"resources"===s&&t.jsx("section",{className:H().embla_resources,children:t.jsx("div",{className:H().embla__viewport_resources,ref:N,children:t.jsx("div",{className:H().embla__container,children:k&&b?.map((e,o)=>t.jsx(l().Fragment,{children:w?t.jsxs("div",{className:H().embla__slide_mobile,children:[e?.image?.data?.attributes?.url&&t.jsx(_.default,{src:e?.image?.data?.attributes?.url,alt:"Image not found",width:576,height:245}),t.jsxs("div",{className:H().main_section_mobile_resources,children:[t.jsx(x.default,{href:e?.link,className:H().carousel_resources_link,children:t.jsxs("div",{className:H().section_top_mobile,children:[t.jsx(v.Z,{title:e?.title,headingType:"h1",className:H().carousel_title_resources_mobile}),t.jsx("div",{className:H().homepage__desc_mobile_resources,dangerouslySetInnerHTML:{__html:e?.description}})]})}),t.jsxs("div",{className:H().bottom_section_controls_mobile,children:[t.jsxs("div",{className:H().service_title_resources_mobile,children:[e?.banner_name,e?.service_name&&` / ${e?.service_name}`]}),t.jsx("div",{className:H().embla__controls,children:t.jsx("div",{className:H().embla__dots,children:$.map((e,o)=>t.jsx(h.H,{onClick:()=>E(o),className:o===y?`${H().embla__dot_mobile} ${H().embla__dot_selected_mobile}`:H().embla__dot_mobile},o))})})]})]})]},o):t.jsx(r.default,{fluid:!0,className:H().embla__slide_resources,children:t.jsxs("div",{className:H().inner_container_resources,children:[t.jsx("div",{className:H().section_items,children:t.jsx("div",{className:`${H().section_top_resources} ${o===y?`${H()?.section_top_active}`:""}`,children:t.jsxs(x.default,{href:e?.link,className:H().carousel_resources_link,children:[t.jsx(v.Z,{title:e?.title,headingType:"h1",className:H().carousel_title_resources}),t.jsx("div",{className:H().homepage__desc_resources,dangerouslySetInnerHTML:{__html:e?.description}})]})})}),t.jsxs("div",{className:H().bottom_section_controls,children:[t.jsxs("div",{className:H().service_title_resources,children:[e?.banner_name,e?.service_name&&` / ${e?.service_name}`]}),t.jsx("div",{className:H().embla__controls,children:t.jsx("div",{className:H().embla__dots,children:$.map((e,o)=>t.jsx(h.H,{onClick:()=>E(o),className:o===y?`${H().embla__dot} ${H().embla__dot_selected}`:H().embla__dot},o))})})]})]})},o)},o))})})})]})}},74354:(e,o,s)=>{var t=s(24640),i=s(70048);e.exports={variables:'"@styles/variables.module.css"',gray900:""+t.gray900,colorBlack:""+t.colorBlack,colorWhite:""+t.colorWhite,brandColorOne:""+t.brandColorOne,brandColorTwo:""+t.brandColorTwo,brandColorThree:""+t.brandColorThree,brandColorFour:""+t.brandColorFour,brandColorFive:""+t.brandColorFive,gray300:""+t.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+i["breakpoint-md"],"breakpoint-lg":""+i["breakpoint-lg"],"breakpoint-xl-1024":""+i["breakpoint-xl-1024"],"breakpoint-xl":""+i["breakpoint-xl"],"breakpoint-xl-2000":""+i["breakpoint-xl-2000"],embla:"HeroSectionHome_embla__l6kfO",embla_placeholder:"HeroSectionHome_embla_placeholder__nhagY",inner_container_blur:"HeroSectionHome_inner_container_blur__SmRcG",section_top:"HeroSectionHome_section_top__m8h2B",embla_resources:"HeroSectionHome_embla_resources__SBewv",embla__viewport:"HeroSectionHome_embla__viewport__3JWMU",embla__viewport_resources:"HeroSectionHome_embla__viewport_resources__RHjDR",embla__container:"HeroSectionHome_embla__container__pEcbh",embla__slide_image:"HeroSectionHome_embla__slide_image__VBtpD",thumbnail_image:"HeroSectionHome_thumbnail_image__QXMXr",image_hidden:"HeroSectionHome_image_hidden__UZam_",image_visible:"HeroSectionHome_image_visible__gWco_",embla__slide:"HeroSectionHome_embla__slide__sf5KM",embla__slide_resources:"HeroSectionHome_embla__slide_resources__QT3E_",embla__slide_mobile:"HeroSectionHome_embla__slide_mobile__uk_05",main_section_mobile:"HeroSectionHome_main_section_mobile__OlXI_",main_section_mobile_resources:"HeroSectionHome_main_section_mobile_resources__fB8Hh",inner_container:"HeroSectionHome_inner_container__bRxOK",inner_container_resources:"HeroSectionHome_inner_container_resources__roCRZ",section_top_resources:"HeroSectionHome_section_top_resources__tQbjv",carousel_title_resources:"HeroSectionHome_carousel_title_resources__VWh_H",section_top_active:"HeroSectionHome_section_top_active__Vd3Nk",fadeIn:"HeroSectionHome_fadeIn__c0D1F",section_top_mobile:"HeroSectionHome_section_top_mobile__HD3Eq",carousel_title:"HeroSectionHome_carousel_title__5VPr5",carousel_resources_link:"HeroSectionHome_carousel_resources_link__6zfgA",carousel_title_resources_mobile:"HeroSectionHome_carousel_title_resources_mobile__HEBvp",carousel_title_mobile:"HeroSectionHome_carousel_title_mobile__Cxwyr",homepage__desc:"HeroSectionHome_homepage__desc__9OZ53",homepage__desc_resources:"HeroSectionHome_homepage__desc_resources___JxkP",homepage__desc_mobile:"HeroSectionHome_homepage__desc_mobile__yaCeE",homepage__desc_mobile_resources:"HeroSectionHome_homepage__desc_mobile_resources__sBQDQ",circular_button_mobile:"HeroSectionHome_circular_button_mobile__ea7Qk",bottom_section_controls:"HeroSectionHome_bottom_section_controls__r3MKX",bottom_section_controls_mobile:"HeroSectionHome_bottom_section_controls_mobile__XbLAz",service_title:"HeroSectionHome_service_title__uJrfM",service_title_resources:"HeroSectionHome_service_title_resources__cGyrz",service_title_resources_mobile:"HeroSectionHome_service_title_resources_mobile__xcxBb",embla__controls:"HeroSectionHome_embla__controls__KD67k",embla__dots:"HeroSectionHome_embla__dots__zyC_q",embla__dot:"HeroSectionHome_embla__dot__xLzQL",embla__dot_mobile:"HeroSectionHome_embla__dot_mobile__trAP4",embla__dot_selected:"HeroSectionHome_embla__dot_selected__sW7eq",embla__dot_selected_mobile:"HeroSectionHome_embla__dot_selected_mobile__xqc4R",progress:"HeroSectionHome_progress__DqsmP",circular_button:"HeroSectionHome_circular_button__Udms8"}},54912:(e,o,s)=>{"use strict";s.d(o,{Z:()=>r});let t=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\HomeHeroSection\HeroSectionHome.tsx`),{__esModule:i,$$typeof:l}=t,r=t.default},36067:(e,o,s)=>{"use strict";function t(e,o,s){return Math.min(Math.max(e,o),s)}function i(e){return"number"==typeof e&&!isNaN(e)}function l(e={}){let o,s,l,r;let _=[],n=0,a=0,c=0,m=!1;function d(){p(o.selectedScrollSnap(),1)}function b(){m=!1}function u(){m=!1,n=0,a=0}function h(){let e=o.internalEngine().scrollBody.duration();a=e?0:1,m=!0,e||d()}function p(e,s){o.scrollSnapList().forEach((i,l)=>{let r=Math.abs(s),a=_[l],d=l===e,b=t(d?a+r:a-r,0,1);_[l]=b;let u=d&&m,h=o.previousScrollSnap();u&&(_[h]=1-b),d&&function(e,s){let{index:t,dragHandler:i,scrollSnaps:l}=o.internalEngine(),r=i.pointerDown(),_=1/(l.length-1),a=e,m=r?o.selectedScrollSnap():o.previousScrollSnap();if(r&&a===m){let e=-1*Math.sign(n);a=m,m=t.clone().set(m).add(e).get()}c=m*_+(a-m)*_*s}(e,b),function(e){let s=o.internalEngine().slideRegistry[e],{scrollSnaps:t,containerRect:i}=o.internalEngine(),l=_[e];s.forEach(s=>{let r=o.slideNodes()[s].style,_=parseFloat(l.toFixed(2)),n=_>0,a=function(e){let{axis:s}=o.internalEngine(),t=s.scroll.toUpperCase();return`translate${t}(${s.direction(e)}px)`}(n?t[e]:i.width+2);n&&(r.transform=a),r.opacity=_.toString(),r.pointerEvents=l>.5?"auto":"none",n||(r.transform=a)})}(l)})}function H(){let{dragHandler:e,index:s,scrollBody:t}=o.internalEngine(),i=o.selectedScrollSnap();if(!e.pointerDown())return i;let l=Math.sign(t.velocity()),r=Math.sign(n),_=s.clone().set(i).add(-1*l).get();return l&&r?r===l?_:i:null}let x=e=>{let{dragHandler:t,scrollBody:l}=e.internalEngine(),r=t.pointerDown(),c=l.velocity(),m=l.duration(),d=H(),b=!i(d);if(r){if(!c)return;n+=c,a=Math.abs(c/s),function(e){let{scrollSnaps:s,location:t,target:l}=o.internalEngine();i(e)&&!(_[e]<.5)&&(t.set(s[e]),l.set(t))}(d)}if(!r){if(!m||b)return;a+=(1-_[d])/m,a*=.68}b||p(d,a)};function v(){let{target:e,location:s}=o.internalEngine(),t=e.get()-s.get(),l=H(),r=!i(l);return x(o),!(r||Math.abs(t)>=1)&&_[l]>.999}function g(){return c}return{name:"fade",options:e,init:function(e){let i=(o=e).selectedScrollSnap(),{scrollBody:n,containerRect:a,axis:c}=o.internalEngine();s=t(.75*c.measureSize(a),200,500),m=!1,_=o.scrollSnapList().map((e,o)=>o===i?1:0),l=n.settled,r=o.scrollProgress,n.settled=v,o.scrollProgress=g,o.on("select",h).on("slideFocus",d).on("pointerDown",u).on("pointerUp",b),function(){let{translate:e,slideLooper:s}=o.internalEngine();e.clear(),e.toggleActive(!1),s.loopPoints.forEach(({translate:e})=>{e.clear(),e.toggleActive(!1)})}(),d()},destroy:function(){let{scrollBody:e}=o.internalEngine();e.settled=l,o.scrollProgress=r,o.off("select",h).off("slideFocus",d).off("pointerDown",u).off("pointerUp",b),o.slideNodes().forEach(e=>{let o=e.style;o.opacity="",o.transform="",o.pointerEvents="",e.getAttribute("style")||e.removeAttribute("style")})}}}s.d(o,{Z:()=>l}),l.globalOptions=void 0}};