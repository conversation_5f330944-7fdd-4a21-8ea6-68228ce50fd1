(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6707],{67612:function(e){"use strict";function t(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function r(e){return"function"==typeof e}function n(e){return"object"==typeof e&&null!==e}e.exports=t,t.prototype._events=void 0,t.prototype._maxListeners=void 0,t.defaultMaxListeners=10,t.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},t.prototype.emit=function(e){var t,i,a,s,o,c;if(this._events||(this._events={}),"error"===e&&(!this._events.error||n(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var u=Error('Uncaught, unspecified "error" event. ('+t+")");throw u.context=t,u}if(void 0===(i=this._events[e]))return!1;if(r(i))switch(arguments.length){case 1:i.call(this);break;case 2:i.call(this,arguments[1]);break;case 3:i.call(this,arguments[1],arguments[2]);break;default:s=Array.prototype.slice.call(arguments,1),i.apply(this,s)}else if(n(i))for(o=0,s=Array.prototype.slice.call(arguments,1),a=(c=i.slice()).length;o<a;o++)c[o].apply(this,s);return!0},t.prototype.addListener=function(e,i){var a;if(!r(i))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,r(i.listener)?i.listener:i),this._events[e]?n(this._events[e])?this._events[e].push(i):this._events[e]=[this._events[e],i]:this._events[e]=i,n(this._events[e])&&!this._events[e].warned&&(a=void 0===this._maxListeners?t.defaultMaxListeners:this._maxListeners)&&a>0&&this._events[e].length>a&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace()),this},t.prototype.on=t.prototype.addListener,t.prototype.once=function(e,t){if(!r(t))throw TypeError("listener must be a function");var n=!1;function i(){this.removeListener(e,i),n||(n=!0,t.apply(this,arguments))}return i.listener=t,this.on(e,i),this},t.prototype.removeListener=function(e,t){var i,a,s,o;if(!r(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(s=(i=this._events[e]).length,a=-1,i===t||r(i.listener)&&i.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(n(i)){for(o=s;o-- >0;)if(i[o]===t||i[o].listener&&i[o].listener===t){a=o;break}if(a<0)return this;1===i.length?(i.length=0,delete this._events[e]):i.splice(a,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},t.prototype.removeAllListeners=function(e){var t,n;if(!this._events)return this;if(!this._events.removeListener)return 0==arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0==arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r(n=this._events[e]))this.removeListener(e,n);else if(n)for(;n.length;)this.removeListener(e,n[n.length-1]);return delete this._events[e],this},t.prototype.listeners=function(e){return this._events&&this._events[e]?r(this._events[e])?[this._events[e]]:this._events[e].slice():[]},t.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(r(t))return 1;if(t)return t.length}return 0},t.listenerCount=function(e,t){return e.listenerCount(t)}},13478:function(e,t,r){"use strict";var n=r(90305),i=r(33819),a=r(12254),s=r(67777),o=r(52126);function c(e,t,r,i){return new n(e,t,r,i)}c.version=r(16509),c.AlgoliaSearchHelper=n,c.SearchParameters=s,c.RecommendParameters=i,c.SearchResults=o,c.RecommendResults=a,e.exports=c},57742:function(e,t,r){"use strict";var n=r(67612);function i(e,t,r){this.main=e,this.fn=t,this.recommendFn=r,this.lastResults=null,this.lastRecommendResults=null}r(31965)(i,n),i.prototype.detach=function(){this.removeAllListeners(),this.main.detachDerivedHelper(this)},i.prototype.getModifiedState=function(e){return this.fn(e)},i.prototype.getModifiedRecommendState=function(e){return this.recommendFn(e)},e.exports=i},33819:function(e){"use strict";function t(e){e=e||{},this.params=e.params||[]}t.prototype={constructor:t,addParams:function(e){var r=this.params.slice();return r.push(e),new t({params:r})},removeParams:function(e){return new t({params:this.params.filter(function(t){return t.$$id!==e})})},addFrequentlyBoughtTogether:function(e){return this.addParams(Object.assign({},e,{model:"bought-together"}))},addRelatedProducts:function(e){return this.addParams(Object.assign({},e,{model:"related-products"}))},addTrendingItems:function(e){return this.addParams(Object.assign({},e,{model:"trending-items"}))},addTrendingFacets:function(e){return this.addParams(Object.assign({},e,{model:"trending-facets"}))},addLookingSimilar:function(e){return this.addParams(Object.assign({},e,{model:"looking-similar"}))},_buildQueries:function(e,t){return this.params.filter(function(e){return void 0===t[e.$$id]}).map(function(t){var r=Object.assign({},t,{indexName:e,threshold:t.threshold||0});return delete r.$$id,r})}},e.exports=t},12254:function(e){"use strict";function t(e,t){this._state=e,this._rawResults={};var r=this;e.params.forEach(function(e){var n=e.$$id;r[n]=t[n],r._rawResults[n]=t[n]})}t.prototype={constructor:t},e.exports=t},74702:function(e,t,r){"use strict";var n=r(9923),i=r(72844),a=r(20198),s={addRefinement:function(e,t,r){if(s.isRefined(e,t,r))return e;var i=""+r,a=e[t]?e[t].concat(i):[i],o={};return o[t]=a,n(o,e)},removeRefinement:function(e,t,r){if(void 0===r)return s.clearRefinement(e,function(e,r){return t===r});var n=""+r;return s.clearRefinement(e,function(e,r){return t===r&&n===e})},toggleRefinement:function(e,t,r){if(void 0===r)throw Error("toggleRefinement should be used with a value");return s.isRefined(e,t,r)?s.removeRefinement(e,t,r):s.addRefinement(e,t,r)},clearRefinement:function(e,t,r){if(void 0===t)return i(e)?{}:e;if("string"==typeof t)return a(e,[t]);if("function"==typeof t){var n=!1,s=Object.keys(e).reduce(function(i,a){var s=e[a]||[],o=s.filter(function(e){return!t(e,a,r)});return o.length!==s.length&&(n=!0),i[a]=o,i},{});return n?s:e}},isRefined:function(e,t,r){var n=!!e[t]&&e[t].length>0;return void 0!==r&&n?-1!==e[t].indexOf(""+r):n}};e.exports=s},67777:function(e,t,r){"use strict";var n=r(9923),i=r(46876),a=r(68213),s=r(90549),o=r(72844),c=r(20198),u=r(98825),l=r(66714),h=r(74702);function f(e,t){return Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&e.every(function(e,r){return f(t[r],e)}):e===t}function d(e){var t=e?d._parseNumbers(e):{};void 0===t.userToken||l(t.userToken)||console.warn("[algoliasearch-helper] The `userToken` parameter is invalid. This can lead to wrong analytics.\n  - Format: [a-zA-Z0-9_-]{1,64}"),this.facets=t.facets||[],this.disjunctiveFacets=t.disjunctiveFacets||[],this.hierarchicalFacets=t.hierarchicalFacets||[],this.facetsRefinements=t.facetsRefinements||{},this.facetsExcludes=t.facetsExcludes||{},this.disjunctiveFacetsRefinements=t.disjunctiveFacetsRefinements||{},this.numericRefinements=t.numericRefinements||{},this.tagRefinements=t.tagRefinements||[],this.hierarchicalFacetsRefinements=t.hierarchicalFacetsRefinements||{};var r=this;Object.keys(t).forEach(function(e){var n=-1!==d.PARAMETERS.indexOf(e),i=void 0!==t[e];!n&&i&&(r[e]=t[e])})}d.PARAMETERS=Object.keys(new d),d._parseNumbers=function(e){if(e instanceof d)return e;var t={};if(["aroundPrecision","aroundRadius","getRankingInfo","minWordSizefor2Typos","minWordSizefor1Typo","page","maxValuesPerFacet","distinct","minimumAroundRadius","hitsPerPage","minProximity"].forEach(function(r){var n=e[r];if("string"==typeof n){var i=parseFloat(n);t[r]=isNaN(i)?n:i}}),Array.isArray(e.insideBoundingBox)&&(t.insideBoundingBox=e.insideBoundingBox.map(function(e){return Array.isArray(e)?e.map(function(e){return parseFloat(e)}):e})),e.numericRefinements){var r={};Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t]||{};r[t]={},Object.keys(n).forEach(function(e){var i=n[e].map(function(e){return Array.isArray(e)?e.map(function(e){return"string"==typeof e?parseFloat(e):e}):"string"==typeof e?parseFloat(e):e});r[t][e]=i})}),t.numericRefinements=r}return s(e,t)},d.make=function(e){var t=new d(e);return(e.hierarchicalFacets||[]).forEach(function(e){if(e.rootPath){var r=t.getHierarchicalRefinement(e.name);r.length>0&&0!==r[0].indexOf(e.rootPath)&&(t=t.clearRefinements(e.name)),0===(r=t.getHierarchicalRefinement(e.name)).length&&(t=t.toggleHierarchicalFacetRefinement(e.name,e.rootPath))}}),t},d.validate=function(e,t){var r=t||{};return e.tagFilters&&r.tagRefinements&&r.tagRefinements.length>0?Error("[Tags] Cannot switch from the managed tag API to the advanced API. It is probably an error, if it is really what you want, you should first clear the tags with clearTags method."):e.tagRefinements.length>0&&r.tagFilters?Error("[Tags] Cannot switch from the advanced tag API to the managed API. It is probably an error, if it is not, you should first clear the tags with clearTags method."):e.numericFilters&&r.numericRefinements&&o(r.numericRefinements)?Error("[Numeric filters] Can't switch from the advanced to the managed API. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):o(e.numericRefinements)&&r.numericFilters?Error("[Numeric filters] Can't switch from the managed API to the advanced. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):null},d.prototype={constructor:d,clearRefinements:function(e){var t={numericRefinements:this._clearNumericRefinements(e),facetsRefinements:h.clearRefinement(this.facetsRefinements,e,"conjunctiveFacet"),facetsExcludes:h.clearRefinement(this.facetsExcludes,e,"exclude"),disjunctiveFacetsRefinements:h.clearRefinement(this.disjunctiveFacetsRefinements,e,"disjunctiveFacet"),hierarchicalFacetsRefinements:h.clearRefinement(this.hierarchicalFacetsRefinements,e,"hierarchicalFacet")};return t.numericRefinements===this.numericRefinements&&t.facetsRefinements===this.facetsRefinements&&t.facetsExcludes===this.facetsExcludes&&t.disjunctiveFacetsRefinements===this.disjunctiveFacetsRefinements&&t.hierarchicalFacetsRefinements===this.hierarchicalFacetsRefinements?this:this.setQueryParameters(t)},clearTags:function(){return void 0===this.tagFilters&&0===this.tagRefinements.length?this:this.setQueryParameters({tagFilters:void 0,tagRefinements:[]})},setIndex:function(e){return e===this.index?this:this.setQueryParameters({index:e})},setQuery:function(e){return e===this.query?this:this.setQueryParameters({query:e})},setPage:function(e){return e===this.page?this:this.setQueryParameters({page:e})},setFacets:function(e){return this.setQueryParameters({facets:e})},setDisjunctiveFacets:function(e){return this.setQueryParameters({disjunctiveFacets:e})},setHitsPerPage:function(e){return this.hitsPerPage===e?this:this.setQueryParameters({hitsPerPage:e})},setTypoTolerance:function(e){return this.typoTolerance===e?this:this.setQueryParameters({typoTolerance:e})},addNumericRefinement:function(e,t,r){var n=u(r);if(this.isNumericRefined(e,t,n))return this;var i=s({},this.numericRefinements);return i[e]=s({},i[e]),i[e][t]?(i[e][t]=i[e][t].slice(),i[e][t].push(n)):i[e][t]=[n],this.setQueryParameters({numericRefinements:i})},getConjunctiveRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsRefinements[e]||[]},getDisjunctiveRefinements:function(e){return this.isDisjunctiveFacet(e)&&this.disjunctiveFacetsRefinements[e]||[]},getHierarchicalRefinement:function(e){return this.hierarchicalFacetsRefinements[e]||[]},getExcludeRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsExcludes[e]||[]},removeNumericRefinement:function(e,t,r){return void 0!==r?this.isNumericRefined(e,t,r)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(n,i){return i===e&&n.op===t&&f(n.val,u(r))})}):this:void 0!==t?this.isNumericRefined(e,t)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(r,n){return n===e&&r.op===t})}):this:this.isNumericRefined(e)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(t,r){return r===e})}):this},getNumericRefinements:function(e){return this.numericRefinements[e]||{}},getNumericRefinement:function(e,t){return this.numericRefinements[e]&&this.numericRefinements[e][t]},_clearNumericRefinements:function(e){if(void 0===e)return o(this.numericRefinements)?{}:this.numericRefinements;if("string"==typeof e)return c(this.numericRefinements,[e]);if("function"==typeof e){var t=!1,r=this.numericRefinements,n=Object.keys(r).reduce(function(n,i){var a=r[i],s={};return Object.keys(a=a||{}).forEach(function(r){var n=a[r]||[],o=[];n.forEach(function(t){e({val:t,op:r},i,"numeric")||o.push(t)}),o.length!==n.length&&(t=!0),s[r]=o}),n[i]=s,n},{});return t?n:this.numericRefinements}},addFacet:function(e){return this.isConjunctiveFacet(e)?this:this.setQueryParameters({facets:this.facets.concat([e])})},addDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this:this.setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.concat([e])})},addHierarchicalFacet:function(e){if(this.isHierarchicalFacet(e.name))throw Error("Cannot declare two hierarchical facets with the same name: `"+e.name+"`");return this.setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.concat([e])})},addFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsRefinements,e,t)?this:this.setQueryParameters({facetsRefinements:h.addRefinement(this.facetsRefinements,e,t)})},addExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsExcludes,e,t)?this:this.setQueryParameters({facetsExcludes:h.addRefinement(this.facetsExcludes,e,t)})},addDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return h.isRefined(this.disjunctiveFacetsRefinements,e,t)?this:this.setQueryParameters({disjunctiveFacetsRefinements:h.addRefinement(this.disjunctiveFacetsRefinements,e,t)})},addTagRefinement:function(e){if(this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.concat(e)};return this.setQueryParameters(t)},removeFacet:function(e){return this.isConjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({facets:this.facets.filter(function(t){return t!==e})}):this},removeDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.filter(function(t){return t!==e})}):this},removeHierarchicalFacet:function(e){return this.isHierarchicalFacet(e)?this.clearRefinements(e).setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.filter(function(t){return t.name!==e})}):this},removeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsRefinements,e,t)?this.setQueryParameters({facetsRefinements:h.removeRefinement(this.facetsRefinements,e,t)}):this},removeExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsExcludes,e,t)?this.setQueryParameters({facetsExcludes:h.removeRefinement(this.facetsExcludes,e,t)}):this},removeDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return h.isRefined(this.disjunctiveFacetsRefinements,e,t)?this.setQueryParameters({disjunctiveFacetsRefinements:h.removeRefinement(this.disjunctiveFacetsRefinements,e,t)}):this},removeTagRefinement:function(e){if(!this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.filter(function(t){return t!==e})};return this.setQueryParameters(t)},toggleRefinement:function(e,t){return this.toggleFacetRefinement(e,t)},toggleFacetRefinement:function(e,t){if(this.isHierarchicalFacet(e))return this.toggleHierarchicalFacetRefinement(e,t);if(this.isConjunctiveFacet(e))return this.toggleConjunctiveFacetRefinement(e,t);if(this.isDisjunctiveFacet(e))return this.toggleDisjunctiveFacetRefinement(e,t);throw Error("Cannot refine the undeclared facet "+e+"; it should be added to the helper options facets, disjunctiveFacets or hierarchicalFacets")},toggleConjunctiveFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsRefinements:h.toggleRefinement(this.facetsRefinements,e,t)})},toggleExcludeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsExcludes:h.toggleRefinement(this.facetsExcludes,e,t)})},toggleDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return this.setQueryParameters({disjunctiveFacetsRefinements:h.toggleRefinement(this.disjunctiveFacetsRefinements,e,t)})},toggleHierarchicalFacetRefinement:function(e,t){if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration");var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e)),i={};return void 0!==this.hierarchicalFacetsRefinements[e]&&this.hierarchicalFacetsRefinements[e].length>0&&(this.hierarchicalFacetsRefinements[e][0]===t||0===this.hierarchicalFacetsRefinements[e][0].indexOf(t+r))?-1===t.indexOf(r)?i[e]=[]:i[e]=[t.slice(0,t.lastIndexOf(r))]:i[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(i,this.hierarchicalFacetsRefinements)})},addHierarchicalFacetRefinement:function(e,t){if(this.isHierarchicalFacetRefined(e))throw Error(e+" is already refined.");if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration.");var r={};return r[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(r,this.hierarchicalFacetsRefinements)})},removeHierarchicalFacetRefinement:function(e){if(!this.isHierarchicalFacetRefined(e))return this;var t={};return t[e]=[],this.setQueryParameters({hierarchicalFacetsRefinements:n(t,this.hierarchicalFacetsRefinements)})},toggleTagRefinement:function(e){return this.isTagRefined(e)?this.removeTagRefinement(e):this.addTagRefinement(e)},isDisjunctiveFacet:function(e){return this.disjunctiveFacets.indexOf(e)>-1},isHierarchicalFacet:function(e){return void 0!==this.getHierarchicalFacetByName(e)},isConjunctiveFacet:function(e){return this.facets.indexOf(e)>-1},isFacetRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&h.isRefined(this.facetsRefinements,e,t)},isExcludeRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&h.isRefined(this.facetsExcludes,e,t)},isDisjunctiveFacetRefined:function(e,t){return!!this.isDisjunctiveFacet(e)&&h.isRefined(this.disjunctiveFacetsRefinements,e,t)},isHierarchicalFacetRefined:function(e,t){if(!this.isHierarchicalFacet(e))return!1;var r=this.getHierarchicalRefinement(e);return t?-1!==r.indexOf(t):r.length>0},isNumericRefined:function(e,t,r){if(void 0===r&&void 0===t)return!!this.numericRefinements[e];var n=this.numericRefinements[e]&&void 0!==this.numericRefinements[e][t];if(void 0===r||!n)return n;var a=u(r),s=void 0!==i(this.numericRefinements[e][t],function(e){return f(e,a)});return n&&s},isTagRefined:function(e){return -1!==this.tagRefinements.indexOf(e)},getRefinedDisjunctiveFacets:function(){var e=this,t=a(Object.keys(this.numericRefinements).filter(function(t){return Object.keys(e.numericRefinements[t]).length>0}),this.disjunctiveFacets);return Object.keys(this.disjunctiveFacetsRefinements).filter(function(t){return e.disjunctiveFacetsRefinements[t].length>0}).concat(t).concat(this.getRefinedHierarchicalFacets()).sort()},getRefinedHierarchicalFacets:function(){var e=this;return a(this.hierarchicalFacets.map(function(e){return e.name}),Object.keys(this.hierarchicalFacetsRefinements).filter(function(t){return e.hierarchicalFacetsRefinements[t].length>0})).sort()},getUnrefinedDisjunctiveFacets:function(){var e=this.getRefinedDisjunctiveFacets();return this.disjunctiveFacets.filter(function(t){return -1===e.indexOf(t)})},managedParameters:["index","facets","disjunctiveFacets","facetsRefinements","hierarchicalFacets","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacetsRefinements"],getQueryParams:function(){var e=this.managedParameters,t={},r=this;return Object.keys(this).forEach(function(n){var i=r[n];-1===e.indexOf(n)&&void 0!==i&&(t[n]=i)}),t},setQueryParameter:function(e,t){if(this[e]===t)return this;var r={};return r[e]=t,this.setQueryParameters(r)},setQueryParameters:function(e){if(!e)return this;var t=d.validate(this,e);if(t)throw t;var r=this,n=d._parseNumbers(e),i=Object.keys(this).reduce(function(e,t){return e[t]=r[t],e},{}),a=Object.keys(n).reduce(function(e,t){var r=void 0!==e[t],i=void 0!==n[t];return r&&!i?c(e,[t]):(i&&(e[t]=n[t]),e)},i);return new this.constructor(a)},resetPage:function(){return void 0===this.page?this:this.setPage(0)},_getHierarchicalFacetSortBy:function(e){return e.sortBy||["isRefined:desc","name:asc"]},_getHierarchicalFacetSeparator:function(e){return e.separator||" > "},_getHierarchicalRootPath:function(e){return e.rootPath||null},_getHierarchicalShowParentLevel:function(e){return"boolean"!=typeof e.showParentLevel||e.showParentLevel},getHierarchicalFacetByName:function(e){return i(this.hierarchicalFacets,function(t){return t.name===e})},getHierarchicalFacetBreadcrumb:function(e){if(!this.isHierarchicalFacet(e))return[];var t=this.getHierarchicalRefinement(e)[0];if(!t)return[];var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e));return t.split(r).map(function(e){return e.trim()})},toString:function(){return JSON.stringify(this,null,2)}},e.exports=d},89540:function(e,t,r){"use strict";e.exports=function(e){return function(t,r){var n=e.hierarchicalFacets[r],u=e.hierarchicalFacetsRefinements[n.name]&&e.hierarchicalFacetsRefinements[n.name][0]||"",l=e._getHierarchicalFacetSeparator(n),h=e._getHierarchicalRootPath(n),f=e._getHierarchicalShowParentLevel(n),d=a(e._getHierarchicalFacetSortBy(n)),m=t.every(function(e){return e.exhaustive}),p=t;return h&&(p=t.slice(h.split(l).length)),p.reduce(function(e,t,r){var n=e;if(r>0){var a=0;for(n=e;a<r;)n=i(n&&Array.isArray(n.data)?n.data:[],function(e){return e.isRefined}),a++}if(n){var m=Object.keys(t.data).map(function(e){return[e,t.data[e]]}).filter(function(e){var t,r;return t=e[0],r=n.path||h,(!h||0===t.indexOf(h)&&h!==t)&&(!h&&-1===t.indexOf(l)||h&&t.split(l).length-h.split(l).length==1||-1===t.indexOf(l)&&-1===u.indexOf(l)||0===u.indexOf(t)||0===t.indexOf(r+l)&&(f||0===t.indexOf(u)))});n.data=s(m.map(function(e){var r,n,i,a,s=e[0];return r=e[1],n=c(u),i=t.exhaustive,{name:(a=s.split(l))[a.length-1].trim(),path:s,escapedValue:o(s),count:r,isRefined:n===s||0===n.indexOf(s+l),exhaustive:i,data:null}}),d[0],d[1])}return e},{name:e.hierarchicalFacets[r].name,count:null,isRefined:!0,path:null,escapedValue:null,exhaustive:m,data:null})}};var n=r(93647),i=r(46876),a=r(85324),s=r(3981),o=n.escapeFacetValue,c=n.unescapeFacetValue},52126:function(e,t,r){"use strict";var n=r(31960),i=r(9923),a=r(93647),s=r(46876),o=r(49500),c=r(85324),u=r(3264),l=r(3981),h=a.escapeFacetValue,f=a.unescapeFacetValue,d=r(89540);function m(e){var t={};return e.forEach(function(e,r){t[e]=r}),t}function p(e,t,r){t&&t[r]&&(e.stats=t[r])}function g(e,t,r){var a=t[0]||{};this._rawResults=t;var c=this;Object.keys(a).forEach(function(e){c[e]=a[e]});var l=i(r,{persistHierarchicalRootCount:!1});Object.keys(l).forEach(function(e){c[e]=l[e]}),this.processingTimeMS=t.reduce(function(e,t){return void 0===t.processingTimeMS?e:e+t.processingTimeMS},0),this.disjunctiveFacets=[],this.hierarchicalFacets=e.hierarchicalFacets.map(function(){return[]}),this.facets=[];var h=e.getRefinedDisjunctiveFacets(),g=m(e.facets),y=m(e.disjunctiveFacets),v=1,b=a.facets||{};Object.keys(b).forEach(function(t){var r=b[t],n=s(e.hierarchicalFacets,function(e){return(e.attributes||[]).indexOf(t)>-1});if(n){var i=n.attributes.indexOf(t),u=o(e.hierarchicalFacets,function(e){return e.name===n.name});c.hierarchicalFacets[u][i]={attribute:t,data:r,exhaustive:a.exhaustiveFacetsCount}}else{var l,h=-1!==e.disjunctiveFacets.indexOf(t),f=-1!==e.facets.indexOf(t);h&&(l=y[t],c.disjunctiveFacets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},p(c.disjunctiveFacets[l],a.facets_stats,t)),f&&(l=g[t],c.facets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},p(c.facets[l],a.facets_stats,t))}}),this.hierarchicalFacets=n(this.hierarchicalFacets),h.forEach(function(r){var n=t[v],s=n&&n.facets?n.facets:{},l=e.getHierarchicalFacetByName(r);Object.keys(s).forEach(function(t){var r,h=s[t];if(l){r=o(e.hierarchicalFacets,function(e){return e.name===l.name});var d=o(c.hierarchicalFacets[r],function(e){return e.attribute===t});if(-1===d)return;c.hierarchicalFacets[r][d].data=c.persistHierarchicalRootCount?u(c.hierarchicalFacets[r][d].data,h):i(h,c.hierarchicalFacets[r][d].data)}else{r=y[t];var m=a.facets&&a.facets[t]||{};c.disjunctiveFacets[r]={name:t,data:u(m,h),exhaustive:n.exhaustiveFacetsCount},p(c.disjunctiveFacets[r],n.facets_stats,t),e.disjunctiveFacetsRefinements[t]&&e.disjunctiveFacetsRefinements[t].forEach(function(n){!c.disjunctiveFacets[r].data[n]&&e.disjunctiveFacetsRefinements[t].indexOf(f(n))>-1&&(c.disjunctiveFacets[r].data[n]=0)})}}),v++}),e.getRefinedHierarchicalFacets().forEach(function(r){var n=e.getHierarchicalFacetByName(r),a=e._getHierarchicalFacetSeparator(n),s=e.getHierarchicalRefinement(r);0===s.length||s[0].split(a).length<2||t.slice(v).forEach(function(t){var r=t&&t.facets?t.facets:{};Object.keys(r).forEach(function(t){var u=r[t],l=o(e.hierarchicalFacets,function(e){return e.name===n.name}),h=o(c.hierarchicalFacets[l],function(e){return e.attribute===t});if(-1!==h){var f={};if(s.length>0&&!c.persistHierarchicalRootCount){var d=s[0].split(a)[0];f[d]=c.hierarchicalFacets[l][h].data[d]}c.hierarchicalFacets[l][h].data=i(f,u,c.hierarchicalFacets[l][h].data)}}),v++})}),Object.keys(e.facetsExcludes).forEach(function(t){var r=e.facetsExcludes[t],n=g[t];c.facets[n]={name:t,data:b[t],exhaustive:a.exhaustiveFacetsCount},r.forEach(function(e){c.facets[n]=c.facets[n]||{name:t},c.facets[n].data=c.facets[n].data||{},c.facets[n].data[e]=0})}),this.hierarchicalFacets=this.hierarchicalFacets.map(d(e)),this.facets=n(this.facets),this.disjunctiveFacets=n(this.disjunctiveFacets),this._state=e}function y(e,t){var r=s(e,function(e){return e.name===t});return r&&r.stats}function v(e,t,r,n,i){var a=s(i,function(e){return e.name===r}),o=a&&a.data&&a.data[n]?a.data[n]:0;return{type:t,attributeName:r,name:n,count:o,exhaustive:a&&a.exhaustive||!1}}g.prototype.getFacetByName=function(e){function t(t){return t.name===e}return s(this.facets,t)||s(this.disjunctiveFacets,t)||s(this.hierarchicalFacets,t)},g.DEFAULT_SORT=["isRefined:desc","count:desc","name:asc"],g.prototype.getFacetValues=function(e,t){var r,n=function(e,t){function r(e){return e.name===t}if(e._state.isConjunctiveFacet(t)){var n=s(e.facets,r);return n?Object.keys(n.data).map(function(r){var i=h(r);return{name:r,escapedValue:i,count:n.data[r],isRefined:e._state.isFacetRefined(t,i),isExcluded:e._state.isExcludeRefined(t,r)}}):[]}if(e._state.isDisjunctiveFacet(t)){var i=s(e.disjunctiveFacets,r);return i?Object.keys(i.data).map(function(r){var n=h(r);return{name:r,escapedValue:n,count:i.data[r],isRefined:e._state.isDisjunctiveFacetRefined(t,n)}}):[]}if(e._state.isHierarchicalFacet(t)){var a=s(e.hierarchicalFacets,r);if(!a)return a;var o=e._state.getHierarchicalFacetByName(t),c=e._state._getHierarchicalFacetSeparator(o),u=f(e._state.getHierarchicalRefinement(t)[0]||"");0===u.indexOf(o.rootPath)&&(u=u.replace(o.rootPath+c,""));var l=u.split(c);return l.unshift(t),function e(t,r,n){t.isRefined=t.name===(r[n]&&r[n].trim()),t.data&&t.data.forEach(function(t){e(t,r,n+1)})}(a,l,0),a}}(this,e);if(n){var a=i(t,{sortBy:g.DEFAULT_SORT,facetOrdering:!(t&&t.sortBy)}),o=this;return r=Array.isArray(n)?[e]:o._state.getHierarchicalFacetByName(n.name).attributes,function e(t,r,n,a){if(a=a||0,Array.isArray(r))return t(r,n[a]);if(!r.data||0===r.data.length)return r;var s=r.data.map(function(r){return e(t,r,n,a+1)});return i({data:t(s,n[a])},r)}(function(e,t){if(a.facetOrdering){var r,n,i,s,u,h,f,d=o.renderingContent&&o.renderingContent.facetOrdering&&o.renderingContent.facetOrdering.values&&o.renderingContent.facetOrdering.values[t];if(d)return i=[],s=[],u=d.hide||[],h=(d.order||[]).reduce(function(e,t,r){return e[t]=r,e},{}),e.forEach(function(e){var t=e.path||e.name,r=u.indexOf(t)>-1;r||void 0===h[t]?r||s.push(e):i[h[t]]=e}),i=i.filter(function(e){return e}),"hidden"===(f=d.sortRemainingBy)?i:(n="alpha"===f?[["path","name"],["asc","asc"]]:[["count"],["desc"]],i.concat(l(s,n[0],n[1])))}if(Array.isArray(a.sortBy)){var m=c(a.sortBy,g.DEFAULT_SORT);return l(e,m[0],m[1])}if("function"==typeof a.sortBy)return r=a.sortBy,e.sort(r);throw Error("options.sortBy is optional but if defined it must be either an array of string (predicates) or a sorting function")},n,r)}},g.prototype.getFacetStats=function(e){return this._state.isConjunctiveFacet(e)?y(this.facets,e):this._state.isDisjunctiveFacet(e)?y(this.disjunctiveFacets,e):void 0},g.prototype.getRefinements=function(){var e=this._state,t=this,r=[];return Object.keys(e.facetsRefinements).forEach(function(n){e.facetsRefinements[n].forEach(function(i){r.push(v(e,"facet",n,i,t.facets))})}),Object.keys(e.facetsExcludes).forEach(function(n){e.facetsExcludes[n].forEach(function(i){r.push(v(e,"exclude",n,i,t.facets))})}),Object.keys(e.disjunctiveFacetsRefinements).forEach(function(n){e.disjunctiveFacetsRefinements[n].forEach(function(i){r.push(v(e,"disjunctive",n,i,t.disjunctiveFacets))})}),Object.keys(e.hierarchicalFacetsRefinements).forEach(function(n){e.hierarchicalFacetsRefinements[n].forEach(function(i){var a,o,c,u,l,h,f,d;r.push((a=t.hierarchicalFacets,o=e.getHierarchicalFacetByName(n),c=e._getHierarchicalFacetSeparator(o),u=i.split(c),l=s(a,function(e){return e.name===n}),f=(h=u.reduce(function(e,t){var r=e&&s(e.data,function(e){return e.name===t});return void 0!==r?r:e},l))&&h.count||0,d=h&&h.exhaustive||!1,{type:"hierarchical",attributeName:n,name:h&&h.path||"",count:f,exhaustive:d}))})}),Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t];Object.keys(n).forEach(function(e){n[e].forEach(function(n){r.push({type:"numeric",attributeName:t,name:n,numericValue:n,operator:e})})})}),e.tagRefinements.forEach(function(e){r.push({type:"tag",attributeName:"_tags",name:e})}),r},e.exports=g},90305:function(e,t,r){"use strict";var n=r(67612),i=r(57742),a=r(93647).escapeFacetValue,s=r(31965),o=r(90549),c=r(72844),u=r(20198),l=r(33819),h=r(12254),f=r(13802),d=r(67777),m=r(52126),p=r(43384),g=r(16509);function y(e,t,r,n){"function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.setClient(e);var i=r||{};i.index=t,this.state=d.make(i),this.recommendState=new l({params:i.recommendState}),this.lastResults=null,this.lastRecommendResults=null,this._queryId=0,this._recommendQueryId=0,this._lastQueryIdReceived=-1,this._lastRecommendQueryIdReceived=-1,this.derivedHelpers=[],this._currentNbQueries=0,this._currentNbRecommendQueries=0,this._searchResultsOptions=n,this._recommendCache={}}function v(e){if(e<0)throw Error("Page requested below 0.");return this._change({state:this.state.setPage(e),isPageReset:!1}),this}function b(){return this.state.page}s(y,n),y.prototype.search=function(){return this._search({onlyWithDerivedHelpers:!1}),this},y.prototype.searchOnlyWithDerivedHelpers=function(){return this._search({onlyWithDerivedHelpers:!0}),this},y.prototype.searchWithComposition=function(){return this._runComposition({onlyWithDerivedHelpers:!0}),this},y.prototype.recommend=function(){return this._recommend(),this},y.prototype.getQuery=function(){var e=this.state;return f._getHitsSearchParams(e)},y.prototype.searchOnce=function(e,t){var r=e?this.state.setQueryParameters(e):this.state,n=f._getQueries(r.index,r),i=this;if(this._currentNbQueries++,this.emit("searchOnce",{state:r}),t){this.client.search(n).then(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(null,new m(r,e.results),r)}).catch(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(e,null,r)});return}return this.client.search(n).then(function(e){return i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),{content:new m(r,e.results),state:r,_originalResponse:e}},function(e){throw i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),e})},y.prototype.findAnswers=function(e){console.warn("[algoliasearch-helper] answers is no longer supported");var t=this.state,r=this.derivedHelpers[0];if(!r)return Promise.resolve([]);var n=r.getModifiedState(t),i=o({attributesForPrediction:e.attributesForPrediction,nbHits:e.nbHits},{params:u(f._getHitsSearchParams(n),["attributesToSnippet","hitsPerPage","restrictSearchableAttributes","snippetEllipsisText"])}),a="search for answers was called, but this client does not have a function client.initIndex(index).findAnswers";if("function"!=typeof this.client.initIndex)throw Error(a);var s=this.client.initIndex(n.index);if("function"!=typeof s.findAnswers)throw Error(a);return s.findAnswers(n.query,e.queryLanguages,i)},y.prototype.searchForFacetValues=function(e,t,r,n){var i,s="function"==typeof this.client.searchForFacetValues&&"function"!=typeof this.client.searchForFacets,o="function"==typeof this.client.initIndex;if(!s&&!o&&"function"!=typeof this.client.search)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues or client.initIndex(index).searchForFacetValues");var c=this.state.setQueryParameters(n||{}),u=c.isDisjunctiveFacet(e),l=f.getSearchForFacetQuery(e,t,r,c);this._currentNbQueries++;var h=this;return s?i=this.client.searchForFacetValues([{indexName:c.index,params:l}]):o?i=this.client.initIndex(c.index).searchForFacetValues(l):(delete l.facetName,i=this.client.search([{type:"facet",facet:e,indexName:c.index,params:l}]).then(function(e){return e.results[0]})),this.emit("searchForFacetValues",{state:c,facet:e,query:t}),i.then(function(t){return h._currentNbQueries--,0===h._currentNbQueries&&h.emit("searchQueueEmpty"),(t=Array.isArray(t)?t[0]:t).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=u?c.isDisjunctiveFacetRefined(e,t.escapedValue):c.isFacetRefined(e,t.escapedValue)}),t},function(e){throw h._currentNbQueries--,0===h._currentNbQueries&&h.emit("searchQueueEmpty"),e})},y.prototype.searchForCompositionFacetValues=function(e,t,r,n){if("function"!=typeof this.client.searchForFacetValues)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues");var i,s=this.state.setQueryParameters(n||{}),o=s.isDisjunctiveFacet(e);this._currentNbQueries++;var c=this;return i=this.client.searchForFacetValues({compositionID:s.index,facetName:e,searchForFacetValuesRequest:{params:{query:t,maxFacetHits:r,searchQuery:f._getCompositionHitsSearchParams(s)}}}),this.emit("searchForFacetValues",{state:s,facet:e,query:t}),i.then(function(t){return c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),(t=t.results[0]).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=o?s.isDisjunctiveFacetRefined(e,t.escapedValue):s.isFacetRefined(e,t.escapedValue)}),t},function(e){throw c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),e})},y.prototype.setQuery=function(e){return this._change({state:this.state.resetPage().setQuery(e),isPageReset:!0}),this},y.prototype.clearRefinements=function(e){return this._change({state:this.state.resetPage().clearRefinements(e),isPageReset:!0}),this},y.prototype.clearTags=function(){return this._change({state:this.state.resetPage().clearTags(),isPageReset:!0}),this},y.prototype.addDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addDisjunctiveRefine=function(){return this.addDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.addHierarchicalFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addHierarchicalFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().addNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.addFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addRefine=function(){return this.addFacetRefinement.apply(this,arguments)},y.prototype.addFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().addExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.addExclude=function(){return this.addFacetExclusion.apply(this,arguments)},y.prototype.addTag=function(e){return this._change({state:this.state.resetPage().addTagRefinement(e),isPageReset:!0}),this},y.prototype.addFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.addFrequentlyBoughtTogether(e)}),this},y.prototype.addRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.addRelatedProducts(e)}),this},y.prototype.addTrendingItems=function(e){return this._recommendChange({state:this.recommendState.addTrendingItems(e)}),this},y.prototype.addTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.addTrendingFacets(e)}),this},y.prototype.addLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.addLookingSimilar(e)}),this},y.prototype.removeNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().removeNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.removeDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeDisjunctiveRefine=function(){return this.removeDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.removeHierarchicalFacetRefinement=function(e){return this._change({state:this.state.resetPage().removeHierarchicalFacetRefinement(e),isPageReset:!0}),this},y.prototype.removeFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeRefine=function(){return this.removeFacetRefinement.apply(this,arguments)},y.prototype.removeFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().removeExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.removeExclude=function(){return this.removeFacetExclusion.apply(this,arguments)},y.prototype.removeTag=function(e){return this._change({state:this.state.resetPage().removeTagRefinement(e),isPageReset:!0}),this},y.prototype.removeFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingItems=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.toggleFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().toggleExcludeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleExclude=function(){return this.toggleFacetExclusion.apply(this,arguments)},y.prototype.toggleRefinement=function(e,t){return this.toggleFacetRefinement(e,t)},y.prototype.toggleFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().toggleFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleRefine=function(){return this.toggleFacetRefinement.apply(this,arguments)},y.prototype.toggleTag=function(e){return this._change({state:this.state.resetPage().toggleTagRefinement(e),isPageReset:!0}),this},y.prototype.nextPage=function(){var e=this.state.page||0;return this.setPage(e+1)},y.prototype.previousPage=function(){var e=this.state.page||0;return this.setPage(e-1)},y.prototype.setCurrentPage=v,y.prototype.setPage=v,y.prototype.setIndex=function(e){return this._change({state:this.state.resetPage().setIndex(e),isPageReset:!0}),this},y.prototype.setQueryParameter=function(e,t){return this._change({state:this.state.resetPage().setQueryParameter(e,t),isPageReset:!0}),this},y.prototype.setState=function(e){return this._change({state:d.make(e),isPageReset:!1}),this},y.prototype.overrideStateWithoutTriggeringChangeEvent=function(e){return this.state=new d(e),this},y.prototype.hasRefinements=function(e){return!!c(this.state.getNumericRefinements(e))||(this.state.isConjunctiveFacet(e)?this.state.isFacetRefined(e):this.state.isDisjunctiveFacet(e)?this.state.isDisjunctiveFacetRefined(e):!!this.state.isHierarchicalFacet(e)&&this.state.isHierarchicalFacetRefined(e))},y.prototype.isExcluded=function(e,t){return this.state.isExcludeRefined(e,t)},y.prototype.isDisjunctiveRefined=function(e,t){return this.state.isDisjunctiveFacetRefined(e,t)},y.prototype.hasTag=function(e){return this.state.isTagRefined(e)},y.prototype.isTagRefined=function(){return this.hasTagRefinements.apply(this,arguments)},y.prototype.getIndex=function(){return this.state.index},y.prototype.getCurrentPage=b,y.prototype.getPage=b,y.prototype.getTags=function(){return this.state.tagRefinements},y.prototype.getRefinements=function(e){var t=[];this.state.isConjunctiveFacet(e)?(this.state.getConjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"conjunctive"})}),this.state.getExcludeRefinements(e).forEach(function(e){t.push({value:e,type:"exclude"})})):this.state.isDisjunctiveFacet(e)&&this.state.getDisjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"disjunctive"})});var r=this.state.getNumericRefinements(e);return Object.keys(r).forEach(function(e){var n=r[e];t.push({value:n,operator:e,type:"numeric"})}),t},y.prototype.getNumericRefinement=function(e,t){return this.state.getNumericRefinement(e,t)},y.prototype.getHierarchicalFacetBreadcrumb=function(e){return this.state.getHierarchicalFacetBreadcrumb(e)},y.prototype._search=function(e){var t=this.state,r=[],n=[];e.onlyWithDerivedHelpers||(n=f._getQueries(t.index,t),r.push({state:t,queriesCount:n.length,helper:this}),this.emit("search",{state:t,results:this.lastResults}));var i=this.derivedHelpers.map(function(e){var n=e.getModifiedState(t),i=n.index?f._getQueries(n.index,n):[];return r.push({state:n,queriesCount:i.length,helper:e}),e.emit("search",{state:n,results:e.lastResults}),i}),a=Array.prototype.concat.apply(n,i),s=this._queryId++;if(this._currentNbQueries++,!a.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,r,s));try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,r,s)).catch(this._dispatchAlgoliaError.bind(this,s))}catch(e){this.emit("error",{error:e})}},y.prototype._runComposition=function(){var e=this.state,t=[],r=this.derivedHelpers.map(function(r){var n=r.getModifiedState(e),i=f._getCompositionQueries(n);return t.push({state:n,queriesCount:i.length,helper:r}),r.emit("search",{state:n,results:r.lastResults}),i}),n=Array.prototype.concat.apply([],r),i=this._queryId++;if(this._currentNbQueries++,!n.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,t,i));if(n.length>1)throw Error("Only one query is allowed when using a composition.");var a=n[0];try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,t,i)).catch(this._dispatchAlgoliaError.bind(this,i))}catch(e){this.emit("error",{error:e})}},y.prototype._recommend=function(){var e=this.state,t=this.recommendState,r=this.getIndex(),n=[{state:t,index:r,helper:this}],i=t.params.map(function(e){return e.$$id});this.emit("fetch",{recommend:{state:t,results:this.lastRecommendResults}});var a=this._recommendCache,s=this.derivedHelpers.map(function(t){var r=t.getModifiedState(e).index;if(!r)return[];var s=t.getModifiedRecommendState(new l);return n.push({state:s,index:r,helper:t}),i=Array.prototype.concat.apply(i,s.params.map(function(e){return e.$$id})),t.emit("fetch",{recommend:{state:s,results:t.lastRecommendResults}}),s._buildQueries(r,a)}),o=Array.prototype.concat.apply(this.recommendState._buildQueries(r,a),s);if(0!==o.length){if(o.length>0&&void 0===this.client.getRecommendations){console.warn("Please update algoliasearch/lite to the latest version in order to use recommend widgets.");return}var c=this._recommendQueryId++;this._currentNbRecommendQueries++;try{this.client.getRecommendations(o).then(this._dispatchRecommendResponse.bind(this,c,n,i)).catch(this._dispatchRecommendError.bind(this,c))}catch(e){this.emit("error",{error:e})}}},y.prototype._dispatchAlgoliaResponse=function(e,t,r){var n=this;if(!(t<this._lastQueryIdReceived)){this._currentNbQueries-=t-this._lastQueryIdReceived,this._lastQueryIdReceived=t,0===this._currentNbQueries&&this.emit("searchQueueEmpty");var i=r.results.slice();e.forEach(function(e){var t=e.state,r=e.queriesCount,a=e.helper,s=i.splice(0,r);if(!t.index){a.emit("result",{results:null,state:t});return}a.lastResults=new m(t,s,n._searchResultsOptions),a.emit("result",{results:a.lastResults,state:t})})}},y.prototype._dispatchRecommendResponse=function(e,t,r,n){if(!(e<this._lastRecommendQueryIdReceived)){this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty");var i=this._recommendCache,a={};r.filter(function(e){return void 0===i[e]}).forEach(function(e,t){a[e]||(a[e]=[]),a[e].push(t)}),Object.keys(a).forEach(function(e){var t=a[e],r=n.results[t[0]];if(1===t.length){i[e]=r;return}i[e]=Object.assign({},r,{hits:p(t.map(function(e){return n.results[e].hits}))})});var s={};r.forEach(function(e){s[e]=i[e]}),t.forEach(function(e){var t=e.state,r=e.helper;if(!e.index){r.emit("recommend:result",{results:null,state:t});return}r.lastRecommendResults=new h(t,s),r.emit("recommend:result",{recommend:{results:r.lastRecommendResults,state:t}})})}},y.prototype._dispatchAlgoliaError=function(e,t){e<this._lastQueryIdReceived||(this._currentNbQueries-=e-this._lastQueryIdReceived,this._lastQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbQueries&&this.emit("searchQueueEmpty"))},y.prototype._dispatchRecommendError=function(e,t){e<this._lastRecommendQueryIdReceived||(this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty"))},y.prototype.containsRefinement=function(e,t,r,n){return e||0!==t.length||0!==r.length||0!==n.length},y.prototype._hasDisjunctiveRefinements=function(e){return this.state.disjunctiveRefinements[e]&&this.state.disjunctiveRefinements[e].length>0},y.prototype._change=function(e){var t=e.state,r=e.isPageReset;t!==this.state&&(this.state=t,this.emit("change",{state:this.state,results:this.lastResults,isPageReset:r}))},y.prototype._recommendChange=function(e){var t=e.state;t!==this.recommendState&&(this.recommendState=t,this.emit("recommend:change",{search:{results:this.lastResults,state:this.state},recommend:{results:this.lastRecommendResults,state:this.recommendState}}))},y.prototype.clearCache=function(){return this.client.clearCache&&this.client.clearCache(),this},y.prototype.setClient=function(e){return this.client===e||("function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.client=e),this},y.prototype.getClient=function(){return this.client},y.prototype.derive=function(e,t){var r=new i(this,e,t);return this.derivedHelpers.push(r),r},y.prototype.detachDerivedHelper=function(e){var t=this.derivedHelpers.indexOf(e);if(-1===t)throw Error("Derived helper already detached");this.derivedHelpers.splice(t,1)},y.prototype.hasPendingRequests=function(){return this._currentNbQueries>0},e.exports=y},31960:function(e){"use strict";e.exports=function(e){return Array.isArray(e)?e.filter(Boolean):[]}},9923:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){void 0!==t[r]&&(void 0!==e[r]&&delete e[r],e[r]=t[r])}),e},{})}},93647:function(e){"use strict";e.exports={escapeFacetValue:function(e){return"string"!=typeof e?e:String(e).replace(/^-/,"\\-")},unescapeFacetValue:function(e){return"string"!=typeof e?e:e.replace(/^\\-/,"-")}}},46876:function(e){"use strict";e.exports=function(e,t){if(Array.isArray(e)){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}}},49500:function(e){"use strict";e.exports=function(e,t){if(!Array.isArray(e))return -1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return -1}},88225:function(e){"use strict";e.exports=function(e){return e.reduce(function(e,t){return e.concat(t)},[])}},85324:function(e,t,r){"use strict";var n=r(46876);e.exports=function(e,t){var r=(t||[]).map(function(e){return e.split(":")});return e.reduce(function(e,t){var i=t.split(":"),a=n(r,function(e){return e[0]===i[0]});return i.length>1||!a?(e[0].push(i[0]),e[1].push(i[1])):(e[0].push(a[0]),e[1].push(a[1])),e},[[],[]])}},31965:function(e){"use strict";e.exports=function(e,t){e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}},68213:function(e){"use strict";e.exports=function(e,t){return e.filter(function(r,n){return t.indexOf(r)>-1&&e.indexOf(r)===n})}},90549:function(e){"use strict";function t(e){return"function"==typeof e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}e.exports=function(e){t(e)||(e={});for(var r=1,n=arguments.length;r<n;r++){var i=arguments[r];t(i)&&function e(r,n){if(r===n)return r;for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&"__proto__"!==i&&"constructor"!==i){var a=n[i],s=r[i];(void 0===s||void 0!==a)&&(t(s)&&t(a)?r[i]=e(s,a):r[i]="object"==typeof a&&null!==a?e(Array.isArray(a)?[]:{},a):a)}return r}(e,i)}return e}},3264:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){var n="number"==typeof e[r]?e[r]:0,i=t[r];void 0!==i&&i>=n&&(void 0!==e[r]&&delete e[r],e[r]=i)}),e},{})}},72844:function(e){"use strict";e.exports=function(e){return e&&Object.keys(e).length>0}},20198:function(e){"use strict";e.exports=function(e,t){if(null===e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}},3981:function(e){"use strict";e.exports=function(e,t,r){if(!Array.isArray(e))return[];Array.isArray(r)||(r=[]);var n=e.map(function(e,r){return{criteria:t.map(function(t){return e[t]}),index:r,value:e}});return n.sort(function(e,t){for(var n=-1;++n<e.criteria.length;){var i=function(e,t){if(e!==t){var r=void 0!==e,n=null===e,i=void 0!==t,a=null===t;if(!a&&e>t||n&&i||!r)return 1;if(!n&&e<t||a&&r||!i)return -1}return 0}(e.criteria[n],t.criteria[n]);if(i){if(n>=r.length)return i;if("desc"===r[n])return-i;return i}}return e.index-t.index}),n.map(function(e){return e.value})}},98825:function(e){"use strict";e.exports=function e(t){if("number"==typeof t)return t;if("string"==typeof t)return parseFloat(t);if(Array.isArray(t))return t.map(e);throw Error("The value should be a number, a parsable string or an array of those.")}},13802:function(e,t,r){"use strict";var n=r(90549);function i(e){return Object.keys(e).sort().reduce(function(t,r){return t[r]=e[r],t},{})}var a={_getQueries:function(e,t){var r=[];return r.push({indexName:e,params:a._getHitsSearchParams(t)}),t.getRefinedDisjunctiveFacets().forEach(function(n){r.push({indexName:e,params:a._getDisjunctiveFacetSearchParams(t,n)})}),t.getRefinedHierarchicalFacets().forEach(function(n){var i=t.getHierarchicalFacetByName(n),s=t.getHierarchicalRefinement(n),o=t._getHierarchicalFacetSeparator(i);if(s.length>0&&s[0].split(o).length>1){var c=s[0].split(o).slice(0,-1).reduce(function(e,t,r){return e.concat({attribute:i.attributes[r],value:0===r?t:[e[e.length-1].value,t].join(o)})},[]);c.forEach(function(n,s){var o=a._getDisjunctiveFacetSearchParams(t,n.attribute,0===s);function u(e){return i.attributes.some(function(t){return t===e.split(":")[0]})}var l=(o.facetFilters||[]).reduce(function(e,t){if(Array.isArray(t)){var r=t.filter(function(e){return!u(e)});r.length>0&&e.push(r)}return"string"!=typeof t||u(t)||e.push(t),e},[]),h=c[s-1];s>0?o.facetFilters=l.concat(h.attribute+":"+h.value):l.length>0?o.facetFilters=l:delete o.facetFilters,r.push({indexName:e,params:o})})}}),r},_getCompositionQueries:function(e){return[{compositionID:e.index,requestBody:{params:a._getCompositionHitsSearchParams(e)}}]},_getHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),s=a._getNumericFilters(e),o=a._getTagFilters(e),c={};return t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),o.length>0&&(c.tagFilters=o),r.length>0&&(c.facetFilters=r),s.length>0&&(c.numericFilters=s),i(n({},e.getQueryParams(),c))},_getCompositionHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets.map(function(e){return"disjunctive("+e+")"})).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),s=a._getNumericFilters(e),o=a._getTagFilters(e),c={};t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),o.length>0&&(c.tagFilters=o),r.length>0&&(c.facetFilters=r),s.length>0&&(c.numericFilters=s);var u=e.getQueryParams();return delete u.highlightPreTag,delete u.highlightPostTag,delete u.index,i(n({},u,c))},_getDisjunctiveFacetSearchParams:function(e,t,r){var s=a._getFacetFilters(e,t,r),o=a._getNumericFilters(e,t),c=a._getTagFilters(e),u={hitsPerPage:0,page:0,analytics:!1,clickAnalytics:!1};c.length>0&&(u.tagFilters=c);var l=e.getHierarchicalFacetByName(t);return l?u.facets=a._getDisjunctiveHierarchicalFacetAttribute(e,l,r):u.facets=t,o.length>0&&(u.numericFilters=o),s.length>0&&(u.facetFilters=s),i(n({},e.getQueryParams(),u))},_getNumericFilters:function(e,t){if(e.numericFilters)return e.numericFilters;var r=[];return Object.keys(e.numericRefinements).forEach(function(n){var i=e.numericRefinements[n]||{};Object.keys(i).forEach(function(e){var a=i[e]||[];t!==n&&a.forEach(function(t){if(Array.isArray(t)){var i=t.map(function(t){return n+e+t});r.push(i)}else r.push(n+e+t)})})}),r},_getTagFilters:function(e){return e.tagFilters?e.tagFilters:e.tagRefinements.join(",")},_getFacetFilters:function(e,t,r){var n=[],i=e.facetsRefinements||{};Object.keys(i).sort().forEach(function(e){(i[e]||[]).slice().sort().forEach(function(t){n.push(e+":"+t)})});var a=e.facetsExcludes||{};Object.keys(a).sort().forEach(function(e){(a[e]||[]).sort().forEach(function(t){n.push(e+":-"+t)})});var s=e.disjunctiveFacetsRefinements||{};Object.keys(s).sort().forEach(function(e){var r=s[e]||[];if(e!==t&&r&&0!==r.length){var i=[];r.slice().sort().forEach(function(t){i.push(e+":"+t)}),n.push(i)}});var o=e.hierarchicalFacetsRefinements||{};return Object.keys(o).sort().forEach(function(i){var a,s,c=(o[i]||[])[0];if(void 0!==c){var u=e.getHierarchicalFacetByName(i),l=e._getHierarchicalFacetSeparator(u),h=e._getHierarchicalRootPath(u);if(t===i){if(-1===c.indexOf(l)||!h&&!0===r||h&&h.split(l).length===c.split(l).length)return;h?(s=h.split(l).length-1,c=h):(s=c.split(l).length-2,c=c.slice(0,c.lastIndexOf(l))),a=u.attributes[s]}else s=c.split(l).length-1,a=u.attributes[s];a&&n.push([a+":"+c])}}),n},_getHitsHierarchicalFacetsAttributes:function(e){return e.hierarchicalFacets.reduce(function(t,r){var n=e.getHierarchicalRefinement(r.name)[0];if(!n)return t.push(r.attributes[0]),t;var i=e._getHierarchicalFacetSeparator(r),a=n.split(i).length,s=r.attributes.slice(0,a+1);return t.concat(s)},[])},_getDisjunctiveHierarchicalFacetAttribute:function(e,t,r){var n=e._getHierarchicalFacetSeparator(t);if(!0===r){var i=e._getHierarchicalRootPath(t),a=0;return i&&(a=i.split(n).length),[t.attributes[a]]}var s=(e.getHierarchicalRefinement(t.name)[0]||"").split(n).length-1;return t.attributes.slice(0,s+1)},getSearchForFacetQuery:function(e,t,r,s){var o=s.isDisjunctiveFacet(e)?s.clearRefinements(e):s,c={facetQuery:t,facetName:e};return"number"==typeof r&&(c.maxFacetHits=r),i(n({},a._getHitsSearchParams(o),c))}};e.exports=a},66714:function(e){"use strict";e.exports=function(e){return null!==e&&/^[a-zA-Z0-9_-]{1,64}$/.test(e)}},43384:function(e,t,r){"use strict";var n=r(46876),i=r(88225);e.exports=function(e){var t,r,a={};return e.forEach(function(e){e.forEach(function(e,t){a[e.objectID]?a[e.objectID]={indexSum:a[e.objectID].indexSum+t,count:a[e.objectID].count+1}:a[e.objectID]={indexSum:t,count:1}})}),(t=e.length,r=[],Object.keys(a).forEach(function(e){a[e].count<2&&(a[e].indexSum+=100),r.push({objectID:e,avgOfIndices:a[e].indexSum/t})}),r.sort(function(e,t){return e.avgOfIndices>t.avgOfIndices?1:-1})).reduce(function(t,r){var a=n(i(e),function(e){return e.objectID===r.objectID});return a?t.concat(a):t},[])}},16509:function(e){"use strict";e.exports="3.23.1"},72803:function(e){"use strict";var t=String.prototype.replace,r=/%20/g,n={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:n.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:n.RFC1738,RFC3986:n.RFC3986}},81850:function(e,t,r){"use strict";var n=r(80009),i=r(92379),a=r(72803);e.exports={formats:a,parse:i,stringify:n}},92379:function(e,t,r){"use strict";var n=r(41390),i=Object.prototype.hasOwnProperty,a=Array.isArray,s={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:n.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},o=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},c=function(e,t){var r={},c=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,u=t.parameterLimit===1/0?void 0:t.parameterLimit,l=c.split(t.delimiter,u),h=-1,f=t.charset;if(t.charsetSentinel)for(d=0;d<l.length;++d)0===l[d].indexOf("utf8=")&&("utf8=%E2%9C%93"===l[d]?f="utf-8":"utf8=%26%2310003%3B"===l[d]&&(f="iso-8859-1"),h=d,d=l.length);for(d=0;d<l.length;++d)if(d!==h){var d,m,p,g=l[d],y=g.indexOf("]="),v=-1===y?g.indexOf("="):y+1;-1===v?(m=t.decoder(g,s.decoder,f,"key"),p=t.strictNullHandling?null:""):(m=t.decoder(g.slice(0,v),s.decoder,f,"key"),p=n.maybeMap(o(g.slice(v+1),t),function(e){return t.decoder(e,s.decoder,f,"value")})),p&&t.interpretNumericEntities&&"iso-8859-1"===f&&(p=p.replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),g.indexOf("[]=")>-1&&(p=a(p)?[p]:p),i.call(r,m)?r[m]=n.combine(r[m],p):r[m]=p}return r},u=function(e,t,r,n){for(var i=n?t:o(t,r),a=e.length-1;a>=0;--a){var s,c=e[a];if("[]"===c&&r.parseArrays)s=[].concat(i);else{s=r.plainObjects?Object.create(null):{};var u="["===c.charAt(0)&&"]"===c.charAt(c.length-1)?c.slice(1,-1):c,l=parseInt(u,10);r.parseArrays||""!==u?!isNaN(l)&&c!==u&&String(l)===u&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(s=[])[l]=i:"__proto__"!==u&&(s[u]=i):s={0:i}}i=s}return i},l=function(e,t,r,n){if(e){var a=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,s=/(\[[^[\]]*])/g,o=r.depth>0&&/(\[[^[\]]*])/.exec(a),c=o?a.slice(0,o.index):a,l=[];if(c){if(!r.plainObjects&&i.call(Object.prototype,c)&&!r.allowPrototypes)return;l.push(c)}for(var h=0;r.depth>0&&null!==(o=s.exec(a))&&h<r.depth;){if(h+=1,!r.plainObjects&&i.call(Object.prototype,o[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(o[1])}return o&&l.push("["+a.slice(o.index)+"]"),u(l,t,r,n)}},h=function(e){if(!e)return s;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?s.charset:e.charset;return{allowDots:void 0===e.allowDots?s.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:s.allowPrototypes,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:s.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:s.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:s.comma,decoder:"function"==typeof e.decoder?e.decoder:s.decoder,delimiter:"string"==typeof e.delimiter||n.isRegExp(e.delimiter)?e.delimiter:s.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:s.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:s.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:s.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:s.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:s.strictNullHandling}};e.exports=function(e,t){var r=h(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var i="string"==typeof e?c(e,r):e,a=r.plainObjects?Object.create(null):{},s=Object.keys(i),o=0;o<s.length;++o){var u=s[o],f=l(u,i[u],r,"string"==typeof e);a=n.merge(a,f,r)}return n.compact(a)}},80009:function(e,t,r){"use strict";var n=r(41390),i=r(72803),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},o=Array.isArray,c=String.prototype.split,u=Array.prototype.push,l=function(e,t){u.apply(e,o(t)?t:[t])},h=Date.prototype.toISOString,f=i.default,d={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:f,formatter:i.formatters[f],indices:!1,serializeDate:function(e){return h.call(e)},skipNulls:!1,strictNullHandling:!1},m=function e(t,r,i,a,s,u,h,f,m,p,g,y,v,b){var P,w,R=t;if("function"==typeof h?R=h(r,R):R instanceof Date?R=p(R):"comma"===i&&o(R)&&(R=n.maybeMap(R,function(e){return e instanceof Date?p(e):e})),null===R){if(a)return u&&!v?u(r,d.encoder,b,"key",g):r;R=""}if("string"==typeof(P=R)||"number"==typeof P||"boolean"==typeof P||"symbol"==typeof P||"bigint"==typeof P||n.isBuffer(R)){if(u){var S=v?r:u(r,d.encoder,b,"key",g);if("comma"===i&&v){for(var E=c.call(String(R),","),j="",O=0;O<E.length;++O)j+=(0===O?"":",")+y(u(E[O],d.encoder,b,"value",g));return[y(S)+"="+j]}return[y(S)+"="+y(u(R,d.encoder,b,"value",g))]}return[y(r)+"="+y(String(R))]}var x=[];if(void 0===R)return x;if("comma"===i&&o(R))w=[{value:R.length>0?R.join(",")||null:void 0}];else if(o(h))w=h;else{var F=Object.keys(R);w=f?F.sort(f):F}for(var q=0;q<w.length;++q){var T=w[q],I="object"==typeof T&&void 0!==T.value?T.value:R[T];s&&null===I||l(x,e(I,o(R)?"function"==typeof i?i(r,T):r:r+(m?"."+T:"["+T+"]"),i,a,s,u,h,f,m,p,g,y,v,b))}return x},p=function(e){if(!e)return d;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t=e.charset||d.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");r=e.format}var n=i.formatters[r],s=d.filter;return("function"==typeof e.filter||o(e.filter))&&(s=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:d.addQueryPrefix,allowDots:void 0===e.allowDots?d.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:d.charsetSentinel,delimiter:void 0===e.delimiter?d.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:d.encode,encoder:"function"==typeof e.encoder?e.encoder:d.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:d.encodeValuesOnly,filter:s,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:d.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:d.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:d.strictNullHandling}};e.exports=function(e,t){var r,n,i=e,a=p(t);"function"==typeof a.filter?i=(0,a.filter)("",i):o(a.filter)&&(r=a.filter);var c=[];if("object"!=typeof i||null===i)return"";n=t&&t.arrayFormat in s?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var u=s[n];r||(r=Object.keys(i)),a.sort&&r.sort(a.sort);for(var h=0;h<r.length;++h){var f=r[h];a.skipNulls&&null===i[f]||l(c,m(i[f],f,u,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset))}var d=c.join(a.delimiter),g=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),d.length>0?g+d:""}},41390:function(e,t,r){"use strict";var n=r(72803),i=Object.prototype.hasOwnProperty,a=Array.isArray,s=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),o=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(a(r)){for(var n=[],i=0;i<r.length;++i)void 0!==r[i]&&n.push(r[i]);t.obj[t.prop]=n}}},c=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r};e.exports={arrayToObject:c,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var i=t[n],a=i.obj[i.prop],s=Object.keys(a),c=0;c<s.length;++c){var u=s[c],l=a[u];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(t.push({obj:a,prop:u}),r.push(l))}return o(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(e){return n}},encode:function(e,t,r,i,a){if(0===e.length)return e;var o=e;if("symbol"==typeof e?o=Symbol.prototype.toString.call(e):"string"!=typeof e&&(o=String(e)),"iso-8859-1"===r)return escape(o).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var c="",u=0;u<o.length;++u){var l=o.charCodeAt(u);if(45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||a===n.RFC1738&&(40===l||41===l)){c+=o.charAt(u);continue}if(l<128){c+=s[l];continue}if(l<2048){c+=s[192|l>>6]+s[128|63&l];continue}if(l<55296||l>=57344){c+=s[224|l>>12]+s[128|l>>6&63]+s[128|63&l];continue}u+=1,c+=s[240|(l=65536+((1023&l)<<10|1023&o.charCodeAt(u)))>>18]+s[128|l>>12&63]+s[128|l>>6&63]+s[128|63&l]}return c},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(a(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,n){if(!r)return t;if("object"!=typeof r){if(a(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!i.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var s=t;return(a(t)&&!a(r)&&(s=c(t,n)),a(t)&&a(r))?(r.forEach(function(r,a){if(i.call(t,a)){var s=t[a];s&&"object"==typeof s&&r&&"object"==typeof r?t[a]=e(s,r,n):t.push(r)}else t[a]=r}),t):Object.keys(r).reduce(function(t,a){var s=r[a];return i.call(t,a)?t[a]=e(t[a],s,n):t[a]=s,t},s)}}},97753:function(e,t,r){"use strict";r.r(t);var n=r(16480),i=r.n(n),a=r(2265),s=r(12865),o=r(57437);let c=a.forwardRef((e,t)=>{let{bsPrefix:r,fluid:n=!1,as:a="div",className:c,...u}=e,l=(0,s.vE)(r,"container");return(0,o.jsx)(a,{ref:t,...u,className:i()(c,n?"".concat(l).concat("string"==typeof n?"-".concat(n):"-fluid"):l)})});c.displayName="Container",t.default=c},12865:function(e,t,r){"use strict";r.d(t,{SC:function(){return l},pi:function(){return c},vE:function(){return o},zG:function(){return u}});var n=r(2265);r(57437);let i=n.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:a,Provider:s}=i;function o(e,t){let{prefixes:r}=(0,n.useContext)(i);return e||r[t]||t}function c(){let{breakpoints:e}=(0,n.useContext)(i);return e}function u(){let{minBreakpoint:e}=(0,n.useContext)(i);return e}function l(){let{dir:e}=(0,n.useContext)(i);return"rtl"===e}},54015:function(e,t,r){"use strict";var n=r(676),i=r(30676),a=r(68908),s=r(11024),o=r(38647);function c(e,t,r,i){return new n(e,t,r,i)}c.version=r(79700),c.AlgoliaSearchHelper=n,c.SearchParameters=s,c.RecommendParameters=i,c.SearchResults=o,c.RecommendResults=a,e.exports=c},27295:function(e,t,r){"use strict";var n=r(67612);function i(e,t,r){this.main=e,this.fn=t,this.recommendFn=r,this.lastResults=null,this.lastRecommendResults=null}r(24176)(i,n),i.prototype.detach=function(){this.removeAllListeners(),this.main.detachDerivedHelper(this)},i.prototype.getModifiedState=function(e){return this.fn(e)},i.prototype.getModifiedRecommendState=function(e){return this.recommendFn(e)},e.exports=i},30676:function(e){"use strict";function t(e){e=e||{},this.params=e.params||[]}t.prototype={constructor:t,addParams:function(e){var r=this.params.slice();return r.push(e),new t({params:r})},removeParams:function(e){return new t({params:this.params.filter(function(t){return t.$$id!==e})})},addFrequentlyBoughtTogether:function(e){return this.addParams(Object.assign({},e,{model:"bought-together"}))},addRelatedProducts:function(e){return this.addParams(Object.assign({},e,{model:"related-products"}))},addTrendingItems:function(e){return this.addParams(Object.assign({},e,{model:"trending-items"}))},addTrendingFacets:function(e){return this.addParams(Object.assign({},e,{model:"trending-facets"}))},addLookingSimilar:function(e){return this.addParams(Object.assign({},e,{model:"looking-similar"}))},_buildQueries:function(e,t){return this.params.filter(function(e){return void 0===t[e.$$id]}).map(function(t){var r=Object.assign({},t,{indexName:e,threshold:t.threshold||0});return delete r.$$id,r})}},e.exports=t},68908:function(e){"use strict";function t(e,t){this._state=e,this._rawResults={};var r=this;e.params.forEach(function(e){var n=e.$$id;r[n]=t[n],r._rawResults[n]=t[n]})}t.prototype={constructor:t},e.exports=t},24387:function(e,t,r){"use strict";var n=r(26227),i=r(77160),a=r(635),s={addRefinement:function(e,t,r){if(s.isRefined(e,t,r))return e;var i=""+r,a=e[t]?e[t].concat(i):[i],o={};return o[t]=a,n(o,e)},removeRefinement:function(e,t,r){if(void 0===r)return s.clearRefinement(e,function(e,r){return t===r});var n=""+r;return s.clearRefinement(e,function(e,r){return t===r&&n===e})},toggleRefinement:function(e,t,r){if(void 0===r)throw Error("toggleRefinement should be used with a value");return s.isRefined(e,t,r)?s.removeRefinement(e,t,r):s.addRefinement(e,t,r)},clearRefinement:function(e,t,r){if(void 0===t)return i(e)?{}:e;if("string"==typeof t)return a(e,[t]);if("function"==typeof t){var n=!1,s=Object.keys(e).reduce(function(i,a){var s=e[a]||[],o=s.filter(function(e){return!t(e,a,r)});return o.length!==s.length&&(n=!0),i[a]=o,i},{});return n?s:e}},isRefined:function(e,t,r){var n=!!e[t]&&e[t].length>0;return void 0!==r&&n?-1!==e[t].indexOf(""+r):n}};e.exports=s},11024:function(e,t,r){"use strict";var n=r(26227),i=r(80040),a=r(53740),s=r(99465),o=r(77160),c=r(635),u=r(44566),l=r(87899),h=r(24387);function f(e,t){return Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&e.every(function(e,r){return f(t[r],e)}):e===t}function d(e){var t=e?d._parseNumbers(e):{};void 0===t.userToken||l(t.userToken)||console.warn("[algoliasearch-helper] The `userToken` parameter is invalid. This can lead to wrong analytics.\n  - Format: [a-zA-Z0-9_-]{1,64}"),this.facets=t.facets||[],this.disjunctiveFacets=t.disjunctiveFacets||[],this.hierarchicalFacets=t.hierarchicalFacets||[],this.facetsRefinements=t.facetsRefinements||{},this.facetsExcludes=t.facetsExcludes||{},this.disjunctiveFacetsRefinements=t.disjunctiveFacetsRefinements||{},this.numericRefinements=t.numericRefinements||{},this.tagRefinements=t.tagRefinements||[],this.hierarchicalFacetsRefinements=t.hierarchicalFacetsRefinements||{};var r=this;Object.keys(t).forEach(function(e){var n=-1!==d.PARAMETERS.indexOf(e),i=void 0!==t[e];!n&&i&&(r[e]=t[e])})}d.PARAMETERS=Object.keys(new d),d._parseNumbers=function(e){if(e instanceof d)return e;var t={};if(["aroundPrecision","aroundRadius","getRankingInfo","minWordSizefor2Typos","minWordSizefor1Typo","page","maxValuesPerFacet","distinct","minimumAroundRadius","hitsPerPage","minProximity"].forEach(function(r){var n=e[r];if("string"==typeof n){var i=parseFloat(n);t[r]=isNaN(i)?n:i}}),Array.isArray(e.insideBoundingBox)&&(t.insideBoundingBox=e.insideBoundingBox.map(function(e){return Array.isArray(e)?e.map(function(e){return parseFloat(e)}):e})),e.numericRefinements){var r={};Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t]||{};r[t]={},Object.keys(n).forEach(function(e){var i=n[e].map(function(e){return Array.isArray(e)?e.map(function(e){return"string"==typeof e?parseFloat(e):e}):"string"==typeof e?parseFloat(e):e});r[t][e]=i})}),t.numericRefinements=r}return s(e,t)},d.make=function(e){var t=new d(e);return(e.hierarchicalFacets||[]).forEach(function(e){if(e.rootPath){var r=t.getHierarchicalRefinement(e.name);r.length>0&&0!==r[0].indexOf(e.rootPath)&&(t=t.clearRefinements(e.name)),0===(r=t.getHierarchicalRefinement(e.name)).length&&(t=t.toggleHierarchicalFacetRefinement(e.name,e.rootPath))}}),t},d.validate=function(e,t){var r=t||{};return e.tagFilters&&r.tagRefinements&&r.tagRefinements.length>0?Error("[Tags] Cannot switch from the managed tag API to the advanced API. It is probably an error, if it is really what you want, you should first clear the tags with clearTags method."):e.tagRefinements.length>0&&r.tagFilters?Error("[Tags] Cannot switch from the advanced tag API to the managed API. It is probably an error, if it is not, you should first clear the tags with clearTags method."):e.numericFilters&&r.numericRefinements&&o(r.numericRefinements)?Error("[Numeric filters] Can't switch from the advanced to the managed API. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):o(e.numericRefinements)&&r.numericFilters?Error("[Numeric filters] Can't switch from the managed API to the advanced. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):null},d.prototype={constructor:d,clearRefinements:function(e){var t={numericRefinements:this._clearNumericRefinements(e),facetsRefinements:h.clearRefinement(this.facetsRefinements,e,"conjunctiveFacet"),facetsExcludes:h.clearRefinement(this.facetsExcludes,e,"exclude"),disjunctiveFacetsRefinements:h.clearRefinement(this.disjunctiveFacetsRefinements,e,"disjunctiveFacet"),hierarchicalFacetsRefinements:h.clearRefinement(this.hierarchicalFacetsRefinements,e,"hierarchicalFacet")};return t.numericRefinements===this.numericRefinements&&t.facetsRefinements===this.facetsRefinements&&t.facetsExcludes===this.facetsExcludes&&t.disjunctiveFacetsRefinements===this.disjunctiveFacetsRefinements&&t.hierarchicalFacetsRefinements===this.hierarchicalFacetsRefinements?this:this.setQueryParameters(t)},clearTags:function(){return void 0===this.tagFilters&&0===this.tagRefinements.length?this:this.setQueryParameters({tagFilters:void 0,tagRefinements:[]})},setIndex:function(e){return e===this.index?this:this.setQueryParameters({index:e})},setQuery:function(e){return e===this.query?this:this.setQueryParameters({query:e})},setPage:function(e){return e===this.page?this:this.setQueryParameters({page:e})},setFacets:function(e){return this.setQueryParameters({facets:e})},setDisjunctiveFacets:function(e){return this.setQueryParameters({disjunctiveFacets:e})},setHitsPerPage:function(e){return this.hitsPerPage===e?this:this.setQueryParameters({hitsPerPage:e})},setTypoTolerance:function(e){return this.typoTolerance===e?this:this.setQueryParameters({typoTolerance:e})},addNumericRefinement:function(e,t,r){var n=u(r);if(this.isNumericRefined(e,t,n))return this;var i=s({},this.numericRefinements);return i[e]=s({},i[e]),i[e][t]?(i[e][t]=i[e][t].slice(),i[e][t].push(n)):i[e][t]=[n],this.setQueryParameters({numericRefinements:i})},getConjunctiveRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsRefinements[e]||[]},getDisjunctiveRefinements:function(e){return this.isDisjunctiveFacet(e)&&this.disjunctiveFacetsRefinements[e]||[]},getHierarchicalRefinement:function(e){return this.hierarchicalFacetsRefinements[e]||[]},getExcludeRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsExcludes[e]||[]},removeNumericRefinement:function(e,t,r){return void 0!==r?this.isNumericRefined(e,t,r)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(n,i){return i===e&&n.op===t&&f(n.val,u(r))})}):this:void 0!==t?this.isNumericRefined(e,t)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(r,n){return n===e&&r.op===t})}):this:this.isNumericRefined(e)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(t,r){return r===e})}):this},getNumericRefinements:function(e){return this.numericRefinements[e]||{}},getNumericRefinement:function(e,t){return this.numericRefinements[e]&&this.numericRefinements[e][t]},_clearNumericRefinements:function(e){if(void 0===e)return o(this.numericRefinements)?{}:this.numericRefinements;if("string"==typeof e)return c(this.numericRefinements,[e]);if("function"==typeof e){var t=!1,r=this.numericRefinements,n=Object.keys(r).reduce(function(n,i){var a=r[i],s={};return Object.keys(a=a||{}).forEach(function(r){var n=a[r]||[],o=[];n.forEach(function(t){e({val:t,op:r},i,"numeric")||o.push(t)}),o.length!==n.length&&(t=!0),s[r]=o}),n[i]=s,n},{});return t?n:this.numericRefinements}},addFacet:function(e){return this.isConjunctiveFacet(e)?this:this.setQueryParameters({facets:this.facets.concat([e])})},addDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this:this.setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.concat([e])})},addHierarchicalFacet:function(e){if(this.isHierarchicalFacet(e.name))throw Error("Cannot declare two hierarchical facets with the same name: `"+e.name+"`");return this.setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.concat([e])})},addFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsRefinements,e,t)?this:this.setQueryParameters({facetsRefinements:h.addRefinement(this.facetsRefinements,e,t)})},addExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsExcludes,e,t)?this:this.setQueryParameters({facetsExcludes:h.addRefinement(this.facetsExcludes,e,t)})},addDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return h.isRefined(this.disjunctiveFacetsRefinements,e,t)?this:this.setQueryParameters({disjunctiveFacetsRefinements:h.addRefinement(this.disjunctiveFacetsRefinements,e,t)})},addTagRefinement:function(e){if(this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.concat(e)};return this.setQueryParameters(t)},removeFacet:function(e){return this.isConjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({facets:this.facets.filter(function(t){return t!==e})}):this},removeDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.filter(function(t){return t!==e})}):this},removeHierarchicalFacet:function(e){return this.isHierarchicalFacet(e)?this.clearRefinements(e).setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.filter(function(t){return t.name!==e})}):this},removeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsRefinements,e,t)?this.setQueryParameters({facetsRefinements:h.removeRefinement(this.facetsRefinements,e,t)}):this},removeExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return h.isRefined(this.facetsExcludes,e,t)?this.setQueryParameters({facetsExcludes:h.removeRefinement(this.facetsExcludes,e,t)}):this},removeDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return h.isRefined(this.disjunctiveFacetsRefinements,e,t)?this.setQueryParameters({disjunctiveFacetsRefinements:h.removeRefinement(this.disjunctiveFacetsRefinements,e,t)}):this},removeTagRefinement:function(e){if(!this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.filter(function(t){return t!==e})};return this.setQueryParameters(t)},toggleRefinement:function(e,t){return this.toggleFacetRefinement(e,t)},toggleFacetRefinement:function(e,t){if(this.isHierarchicalFacet(e))return this.toggleHierarchicalFacetRefinement(e,t);if(this.isConjunctiveFacet(e))return this.toggleConjunctiveFacetRefinement(e,t);if(this.isDisjunctiveFacet(e))return this.toggleDisjunctiveFacetRefinement(e,t);throw Error("Cannot refine the undeclared facet "+e+"; it should be added to the helper options facets, disjunctiveFacets or hierarchicalFacets")},toggleConjunctiveFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsRefinements:h.toggleRefinement(this.facetsRefinements,e,t)})},toggleExcludeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsExcludes:h.toggleRefinement(this.facetsExcludes,e,t)})},toggleDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return this.setQueryParameters({disjunctiveFacetsRefinements:h.toggleRefinement(this.disjunctiveFacetsRefinements,e,t)})},toggleHierarchicalFacetRefinement:function(e,t){if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration");var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e)),i={};return void 0!==this.hierarchicalFacetsRefinements[e]&&this.hierarchicalFacetsRefinements[e].length>0&&(this.hierarchicalFacetsRefinements[e][0]===t||0===this.hierarchicalFacetsRefinements[e][0].indexOf(t+r))?-1===t.indexOf(r)?i[e]=[]:i[e]=[t.slice(0,t.lastIndexOf(r))]:i[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(i,this.hierarchicalFacetsRefinements)})},addHierarchicalFacetRefinement:function(e,t){if(this.isHierarchicalFacetRefined(e))throw Error(e+" is already refined.");if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration.");var r={};return r[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(r,this.hierarchicalFacetsRefinements)})},removeHierarchicalFacetRefinement:function(e){if(!this.isHierarchicalFacetRefined(e))return this;var t={};return t[e]=[],this.setQueryParameters({hierarchicalFacetsRefinements:n(t,this.hierarchicalFacetsRefinements)})},toggleTagRefinement:function(e){return this.isTagRefined(e)?this.removeTagRefinement(e):this.addTagRefinement(e)},isDisjunctiveFacet:function(e){return this.disjunctiveFacets.indexOf(e)>-1},isHierarchicalFacet:function(e){return void 0!==this.getHierarchicalFacetByName(e)},isConjunctiveFacet:function(e){return this.facets.indexOf(e)>-1},isFacetRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&h.isRefined(this.facetsRefinements,e,t)},isExcludeRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&h.isRefined(this.facetsExcludes,e,t)},isDisjunctiveFacetRefined:function(e,t){return!!this.isDisjunctiveFacet(e)&&h.isRefined(this.disjunctiveFacetsRefinements,e,t)},isHierarchicalFacetRefined:function(e,t){if(!this.isHierarchicalFacet(e))return!1;var r=this.getHierarchicalRefinement(e);return t?-1!==r.indexOf(t):r.length>0},isNumericRefined:function(e,t,r){if(void 0===r&&void 0===t)return!!this.numericRefinements[e];var n=this.numericRefinements[e]&&void 0!==this.numericRefinements[e][t];if(void 0===r||!n)return n;var a=u(r),s=void 0!==i(this.numericRefinements[e][t],function(e){return f(e,a)});return n&&s},isTagRefined:function(e){return -1!==this.tagRefinements.indexOf(e)},getRefinedDisjunctiveFacets:function(){var e=this,t=a(Object.keys(this.numericRefinements).filter(function(t){return Object.keys(e.numericRefinements[t]).length>0}),this.disjunctiveFacets);return Object.keys(this.disjunctiveFacetsRefinements).filter(function(t){return e.disjunctiveFacetsRefinements[t].length>0}).concat(t).concat(this.getRefinedHierarchicalFacets()).sort()},getRefinedHierarchicalFacets:function(){var e=this;return a(this.hierarchicalFacets.map(function(e){return e.name}),Object.keys(this.hierarchicalFacetsRefinements).filter(function(t){return e.hierarchicalFacetsRefinements[t].length>0})).sort()},getUnrefinedDisjunctiveFacets:function(){var e=this.getRefinedDisjunctiveFacets();return this.disjunctiveFacets.filter(function(t){return -1===e.indexOf(t)})},managedParameters:["index","facets","disjunctiveFacets","facetsRefinements","hierarchicalFacets","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacetsRefinements"],getQueryParams:function(){var e=this.managedParameters,t={},r=this;return Object.keys(this).forEach(function(n){var i=r[n];-1===e.indexOf(n)&&void 0!==i&&(t[n]=i)}),t},setQueryParameter:function(e,t){if(this[e]===t)return this;var r={};return r[e]=t,this.setQueryParameters(r)},setQueryParameters:function(e){if(!e)return this;var t=d.validate(this,e);if(t)throw t;var r=this,n=d._parseNumbers(e),i=Object.keys(this).reduce(function(e,t){return e[t]=r[t],e},{}),a=Object.keys(n).reduce(function(e,t){var r=void 0!==e[t],i=void 0!==n[t];return r&&!i?c(e,[t]):(i&&(e[t]=n[t]),e)},i);return new this.constructor(a)},resetPage:function(){return void 0===this.page?this:this.setPage(0)},_getHierarchicalFacetSortBy:function(e){return e.sortBy||["isRefined:desc","name:asc"]},_getHierarchicalFacetSeparator:function(e){return e.separator||" > "},_getHierarchicalRootPath:function(e){return e.rootPath||null},_getHierarchicalShowParentLevel:function(e){return"boolean"!=typeof e.showParentLevel||e.showParentLevel},getHierarchicalFacetByName:function(e){return i(this.hierarchicalFacets,function(t){return t.name===e})},getHierarchicalFacetBreadcrumb:function(e){if(!this.isHierarchicalFacet(e))return[];var t=this.getHierarchicalRefinement(e)[0];if(!t)return[];var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e));return t.split(r).map(function(e){return e.trim()})},toString:function(){return JSON.stringify(this,null,2)}},e.exports=d},24887:function(e,t,r){"use strict";e.exports=function(e){return function(t,r){var n=e.hierarchicalFacets[r],u=e.hierarchicalFacetsRefinements[n.name]&&e.hierarchicalFacetsRefinements[n.name][0]||"",l=e._getHierarchicalFacetSeparator(n),h=e._getHierarchicalRootPath(n),f=e._getHierarchicalShowParentLevel(n),d=a(e._getHierarchicalFacetSortBy(n)),m=t.every(function(e){return e.exhaustive}),p=t;return h&&(p=t.slice(h.split(l).length)),p.reduce(function(e,t,r){var n=e;if(r>0){var a=0;for(n=e;a<r;)n=i(n&&Array.isArray(n.data)?n.data:[],function(e){return e.isRefined}),a++}if(n){var m=Object.keys(t.data).map(function(e){return[e,t.data[e]]}).filter(function(e){var t,r;return t=e[0],r=n.path||h,(!h||0===t.indexOf(h)&&h!==t)&&(!h&&-1===t.indexOf(l)||h&&t.split(l).length-h.split(l).length==1||-1===t.indexOf(l)&&-1===u.indexOf(l)||0===u.indexOf(t)||0===t.indexOf(r+l)&&(f||0===t.indexOf(u)))});n.data=s(m.map(function(e){var r,n,i,a,s=e[0];return r=e[1],n=c(u),i=t.exhaustive,{name:(a=s.split(l))[a.length-1].trim(),path:s,escapedValue:o(s),count:r,isRefined:n===s||0===n.indexOf(s+l),exhaustive:i,data:null}}),d[0],d[1])}return e},{name:e.hierarchicalFacets[r].name,count:null,isRefined:!0,path:null,escapedValue:null,exhaustive:m,data:null})}};var n=r(42312),i=r(80040),a=r(22524),s=r(92590),o=n.escapeFacetValue,c=n.unescapeFacetValue},38647:function(e,t,r){"use strict";var n=r(75295),i=r(26227),a=r(42312),s=r(80040),o=r(41260),c=r(22524),u=r(97540),l=r(92590),h=a.escapeFacetValue,f=a.unescapeFacetValue,d=r(24887);function m(e){var t={};return e.forEach(function(e,r){t[e]=r}),t}function p(e,t,r){t&&t[r]&&(e.stats=t[r])}function g(e,t,r){var a=t[0]||{};this._rawResults=t;var c=this;Object.keys(a).forEach(function(e){c[e]=a[e]});var l=i(r,{persistHierarchicalRootCount:!1});Object.keys(l).forEach(function(e){c[e]=l[e]}),this.processingTimeMS=t.reduce(function(e,t){return void 0===t.processingTimeMS?e:e+t.processingTimeMS},0),this.disjunctiveFacets=[],this.hierarchicalFacets=e.hierarchicalFacets.map(function(){return[]}),this.facets=[];var h=e.getRefinedDisjunctiveFacets(),g=m(e.facets),y=m(e.disjunctiveFacets),v=1,b=a.facets||{};Object.keys(b).forEach(function(t){var r=b[t],n=s(e.hierarchicalFacets,function(e){return(e.attributes||[]).indexOf(t)>-1});if(n){var i=n.attributes.indexOf(t),u=o(e.hierarchicalFacets,function(e){return e.name===n.name});c.hierarchicalFacets[u][i]={attribute:t,data:r,exhaustive:a.exhaustiveFacetsCount}}else{var l,h=-1!==e.disjunctiveFacets.indexOf(t),f=-1!==e.facets.indexOf(t);h&&(l=y[t],c.disjunctiveFacets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},p(c.disjunctiveFacets[l],a.facets_stats,t)),f&&(l=g[t],c.facets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},p(c.facets[l],a.facets_stats,t))}}),this.hierarchicalFacets=n(this.hierarchicalFacets),h.forEach(function(r){var n=t[v],s=n&&n.facets?n.facets:{},l=e.getHierarchicalFacetByName(r);Object.keys(s).forEach(function(t){var r,h=s[t];if(l){r=o(e.hierarchicalFacets,function(e){return e.name===l.name});var d=o(c.hierarchicalFacets[r],function(e){return e.attribute===t});if(-1===d)return;c.hierarchicalFacets[r][d].data=c.persistHierarchicalRootCount?u(c.hierarchicalFacets[r][d].data,h):i(h,c.hierarchicalFacets[r][d].data)}else{r=y[t];var m=a.facets&&a.facets[t]||{};c.disjunctiveFacets[r]={name:t,data:u(m,h),exhaustive:n.exhaustiveFacetsCount},p(c.disjunctiveFacets[r],n.facets_stats,t),e.disjunctiveFacetsRefinements[t]&&e.disjunctiveFacetsRefinements[t].forEach(function(n){!c.disjunctiveFacets[r].data[n]&&e.disjunctiveFacetsRefinements[t].indexOf(f(n))>-1&&(c.disjunctiveFacets[r].data[n]=0)})}}),v++}),e.getRefinedHierarchicalFacets().forEach(function(r){var n=e.getHierarchicalFacetByName(r),a=e._getHierarchicalFacetSeparator(n),s=e.getHierarchicalRefinement(r);0===s.length||s[0].split(a).length<2||t.slice(v).forEach(function(t){var r=t&&t.facets?t.facets:{};Object.keys(r).forEach(function(t){var u=r[t],l=o(e.hierarchicalFacets,function(e){return e.name===n.name}),h=o(c.hierarchicalFacets[l],function(e){return e.attribute===t});if(-1!==h){var f={};if(s.length>0&&!c.persistHierarchicalRootCount){var d=s[0].split(a)[0];f[d]=c.hierarchicalFacets[l][h].data[d]}c.hierarchicalFacets[l][h].data=i(f,u,c.hierarchicalFacets[l][h].data)}}),v++})}),Object.keys(e.facetsExcludes).forEach(function(t){var r=e.facetsExcludes[t],n=g[t];c.facets[n]={name:t,data:b[t],exhaustive:a.exhaustiveFacetsCount},r.forEach(function(e){c.facets[n]=c.facets[n]||{name:t},c.facets[n].data=c.facets[n].data||{},c.facets[n].data[e]=0})}),this.hierarchicalFacets=this.hierarchicalFacets.map(d(e)),this.facets=n(this.facets),this.disjunctiveFacets=n(this.disjunctiveFacets),this._state=e}function y(e,t){var r=s(e,function(e){return e.name===t});return r&&r.stats}function v(e,t,r,n,i){var a=s(i,function(e){return e.name===r}),o=a&&a.data&&a.data[n]?a.data[n]:0;return{type:t,attributeName:r,name:n,count:o,exhaustive:a&&a.exhaustive||!1}}g.prototype.getFacetByName=function(e){function t(t){return t.name===e}return s(this.facets,t)||s(this.disjunctiveFacets,t)||s(this.hierarchicalFacets,t)},g.DEFAULT_SORT=["isRefined:desc","count:desc","name:asc"],g.prototype.getFacetValues=function(e,t){var r,n=function(e,t){function r(e){return e.name===t}if(e._state.isConjunctiveFacet(t)){var n=s(e.facets,r);return n?Object.keys(n.data).map(function(r){var i=h(r);return{name:r,escapedValue:i,count:n.data[r],isRefined:e._state.isFacetRefined(t,i),isExcluded:e._state.isExcludeRefined(t,r)}}):[]}if(e._state.isDisjunctiveFacet(t)){var i=s(e.disjunctiveFacets,r);return i?Object.keys(i.data).map(function(r){var n=h(r);return{name:r,escapedValue:n,count:i.data[r],isRefined:e._state.isDisjunctiveFacetRefined(t,n)}}):[]}if(e._state.isHierarchicalFacet(t)){var a=s(e.hierarchicalFacets,r);if(!a)return a;var o=e._state.getHierarchicalFacetByName(t),c=e._state._getHierarchicalFacetSeparator(o),u=f(e._state.getHierarchicalRefinement(t)[0]||"");0===u.indexOf(o.rootPath)&&(u=u.replace(o.rootPath+c,""));var l=u.split(c);return l.unshift(t),function e(t,r,n){t.isRefined=t.name===(r[n]&&r[n].trim()),t.data&&t.data.forEach(function(t){e(t,r,n+1)})}(a,l,0),a}}(this,e);if(n){var a=i(t,{sortBy:g.DEFAULT_SORT,facetOrdering:!(t&&t.sortBy)}),o=this;return r=Array.isArray(n)?[e]:o._state.getHierarchicalFacetByName(n.name).attributes,function e(t,r,n,a){if(a=a||0,Array.isArray(r))return t(r,n[a]);if(!r.data||0===r.data.length)return r;var s=r.data.map(function(r){return e(t,r,n,a+1)});return i({data:t(s,n[a])},r)}(function(e,t){if(a.facetOrdering){var r,n,i,s,u,h,f,d=o.renderingContent&&o.renderingContent.facetOrdering&&o.renderingContent.facetOrdering.values&&o.renderingContent.facetOrdering.values[t];if(d)return i=[],s=[],u=d.hide||[],h=(d.order||[]).reduce(function(e,t,r){return e[t]=r,e},{}),e.forEach(function(e){var t=e.path||e.name,r=u.indexOf(t)>-1;r||void 0===h[t]?r||s.push(e):i[h[t]]=e}),i=i.filter(function(e){return e}),"hidden"===(f=d.sortRemainingBy)?i:(n="alpha"===f?[["path","name"],["asc","asc"]]:[["count"],["desc"]],i.concat(l(s,n[0],n[1])))}if(Array.isArray(a.sortBy)){var m=c(a.sortBy,g.DEFAULT_SORT);return l(e,m[0],m[1])}if("function"==typeof a.sortBy)return r=a.sortBy,e.sort(r);throw Error("options.sortBy is optional but if defined it must be either an array of string (predicates) or a sorting function")},n,r)}},g.prototype.getFacetStats=function(e){return this._state.isConjunctiveFacet(e)?y(this.facets,e):this._state.isDisjunctiveFacet(e)?y(this.disjunctiveFacets,e):void 0},g.prototype.getRefinements=function(){var e=this._state,t=this,r=[];return Object.keys(e.facetsRefinements).forEach(function(n){e.facetsRefinements[n].forEach(function(i){r.push(v(e,"facet",n,i,t.facets))})}),Object.keys(e.facetsExcludes).forEach(function(n){e.facetsExcludes[n].forEach(function(i){r.push(v(e,"exclude",n,i,t.facets))})}),Object.keys(e.disjunctiveFacetsRefinements).forEach(function(n){e.disjunctiveFacetsRefinements[n].forEach(function(i){r.push(v(e,"disjunctive",n,i,t.disjunctiveFacets))})}),Object.keys(e.hierarchicalFacetsRefinements).forEach(function(n){e.hierarchicalFacetsRefinements[n].forEach(function(i){var a,o,c,u,l,h,f,d;r.push((a=t.hierarchicalFacets,o=e.getHierarchicalFacetByName(n),c=e._getHierarchicalFacetSeparator(o),u=i.split(c),l=s(a,function(e){return e.name===n}),f=(h=u.reduce(function(e,t){var r=e&&s(e.data,function(e){return e.name===t});return void 0!==r?r:e},l))&&h.count||0,d=h&&h.exhaustive||!1,{type:"hierarchical",attributeName:n,name:h&&h.path||"",count:f,exhaustive:d}))})}),Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t];Object.keys(n).forEach(function(e){n[e].forEach(function(n){r.push({type:"numeric",attributeName:t,name:n,numericValue:n,operator:e})})})}),e.tagRefinements.forEach(function(e){r.push({type:"tag",attributeName:"_tags",name:e})}),r},e.exports=g},676:function(e,t,r){"use strict";var n=r(67612),i=r(27295),a=r(42312).escapeFacetValue,s=r(24176),o=r(99465),c=r(77160),u=r(635),l=r(30676),h=r(68908),f=r(73799),d=r(11024),m=r(38647),p=r(2880),g=r(79700);function y(e,t,r,n){"function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.setClient(e);var i=r||{};i.index=t,this.state=d.make(i),this.recommendState=new l({params:i.recommendState}),this.lastResults=null,this.lastRecommendResults=null,this._queryId=0,this._recommendQueryId=0,this._lastQueryIdReceived=-1,this._lastRecommendQueryIdReceived=-1,this.derivedHelpers=[],this._currentNbQueries=0,this._currentNbRecommendQueries=0,this._searchResultsOptions=n,this._recommendCache={}}function v(e){if(e<0)throw Error("Page requested below 0.");return this._change({state:this.state.setPage(e),isPageReset:!1}),this}function b(){return this.state.page}s(y,n),y.prototype.search=function(){return this._search({onlyWithDerivedHelpers:!1}),this},y.prototype.searchOnlyWithDerivedHelpers=function(){return this._search({onlyWithDerivedHelpers:!0}),this},y.prototype.searchWithComposition=function(){return this._runComposition({onlyWithDerivedHelpers:!0}),this},y.prototype.recommend=function(){return this._recommend(),this},y.prototype.getQuery=function(){var e=this.state;return f._getHitsSearchParams(e)},y.prototype.searchOnce=function(e,t){var r=e?this.state.setQueryParameters(e):this.state,n=f._getQueries(r.index,r),i=this;if(this._currentNbQueries++,this.emit("searchOnce",{state:r}),t){this.client.search(n).then(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(null,new m(r,e.results),r)}).catch(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(e,null,r)});return}return this.client.search(n).then(function(e){return i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),{content:new m(r,e.results),state:r,_originalResponse:e}},function(e){throw i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),e})},y.prototype.findAnswers=function(e){console.warn("[algoliasearch-helper] answers is no longer supported");var t=this.state,r=this.derivedHelpers[0];if(!r)return Promise.resolve([]);var n=r.getModifiedState(t),i=o({attributesForPrediction:e.attributesForPrediction,nbHits:e.nbHits},{params:u(f._getHitsSearchParams(n),["attributesToSnippet","hitsPerPage","restrictSearchableAttributes","snippetEllipsisText"])}),a="search for answers was called, but this client does not have a function client.initIndex(index).findAnswers";if("function"!=typeof this.client.initIndex)throw Error(a);var s=this.client.initIndex(n.index);if("function"!=typeof s.findAnswers)throw Error(a);return s.findAnswers(n.query,e.queryLanguages,i)},y.prototype.searchForFacetValues=function(e,t,r,n){var i,s="function"==typeof this.client.searchForFacetValues&&"function"!=typeof this.client.searchForFacets,o="function"==typeof this.client.initIndex;if(!s&&!o&&"function"!=typeof this.client.search)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues or client.initIndex(index).searchForFacetValues");var c=this.state.setQueryParameters(n||{}),u=c.isDisjunctiveFacet(e),l=f.getSearchForFacetQuery(e,t,r,c);this._currentNbQueries++;var h=this;return s?i=this.client.searchForFacetValues([{indexName:c.index,params:l}]):o?i=this.client.initIndex(c.index).searchForFacetValues(l):(delete l.facetName,i=this.client.search([{type:"facet",facet:e,indexName:c.index,params:l}]).then(function(e){return e.results[0]})),this.emit("searchForFacetValues",{state:c,facet:e,query:t}),i.then(function(t){return h._currentNbQueries--,0===h._currentNbQueries&&h.emit("searchQueueEmpty"),(t=Array.isArray(t)?t[0]:t).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=u?c.isDisjunctiveFacetRefined(e,t.escapedValue):c.isFacetRefined(e,t.escapedValue)}),t},function(e){throw h._currentNbQueries--,0===h._currentNbQueries&&h.emit("searchQueueEmpty"),e})},y.prototype.searchForCompositionFacetValues=function(e,t,r,n){if("function"!=typeof this.client.searchForFacetValues)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues");var i,s=this.state.setQueryParameters(n||{}),o=s.isDisjunctiveFacet(e);this._currentNbQueries++;var c=this;return i=this.client.searchForFacetValues({compositionID:s.index,facetName:e,searchForFacetValuesRequest:{params:{query:t,maxFacetHits:r,searchQuery:f._getCompositionHitsSearchParams(s)}}}),this.emit("searchForFacetValues",{state:s,facet:e,query:t}),i.then(function(t){return c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),(t=t.results[0]).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=o?s.isDisjunctiveFacetRefined(e,t.escapedValue):s.isFacetRefined(e,t.escapedValue)}),t},function(e){throw c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),e})},y.prototype.setQuery=function(e){return this._change({state:this.state.resetPage().setQuery(e),isPageReset:!0}),this},y.prototype.clearRefinements=function(e){return this._change({state:this.state.resetPage().clearRefinements(e),isPageReset:!0}),this},y.prototype.clearTags=function(){return this._change({state:this.state.resetPage().clearTags(),isPageReset:!0}),this},y.prototype.addDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addDisjunctiveRefine=function(){return this.addDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.addHierarchicalFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addHierarchicalFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().addNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.addFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addRefine=function(){return this.addFacetRefinement.apply(this,arguments)},y.prototype.addFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().addExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.addExclude=function(){return this.addFacetExclusion.apply(this,arguments)},y.prototype.addTag=function(e){return this._change({state:this.state.resetPage().addTagRefinement(e),isPageReset:!0}),this},y.prototype.addFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.addFrequentlyBoughtTogether(e)}),this},y.prototype.addRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.addRelatedProducts(e)}),this},y.prototype.addTrendingItems=function(e){return this._recommendChange({state:this.recommendState.addTrendingItems(e)}),this},y.prototype.addTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.addTrendingFacets(e)}),this},y.prototype.addLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.addLookingSimilar(e)}),this},y.prototype.removeNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().removeNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.removeDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeDisjunctiveRefine=function(){return this.removeDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.removeHierarchicalFacetRefinement=function(e){return this._change({state:this.state.resetPage().removeHierarchicalFacetRefinement(e),isPageReset:!0}),this},y.prototype.removeFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeRefine=function(){return this.removeFacetRefinement.apply(this,arguments)},y.prototype.removeFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().removeExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.removeExclude=function(){return this.removeFacetExclusion.apply(this,arguments)},y.prototype.removeTag=function(e){return this._change({state:this.state.resetPage().removeTagRefinement(e),isPageReset:!0}),this},y.prototype.removeFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingItems=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.toggleFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().toggleExcludeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleExclude=function(){return this.toggleFacetExclusion.apply(this,arguments)},y.prototype.toggleRefinement=function(e,t){return this.toggleFacetRefinement(e,t)},y.prototype.toggleFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().toggleFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleRefine=function(){return this.toggleFacetRefinement.apply(this,arguments)},y.prototype.toggleTag=function(e){return this._change({state:this.state.resetPage().toggleTagRefinement(e),isPageReset:!0}),this},y.prototype.nextPage=function(){var e=this.state.page||0;return this.setPage(e+1)},y.prototype.previousPage=function(){var e=this.state.page||0;return this.setPage(e-1)},y.prototype.setCurrentPage=v,y.prototype.setPage=v,y.prototype.setIndex=function(e){return this._change({state:this.state.resetPage().setIndex(e),isPageReset:!0}),this},y.prototype.setQueryParameter=function(e,t){return this._change({state:this.state.resetPage().setQueryParameter(e,t),isPageReset:!0}),this},y.prototype.setState=function(e){return this._change({state:d.make(e),isPageReset:!1}),this},y.prototype.overrideStateWithoutTriggeringChangeEvent=function(e){return this.state=new d(e),this},y.prototype.hasRefinements=function(e){return!!c(this.state.getNumericRefinements(e))||(this.state.isConjunctiveFacet(e)?this.state.isFacetRefined(e):this.state.isDisjunctiveFacet(e)?this.state.isDisjunctiveFacetRefined(e):!!this.state.isHierarchicalFacet(e)&&this.state.isHierarchicalFacetRefined(e))},y.prototype.isExcluded=function(e,t){return this.state.isExcludeRefined(e,t)},y.prototype.isDisjunctiveRefined=function(e,t){return this.state.isDisjunctiveFacetRefined(e,t)},y.prototype.hasTag=function(e){return this.state.isTagRefined(e)},y.prototype.isTagRefined=function(){return this.hasTagRefinements.apply(this,arguments)},y.prototype.getIndex=function(){return this.state.index},y.prototype.getCurrentPage=b,y.prototype.getPage=b,y.prototype.getTags=function(){return this.state.tagRefinements},y.prototype.getRefinements=function(e){var t=[];this.state.isConjunctiveFacet(e)?(this.state.getConjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"conjunctive"})}),this.state.getExcludeRefinements(e).forEach(function(e){t.push({value:e,type:"exclude"})})):this.state.isDisjunctiveFacet(e)&&this.state.getDisjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"disjunctive"})});var r=this.state.getNumericRefinements(e);return Object.keys(r).forEach(function(e){var n=r[e];t.push({value:n,operator:e,type:"numeric"})}),t},y.prototype.getNumericRefinement=function(e,t){return this.state.getNumericRefinement(e,t)},y.prototype.getHierarchicalFacetBreadcrumb=function(e){return this.state.getHierarchicalFacetBreadcrumb(e)},y.prototype._search=function(e){var t=this.state,r=[],n=[];e.onlyWithDerivedHelpers||(n=f._getQueries(t.index,t),r.push({state:t,queriesCount:n.length,helper:this}),this.emit("search",{state:t,results:this.lastResults}));var i=this.derivedHelpers.map(function(e){var n=e.getModifiedState(t),i=n.index?f._getQueries(n.index,n):[];return r.push({state:n,queriesCount:i.length,helper:e}),e.emit("search",{state:n,results:e.lastResults}),i}),a=Array.prototype.concat.apply(n,i),s=this._queryId++;if(this._currentNbQueries++,!a.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,r,s));try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,r,s)).catch(this._dispatchAlgoliaError.bind(this,s))}catch(e){this.emit("error",{error:e})}},y.prototype._runComposition=function(){var e=this.state,t=[],r=this.derivedHelpers.map(function(r){var n=r.getModifiedState(e),i=f._getCompositionQueries(n);return t.push({state:n,queriesCount:i.length,helper:r}),r.emit("search",{state:n,results:r.lastResults}),i}),n=Array.prototype.concat.apply([],r),i=this._queryId++;if(this._currentNbQueries++,!n.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,t,i));if(n.length>1)throw Error("Only one query is allowed when using a composition.");var a=n[0];try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,t,i)).catch(this._dispatchAlgoliaError.bind(this,i))}catch(e){this.emit("error",{error:e})}},y.prototype._recommend=function(){var e=this.state,t=this.recommendState,r=this.getIndex(),n=[{state:t,index:r,helper:this}],i=t.params.map(function(e){return e.$$id});this.emit("fetch",{recommend:{state:t,results:this.lastRecommendResults}});var a=this._recommendCache,s=this.derivedHelpers.map(function(t){var r=t.getModifiedState(e).index;if(!r)return[];var s=t.getModifiedRecommendState(new l);return n.push({state:s,index:r,helper:t}),i=Array.prototype.concat.apply(i,s.params.map(function(e){return e.$$id})),t.emit("fetch",{recommend:{state:s,results:t.lastRecommendResults}}),s._buildQueries(r,a)}),o=Array.prototype.concat.apply(this.recommendState._buildQueries(r,a),s);if(0!==o.length){if(o.length>0&&void 0===this.client.getRecommendations){console.warn("Please update algoliasearch/lite to the latest version in order to use recommend widgets.");return}var c=this._recommendQueryId++;this._currentNbRecommendQueries++;try{this.client.getRecommendations(o).then(this._dispatchRecommendResponse.bind(this,c,n,i)).catch(this._dispatchRecommendError.bind(this,c))}catch(e){this.emit("error",{error:e})}}},y.prototype._dispatchAlgoliaResponse=function(e,t,r){var n=this;if(!(t<this._lastQueryIdReceived)){this._currentNbQueries-=t-this._lastQueryIdReceived,this._lastQueryIdReceived=t,0===this._currentNbQueries&&this.emit("searchQueueEmpty");var i=r.results.slice();e.forEach(function(e){var t=e.state,r=e.queriesCount,a=e.helper,s=i.splice(0,r);if(!t.index){a.emit("result",{results:null,state:t});return}a.lastResults=new m(t,s,n._searchResultsOptions),a.emit("result",{results:a.lastResults,state:t})})}},y.prototype._dispatchRecommendResponse=function(e,t,r,n){if(!(e<this._lastRecommendQueryIdReceived)){this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty");var i=this._recommendCache,a={};r.filter(function(e){return void 0===i[e]}).forEach(function(e,t){a[e]||(a[e]=[]),a[e].push(t)}),Object.keys(a).forEach(function(e){var t=a[e],r=n.results[t[0]];if(1===t.length){i[e]=r;return}i[e]=Object.assign({},r,{hits:p(t.map(function(e){return n.results[e].hits}))})});var s={};r.forEach(function(e){s[e]=i[e]}),t.forEach(function(e){var t=e.state,r=e.helper;if(!e.index){r.emit("recommend:result",{results:null,state:t});return}r.lastRecommendResults=new h(t,s),r.emit("recommend:result",{recommend:{results:r.lastRecommendResults,state:t}})})}},y.prototype._dispatchAlgoliaError=function(e,t){e<this._lastQueryIdReceived||(this._currentNbQueries-=e-this._lastQueryIdReceived,this._lastQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbQueries&&this.emit("searchQueueEmpty"))},y.prototype._dispatchRecommendError=function(e,t){e<this._lastRecommendQueryIdReceived||(this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty"))},y.prototype.containsRefinement=function(e,t,r,n){return e||0!==t.length||0!==r.length||0!==n.length},y.prototype._hasDisjunctiveRefinements=function(e){return this.state.disjunctiveRefinements[e]&&this.state.disjunctiveRefinements[e].length>0},y.prototype._change=function(e){var t=e.state,r=e.isPageReset;t!==this.state&&(this.state=t,this.emit("change",{state:this.state,results:this.lastResults,isPageReset:r}))},y.prototype._recommendChange=function(e){var t=e.state;t!==this.recommendState&&(this.recommendState=t,this.emit("recommend:change",{search:{results:this.lastResults,state:this.state},recommend:{results:this.lastRecommendResults,state:this.recommendState}}))},y.prototype.clearCache=function(){return this.client.clearCache&&this.client.clearCache(),this},y.prototype.setClient=function(e){return this.client===e||("function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.client=e),this},y.prototype.getClient=function(){return this.client},y.prototype.derive=function(e,t){var r=new i(this,e,t);return this.derivedHelpers.push(r),r},y.prototype.detachDerivedHelper=function(e){var t=this.derivedHelpers.indexOf(e);if(-1===t)throw Error("Derived helper already detached");this.derivedHelpers.splice(t,1)},y.prototype.hasPendingRequests=function(){return this._currentNbQueries>0},e.exports=y},75295:function(e){"use strict";e.exports=function(e){return Array.isArray(e)?e.filter(Boolean):[]}},26227:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){void 0!==t[r]&&(void 0!==e[r]&&delete e[r],e[r]=t[r])}),e},{})}},42312:function(e){"use strict";e.exports={escapeFacetValue:function(e){return"string"!=typeof e?e:String(e).replace(/^-/,"\\-")},unescapeFacetValue:function(e){return"string"!=typeof e?e:e.replace(/^\\-/,"-")}}},80040:function(e){"use strict";e.exports=function(e,t){if(Array.isArray(e)){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}}},41260:function(e){"use strict";e.exports=function(e,t){if(!Array.isArray(e))return -1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return -1}},73719:function(e){"use strict";e.exports=function(e){return e.reduce(function(e,t){return e.concat(t)},[])}},22524:function(e,t,r){"use strict";var n=r(80040);e.exports=function(e,t){var r=(t||[]).map(function(e){return e.split(":")});return e.reduce(function(e,t){var i=t.split(":"),a=n(r,function(e){return e[0]===i[0]});return i.length>1||!a?(e[0].push(i[0]),e[1].push(i[1])):(e[0].push(a[0]),e[1].push(a[1])),e},[[],[]])}},24176:function(e){"use strict";e.exports=function(e,t){e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}},53740:function(e){"use strict";e.exports=function(e,t){return e.filter(function(r,n){return t.indexOf(r)>-1&&e.indexOf(r)===n})}},99465:function(e){"use strict";function t(e){return"function"==typeof e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}e.exports=function(e){t(e)||(e={});for(var r=1,n=arguments.length;r<n;r++){var i=arguments[r];t(i)&&function e(r,n){if(r===n)return r;for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&"__proto__"!==i&&"constructor"!==i){var a=n[i],s=r[i];(void 0===s||void 0!==a)&&(t(s)&&t(a)?r[i]=e(s,a):r[i]="object"==typeof a&&null!==a?e(Array.isArray(a)?[]:{},a):a)}return r}(e,i)}return e}},97540:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){var n="number"==typeof e[r]?e[r]:0,i=t[r];void 0!==i&&i>=n&&(void 0!==e[r]&&delete e[r],e[r]=i)}),e},{})}},77160:function(e){"use strict";e.exports=function(e){return e&&Object.keys(e).length>0}},635:function(e){"use strict";e.exports=function(e,t){if(null===e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}},92590:function(e){"use strict";e.exports=function(e,t,r){if(!Array.isArray(e))return[];Array.isArray(r)||(r=[]);var n=e.map(function(e,r){return{criteria:t.map(function(t){return e[t]}),index:r,value:e}});return n.sort(function(e,t){for(var n=-1;++n<e.criteria.length;){var i=function(e,t){if(e!==t){var r=void 0!==e,n=null===e,i=void 0!==t,a=null===t;if(!a&&e>t||n&&i||!r)return 1;if(!n&&e<t||a&&r||!i)return -1}return 0}(e.criteria[n],t.criteria[n]);if(i){if(n>=r.length)return i;if("desc"===r[n])return-i;return i}}return e.index-t.index}),n.map(function(e){return e.value})}},44566:function(e){"use strict";e.exports=function e(t){if("number"==typeof t)return t;if("string"==typeof t)return parseFloat(t);if(Array.isArray(t))return t.map(e);throw Error("The value should be a number, a parsable string or an array of those.")}},73799:function(e,t,r){"use strict";var n=r(99465);function i(e){return Object.keys(e).sort().reduce(function(t,r){return t[r]=e[r],t},{})}var a={_getQueries:function(e,t){var r=[];return r.push({indexName:e,params:a._getHitsSearchParams(t)}),t.getRefinedDisjunctiveFacets().forEach(function(n){r.push({indexName:e,params:a._getDisjunctiveFacetSearchParams(t,n)})}),t.getRefinedHierarchicalFacets().forEach(function(n){var i=t.getHierarchicalFacetByName(n),s=t.getHierarchicalRefinement(n),o=t._getHierarchicalFacetSeparator(i);if(s.length>0&&s[0].split(o).length>1){var c=s[0].split(o).slice(0,-1).reduce(function(e,t,r){return e.concat({attribute:i.attributes[r],value:0===r?t:[e[e.length-1].value,t].join(o)})},[]);c.forEach(function(n,s){var o=a._getDisjunctiveFacetSearchParams(t,n.attribute,0===s);function u(e){return i.attributes.some(function(t){return t===e.split(":")[0]})}var l=(o.facetFilters||[]).reduce(function(e,t){if(Array.isArray(t)){var r=t.filter(function(e){return!u(e)});r.length>0&&e.push(r)}return"string"!=typeof t||u(t)||e.push(t),e},[]),h=c[s-1];s>0?o.facetFilters=l.concat(h.attribute+":"+h.value):l.length>0?o.facetFilters=l:delete o.facetFilters,r.push({indexName:e,params:o})})}}),r},_getCompositionQueries:function(e){return[{compositionID:e.index,requestBody:{params:a._getCompositionHitsSearchParams(e)}}]},_getHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),s=a._getNumericFilters(e),o=a._getTagFilters(e),c={};return t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),o.length>0&&(c.tagFilters=o),r.length>0&&(c.facetFilters=r),s.length>0&&(c.numericFilters=s),i(n({},e.getQueryParams(),c))},_getCompositionHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets.map(function(e){return"disjunctive("+e+")"})).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),s=a._getNumericFilters(e),o=a._getTagFilters(e),c={};t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),o.length>0&&(c.tagFilters=o),r.length>0&&(c.facetFilters=r),s.length>0&&(c.numericFilters=s);var u=e.getQueryParams();return delete u.highlightPreTag,delete u.highlightPostTag,delete u.index,i(n({},u,c))},_getDisjunctiveFacetSearchParams:function(e,t,r){var s=a._getFacetFilters(e,t,r),o=a._getNumericFilters(e,t),c=a._getTagFilters(e),u={hitsPerPage:0,page:0,analytics:!1,clickAnalytics:!1};c.length>0&&(u.tagFilters=c);var l=e.getHierarchicalFacetByName(t);return l?u.facets=a._getDisjunctiveHierarchicalFacetAttribute(e,l,r):u.facets=t,o.length>0&&(u.numericFilters=o),s.length>0&&(u.facetFilters=s),i(n({},e.getQueryParams(),u))},_getNumericFilters:function(e,t){if(e.numericFilters)return e.numericFilters;var r=[];return Object.keys(e.numericRefinements).forEach(function(n){var i=e.numericRefinements[n]||{};Object.keys(i).forEach(function(e){var a=i[e]||[];t!==n&&a.forEach(function(t){if(Array.isArray(t)){var i=t.map(function(t){return n+e+t});r.push(i)}else r.push(n+e+t)})})}),r},_getTagFilters:function(e){return e.tagFilters?e.tagFilters:e.tagRefinements.join(",")},_getFacetFilters:function(e,t,r){var n=[],i=e.facetsRefinements||{};Object.keys(i).sort().forEach(function(e){(i[e]||[]).slice().sort().forEach(function(t){n.push(e+":"+t)})});var a=e.facetsExcludes||{};Object.keys(a).sort().forEach(function(e){(a[e]||[]).sort().forEach(function(t){n.push(e+":-"+t)})});var s=e.disjunctiveFacetsRefinements||{};Object.keys(s).sort().forEach(function(e){var r=s[e]||[];if(e!==t&&r&&0!==r.length){var i=[];r.slice().sort().forEach(function(t){i.push(e+":"+t)}),n.push(i)}});var o=e.hierarchicalFacetsRefinements||{};return Object.keys(o).sort().forEach(function(i){var a,s,c=(o[i]||[])[0];if(void 0!==c){var u=e.getHierarchicalFacetByName(i),l=e._getHierarchicalFacetSeparator(u),h=e._getHierarchicalRootPath(u);if(t===i){if(-1===c.indexOf(l)||!h&&!0===r||h&&h.split(l).length===c.split(l).length)return;h?(s=h.split(l).length-1,c=h):(s=c.split(l).length-2,c=c.slice(0,c.lastIndexOf(l))),a=u.attributes[s]}else s=c.split(l).length-1,a=u.attributes[s];a&&n.push([a+":"+c])}}),n},_getHitsHierarchicalFacetsAttributes:function(e){return e.hierarchicalFacets.reduce(function(t,r){var n=e.getHierarchicalRefinement(r.name)[0];if(!n)return t.push(r.attributes[0]),t;var i=e._getHierarchicalFacetSeparator(r),a=n.split(i).length,s=r.attributes.slice(0,a+1);return t.concat(s)},[])},_getDisjunctiveHierarchicalFacetAttribute:function(e,t,r){var n=e._getHierarchicalFacetSeparator(t);if(!0===r){var i=e._getHierarchicalRootPath(t),a=0;return i&&(a=i.split(n).length),[t.attributes[a]]}var s=(e.getHierarchicalRefinement(t.name)[0]||"").split(n).length-1;return t.attributes.slice(0,s+1)},getSearchForFacetQuery:function(e,t,r,s){var o=s.isDisjunctiveFacet(e)?s.clearRefinements(e):s,c={facetQuery:t,facetName:e};return"number"==typeof r&&(c.maxFacetHits=r),i(n({},a._getHitsSearchParams(o),c))}};e.exports=a},87899:function(e){"use strict";e.exports=function(e){return null!==e&&/^[a-zA-Z0-9_-]{1,64}$/.test(e)}},2880:function(e,t,r){"use strict";var n=r(80040),i=r(73719);e.exports=function(e){var t,r,a={};return e.forEach(function(e){e.forEach(function(e,t){a[e.objectID]?a[e.objectID]={indexSum:a[e.objectID].indexSum+t,count:a[e.objectID].count+1}:a[e.objectID]={indexSum:t,count:1}})}),(t=e.length,r=[],Object.keys(a).forEach(function(e){a[e].count<2&&(a[e].indexSum+=100),r.push({objectID:e,avgOfIndices:a[e].indexSum/t})}),r.sort(function(e,t){return e.avgOfIndices>t.avgOfIndices?1:-1})).reduce(function(t,r){var a=n(i(e),function(e){return e.objectID===r.objectID});return a?t.concat(a):t},[])}},79700:function(e){"use strict";e.exports="3.23.1"},10221:function(e,t,r){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(2265),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,s=n.useEffect,o=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,l=n[1];return o(function(){i.value=r,i.getSnapshot=t,u(i)&&l({inst:i})},[e,r,t]),s(function(){return u(i)&&l({inst:i}),e(function(){u(i)&&l({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},22362:function(e,t,r){"use strict";e.exports=r(10221)},25566:function(e){var t,r,n,i=e.exports={};function a(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var c=[],u=!1,l=-1;function h(){u&&n&&(u=!1,n.length?c=n.concat(c):l=-1,c.length&&f())}function f(){if(!u){var e=o(h);u=!0;for(var t=c.length;t;){for(n=c,c=[];++l<t;)n&&n[l].run();l=-1,t=c.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new d(e,t)),1!==c.length||u||o(f)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}},16480:function(e,t){"use strict";var r;!/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/function(){var n={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=a(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=a(t,r));return t}(r)))}return e}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0!==(r=(function(){return i}).apply(t,[]))&&(e.exports=r)}()},14749:function(e,t,r){"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,{Z:function(){return n}})},70444:function(e,t,r){"use strict";function n(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}r.d(t,{Z:function(){return n}})},93855:function(e,t,r){"use strict";r.d(t,{x:function(){return ef}});var n={};r.r(n),r.d(n,{abtestingClient:function(){return D},apiClientVersion:function(){return T}});var i={};r.r(i),r.d(i,{analyticsClient:function(){return k},apiClientVersion:function(){return _}});var a={};r.r(a),r.d(a,{apiClientVersion:function(){return A},insightsClient:function(){return H}});var s={};r.r(s),r.d(s,{apiClientVersion:function(){return U},personalizationClient:function(){return B}});var o={};r.r(o),r.d(o,{apiClientVersion:function(){return L},querySuggestionsClient:function(){return $}});var c={};r.r(c),r.d(c,{apiClientVersion:function(){return V},searchClient:function(){return G}});var u={};r.r(u),r.d(u,{apiClientVersion:function(){return M},ingestionClient:function(){return Y},isOnDemandTrigger:function(){return z},isScheduleTrigger:function(){return J},isSubscriptionTrigger:function(){return Z}});var l={};r.r(l),r.d(l,{apiClientVersion:function(){return X},monitoringClient:function(){return ee}});var h={};function f(){return{send:function(e){return new Promise(t=>{let r=new XMLHttpRequest;r.open(e.method,e.url,!0),Object.keys(e.headers).forEach(t=>r.setRequestHeader(t,e.headers[t]));let n=(e,n)=>setTimeout(()=>{r.abort(),t({status:0,content:n,isTimedOut:!0})},e),i=n(e.connectTimeout,"Connection timeout"),a;r.onreadystatechange=()=>{r.readyState>r.OPENED&&void 0===a&&(clearTimeout(i),a=n(e.responseTimeout,"Socket timeout"))},r.onerror=()=>{0===r.status&&(clearTimeout(i),clearTimeout(a),t({content:r.responseText||"Network request failed",status:r.status,isTimedOut:!1}))},r.onload=()=>{clearTimeout(i),clearTimeout(a),t({content:r.responseText,status:r.status,isTimedOut:!1})},r.send(e.data)})}}}function d(e){let t;let r="algolia-client-js-".concat(e.key);function n(){return void 0===t&&(t=e.localStorage||window.localStorage),t}function i(){return JSON.parse(n().getItem(r)||"{}")}function a(e){n().setItem(r,JSON.stringify(e))}return{get(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:()=>Promise.resolve()};return Promise.resolve().then(()=>(!function(){let t=e.timeToLive?1e3*e.timeToLive:null,r=Object.fromEntries(Object.entries(i()).filter(e=>{let[,t]=e;return void 0!==t.timestamp}));a(r),t&&a(Object.fromEntries(Object.entries(r).filter(e=>{let[,r]=e,n=new Date().getTime();return!(r.timestamp+t<n)})))}(),i()[JSON.stringify(t)])).then(e=>Promise.all([e?e.value:r(),void 0!==e])).then(e=>{let[t,r]=e;return Promise.all([t,r||n.miss(t)])}).then(e=>{let[t]=e;return t})},set:(e,t)=>Promise.resolve().then(()=>{let a=i();return a[JSON.stringify(e)]={timestamp:new Date().getTime(),value:t},n().setItem(r,JSON.stringify(a)),t}),delete:e=>Promise.resolve().then(()=>{let t=i();delete t[JSON.stringify(e)],n().setItem(r,JSON.stringify(t))}),clear:()=>Promise.resolve().then(()=>{n().removeItem(r)})}}function m(e){let t=[...e.caches],r=t.shift();return void 0===r?{get(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:()=>Promise.resolve()};return t().then(e=>Promise.all([e,r.miss(e)])).then(e=>{let[t]=e;return t})},set:(e,t)=>Promise.resolve(t),delete:e=>Promise.resolve(),clear:()=>Promise.resolve()}:{get(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:()=>Promise.resolve()};return r.get(e,n,i).catch(()=>m({caches:t}).get(e,n,i))},set:(e,n)=>r.set(e,n).catch(()=>m({caches:t}).set(e,n)),delete:e=>r.delete(e).catch(()=>m({caches:t}).delete(e)),clear:()=>r.clear().catch(()=>m({caches:t}).clear())}}function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{serializable:!0},t={};return{get(r,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:()=>Promise.resolve()},a=JSON.stringify(r);if(a in t)return Promise.resolve(e.serializable?JSON.parse(t[a]):t[a]);let s=n();return s.then(e=>i.miss(e)).then(()=>s)},set:(r,n)=>(t[JSON.stringify(r)]=e.serializable?JSON.stringify(n):n,Promise.resolve(n)),delete:e=>(delete t[JSON.stringify(e)],Promise.resolve()),clear:()=>(t={},Promise.resolve())}}function g(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"WithinHeaders",n={"x-algolia-api-key":t,"x-algolia-application-id":e};return{headers:()=>"WithinHeaders"===r?n:{},queryParameters:()=>"WithinQueryParameters"===r?n:{}}}function y(e){let{func:t,validate:r,aggregator:n,error:i,timeout:a=()=>0}=e,s=e=>new Promise((o,c)=>{t(e).then(async e=>(n&&await n(e),await r(e))?o(e):i&&await i.validate(e)?c(Error(await i.message(e))):setTimeout(()=>{s(e).then(o).catch(c)},await a())).catch(e=>{c(e)})});return s()}function v(e){let{algoliaAgents:t,client:r,version:n}=e,i=(function(e){let t={value:"Algolia for JavaScript (".concat(e,")"),add(e){let r="; ".concat(e.segment).concat(void 0!==e.version?" (".concat(e.version,")"):"");return -1===t.value.indexOf(r)&&(t.value="".concat(t.value).concat(r)),t}};return t})(n).add({segment:r,version:n});return t.forEach(e=>i.add(e)),i}function b(){return{debug:(e,t)=>Promise.resolve(),info:(e,t)=>Promise.resolve(),error:(e,t)=>Promise.resolve()}}function P(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"up",r=Date.now();return{...e,status:t,lastUpdate:r,isUp:function(){return"up"===t||Date.now()-r>12e4},isTimedOut:function(){return"timed out"===t&&Date.now()-r<=12e4}}}r.r(h),r.d(h,{apiClientVersion:function(){return et},recommendClient:function(){return er}});var w=class extends Error{constructor(e,t){super(e),this.name="AlgoliaError",t&&(this.name=t)}},R=class extends w{constructor(e,t,r){super(e,r),this.stackTrace=t}},S=class extends R{constructor(e){super("Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",e,"RetryError")}},E=class extends R{constructor(e,t,r,n="ApiError"){super(e,r,n),this.status=t}},j=class extends w{constructor(e,t){super(e,"DeserializationError"),this.response=t}},O=class extends E{constructor(e,t,r,n){super(e,t,n,"DetailedApiError"),this.error=r}};function x(e){for(let t=e.length-1;t>0;t--){let r=Math.floor(Math.random()*(t+1)),n=e[t];e[t]=e[r],e[r]=n}return e}function F(e){let t=e.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return{...e,request:{...e.request,headers:{...e.request.headers,...t}}}}function q(e){let{hosts:t,hostsCache:r,baseHeaders:n,logger:i,baseQueryParameters:a,algoliaAgent:s,timeouts:o,requester:c,requestsCache:u,responsesCache:l}=e;async function h(e){let t=await Promise.all(e.map(e=>r.get(e,()=>Promise.resolve(P(e))))),n=t.filter(e=>e.isUp()),i=t.filter(e=>e.isTimedOut()),a=[...n,...i];return{hosts:a.length>0?a:e,getTimeout:(e,t)=>(0===i.length&&0===e?1:i.length+3+e)*t}}async function f(e,u){let l=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=[],d=function(e,t){if("GET"!==e.method&&(void 0!==e.data||void 0!==t.data))return JSON.stringify(Array.isArray(e.data)?e.data:{...e.data,...t.data})}(e,u),m=function(e,t,r){let n={Accept:"application/json",...e,...t,...r},i={};return Object.keys(n).forEach(e=>{let t=n[e];i[e.toLowerCase()]=t}),i}(n,e.headers,u.headers),p="GET"===e.method?{...e.data,...u.data}:{},g={...a,...e.queryParameters,...p};if(s.value&&(g["x-algolia-agent"]=s.value),u&&u.queryParameters)for(let e of Object.keys(u.queryParameters))u.queryParameters[e]&&"[object Object]"!==Object.prototype.toString.call(u.queryParameters[e])?g[e]=u.queryParameters[e].toString():g[e]=u.queryParameters[e];let y=0,v=async(t,n)=>{let a=t.pop();if(void 0===a)throw new S(f.map(e=>F(e)));let s={...o,...u.timeouts},h={data:d,headers:m,method:e.method,url:function(e,t,r){let n=Object.keys(r).filter(e=>void 0!==r[e]).sort().map(e=>"".concat(e,"=").concat(encodeURIComponent("[object Array]"===Object.prototype.toString.call(r[e])?r[e].join(","):r[e]).replace(/\+/g,"%20"))).join("&"),i="".concat(e.protocol,"://").concat(e.url).concat(e.port?":".concat(e.port):"","/").concat("/"===t.charAt(0)?t.substring(1):t);return n.length&&(i+="?".concat(n)),i}(a,e.path,g),connectTimeout:n(y,s.connect),responseTimeout:n(y,l?s.read:s.write)},p=e=>{let r={request:h,response:e,host:a,triesLeft:t.length};return f.push(r),r},b=await c.send(h);if(function(e){let{isTimedOut:t,status:r}=e;return t||function(e){let{isTimedOut:t,status:r}=e;return!t&&0==~~r}({isTimedOut:t,status:r})||2!=~~(r/100)&&4!=~~(r/100)}(b)){let e=p(b);return b.isTimedOut&&y++,i.info("Retryable failure",F(e)),await r.set(a,P(a,b.isTimedOut?"timed out":"down")),v(t,n)}if(function(e){let{status:t}=e;return 2==~~(t/100)}(b))return function(e){try{return JSON.parse(e.content)}catch(t){throw new j(t.message,e)}}(b);throw p(b),function(e,t){let{content:r,status:n}=e;try{let e=JSON.parse(r);if("error"in e)return new O(e.message,n,e.error,t);return new E(e.message,n,t)}catch(e){}return new E(r,n,t)}(b,f)},b=t.filter(e=>"readWrite"===e.accept||(l?"read"===e.accept:"write"===e.accept)),w=await h(b);return v([...w.hosts].reverse(),w.getTimeout)}return{hostsCache:r,requester:c,timeouts:o,logger:i,algoliaAgent:s,baseHeaders:n,baseQueryParameters:a,hosts:t,request:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.useReadTransporter||"GET"===e.method;if(!r)return f(e,t,r);let i=()=>f(e,t);if(!0!==(t.cacheable||e.cacheable))return i();let s={request:e,requestOptions:t,transporter:{queryParameters:a,headers:n}};return l.get(s,()=>u.get(s,()=>u.set(s,i()).then(e=>Promise.all([u.delete(s),e]),e=>Promise.all([u.delete(s),Promise.reject(e)])).then(e=>{let[t,r]=e;return r})),{miss:e=>l.set(s,e)})},requestsCache:u,responsesCache:l}}var T="5.20.0",I=["de","us"];function D(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(r&&("string"!=typeof r||!I.includes(r)))throw Error("`region` must be one of the following: ".concat(I.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:a?"analytics.{region}.algolia.com".replace("{region}",a):"analytics.algolia.com",accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"Abtesting",version:T}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},addABTests(e,t){if(!e)throw Error("Parameter `addABTestsRequest` is required when calling `addABTests`.");if(!e.name)throw Error("Parameter `addABTestsRequest.name` is required when calling `addABTests`.");if(!e.variants)throw Error("Parameter `addABTestsRequest.variants` is required when calling `addABTests`.");if(!e.endAt)throw Error("Parameter `addABTestsRequest.endAt` is required when calling `addABTests`.");return c.request({method:"POST",path:"/2/abtests",queryParameters:{},headers:{},data:e},t)},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteABTest(e,t){let{id:r}=e;if(!r)throw Error("Parameter `id` is required when calling `deleteABTest`.");let n="/2/abtests/{id}".replace("{id}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},estimateABTest(e,t){if(!e)throw Error("Parameter `estimateABTestRequest` is required when calling `estimateABTest`.");if(!e.configuration)throw Error("Parameter `estimateABTestRequest.configuration` is required when calling `estimateABTest`.");if(!e.variants)throw Error("Parameter `estimateABTestRequest.variants` is required when calling `estimateABTest`.");return c.request({method:"POST",path:"/2/abtests/estimate",queryParameters:{},headers:{},data:e},t)},getABTest(e,t){let{id:r}=e;if(!r)throw Error("Parameter `id` is required when calling `getABTest`.");let n="/2/abtests/{id}".replace("{id}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},listABTests(){let{offset:e,limit:t,indexPrefix:r,indexSuffix:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a={};return void 0!==e&&(a.offset=e.toString()),void 0!==t&&(a.limit=t.toString()),void 0!==r&&(a.indexPrefix=r.toString()),void 0!==n&&(a.indexSuffix=n.toString()),c.request({method:"GET",path:"/2/abtests",queryParameters:a,headers:{}},i)},scheduleABTest(e,t){if(!e)throw Error("Parameter `scheduleABTestsRequest` is required when calling `scheduleABTest`.");if(!e.name)throw Error("Parameter `scheduleABTestsRequest.name` is required when calling `scheduleABTest`.");if(!e.variants)throw Error("Parameter `scheduleABTestsRequest.variants` is required when calling `scheduleABTest`.");if(!e.scheduledAt)throw Error("Parameter `scheduleABTestsRequest.scheduledAt` is required when calling `scheduleABTest`.");if(!e.endAt)throw Error("Parameter `scheduleABTestsRequest.endAt` is required when calling `scheduleABTest`.");return c.request({method:"POST",path:"/2/abtests/schedule",queryParameters:{},headers:{},data:e},t)},stopABTest(e,t){let{id:r}=e;if(!r)throw Error("Parameter `id` is required when calling `stopABTest`.");let n="/2/abtests/{id}/stop".replace("{id}",encodeURIComponent(r));return c.request({method:"POST",path:n,queryParameters:{},headers:{}},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(T,"-").concat(e)}),p()]}),...n})}var _="5.20.0",C=["de","us"];function k(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(r&&("string"!=typeof r||!C.includes(r)))throw Error("`region` must be one of the following: ".concat(C.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:a?"analytics.{region}.algolia.com".replace("{region}",a):"analytics.algolia.com",accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"Analytics",version:_}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},getAddToCartRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getAddToCartRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/conversions/addToCartRate",queryParameters:s,headers:{}},t)},getAverageClickPosition(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getAverageClickPosition`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/clicks/averageClickPosition",queryParameters:s,headers:{}},t)},getClickPositions(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getClickPositions`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/clicks/positions",queryParameters:s,headers:{}},t)},getClickThroughRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getClickThroughRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/clicks/clickThroughRate",queryParameters:s,headers:{}},t)},getConversionRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getConversionRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/conversions/conversionRate",queryParameters:s,headers:{}},t)},getNoClickRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getNoClickRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/searches/noClickRate",queryParameters:s,headers:{}},t)},getNoResultsRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getNoResultsRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/searches/noResultRate",queryParameters:s,headers:{}},t)},getPurchaseRate(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getPurchaseRate`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/conversions/purchaseRate",queryParameters:s,headers:{}},t)},getRevenue(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getRevenue`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/conversions/revenue",queryParameters:s,headers:{}},t)},getSearchesCount(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getSearchesCount`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/searches/count",queryParameters:s,headers:{}},t)},getSearchesNoClicks(e,t){let{index:r,startDate:n,endDate:i,limit:a,offset:s,tags:o}=e;if(!r)throw Error("Parameter `index` is required when calling `getSearchesNoClicks`.");let u={};return void 0!==r&&(u.index=r.toString()),void 0!==n&&(u.startDate=n.toString()),void 0!==i&&(u.endDate=i.toString()),void 0!==a&&(u.limit=a.toString()),void 0!==s&&(u.offset=s.toString()),void 0!==o&&(u.tags=o.toString()),c.request({method:"GET",path:"/2/searches/noClicks",queryParameters:u,headers:{}},t)},getSearchesNoResults(e,t){let{index:r,startDate:n,endDate:i,limit:a,offset:s,tags:o}=e;if(!r)throw Error("Parameter `index` is required when calling `getSearchesNoResults`.");let u={};return void 0!==r&&(u.index=r.toString()),void 0!==n&&(u.startDate=n.toString()),void 0!==i&&(u.endDate=i.toString()),void 0!==a&&(u.limit=a.toString()),void 0!==s&&(u.offset=s.toString()),void 0!==o&&(u.tags=o.toString()),c.request({method:"GET",path:"/2/searches/noResults",queryParameters:u,headers:{}},t)},getStatus(e,t){let{index:r}=e;if(!r)throw Error("Parameter `index` is required when calling `getStatus`.");let n={};return void 0!==r&&(n.index=r.toString()),c.request({method:"GET",path:"/2/status",queryParameters:n,headers:{}},t)},getTopCountries(e,t){let{index:r,startDate:n,endDate:i,limit:a,offset:s,tags:o}=e;if(!r)throw Error("Parameter `index` is required when calling `getTopCountries`.");let u={};return void 0!==r&&(u.index=r.toString()),void 0!==n&&(u.startDate=n.toString()),void 0!==i&&(u.endDate=i.toString()),void 0!==a&&(u.limit=a.toString()),void 0!==s&&(u.offset=s.toString()),void 0!==o&&(u.tags=o.toString()),c.request({method:"GET",path:"/2/countries",queryParameters:u,headers:{}},t)},getTopFilterAttributes(e,t){let{index:r,search:n,startDate:i,endDate:a,limit:s,offset:o,tags:u}=e;if(!r)throw Error("Parameter `index` is required when calling `getTopFilterAttributes`.");let l={};return void 0!==r&&(l.index=r.toString()),void 0!==n&&(l.search=n.toString()),void 0!==i&&(l.startDate=i.toString()),void 0!==a&&(l.endDate=a.toString()),void 0!==s&&(l.limit=s.toString()),void 0!==o&&(l.offset=o.toString()),void 0!==u&&(l.tags=u.toString()),c.request({method:"GET",path:"/2/filters",queryParameters:l,headers:{}},t)},getTopFilterForAttribute(e,t){let{attribute:r,index:n,search:i,startDate:a,endDate:s,limit:o,offset:u,tags:l}=e;if(!r)throw Error("Parameter `attribute` is required when calling `getTopFilterForAttribute`.");if(!n)throw Error("Parameter `index` is required when calling `getTopFilterForAttribute`.");let h="/2/filters/{attribute}".replace("{attribute}",encodeURIComponent(r)),f={};return void 0!==n&&(f.index=n.toString()),void 0!==i&&(f.search=i.toString()),void 0!==a&&(f.startDate=a.toString()),void 0!==s&&(f.endDate=s.toString()),void 0!==o&&(f.limit=o.toString()),void 0!==u&&(f.offset=u.toString()),void 0!==l&&(f.tags=l.toString()),c.request({method:"GET",path:h,queryParameters:f,headers:{}},t)},getTopFiltersNoResults(e,t){let{index:r,search:n,startDate:i,endDate:a,limit:s,offset:o,tags:u}=e;if(!r)throw Error("Parameter `index` is required when calling `getTopFiltersNoResults`.");let l={};return void 0!==r&&(l.index=r.toString()),void 0!==n&&(l.search=n.toString()),void 0!==i&&(l.startDate=i.toString()),void 0!==a&&(l.endDate=a.toString()),void 0!==s&&(l.limit=s.toString()),void 0!==o&&(l.offset=o.toString()),void 0!==u&&(l.tags=u.toString()),c.request({method:"GET",path:"/2/filters/noResults",queryParameters:l,headers:{}},t)},getTopHits(e,t){let{index:r,search:n,clickAnalytics:i,revenueAnalytics:a,startDate:s,endDate:o,limit:u,offset:l,tags:h}=e;if(!r)throw Error("Parameter `index` is required when calling `getTopHits`.");let f={};return void 0!==r&&(f.index=r.toString()),void 0!==n&&(f.search=n.toString()),void 0!==i&&(f.clickAnalytics=i.toString()),void 0!==a&&(f.revenueAnalytics=a.toString()),void 0!==s&&(f.startDate=s.toString()),void 0!==o&&(f.endDate=o.toString()),void 0!==u&&(f.limit=u.toString()),void 0!==l&&(f.offset=l.toString()),void 0!==h&&(f.tags=h.toString()),c.request({method:"GET",path:"/2/hits",queryParameters:f,headers:{}},t)},getTopSearches(e,t){let{index:r,clickAnalytics:n,revenueAnalytics:i,startDate:a,endDate:s,orderBy:o,direction:u,limit:l,offset:h,tags:f}=e;if(!r)throw Error("Parameter `index` is required when calling `getTopSearches`.");let d={};return void 0!==r&&(d.index=r.toString()),void 0!==n&&(d.clickAnalytics=n.toString()),void 0!==i&&(d.revenueAnalytics=i.toString()),void 0!==a&&(d.startDate=a.toString()),void 0!==s&&(d.endDate=s.toString()),void 0!==o&&(d.orderBy=o.toString()),void 0!==u&&(d.direction=u.toString()),void 0!==l&&(d.limit=l.toString()),void 0!==h&&(d.offset=h.toString()),void 0!==f&&(d.tags=f.toString()),c.request({method:"GET",path:"/2/searches",queryParameters:d,headers:{}},t)},getUsersCount(e,t){let{index:r,startDate:n,endDate:i,tags:a}=e;if(!r)throw Error("Parameter `index` is required when calling `getUsersCount`.");let s={};return void 0!==r&&(s.index=r.toString()),void 0!==n&&(s.startDate=n.toString()),void 0!==i&&(s.endDate=i.toString()),void 0!==a&&(s.tags=a.toString()),c.request({method:"GET",path:"/2/users/count",queryParameters:s,headers:{}},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(_,"-").concat(e)}),p()]}),...n})}var A="5.20.0",N=["de","us"];function H(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(r&&("string"!=typeof r||!N.includes(r)))throw Error("`region` must be one of the following: ".concat(N.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:a?"insights.{region}.algolia.io".replace("{region}",a):"insights.algolia.io",accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"Insights",version:A}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteUserToken(e,t){let{userToken:r}=e;if(!r)throw Error("Parameter `userToken` is required when calling `deleteUserToken`.");let n="/1/usertokens/{userToken}".replace("{userToken}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},pushEvents(e,t){if(!e)throw Error("Parameter `insightsEvents` is required when calling `pushEvents`.");if(!e.events)throw Error("Parameter `insightsEvents.events` is required when calling `pushEvents`.");return c.request({method:"POST",path:"/1/events",queryParameters:{},headers:{},data:e},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(A,"-").concat(e)}),p()]}),...n})}var U="5.20.0",Q=["eu","us"];function B(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(!r||r&&("string"!=typeof r||!Q.includes(r)))throw Error("`region` is required and must be one of the following: ".concat(Q.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:"personalization.{region}.algolia.com".replace("{region}",a),accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"Personalization",version:U}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteUserProfile(e,t){let{userToken:r}=e;if(!r)throw Error("Parameter `userToken` is required when calling `deleteUserProfile`.");let n="/1/profiles/{userToken}".replace("{userToken}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},getPersonalizationStrategy:e=>c.request({method:"GET",path:"/1/strategies/personalization",queryParameters:{},headers:{}},e),getUserTokenProfile(e,t){let{userToken:r}=e;if(!r)throw Error("Parameter `userToken` is required when calling `getUserTokenProfile`.");let n="/1/profiles/personalization/{userToken}".replace("{userToken}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},setPersonalizationStrategy(e,t){if(!e)throw Error("Parameter `personalizationStrategyParams` is required when calling `setPersonalizationStrategy`.");if(!e.eventsScoring)throw Error("Parameter `personalizationStrategyParams.eventsScoring` is required when calling `setPersonalizationStrategy`.");if(!e.facetsScoring)throw Error("Parameter `personalizationStrategyParams.facetsScoring` is required when calling `setPersonalizationStrategy`.");if(!e.personalizationImpact)throw Error("Parameter `personalizationStrategyParams.personalizationImpact` is required when calling `setPersonalizationStrategy`.");return c.request({method:"POST",path:"/1/strategies/personalization",queryParameters:{},headers:{},data:e},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(U,"-").concat(e)}),p()]}),...n})}var L="5.20.0",W=["eu","us"];function $(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(!r||r&&("string"!=typeof r||!W.includes(r)))throw Error("`region` is required and must be one of the following: ".concat(W.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:"query-suggestions.{region}.algolia.com".replace("{region}",a),accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"QuerySuggestions",version:L}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},createConfig(e,t){if(!e)throw Error("Parameter `configurationWithIndex` is required when calling `createConfig`.");return c.request({method:"POST",path:"/1/configs",queryParameters:{},headers:{},data:e},t)},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteConfig(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteConfig`.");let n="/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},getAllConfigs:e=>c.request({method:"GET",path:"/1/configs",queryParameters:{},headers:{}},e),getConfig(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getConfig`.");let n="/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getConfigStatus(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getConfigStatus`.");let n="/1/configs/{indexName}/status".replace("{indexName}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getLogFile(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getLogFile`.");let n="/1/logs/{indexName}".replace("{indexName}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},updateConfig(e,t){let{indexName:r,configuration:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `updateConfig`.");if(!n)throw Error("Parameter `configuration` is required when calling `updateConfig`.");if(!n.sourceIndices)throw Error("Parameter `configuration.sourceIndices` is required when calling `updateConfig`.");let i="/1/configs/{indexName}".replace("{indexName}",encodeURIComponent(r));return c.request({method:"PUT",path:i,queryParameters:{},headers:{},data:n},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(L,"-").concat(e)}),p()]}),...n})}var V="5.20.0";function G(e,t,r){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,...a}=e,s=g(t,r,n),o=q({hosts:[{url:"".concat(t,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(t,".algolia.net"),accept:"write",protocol:"https"}].concat(x([{url:"".concat(t,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}])),...a,algoliaAgent:v({algoliaAgents:i,client:"Search",version:V}),baseHeaders:{"content-type":"text/plain",...s.headers(),...a.baseHeaders},baseQueryParameters:{...s.queryParameters(),...a.baseQueryParameters}});return{transporter:o,appId:t,apiKey:r,clearCache:()=>Promise.all([o.requestsCache.clear(),o.responsesCache.clear()]).then(()=>void 0),get _ua(){return o.algoliaAgent.value},addAlgoliaAgent(e,t){o.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?o.baseQueryParameters["x-algolia-api-key"]=t:o.baseHeaders["x-algolia-api-key"]=t},waitForTask(e,t){let{indexName:r,taskID:n,maxRetries:i=50,timeout:a=e=>Math.min(200*e,5e3)}=e,s=0;return y({func:()=>this.getTask({indexName:r,taskID:n},t),validate:e=>"published"===e.status,aggregator:()=>s+=1,error:{validate:()=>s>=i,message:()=>"The maximum number of retries exceeded. (".concat(s,"/").concat(i,")")},timeout:()=>a(s)})},waitForAppTask(e,t){let{taskID:r,maxRetries:n=50,timeout:i=e=>Math.min(200*e,5e3)}=e,a=0;return y({func:()=>this.getAppTask({taskID:r},t),validate:e=>"published"===e.status,aggregator:()=>a+=1,error:{validate:()=>a>=n,message:()=>"The maximum number of retries exceeded. (".concat(a,"/").concat(n,")")},timeout:()=>i(a)})},waitForApiKey(e,t){let{operation:r,key:n,apiKey:i,maxRetries:a=50,timeout:s=e=>Math.min(200*e,5e3)}=e,o=0,c={aggregator:()=>o+=1,error:{validate:()=>o>=a,message:()=>"The maximum number of retries exceeded. (".concat(o,"/").concat(a,")")},timeout:()=>s(o)};if("update"===r){if(!i)throw Error("`apiKey` is required when waiting for an `update` operation.");return y({...c,func:()=>this.getApiKey({key:n},t),validate:e=>{for(let t of Object.keys(i)){let r=i[t],n=e[t];if(Array.isArray(r)&&Array.isArray(n)){if(r.length!==n.length||r.some((e,t)=>e!==n[t]))return!1}else if(r!==n)return!1}return!0}})}return y({...c,func:()=>this.getApiKey({key:n},t).catch(e=>{if(404!==e.status)throw e}),validate:e=>"add"===r?void 0!==e:void 0===e})},browseObjects(e,t){let{indexName:r,browseParams:n,...i}=e;return y({func:e=>this.browse({indexName:r,browseParams:{cursor:e?e.cursor:void 0,hitsPerPage:1e3,...n}},t),validate:e=>void 0===e.cursor,...i})},browseRules(e,t){let{indexName:r,searchRulesParams:n,...i}=e,a={hitsPerPage:1e3,...n};return y({func:e=>this.searchRules({indexName:r,searchRulesParams:{...a,page:e?e.page+1:a.page||0}},t),validate:e=>e.hits.length<a.hitsPerPage,...i})},browseSynonyms(e,t){let{indexName:r,searchSynonymsParams:n,...i}=e,a={page:0,...n,hitsPerPage:1e3};return y({func:e=>{let n=this.searchSynonyms({indexName:r,searchSynonymsParams:{...a,page:a.page}},t);return a.page+=1,n},validate:e=>e.hits.length<a.hitsPerPage,...i})},async chunkedBatch(e,t){let{indexName:r,objects:n,action:i="addObject",waitForTasks:a,batchSize:s=1e3}=e,o=[],c=[];for(let[e,a]of n.entries())o.push({action:i,body:a}),(o.length===s||e===n.length-1)&&(c.push(await this.batch({indexName:r,batchWriteParams:{requests:o}},t)),o=[]);if(a)for(let e of c)await this.waitForTask({indexName:r,taskID:e.taskID});return c},async saveObjects(e,t){let{indexName:r,objects:n,waitForTasks:i,batchSize:a}=e;return await this.chunkedBatch({indexName:r,objects:n,action:"addObject",waitForTasks:i,batchSize:a},t)},async deleteObjects(e,t){let{indexName:r,objectIDs:n,waitForTasks:i,batchSize:a}=e;return await this.chunkedBatch({indexName:r,objects:n.map(e=>({objectID:e})),action:"deleteObject",waitForTasks:i,batchSize:a},t)},async partialUpdateObjects(e,t){let{indexName:r,objects:n,createIfNotExists:i,waitForTasks:a,batchSize:s}=e;return await this.chunkedBatch({indexName:r,objects:n,action:i?"partialUpdateObject":"partialUpdateObjectNoCreate",batchSize:s,waitForTasks:a},t)},async replaceAllObjects(e,t){let{indexName:r,objects:n,batchSize:i,scopes:a}=e,s="".concat(r,"_tmp_").concat(Math.floor(1e6*Math.random())+1e5);void 0===a&&(a=["settings","rules","synonyms"]);try{let e=await this.operationIndex({indexName:r,operationIndexParams:{operation:"copy",destination:s,scope:a}},t),o=await this.chunkedBatch({indexName:s,objects:n,waitForTasks:!0,batchSize:i},t);await this.waitForTask({indexName:s,taskID:e.taskID}),e=await this.operationIndex({indexName:r,operationIndexParams:{operation:"copy",destination:s,scope:a}},t),await this.waitForTask({indexName:s,taskID:e.taskID});let c=await this.operationIndex({indexName:s,operationIndexParams:{operation:"move",destination:r}},t);return await this.waitForTask({indexName:s,taskID:c.taskID}),{copyOperationResponse:e,batchResponses:o,moveOperationResponse:c}}catch(e){throw await this.deleteIndex({indexName:s}),e}},async indexExists(e){let{indexName:t}=e;try{await this.getSettings({indexName:t})}catch(e){if(e instanceof E&&404===e.status)return!1;throw e}return!0},searchForHits(e,t){return this.search(e,t)},searchForFacets(e,t){return this.search(e,t)},addApiKey(e,t){if(!e)throw Error("Parameter `apiKey` is required when calling `addApiKey`.");if(!e.acl)throw Error("Parameter `apiKey.acl` is required when calling `addApiKey`.");return o.request({method:"POST",path:"/1/keys",queryParameters:{},headers:{},data:e},t)},addOrUpdateObject(e,t){let{indexName:r,objectID:n,body:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `addOrUpdateObject`.");if(!n)throw Error("Parameter `objectID` is required when calling `addOrUpdateObject`.");if(!i)throw Error("Parameter `body` is required when calling `addOrUpdateObject`.");let a="/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n));return o.request({method:"PUT",path:a,queryParameters:{},headers:{},data:i},t)},appendSource(e,t){if(!e)throw Error("Parameter `source` is required when calling `appendSource`.");if(!e.source)throw Error("Parameter `source.source` is required when calling `appendSource`.");return o.request({method:"POST",path:"/1/security/sources/append",queryParameters:{},headers:{},data:e},t)},assignUserId(e,t){let{xAlgoliaUserID:r,assignUserIdParams:n}=e;if(!r)throw Error("Parameter `xAlgoliaUserID` is required when calling `assignUserId`.");if(!n)throw Error("Parameter `assignUserIdParams` is required when calling `assignUserId`.");if(!n.cluster)throw Error("Parameter `assignUserIdParams.cluster` is required when calling `assignUserId`.");let i={};return void 0!==r&&(i["X-Algolia-User-ID"]=r.toString()),o.request({method:"POST",path:"/1/clusters/mapping",queryParameters:{},headers:i,data:n},t)},batch(e,t){let{indexName:r,batchWriteParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `batch`.");if(!n)throw Error("Parameter `batchWriteParams` is required when calling `batch`.");if(!n.requests)throw Error("Parameter `batchWriteParams.requests` is required when calling `batch`.");let i="/1/indexes/{indexName}/batch".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},batchAssignUserIds(e,t){let{xAlgoliaUserID:r,batchAssignUserIdsParams:n}=e;if(!r)throw Error("Parameter `xAlgoliaUserID` is required when calling `batchAssignUserIds`.");if(!n)throw Error("Parameter `batchAssignUserIdsParams` is required when calling `batchAssignUserIds`.");if(!n.cluster)throw Error("Parameter `batchAssignUserIdsParams.cluster` is required when calling `batchAssignUserIds`.");if(!n.users)throw Error("Parameter `batchAssignUserIdsParams.users` is required when calling `batchAssignUserIds`.");let i={};return void 0!==r&&(i["X-Algolia-User-ID"]=r.toString()),o.request({method:"POST",path:"/1/clusters/mapping/batch",queryParameters:{},headers:i,data:n},t)},batchDictionaryEntries(e,t){let{dictionaryName:r,batchDictionaryEntriesParams:n}=e;if(!r)throw Error("Parameter `dictionaryName` is required when calling `batchDictionaryEntries`.");if(!n)throw Error("Parameter `batchDictionaryEntriesParams` is required when calling `batchDictionaryEntries`.");if(!n.requests)throw Error("Parameter `batchDictionaryEntriesParams.requests` is required when calling `batchDictionaryEntries`.");let i="/1/dictionaries/{dictionaryName}/batch".replace("{dictionaryName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},browse(e,t){let{indexName:r,browseParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `browse`.");let i="/1/indexes/{indexName}/browse".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n||{},useReadTransporter:!0},t)},clearObjects(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `clearObjects`.");let n="/1/indexes/{indexName}/clear".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:n,queryParameters:{},headers:{}},t)},clearRules(e,t){let{indexName:r,forwardToReplicas:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `clearRules`.");let i="/1/indexes/{indexName}/rules/clear".replace("{indexName}",encodeURIComponent(r)),a={};return void 0!==n&&(a.forwardToReplicas=n.toString()),o.request({method:"POST",path:i,queryParameters:a,headers:{}},t)},clearSynonyms(e,t){let{indexName:r,forwardToReplicas:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `clearSynonyms`.");let i="/1/indexes/{indexName}/synonyms/clear".replace("{indexName}",encodeURIComponent(r)),a={};return void 0!==n&&(a.forwardToReplicas=n.toString()),o.request({method:"POST",path:i,queryParameters:a,headers:{}},t)},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return o.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return o.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return o.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return o.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteApiKey(e,t){let{key:r}=e;if(!r)throw Error("Parameter `key` is required when calling `deleteApiKey`.");let n="/1/keys/{key}".replace("{key}",encodeURIComponent(r));return o.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteBy(e,t){let{indexName:r,deleteByParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteBy`.");if(!n)throw Error("Parameter `deleteByParams` is required when calling `deleteBy`.");let i="/1/indexes/{indexName}/deleteByQuery".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},deleteIndex(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteIndex`.");let n="/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(r));return o.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteObject(e,t){let{indexName:r,objectID:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteObject`.");if(!n)throw Error("Parameter `objectID` is required when calling `deleteObject`.");let i="/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n));return o.request({method:"DELETE",path:i,queryParameters:{},headers:{}},t)},deleteRule(e,t){let{indexName:r,objectID:n,forwardToReplicas:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteRule`.");if(!n)throw Error("Parameter `objectID` is required when calling `deleteRule`.");let a="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),s={};return void 0!==i&&(s.forwardToReplicas=i.toString()),o.request({method:"DELETE",path:a,queryParameters:s,headers:{}},t)},deleteSource(e,t){let{source:r}=e;if(!r)throw Error("Parameter `source` is required when calling `deleteSource`.");let n="/1/security/sources/{source}".replace("{source}",encodeURIComponent(r));return o.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteSynonym(e,t){let{indexName:r,objectID:n,forwardToReplicas:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteSynonym`.");if(!n)throw Error("Parameter `objectID` is required when calling `deleteSynonym`.");let a="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),s={};return void 0!==i&&(s.forwardToReplicas=i.toString()),o.request({method:"DELETE",path:a,queryParameters:s,headers:{}},t)},getApiKey(e,t){let{key:r}=e;if(!r)throw Error("Parameter `key` is required when calling `getApiKey`.");let n="/1/keys/{key}".replace("{key}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getAppTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `getAppTask`.");let n="/1/task/{taskID}".replace("{taskID}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getDictionaryLanguages:e=>o.request({method:"GET",path:"/1/dictionaries/*/languages",queryParameters:{},headers:{}},e),getDictionarySettings:e=>o.request({method:"GET",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{}},e),getLogs(){let{offset:e,length:t,indexName:r,type:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a={};return void 0!==e&&(a.offset=e.toString()),void 0!==t&&(a.length=t.toString()),void 0!==r&&(a.indexName=r.toString()),void 0!==n&&(a.type=n.toString()),o.request({method:"GET",path:"/1/logs",queryParameters:a,headers:{}},i)},getObject(e,t){let{indexName:r,objectID:n,attributesToRetrieve:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getObject`.");if(!n)throw Error("Parameter `objectID` is required when calling `getObject`.");let a="/1/indexes/{indexName}/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),s={};return void 0!==i&&(s.attributesToRetrieve=i.toString()),o.request({method:"GET",path:a,queryParameters:s,headers:{}},t)},getObjects(e,t){if(!e)throw Error("Parameter `getObjectsParams` is required when calling `getObjects`.");if(!e.requests)throw Error("Parameter `getObjectsParams.requests` is required when calling `getObjects`.");return o.request({method:"POST",path:"/1/indexes/*/objects",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0},t)},getRule(e,t){let{indexName:r,objectID:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getRule`.");if(!n)throw Error("Parameter `objectID` is required when calling `getRule`.");let i="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n));return o.request({method:"GET",path:i,queryParameters:{},headers:{}},t)},getSettings(e,t){let{indexName:r}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getSettings`.");let n="/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getSources:e=>o.request({method:"GET",path:"/1/security/sources",queryParameters:{},headers:{}},e),getSynonym(e,t){let{indexName:r,objectID:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getSynonym`.");if(!n)throw Error("Parameter `objectID` is required when calling `getSynonym`.");let i="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n));return o.request({method:"GET",path:i,queryParameters:{},headers:{}},t)},getTask(e,t){let{indexName:r,taskID:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getTask`.");if(!n)throw Error("Parameter `taskID` is required when calling `getTask`.");let i="/1/indexes/{indexName}/task/{taskID}".replace("{indexName}",encodeURIComponent(r)).replace("{taskID}",encodeURIComponent(n));return o.request({method:"GET",path:i,queryParameters:{},headers:{}},t)},getTopUserIds:e=>o.request({method:"GET",path:"/1/clusters/mapping/top",queryParameters:{},headers:{}},e),getUserId(e,t){let{userID:r}=e;if(!r)throw Error("Parameter `userID` is required when calling `getUserId`.");let n="/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},hasPendingMappings(){let{getClusters:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r={};return void 0!==e&&(r.getClusters=e.toString()),o.request({method:"GET",path:"/1/clusters/mapping/pending",queryParameters:r,headers:{}},t)},listApiKeys:e=>o.request({method:"GET",path:"/1/keys",queryParameters:{},headers:{}},e),listClusters:e=>o.request({method:"GET",path:"/1/clusters",queryParameters:{},headers:{}},e),listIndices(){let{page:e,hitsPerPage:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n={};return void 0!==e&&(n.page=e.toString()),void 0!==t&&(n.hitsPerPage=t.toString()),o.request({method:"GET",path:"/1/indexes",queryParameters:n,headers:{}},r)},listUserIds(){let{page:e,hitsPerPage:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n={};return void 0!==e&&(n.page=e.toString()),void 0!==t&&(n.hitsPerPage=t.toString()),o.request({method:"GET",path:"/1/clusters/mapping",queryParameters:n,headers:{}},r)},multipleBatch(e,t){if(!e)throw Error("Parameter `batchParams` is required when calling `multipleBatch`.");if(!e.requests)throw Error("Parameter `batchParams.requests` is required when calling `multipleBatch`.");return o.request({method:"POST",path:"/1/indexes/*/batch",queryParameters:{},headers:{},data:e},t)},operationIndex(e,t){let{indexName:r,operationIndexParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `operationIndex`.");if(!n)throw Error("Parameter `operationIndexParams` is required when calling `operationIndex`.");if(!n.operation)throw Error("Parameter `operationIndexParams.operation` is required when calling `operationIndex`.");if(!n.destination)throw Error("Parameter `operationIndexParams.destination` is required when calling `operationIndex`.");let i="/1/indexes/{indexName}/operation".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},partialUpdateObject(e,t){let{indexName:r,objectID:n,attributesToUpdate:i,createIfNotExists:a}=e;if(!r)throw Error("Parameter `indexName` is required when calling `partialUpdateObject`.");if(!n)throw Error("Parameter `objectID` is required when calling `partialUpdateObject`.");if(!i)throw Error("Parameter `attributesToUpdate` is required when calling `partialUpdateObject`.");let s="/1/indexes/{indexName}/{objectID}/partial".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),c={};return void 0!==a&&(c.createIfNotExists=a.toString()),o.request({method:"POST",path:s,queryParameters:c,headers:{},data:i},t)},removeUserId(e,t){let{userID:r}=e;if(!r)throw Error("Parameter `userID` is required when calling `removeUserId`.");let n="/1/clusters/mapping/{userID}".replace("{userID}",encodeURIComponent(r));return o.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},replaceSources(e,t){let{source:r}=e;if(!r)throw Error("Parameter `source` is required when calling `replaceSources`.");return o.request({method:"PUT",path:"/1/security/sources",queryParameters:{},headers:{},data:r},t)},restoreApiKey(e,t){let{key:r}=e;if(!r)throw Error("Parameter `key` is required when calling `restoreApiKey`.");let n="/1/keys/{key}/restore".replace("{key}",encodeURIComponent(r));return o.request({method:"POST",path:n,queryParameters:{},headers:{}},t)},saveObject(e,t){let{indexName:r,body:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `saveObject`.");if(!n)throw Error("Parameter `body` is required when calling `saveObject`.");let i="/1/indexes/{indexName}".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},saveRule(e,t){let{indexName:r,objectID:n,rule:i,forwardToReplicas:a}=e;if(!r)throw Error("Parameter `indexName` is required when calling `saveRule`.");if(!n)throw Error("Parameter `objectID` is required when calling `saveRule`.");if(!i)throw Error("Parameter `rule` is required when calling `saveRule`.");if(!i.objectID)throw Error("Parameter `rule.objectID` is required when calling `saveRule`.");if(!i.consequence)throw Error("Parameter `rule.consequence` is required when calling `saveRule`.");let s="/1/indexes/{indexName}/rules/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),c={};return void 0!==a&&(c.forwardToReplicas=a.toString()),o.request({method:"PUT",path:s,queryParameters:c,headers:{},data:i},t)},saveRules(e,t){let{indexName:r,rules:n,forwardToReplicas:i,clearExistingRules:a}=e;if(!r)throw Error("Parameter `indexName` is required when calling `saveRules`.");if(!n)throw Error("Parameter `rules` is required when calling `saveRules`.");let s="/1/indexes/{indexName}/rules/batch".replace("{indexName}",encodeURIComponent(r)),c={};return void 0!==i&&(c.forwardToReplicas=i.toString()),void 0!==a&&(c.clearExistingRules=a.toString()),o.request({method:"POST",path:s,queryParameters:c,headers:{},data:n},t)},saveSynonym(e,t){let{indexName:r,objectID:n,synonymHit:i,forwardToReplicas:a}=e;if(!r)throw Error("Parameter `indexName` is required when calling `saveSynonym`.");if(!n)throw Error("Parameter `objectID` is required when calling `saveSynonym`.");if(!i)throw Error("Parameter `synonymHit` is required when calling `saveSynonym`.");if(!i.objectID)throw Error("Parameter `synonymHit.objectID` is required when calling `saveSynonym`.");if(!i.type)throw Error("Parameter `synonymHit.type` is required when calling `saveSynonym`.");let s="/1/indexes/{indexName}/synonyms/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{objectID}",encodeURIComponent(n)),c={};return void 0!==a&&(c.forwardToReplicas=a.toString()),o.request({method:"PUT",path:s,queryParameters:c,headers:{},data:i},t)},saveSynonyms(e,t){let{indexName:r,synonymHit:n,forwardToReplicas:i,replaceExistingSynonyms:a}=e;if(!r)throw Error("Parameter `indexName` is required when calling `saveSynonyms`.");if(!n)throw Error("Parameter `synonymHit` is required when calling `saveSynonyms`.");let s="/1/indexes/{indexName}/synonyms/batch".replace("{indexName}",encodeURIComponent(r)),c={};return void 0!==i&&(c.forwardToReplicas=i.toString()),void 0!==a&&(c.replaceExistingSynonyms=a.toString()),o.request({method:"POST",path:s,queryParameters:c,headers:{},data:n},t)},search(e,t){if(e&&Array.isArray(e)&&(e={requests:e.map(e=>{let{params:t,...r}=e;return"facet"===r.type?{...r,...t,type:"facet"}:{...r,...t,facet:void 0,maxFacetHits:void 0,facetQuery:void 0}})}),!e)throw Error("Parameter `searchMethodParams` is required when calling `search`.");if(!e.requests)throw Error("Parameter `searchMethodParams.requests` is required when calling `search`.");let r={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return o.request(r,t)},searchDictionaryEntries(e,t){let{dictionaryName:r,searchDictionaryEntriesParams:n}=e;if(!r)throw Error("Parameter `dictionaryName` is required when calling `searchDictionaryEntries`.");if(!n)throw Error("Parameter `searchDictionaryEntriesParams` is required when calling `searchDictionaryEntries`.");if(!n.query)throw Error("Parameter `searchDictionaryEntriesParams.query` is required when calling `searchDictionaryEntries`.");let i="/1/dictionaries/{dictionaryName}/search".replace("{dictionaryName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n,useReadTransporter:!0,cacheable:!0},t)},searchForFacetValues(e,t){let{indexName:r,facetName:n,searchForFacetValuesRequest:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `searchForFacetValues`.");if(!n)throw Error("Parameter `facetName` is required when calling `searchForFacetValues`.");let a="/1/indexes/{indexName}/facets/{facetName}/query".replace("{indexName}",encodeURIComponent(r)).replace("{facetName}",encodeURIComponent(n));return o.request({method:"POST",path:a,queryParameters:{},headers:{},data:i||{},useReadTransporter:!0,cacheable:!0},t)},searchRules(e,t){let{indexName:r,searchRulesParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `searchRules`.");let i="/1/indexes/{indexName}/rules/search".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n||{},useReadTransporter:!0,cacheable:!0},t)},searchSingleIndex(e,t){let{indexName:r,searchParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `searchSingleIndex`.");let i="/1/indexes/{indexName}/query".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n||{},useReadTransporter:!0,cacheable:!0},t)},searchSynonyms(e,t){let{indexName:r,searchSynonymsParams:n}=e;if(!r)throw Error("Parameter `indexName` is required when calling `searchSynonyms`.");let i="/1/indexes/{indexName}/synonyms/search".replace("{indexName}",encodeURIComponent(r));return o.request({method:"POST",path:i,queryParameters:{},headers:{},data:n||{},useReadTransporter:!0,cacheable:!0},t)},searchUserIds(e,t){if(!e)throw Error("Parameter `searchUserIdsParams` is required when calling `searchUserIds`.");if(!e.query)throw Error("Parameter `searchUserIdsParams.query` is required when calling `searchUserIds`.");return o.request({method:"POST",path:"/1/clusters/mapping/search",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0},t)},setDictionarySettings(e,t){if(!e)throw Error("Parameter `dictionarySettingsParams` is required when calling `setDictionarySettings`.");if(!e.disableStandardEntries)throw Error("Parameter `dictionarySettingsParams.disableStandardEntries` is required when calling `setDictionarySettings`.");return o.request({method:"PUT",path:"/1/dictionaries/*/settings",queryParameters:{},headers:{},data:e},t)},setSettings(e,t){let{indexName:r,indexSettings:n,forwardToReplicas:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `setSettings`.");if(!n)throw Error("Parameter `indexSettings` is required when calling `setSettings`.");let a="/1/indexes/{indexName}/settings".replace("{indexName}",encodeURIComponent(r)),s={};return void 0!==i&&(s.forwardToReplicas=i.toString()),o.request({method:"PUT",path:a,queryParameters:s,headers:{},data:n},t)},updateApiKey(e,t){let{key:r,apiKey:n}=e;if(!r)throw Error("Parameter `key` is required when calling `updateApiKey`.");if(!n)throw Error("Parameter `apiKey` is required when calling `updateApiKey`.");if(!n.acl)throw Error("Parameter `apiKey.acl` is required when calling `updateApiKey`.");let i="/1/keys/{key}".replace("{key}",encodeURIComponent(r));return o.request({method:"PUT",path:i,queryParameters:{},headers:{},data:n},t)}}}({appId:e,apiKey:t,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(V,"-").concat(e)}),p()]}),...r})}var M="1.20.0",K=["eu","us"];function z(e){return"onDemand"===e.type}function J(e){return"schedule"===e.type}function Z(e){return"subscription"===e.type}function Y(e,t,r,n){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");if(!r||r&&("string"!=typeof r||!K.includes(r)))throw Error("`region` is required and must be one of the following: ".concat(K.join(", ")));return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,region:a,...s}=e,o=g(t,r,n),c=q({hosts:[{url:"data.{region}.algolia.com".replace("{region}",a),accept:"readWrite",protocol:"https"}],...s,algoliaAgent:v({algoliaAgents:i,client:"Ingestion",version:M}),baseHeaders:{"content-type":"text/plain",...o.headers(),...s.baseHeaders},baseQueryParameters:{...o.queryParameters(),...s.baseQueryParameters}});return{transporter:c,appId:t,apiKey:r,clearCache:()=>Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then(()=>void 0),get _ua(){return c.algoliaAgent.value},addAlgoliaAgent(e,t){c.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?c.baseQueryParameters["x-algolia-api-key"]=t:c.baseHeaders["x-algolia-api-key"]=t},createAuthentication(e,t){if(!e)throw Error("Parameter `authenticationCreate` is required when calling `createAuthentication`.");if(!e.type)throw Error("Parameter `authenticationCreate.type` is required when calling `createAuthentication`.");if(!e.name)throw Error("Parameter `authenticationCreate.name` is required when calling `createAuthentication`.");if(!e.input)throw Error("Parameter `authenticationCreate.input` is required when calling `createAuthentication`.");return c.request({method:"POST",path:"/1/authentications",queryParameters:{},headers:{},data:e},t)},createDestination(e,t){if(!e)throw Error("Parameter `destinationCreate` is required when calling `createDestination`.");if(!e.type)throw Error("Parameter `destinationCreate.type` is required when calling `createDestination`.");if(!e.name)throw Error("Parameter `destinationCreate.name` is required when calling `createDestination`.");if(!e.input)throw Error("Parameter `destinationCreate.input` is required when calling `createDestination`.");return c.request({method:"POST",path:"/1/destinations",queryParameters:{},headers:{},data:e},t)},createSource(e,t){if(!e)throw Error("Parameter `sourceCreate` is required when calling `createSource`.");if(!e.type)throw Error("Parameter `sourceCreate.type` is required when calling `createSource`.");if(!e.name)throw Error("Parameter `sourceCreate.name` is required when calling `createSource`.");return c.request({method:"POST",path:"/1/sources",queryParameters:{},headers:{},data:e},t)},createTask(e,t){if(!e)throw Error("Parameter `taskCreate` is required when calling `createTask`.");if(!e.sourceID)throw Error("Parameter `taskCreate.sourceID` is required when calling `createTask`.");if(!e.destinationID)throw Error("Parameter `taskCreate.destinationID` is required when calling `createTask`.");if(!e.action)throw Error("Parameter `taskCreate.action` is required when calling `createTask`.");return c.request({method:"POST",path:"/2/tasks",queryParameters:{},headers:{},data:e},t)},createTaskV1(e,t){if(!e)throw Error("Parameter `taskCreate` is required when calling `createTaskV1`.");if(!e.sourceID)throw Error("Parameter `taskCreate.sourceID` is required when calling `createTaskV1`.");if(!e.destinationID)throw Error("Parameter `taskCreate.destinationID` is required when calling `createTaskV1`.");if(!e.trigger)throw Error("Parameter `taskCreate.trigger` is required when calling `createTaskV1`.");if(!e.action)throw Error("Parameter `taskCreate.action` is required when calling `createTaskV1`.");return c.request({method:"POST",path:"/1/tasks",queryParameters:{},headers:{},data:e},t)},createTransformation(e,t){if(!e)throw Error("Parameter `transformationCreate` is required when calling `createTransformation`.");if(!e.code)throw Error("Parameter `transformationCreate.code` is required when calling `createTransformation`.");if(!e.name)throw Error("Parameter `transformationCreate.name` is required when calling `createTransformation`.");return c.request({method:"POST",path:"/1/transformations",queryParameters:{},headers:{},data:e},t)},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return c.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return c.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return c.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return c.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteAuthentication(e,t){let{authenticationID:r}=e;if(!r)throw Error("Parameter `authenticationID` is required when calling `deleteAuthentication`.");let n="/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteDestination(e,t){let{destinationID:r}=e;if(!r)throw Error("Parameter `destinationID` is required when calling `deleteDestination`.");let n="/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteSource(e,t){let{sourceID:r}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `deleteSource`.");let n="/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `deleteTask`.");let n="/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteTaskV1(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `deleteTaskV1`.");let n="/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},deleteTransformation(e,t){let{transformationID:r}=e;if(!r)throw Error("Parameter `transformationID` is required when calling `deleteTransformation`.");let n="/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(r));return c.request({method:"DELETE",path:n,queryParameters:{},headers:{}},t)},disableTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `disableTask`.");let n="/2/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PUT",path:n,queryParameters:{},headers:{}},t)},disableTaskV1(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `disableTaskV1`.");let n="/1/tasks/{taskID}/disable".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PUT",path:n,queryParameters:{},headers:{}},t)},enableTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `enableTask`.");let n="/2/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PUT",path:n,queryParameters:{},headers:{}},t)},enableTaskV1(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `enableTaskV1`.");let n="/1/tasks/{taskID}/enable".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PUT",path:n,queryParameters:{},headers:{}},t)},getAuthentication(e,t){let{authenticationID:r}=e;if(!r)throw Error("Parameter `authenticationID` is required when calling `getAuthentication`.");let n="/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getDestination(e,t){let{destinationID:r}=e;if(!r)throw Error("Parameter `destinationID` is required when calling `getDestination`.");let n="/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getEvent(e,t){let{runID:r,eventID:n}=e;if(!r)throw Error("Parameter `runID` is required when calling `getEvent`.");if(!n)throw Error("Parameter `eventID` is required when calling `getEvent`.");let i="/1/runs/{runID}/events/{eventID}".replace("{runID}",encodeURIComponent(r)).replace("{eventID}",encodeURIComponent(n));return c.request({method:"GET",path:i,queryParameters:{},headers:{}},t)},getRun(e,t){let{runID:r}=e;if(!r)throw Error("Parameter `runID` is required when calling `getRun`.");let n="/1/runs/{runID}".replace("{runID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getSource(e,t){let{sourceID:r}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `getSource`.");let n="/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `getTask`.");let n="/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getTaskV1(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `getTaskV1`.");let n="/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getTransformation(e,t){let{transformationID:r}=e;if(!r)throw Error("Parameter `transformationID` is required when calling `getTransformation`.");let n="/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(r));return c.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},listAuthentications(){let{itemsPerPage:e,page:t,type:r,platform:n,sort:i,order:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,o={};return void 0!==e&&(o.itemsPerPage=e.toString()),void 0!==t&&(o.page=t.toString()),void 0!==r&&(o.type=r.toString()),void 0!==n&&(o.platform=n.toString()),void 0!==i&&(o.sort=i.toString()),void 0!==a&&(o.order=a.toString()),c.request({method:"GET",path:"/1/authentications",queryParameters:o,headers:{}},s)},listDestinations(){let{itemsPerPage:e,page:t,type:r,authenticationID:n,transformationID:i,sort:a,order:s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,u={};return void 0!==e&&(u.itemsPerPage=e.toString()),void 0!==t&&(u.page=t.toString()),void 0!==r&&(u.type=r.toString()),void 0!==n&&(u.authenticationID=n.toString()),void 0!==i&&(u.transformationID=i.toString()),void 0!==a&&(u.sort=a.toString()),void 0!==s&&(u.order=s.toString()),c.request({method:"GET",path:"/1/destinations",queryParameters:u,headers:{}},o)},listEvents(e,t){let{runID:r,itemsPerPage:n,page:i,status:a,type:s,sort:o,order:u,startDate:l,endDate:h}=e;if(!r)throw Error("Parameter `runID` is required when calling `listEvents`.");let f="/1/runs/{runID}/events".replace("{runID}",encodeURIComponent(r)),d={};return void 0!==n&&(d.itemsPerPage=n.toString()),void 0!==i&&(d.page=i.toString()),void 0!==a&&(d.status=a.toString()),void 0!==s&&(d.type=s.toString()),void 0!==o&&(d.sort=o.toString()),void 0!==u&&(d.order=u.toString()),void 0!==l&&(d.startDate=l.toString()),void 0!==h&&(d.endDate=h.toString()),c.request({method:"GET",path:f,queryParameters:d,headers:{}},t)},listRuns(){let{itemsPerPage:e,page:t,status:r,type:n,taskID:i,sort:a,order:s,startDate:o,endDate:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,h={};return void 0!==e&&(h.itemsPerPage=e.toString()),void 0!==t&&(h.page=t.toString()),void 0!==r&&(h.status=r.toString()),void 0!==n&&(h.type=n.toString()),void 0!==i&&(h.taskID=i.toString()),void 0!==a&&(h.sort=a.toString()),void 0!==s&&(h.order=s.toString()),void 0!==o&&(h.startDate=o.toString()),void 0!==u&&(h.endDate=u.toString()),c.request({method:"GET",path:"/1/runs",queryParameters:h,headers:{}},l)},listSources(){let{itemsPerPage:e,page:t,type:r,authenticationID:n,sort:i,order:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,o={};return void 0!==e&&(o.itemsPerPage=e.toString()),void 0!==t&&(o.page=t.toString()),void 0!==r&&(o.type=r.toString()),void 0!==n&&(o.authenticationID=n.toString()),void 0!==i&&(o.sort=i.toString()),void 0!==a&&(o.order=a.toString()),c.request({method:"GET",path:"/1/sources",queryParameters:o,headers:{}},s)},listTasks(){let{itemsPerPage:e,page:t,action:r,enabled:n,sourceID:i,sourceType:a,destinationID:s,triggerType:o,withEmailNotifications:u,sort:l,order:h}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,d={};return void 0!==e&&(d.itemsPerPage=e.toString()),void 0!==t&&(d.page=t.toString()),void 0!==r&&(d.action=r.toString()),void 0!==n&&(d.enabled=n.toString()),void 0!==i&&(d.sourceID=i.toString()),void 0!==a&&(d.sourceType=a.toString()),void 0!==s&&(d.destinationID=s.toString()),void 0!==o&&(d.triggerType=o.toString()),void 0!==u&&(d.withEmailNotifications=u.toString()),void 0!==l&&(d.sort=l.toString()),void 0!==h&&(d.order=h.toString()),c.request({method:"GET",path:"/2/tasks",queryParameters:d,headers:{}},f)},listTasksV1(){let{itemsPerPage:e,page:t,action:r,enabled:n,sourceID:i,destinationID:a,triggerType:s,sort:o,order:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,h={};return void 0!==e&&(h.itemsPerPage=e.toString()),void 0!==t&&(h.page=t.toString()),void 0!==r&&(h.action=r.toString()),void 0!==n&&(h.enabled=n.toString()),void 0!==i&&(h.sourceID=i.toString()),void 0!==a&&(h.destinationID=a.toString()),void 0!==s&&(h.triggerType=s.toString()),void 0!==o&&(h.sort=o.toString()),void 0!==u&&(h.order=u.toString()),c.request({method:"GET",path:"/1/tasks",queryParameters:h,headers:{}},l)},listTransformations(){let{itemsPerPage:e,page:t,sort:r,order:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a={};return void 0!==e&&(a.itemsPerPage=e.toString()),void 0!==t&&(a.page=t.toString()),void 0!==r&&(a.sort=r.toString()),void 0!==n&&(a.order=n.toString()),c.request({method:"GET",path:"/1/transformations",queryParameters:a,headers:{}},i)},pushTask(e,t){let{taskID:r,pushTaskPayload:n,watch:i}=e;if(!r)throw Error("Parameter `taskID` is required when calling `pushTask`.");if(!n)throw Error("Parameter `pushTaskPayload` is required when calling `pushTask`.");if(!n.action)throw Error("Parameter `pushTaskPayload.action` is required when calling `pushTask`.");if(!n.records)throw Error("Parameter `pushTaskPayload.records` is required when calling `pushTask`.");let a="/2/tasks/{taskID}/push".replace("{taskID}",encodeURIComponent(r)),s={};return void 0!==i&&(s.watch=i.toString()),t={timeouts:{connect:18e4,read:18e4,write:18e4,...null==t?void 0:t.timeouts}},c.request({method:"POST",path:a,queryParameters:s,headers:{},data:n},t)},runSource(e,t){let{sourceID:r,runSourcePayload:n}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `runSource`.");let i="/1/sources/{sourceID}/run".replace("{sourceID}",encodeURIComponent(r));return c.request({method:"POST",path:i,queryParameters:{},headers:{},data:n||{}},t)},runTask(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `runTask`.");let n="/2/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(r));return c.request({method:"POST",path:n,queryParameters:{},headers:{}},t)},runTaskV1(e,t){let{taskID:r}=e;if(!r)throw Error("Parameter `taskID` is required when calling `runTaskV1`.");let n="/1/tasks/{taskID}/run".replace("{taskID}",encodeURIComponent(r));return c.request({method:"POST",path:n,queryParameters:{},headers:{}},t)},searchAuthentications(e,t){if(!e)throw Error("Parameter `authenticationSearch` is required when calling `searchAuthentications`.");if(!e.authenticationIDs)throw Error("Parameter `authenticationSearch.authenticationIDs` is required when calling `searchAuthentications`.");return c.request({method:"POST",path:"/1/authentications/search",queryParameters:{},headers:{},data:e},t)},searchDestinations(e,t){if(!e)throw Error("Parameter `destinationSearch` is required when calling `searchDestinations`.");if(!e.destinationIDs)throw Error("Parameter `destinationSearch.destinationIDs` is required when calling `searchDestinations`.");return c.request({method:"POST",path:"/1/destinations/search",queryParameters:{},headers:{},data:e},t)},searchSources(e,t){if(!e)throw Error("Parameter `sourceSearch` is required when calling `searchSources`.");if(!e.sourceIDs)throw Error("Parameter `sourceSearch.sourceIDs` is required when calling `searchSources`.");return c.request({method:"POST",path:"/1/sources/search",queryParameters:{},headers:{},data:e},t)},searchTasks(e,t){if(!e)throw Error("Parameter `taskSearch` is required when calling `searchTasks`.");if(!e.taskIDs)throw Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasks`.");return c.request({method:"POST",path:"/2/tasks/search",queryParameters:{},headers:{},data:e},t)},searchTasksV1(e,t){if(!e)throw Error("Parameter `taskSearch` is required when calling `searchTasksV1`.");if(!e.taskIDs)throw Error("Parameter `taskSearch.taskIDs` is required when calling `searchTasksV1`.");return c.request({method:"POST",path:"/1/tasks/search",queryParameters:{},headers:{},data:e},t)},searchTransformations(e,t){if(!e)throw Error("Parameter `transformationSearch` is required when calling `searchTransformations`.");if(!e.transformationIDs)throw Error("Parameter `transformationSearch.transformationIDs` is required when calling `searchTransformations`.");return c.request({method:"POST",path:"/1/transformations/search",queryParameters:{},headers:{},data:e},t)},triggerDockerSourceDiscover(e,t){let{sourceID:r}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.");let n="/1/sources/{sourceID}/discover".replace("{sourceID}",encodeURIComponent(r));return t={timeouts:{connect:18e4,read:18e4,write:18e4,...null==t?void 0:t.timeouts}},c.request({method:"POST",path:n,queryParameters:{},headers:{}},t)},tryTransformation(e,t){if(!e)throw Error("Parameter `transformationTry` is required when calling `tryTransformation`.");if(!e.code)throw Error("Parameter `transformationTry.code` is required when calling `tryTransformation`.");if(!e.sampleRecord)throw Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformation`.");return c.request({method:"POST",path:"/1/transformations/try",queryParameters:{},headers:{},data:e},t)},tryTransformationBeforeUpdate(e,t){let{transformationID:r,transformationTry:n}=e;if(!r)throw Error("Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.");if(!n)throw Error("Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.");if(!n.code)throw Error("Parameter `transformationTry.code` is required when calling `tryTransformationBeforeUpdate`.");if(!n.sampleRecord)throw Error("Parameter `transformationTry.sampleRecord` is required when calling `tryTransformationBeforeUpdate`.");let i="/1/transformations/{transformationID}/try".replace("{transformationID}",encodeURIComponent(r));return c.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)},updateAuthentication(e,t){let{authenticationID:r,authenticationUpdate:n}=e;if(!r)throw Error("Parameter `authenticationID` is required when calling `updateAuthentication`.");if(!n)throw Error("Parameter `authenticationUpdate` is required when calling `updateAuthentication`.");let i="/1/authentications/{authenticationID}".replace("{authenticationID}",encodeURIComponent(r));return c.request({method:"PATCH",path:i,queryParameters:{},headers:{},data:n},t)},updateDestination(e,t){let{destinationID:r,destinationUpdate:n}=e;if(!r)throw Error("Parameter `destinationID` is required when calling `updateDestination`.");if(!n)throw Error("Parameter `destinationUpdate` is required when calling `updateDestination`.");let i="/1/destinations/{destinationID}".replace("{destinationID}",encodeURIComponent(r));return c.request({method:"PATCH",path:i,queryParameters:{},headers:{},data:n},t)},updateSource(e,t){let{sourceID:r,sourceUpdate:n}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `updateSource`.");if(!n)throw Error("Parameter `sourceUpdate` is required when calling `updateSource`.");let i="/1/sources/{sourceID}".replace("{sourceID}",encodeURIComponent(r));return c.request({method:"PATCH",path:i,queryParameters:{},headers:{},data:n},t)},updateTask(e,t){let{taskID:r,taskUpdate:n}=e;if(!r)throw Error("Parameter `taskID` is required when calling `updateTask`.");if(!n)throw Error("Parameter `taskUpdate` is required when calling `updateTask`.");let i="/2/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PATCH",path:i,queryParameters:{},headers:{},data:n},t)},updateTaskV1(e,t){let{taskID:r,taskUpdate:n}=e;if(!r)throw Error("Parameter `taskID` is required when calling `updateTaskV1`.");if(!n)throw Error("Parameter `taskUpdate` is required when calling `updateTaskV1`.");let i="/1/tasks/{taskID}".replace("{taskID}",encodeURIComponent(r));return c.request({method:"PATCH",path:i,queryParameters:{},headers:{},data:n},t)},updateTransformation(e,t){let{transformationID:r,transformationCreate:n}=e;if(!r)throw Error("Parameter `transformationID` is required when calling `updateTransformation`.");if(!n)throw Error("Parameter `transformationCreate` is required when calling `updateTransformation`.");if(!n.code)throw Error("Parameter `transformationCreate.code` is required when calling `updateTransformation`.");if(!n.name)throw Error("Parameter `transformationCreate.name` is required when calling `updateTransformation`.");let i="/1/transformations/{transformationID}".replace("{transformationID}",encodeURIComponent(r));return c.request({method:"PUT",path:i,queryParameters:{},headers:{},data:n},t)},validateSource(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return t={timeouts:{connect:18e4,read:18e4,write:18e4,...null==t?void 0:t.timeouts}},c.request({method:"POST",path:"/1/sources/validate",queryParameters:{},headers:{},data:e||{}},t)},validateSourceBeforeUpdate(e,t){let{sourceID:r,sourceUpdate:n}=e;if(!r)throw Error("Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.");if(!n)throw Error("Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.");let i="/1/sources/{sourceID}/validate".replace("{sourceID}",encodeURIComponent(r));return t={timeouts:{connect:18e4,read:18e4,write:18e4,...null==t?void 0:t.timeouts}},c.request({method:"POST",path:i,queryParameters:{},headers:{},data:n},t)}}}({appId:e,apiKey:t,region:r,timeouts:{connect:25e3,read:25e3,write:25e3},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(M,"-").concat(e)}),p()]}),...n})}var X="1.20.0";function ee(e,t,r){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,...a}=e,s=g(t,r,n),o=q({hosts:[{url:"status.algolia.com",accept:"readWrite",protocol:"https"}],...a,algoliaAgent:v({algoliaAgents:i,client:"Monitoring",version:X}),baseHeaders:{"content-type":"text/plain",...s.headers(),...a.baseHeaders},baseQueryParameters:{...s.queryParameters(),...a.baseQueryParameters}});return{transporter:o,appId:t,apiKey:r,clearCache:()=>Promise.all([o.requestsCache.clear(),o.responsesCache.clear()]).then(()=>void 0),get _ua(){return o.algoliaAgent.value},addAlgoliaAgent(e,t){o.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?o.baseQueryParameters["x-algolia-api-key"]=t:o.baseHeaders["x-algolia-api-key"]=t},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return o.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return o.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return o.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return o.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},getClusterIncidents(e,t){let{clusters:r}=e;if(!r)throw Error("Parameter `clusters` is required when calling `getClusterIncidents`.");let n="/1/incidents/{clusters}".replace("{clusters}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getClusterStatus(e,t){let{clusters:r}=e;if(!r)throw Error("Parameter `clusters` is required when calling `getClusterStatus`.");let n="/1/status/{clusters}".replace("{clusters}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getIncidents:e=>o.request({method:"GET",path:"/1/incidents",queryParameters:{},headers:{}},e),getIndexingTime(e,t){let{clusters:r}=e;if(!r)throw Error("Parameter `clusters` is required when calling `getIndexingTime`.");let n="/1/indexing/{clusters}".replace("{clusters}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getLatency(e,t){let{clusters:r}=e;if(!r)throw Error("Parameter `clusters` is required when calling `getLatency`.");let n="/1/latency/{clusters}".replace("{clusters}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getMetrics(e,t){let{metric:r,period:n}=e;if(!r)throw Error("Parameter `metric` is required when calling `getMetrics`.");if(!n)throw Error("Parameter `period` is required when calling `getMetrics`.");let i="/1/infrastructure/{metric}/period/{period}".replace("{metric}",encodeURIComponent(r)).replace("{period}",encodeURIComponent(n));return o.request({method:"GET",path:i,queryParameters:{},headers:{}},t)},getReachability(e,t){let{clusters:r}=e;if(!r)throw Error("Parameter `clusters` is required when calling `getReachability`.");let n="/1/reachability/{clusters}/probes".replace("{clusters}",encodeURIComponent(r));return o.request({method:"GET",path:n,queryParameters:{},headers:{}},t)},getServers:e=>o.request({method:"GET",path:"/1/inventory/servers",queryParameters:{},headers:{}},e),getStatus:e=>o.request({method:"GET",path:"/1/status",queryParameters:{},headers:{}},e)}}({appId:e,apiKey:t,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(X,"-").concat(e)}),p()]}),...r})}var et="5.20.0";function er(e,t,r){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");return function(e){let{appId:t,apiKey:r,authMode:n,algoliaAgents:i,...a}=e,s=g(t,r,n),o=q({hosts:[{url:"".concat(t,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(t,".algolia.net"),accept:"write",protocol:"https"}].concat(x([{url:"".concat(t,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}])),...a,algoliaAgent:v({algoliaAgents:i,client:"Recommend",version:et}),baseHeaders:{"content-type":"text/plain",...s.headers(),...a.baseHeaders},baseQueryParameters:{...s.queryParameters(),...a.baseQueryParameters}});return{transporter:o,appId:t,apiKey:r,clearCache:()=>Promise.all([o.requestsCache.clear(),o.responsesCache.clear()]).then(()=>void 0),get _ua(){return o.algoliaAgent.value},addAlgoliaAgent(e,t){o.algoliaAgent.add({segment:e,version:t})},setClientApiKey(e){let{apiKey:t}=e;n&&"WithinHeaders"!==n?o.baseQueryParameters["x-algolia-api-key"]=t:o.baseHeaders["x-algolia-api-key"]=t},batchRecommendRules(e,t){let{indexName:r,model:n,recommendRule:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `batchRecommendRules`.");if(!n)throw Error("Parameter `model` is required when calling `batchRecommendRules`.");let a="/1/indexes/{indexName}/{model}/recommend/rules/batch".replace("{indexName}",encodeURIComponent(r)).replace("{model}",encodeURIComponent(n));return o.request({method:"POST",path:a,queryParameters:{},headers:{},data:i||{}},t)},customDelete(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customDelete`.");let i="/{path}".replace("{path}",r);return o.request({method:"DELETE",path:i,queryParameters:n||{},headers:{}},t)},customGet(e,t){let{path:r,parameters:n}=e;if(!r)throw Error("Parameter `path` is required when calling `customGet`.");let i="/{path}".replace("{path}",r);return o.request({method:"GET",path:i,queryParameters:n||{},headers:{}},t)},customPost(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPost`.");let a="/{path}".replace("{path}",r);return o.request({method:"POST",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},customPut(e,t){let{path:r,parameters:n,body:i}=e;if(!r)throw Error("Parameter `path` is required when calling `customPut`.");let a="/{path}".replace("{path}",r);return o.request({method:"PUT",path:a,queryParameters:n||{},headers:{},data:i||{}},t)},deleteRecommendRule(e,t){let{indexName:r,model:n,objectID:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `deleteRecommendRule`.");if(!n)throw Error("Parameter `model` is required when calling `deleteRecommendRule`.");if(!i)throw Error("Parameter `objectID` is required when calling `deleteRecommendRule`.");let a="/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{model}",encodeURIComponent(n)).replace("{objectID}",encodeURIComponent(i));return o.request({method:"DELETE",path:a,queryParameters:{},headers:{}},t)},getRecommendRule(e,t){let{indexName:r,model:n,objectID:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getRecommendRule`.");if(!n)throw Error("Parameter `model` is required when calling `getRecommendRule`.");if(!i)throw Error("Parameter `objectID` is required when calling `getRecommendRule`.");let a="/1/indexes/{indexName}/{model}/recommend/rules/{objectID}".replace("{indexName}",encodeURIComponent(r)).replace("{model}",encodeURIComponent(n)).replace("{objectID}",encodeURIComponent(i));return o.request({method:"GET",path:a,queryParameters:{},headers:{}},t)},getRecommendStatus(e,t){let{indexName:r,model:n,taskID:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `getRecommendStatus`.");if(!n)throw Error("Parameter `model` is required when calling `getRecommendStatus`.");if(!i)throw Error("Parameter `taskID` is required when calling `getRecommendStatus`.");let a="/1/indexes/{indexName}/{model}/task/{taskID}".replace("{indexName}",encodeURIComponent(r)).replace("{model}",encodeURIComponent(n)).replace("{taskID}",encodeURIComponent(i));return o.request({method:"GET",path:a,queryParameters:{},headers:{}},t)},getRecommendations(e,t){if(e&&Array.isArray(e)&&(e={requests:e}),!e)throw Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!e.requests)throw Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");let r={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return o.request(r,t)},searchRecommendRules(e,t){let{indexName:r,model:n,searchRecommendRulesParams:i}=e;if(!r)throw Error("Parameter `indexName` is required when calling `searchRecommendRules`.");if(!n)throw Error("Parameter `model` is required when calling `searchRecommendRules`.");let a="/1/indexes/{indexName}/{model}/recommend/rules/search".replace("{indexName}",encodeURIComponent(r)).replace("{model}",encodeURIComponent(n));return o.request({method:"POST",path:a,queryParameters:{},headers:{},data:i||{},useReadTransporter:!0,cacheable:!0},t)}}}({appId:e,apiKey:t,timeouts:{connect:1e3,read:2e3,write:3e4},logger:b(),requester:f(),algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:p(),requestsCache:p({serializable:!1}),hostsCache:m({caches:[d({key:"".concat(et,"-").concat(e)}),p()]}),...r})}var en=Object.defineProperty,ei=Object.getOwnPropertyDescriptor,ea=Object.getOwnPropertyNames,es=Object.prototype.hasOwnProperty,eo=(e,t)=>{for(var r in t)en(e,r,{get:t[r],enumerable:!0})},ec=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of ea(t))es.call(e,i)||i===r||en(e,i,{get:()=>t[i],enumerable:!(n=ei(t,i))||n.enumerable});return e},eu=(e,t,r)=>(ec(e,t,"default"),r&&ec(r,t,"default")),el={};eo(el,{algoliasearch:()=>ef,apiClientVersion:()=>V});var eh={};function ef(e,t,r){if(!e||"string"!=typeof e)throw Error("`appId` is missing.");if(!t||"string"!=typeof t)throw Error("`apiKey` is missing.");let n=G(e,t,r);return{...n,get _ua(){return n.transporter.algoliaAgent.value},initAbtesting:r=>D(r.appId||e,r.apiKey||t,r.region,r.options),initAnalytics:r=>k(r.appId||e,r.apiKey||t,r.region,r.options),initIngestion:r=>Y(r.appId||e,r.apiKey||t,r.region,r.options),initInsights:r=>H(r.appId||e,r.apiKey||t,r.region,r.options),initMonitoring:function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return ee(r.appId||e,r.apiKey||t,r.options)},initPersonalization:r=>B(r.appId||e,r.apiKey||t,r.region,r.options),initQuerySuggestions:r=>$(r.appId||e,r.apiKey||t,r.region,r.options),initRecommend:function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return er(r.appId||e,r.apiKey||t,r.options)}}}eo(eh,{apiClientVersion:()=>V}),eu(eh,n),eu(eh,i),eu(eh,a),eu(eh,s),eu(eh,o),eu(eh,c),eu(eh,u),eu(eh,l),eu(eh,h),eu(el,eh)},3415:function(e,t,r){"use strict";function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(function(e,t){return Array.isArray(t)?e.concat(t):e.concat([t])},[]).filter(Boolean).join(" ")}r.d(t,{cx:function(){return n}})},97886:function(e,t,r){"use strict";function n(e,t){if(void 0===e||"function"!=typeof e)throw Error("The render function is not valid (received type ".concat(Object.prototype.toString.call(e).slice(8,-1),").\n\n").concat(t))}r.d(t,{_:function(){return n}})},83880:function(e,t,r){"use strict";function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.map(function(e){var t;return["https://www.algolia.com/doc/api-reference/widgets/",e.name,"/js/",void 0!==(t=e.connector)&&t?"#connector":""].join("")}).join(", ");return function(e){return[e,"See documentation: ".concat(n)].filter(Boolean).join("\n\n")}}r.d(t,{K:function(){return n}})},61159:function(e,t,r){"use strict";r.d(t,{Rn:function(){return h},dg:function(){return l},mY:function(){return d}});var n=r(39639),i=r(90647);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){u(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t,r){var n;return(n=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==a(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l={highlightPreTag:"__ais-highlight__",highlightPostTag:"__/ais-highlight__"},h={highlightPreTag:"<mark>",highlightPostTag:"</mark>"};function f(e){var t;return(0,i.P)(e)&&"string"!=typeof e.value?Object.keys(e).reduce(function(t,r){return c(c({},t),{},u({},r,f(e[r])))},{}):Array.isArray(e)?e.map(f):c(c({},e),{},{value:(t=e.value,(0,n.Y)(t).replace(RegExp(l.highlightPreTag,"g"),h.highlightPreTag).replace(RegExp(l.highlightPostTag,"g"),h.highlightPostTag))})}function d(e){return void 0===e.__escaped&&((e=e.map(function(e){var t=s({},(function(e){if(null==e)throw TypeError("Cannot destructure "+e)}(e),e));return t._highlightResult&&(t._highlightResult=f(t._highlightResult)),t._snippetResult&&(t._snippetResult=f(t._snippetResult)),t})).__escaped=!0),e}},39639:function(e,t,r){"use strict";r.d(t,{A:function(){return l},Y:function(){return s}});var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},i=/[&<>"']/g,a=RegExp(i.source);function s(e){return e&&a.test(e)?e.replace(i,function(e){return n[e]}):e}var o={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},c=/&(amp|quot|lt|gt|#39);/g,u=RegExp(c.source);function l(e){return e&&u.test(e)?e.replace(c,function(e){return o[e]}):e}},20148:function(e,t,r){"use strict";function n(e,t){for(var r,n=0;n<e.length;n++)if(t(r=e[n],n,e))return r}r.d(t,{s:function(){return n}})},95332:function(e,t,r){"use strict";function n(e){return"ais.index"===e.$$type}r.d(t,{J:function(){return n}})},90647:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){if(!("object"===n(e)&&null!==e)||"[object Object]"!==(null===e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)))return!1;if(null===Object.getPrototypeOf(e))return!0;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}r.d(t,{P:function(){return i}})},22247:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{Z:function(){return u}});var i=["facets","disjunctiveFacets","facetsRefinements","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacets","hierarchicalFacetsRefinements","ruleContexts"];function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var i,a;i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}(i))in e?Object.defineProperty(e,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=function(e,t){t.facets,t.disjunctiveFacets,t.facetsRefinements,t.facetsExcludes,t.disjunctiveFacetsRefinements,t.numericRefinements,t.tagRefinements,t.hierarchicalFacets,t.hierarchicalFacetsRefinements,t.ruleContexts;var r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(t,i);return e.setQueryParameters(r)},c=function(e,t){var r=[].concat(e.ruleContexts).concat(t.ruleContexts).filter(Boolean).filter(function(e,t,r){return r.indexOf(e)===t});return r.length>0?e.setQueryParameters({ruleContexts:r}):e},u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(function(e,t){var r,n,i,a,u,l,h,f;return o((f=c((n=(r=e.setQueryParameters({hierarchicalFacetsRefinements:s(s({},e.hierarchicalFacetsRefinements),t.hierarchicalFacetsRefinements)})).setQueryParameters({hierarchicalFacets:t.hierarchicalFacets.reduce(function(e,t){var r=function(e,t){if(!Array.isArray(e))return -1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return -1}(e,function(e){return e.name===t.name});if(-1===r)return e.concat(t);var n=e.slice();return n.splice(r,1,t),n},r.hierarchicalFacets)}),h=(l=(u=(a=(i=t.tagRefinements.reduce(function(e,t){return e.addTagRefinement(t)},n)).setQueryParameters({numericRefinements:s(s({},i.numericRefinements),t.numericRefinements)})).setQueryParameters({disjunctiveFacetsRefinements:s(s({},a.disjunctiveFacetsRefinements),t.disjunctiveFacetsRefinements)})).setQueryParameters({facetsExcludes:s(s({},u.facetsExcludes),t.facetsExcludes)})).setQueryParameters({facetsRefinements:s(s({},l.facetsRefinements),t.facetsRefinements)}),t.disjunctiveFacets.reduce(function(e,t){return e.addDisjunctiveFacet(t)},h)),t),t.facets.reduce(function(e,t){return e.addFacet(t)},f)),t)})}},14803:function(e,t,r){"use strict";function n(){}r.d(t,{Z:function(){return n}})},37925:function(e,t,r){"use strict";function n(e,t,r){var n=t.getHelper();return{uiState:r,helper:n,parent:t,instantSearchInstance:e,state:n.state,renderState:e.renderState,templatesConfig:e.templatesConfig,createURL:t.createURL,scopedResults:[],searchMetadata:{isSearchStalled:"stalled"===e.status},status:e.status,error:e.error}}function i(e,t,r){var n=t.getResultsForWidget(r),i=t.getHelper();return{helper:i,parent:t,instantSearchInstance:e,results:n,scopedResults:t.getScopedResults(),state:n&&"_state"in n?n._state:i.state,renderState:e.renderState,templatesConfig:e.templatesConfig,createURL:t.createURL,searchMetadata:{isSearchStalled:"stalled"===e.status},status:e.status,error:e.error}}r.d(t,{d:function(){return i},q:function(){return n}})},90142:function(e,t,r){"use strict";function n(e){return btoa(encodeURIComponent(JSON.stringify(e)))}r.d(t,{a:function(){return n}})},94739:function(e,t,r){"use strict";r.d(t,{Z:function(){return S}});var n=r(13478),i=r(83880),a=r(95332),s=r(37925),o=r(22247),c=0;function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var l=["initialSearchParameters"],h=["initialRecommendParameters"];function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){var n;return(n=function(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==u(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var v=(0,i.K)({name:"index-widget"});function b(e,t){var r=t.state,n=t.recommendState,i=t.isPageReset,a=t._uiState;r!==e.state&&(e.state=r,e.emit("change",{state:e.state,results:e.lastResults,isPageReset:i,_uiState:a})),n!==e.recommendState&&(e.recommendState=n)}function P(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e.reduce(function(e,r){return!(0,a.J)(r)&&(r.getWidgetUiState||r.getWidgetState)?r.getWidgetUiState?r.getWidgetUiState(e,t):r.getWidgetState(e,t):e},r)}function w(e,t){var r=t.initialSearchParameters,n=y(t,l);return e.reduce(function(e,t){return!t.getWidgetSearchParameters||(0,a.J)(t)?e:"search"===t.dependsOn&&t.getWidgetParameters?t.getWidgetParameters(e,n):t.getWidgetSearchParameters(e,n)},r)}function R(e,t){var r=t.initialRecommendParameters,n=y(t,h);return e.reduce(function(e,t){return!(0,a.J)(t)&&"recommend"===t.dependsOn&&t.getWidgetParameters?t.getWidgetParameters(e,n):e},r)}var S=function(e){if(void 0===e||void 0===e.indexName)throw Error(v("The `indexName` option is required."));var t=e.indexName,r=e.indexId,i=void 0===r?t:r,u=[],l={},h=null,f=null,g=null,y=null,S=null,j=!1,O=!1;return{$$type:"ais.index",$$widgetType:"ais.index",getIndexName:function(){return t},getIndexId:function(){return i},getHelper:function(){return g},getResults:function(){var e;return null!==(e=y)&&void 0!==e&&e.lastResults?(y.lastResults._state=g.state,y.lastResults):null},getResultsForWidget:function(e){var t;return"recommend"!==e.dependsOn||(0,a.J)(e)||void 0===e.$$id?this.getResults():null!==(t=g)&&void 0!==t&&t.lastRecommendResults?g.lastRecommendResults[e.$$id]:null},getPreviousState:function(){return S},getScopedResults:function(){var e=this.getParent();return function e(t){return t.filter(a.J).reduce(function(t,r){return t.concat.apply(t,[{indexId:r.getIndexId(),results:r.getResults(),helper:r.getHelper()}].concat(p(e(r.getWidgets()))))},[])}(e?e.getWidgets():0===t.length?this.getWidgets():[this])},getParent:function(){return f},createURL:function(e){return"function"==typeof e?h._createURL(m({},i,e(l))):h._createURL(m({},i,P(u,{searchParameters:e,helper:g})))},getWidgets:function(){return u},addWidgets:function(e){var t=this;if(!Array.isArray(e))throw Error(v("The `addWidgets` method expects an array of widgets."));if(e.some(function(e){return"function"!=typeof e.init&&"function"!=typeof e.render}))throw Error(v("The widget definition expects a `render` and/or an `init` method."));return e.forEach(function(e){if(!(0,a.J)(e))h&&"recommend"===e.dependsOn?h._hasRecommendWidget=!0:h?h._hasSearchWidget=!0:"recommend"===e.dependsOn?j=!0:O=!0,"recommend"===e.dependsOn&&(e.$$id=c++)}),u=u.concat(e),h&&e.length&&(b(g,{state:w(u,{uiState:l,initialSearchParameters:g.state}),recommendState:R(u,{uiState:l,initialRecommendParameters:g.recommendState}),_uiState:l}),e.forEach(function(e){e.getRenderState&&E({renderState:e.getRenderState(h.renderState[t.getIndexId()]||{},(0,s.q)(h,t,h._initialUiState)),instantSearchInstance:h,parent:t})}),e.forEach(function(e){e.init&&e.init((0,s.q)(h,t,h._initialUiState))}),h.scheduleSearch()),this},removeWidgets:function(e){var t=this;if(!Array.isArray(e))throw Error(v("The `removeWidgets` method expects an array of widgets."));if(e.some(function(e){return"function"!=typeof e.dispose}))throw Error(v("The widget definition expects a `dispose` method."));if((u=u.filter(function(t){return -1===e.indexOf(t)})).forEach(function(e){(0,a.J)(e)||(h&&"recommend"===e.dependsOn?h._hasRecommendWidget=!0:h?h._hasSearchWidget=!0:"recommend"===e.dependsOn?j=!0:O=!0)}),h&&e.length){var r=e.reduce(function(e,r){var i=r.dispose({helper:g,state:e.cleanedSearchState,recommendState:e.cleanedRecommendState,parent:t});return i instanceof n.RecommendParameters?e.cleanedRecommendState=i:i&&(e.cleanedSearchState=i),e},{cleanedSearchState:g.state,cleanedRecommendState:g.recommendState}),i=r.cleanedSearchState,s=r.cleanedRecommendState,o=h.future.preserveSharedStateOnUnmount?w(u,{uiState:l,initialSearchParameters:new n.SearchParameters({index:this.getIndexName()})}):w(u,{uiState:P(u,{searchParameters:i,helper:g}),initialSearchParameters:i});l=P(u,{searchParameters:o,helper:g}),g.setState(o),g.recommendState=s,u.length&&h.scheduleSearch()}return this},init:function(e){var r,c=this,d=e.instantSearchInstance,m=e.parent,v=e.uiState;if(null===g){h=d,f=m,l=v[i]||{};var x=d.mainHelper,F=w(u,{uiState:l,initialSearchParameters:new n.SearchParameters({index:t})}),q=R(u,{uiState:l,initialRecommendParameters:new n.RecommendParameters});(g=n({},F.index,F)).recommendState=q,g.search=function(){return d.onStateChange?(d.onStateChange({uiState:d.mainIndex.getWidgetUiState({}),setUiState:function(e){return d.setUiState(e,!1)}}),x):x.search()},g.searchWithoutTriggeringOnStateChange=function(){return x.search()},g.searchForFacetValues=function(e,t,r,n){var i=g.state.setQueryParameters(n);return x.searchForFacetValues(e,t,r,i)},y=x.derive(function(){return o.Z.apply(void 0,[x.state].concat(p(function(e){for(var t=e.getParent(),r=[e.getHelper().state];null!==t;)r=[t.getHelper().state].concat(r),t=t.getParent();return r}(c))))},function(){return c.getHelper().recommendState});var T=null===(r=d._initialResults)||void 0===r?void 0:r[this.getIndexId()];if(null!=T&&T.results){var I=new n.SearchResults(new n.SearchParameters(T.state),T.results);y.lastResults=I,g.lastResults=I}if(null!=T&&T.recommendResults){var D=new n.RecommendResults(new n.RecommendParameters({params:T.recommendResults.params}),T.recommendResults.results);y.lastRecommendResults=D,g.lastRecommendResults=D}g.on("change",function(e){e.isPageReset&&function e(t){var r=t.filter(a.J);0!==r.length&&r.forEach(function(t){var r=t.getHelper();b(r,{state:r.state.resetPage(),recommendState:r.recommendState,isPageReset:!0}),e(t.getWidgets())})}(u)}),y.on("search",function(){d.scheduleStalledRender()}),y.on("result",function(e){var t=e.results;d.scheduleRender(),g.lastResults=t,S=null==t?void 0:t._state}),y.on("recommend:result",function(e){var t=e.recommend;d.scheduleRender(),g.lastRecommendResults=t.results}),u.forEach(function(e){e.getRenderState&&E({renderState:e.getRenderState(d.renderState[c.getIndexId()]||{},(0,s.q)(d,c,v)),instantSearchInstance:d,parent:c})}),u.forEach(function(e){e.init&&e.init((0,s.q)(d,c,v))}),g.on("change",function(e){var t=e.state,r=e._uiState;l=P(u,{searchParameters:t,helper:g},r||{}),d.onStateChange||d.onInternalStateChange()}),T&&d.scheduleRender(),j&&(d._hasRecommendWidget=!0),O&&(d._hasSearchWidget=!0)}},render:function(e){var t,r=this,n=e.instantSearchInstance;"error"===n.status&&!n.mainHelper.hasPendingRequests()&&S&&g.setState(S);var i=this.getResults()||null!==(t=y)&&void 0!==t&&t.lastRecommendResults?u:u.filter(a.J);(i=i.filter(function(e){return!e.shouldRender||e.shouldRender({instantSearchInstance:n})})).forEach(function(e){e.getRenderState&&E({renderState:e.getRenderState(n.renderState[r.getIndexId()]||{},(0,s.d)(n,r,e)),instantSearchInstance:n,parent:r})}),i.forEach(function(e){e.render&&e.render((0,s.d)(n,r,e))})},dispose:function(){var e,t,r=this;u.forEach(function(e){e.dispose&&g&&e.dispose({helper:g,state:g.state,recommendState:g.recommendState,parent:r})}),h=null,f=null,null===(e=g)||void 0===e||e.removeAllListeners(),g=null,null===(t=y)||void 0===t||t.detach(),y=null},getWidgetUiState:function(e){return u.filter(a.J).reduce(function(e,t){return t.getWidgetUiState(e)},d(d({},e),{},m({},i,d(d({},e[i]),l))))},getWidgetState:function(e){return this.getWidgetUiState(e)},getWidgetSearchParameters:function(e,t){var r=t.uiState;return w(u,{uiState:r,initialSearchParameters:e})},refreshUiState:function(){l=P(u,{searchParameters:this.getHelper().state,helper:this.getHelper()},l)},setIndexUiState:function(e){var t="function"==typeof e?e(l):e;h.setUiState(function(e){return d(d({},e),{},m({},i,t))})}}};function E(e){var t=e.renderState,r=e.instantSearchInstance,n=e.parent,i=n?n.getIndexId():r.mainIndex.getIndexId();r.renderState=d(d({},r.renderState),{},m({},i,d(d({},r.renderState[i]),t)))}},66322:function(e,t,r){"use strict";r.d(t,{T:function(){return v}});var n=r(13478),i=r(83880),a=r(14803),s=r(90647),o=r(22247);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){h(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t,r){var n;return(n=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==c(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var f=(0,i.K)({name:"configure",connector:!0});function d(e,t){return e.setQueryParameters(Object.keys(t.searchParameters).reduce(function(e,t){return l(l({},e),{},h({},t,void 0))},{}))}var m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.Z,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.Z;return function(r){if(!r||!(0,s.P)(r.searchParameters))throw Error(f("The `searchParameters` option expects an object."));var i={};return{$$type:"ais.configure",init:function(t){var r=t.instantSearchInstance;e(l(l({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(l(l({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(e){var n=e.state;return t(),d(n,r)},getRenderState:function(e,t){var r,i=this.getWidgetRenderState(t);return l(l({},e),{},{configure:l(l({},i),{},{widgetParams:l(l({},i.widgetParams),{},{searchParameters:(0,o.Z)(new n.SearchParameters(null===(r=e.configure)||void 0===r?void 0:r.widgetParams.searchParameters),new n.SearchParameters(i.widgetParams.searchParameters)).getQueryParams()})})})},getWidgetRenderState:function(e){var t=e.helper;return i.refine||(i.refine=function(e){var i=d(t.state,r),a=(0,o.Z)(i,new n.SearchParameters(e));r.searchParameters=e,t.setState(a).search()}),{refine:i.refine,widgetParams:r}},getWidgetSearchParameters:function(e,t){var i=t.uiState;return(0,o.Z)(e,new n.SearchParameters(l(l({},i.configure),r.searchParameters)))},getWidgetUiState:function(e){return l(l({},e),{},{configure:l(l({},e.configure),r.searchParameters)})}}}},p=r(39531);function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){var t;return t=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e),(0,p.B)(m,{searchParameters:t},{$$widgetType:"ais.configure"}),null}},17246:function(e,t,r){"use strict";r.d(t,{g:function(){return m}});var n=r(2265),i=r(20783),a=r(94739),s=r(36577),o=r(72701),c=r(93248),u=r(81970),l=r(97366),h=r(81182),f=r(63220),d=["children"];function m(e){var t,r,m,p,g,y,v,b,P,w=e.children,R=(t=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d),r=(0,c.a)(),p=null==(m=(0,u.s)())?void 0:m.initialResults,g=(0,o.a)(),y=(0,h.q)(t),b=(v=(0,n.useMemo)(function(){return(0,a.Z)(y)},[y])).getHelper(),P=(0,s.N)(),(0,l.L)(function(){P()},[b,P]),(0,f.F)({widget:v,parentIndex:g,props:y,shouldSsr:!!(r||p)}),v);return null===R.getHelper()?null:n.createElement(i.Z.Provider,{value:R},w)}},22156:function(e,t,r){"use strict";r.d(t,{p:function(){return eY}});var n=r(2265),i=r(20783),a=r(76194),s=r(67612),o=r(13478);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{fallback:function(){}},r=t.fallback;return"undefined"==typeof window?r():e({window:window})}var l=r(14803),h=r(20148);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(e,t)||g(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){if(e){if("string"==typeof e)return y(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(e,t)}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var v="2.17.2",b="https://cdn.jsdelivr.net/npm/search-insights@".concat(v,"/dist/search-insights.min.js");function P(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.insightsClient,r=e.insightsInitParams,n=e.onEvent,i=e.$$internal,a=void 0!==i&&i,s=e.$$automatic,o=void 0!==s&&s,f=t;t||null===t||u(function(e){var t=e.window,r=t.AlgoliaAnalyticsObject||"aa";"string"==typeof r&&(f=t[r]),f||(t.AlgoliaAnalyticsObject=r,t[r]||(t[r]=function(){t[r].queue||(t[r].queue=[]);for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];t[r].queue.push(n)},t[r].version=v,t[r].shouldAddScript=!0),f=t[r])});var d=f||l.Z;return function(e){var t,i,s=e.instantSearchInstance,u=s.middleware.filter(function(e){return"ais.insights"===e.instance.$$type&&e.instance.$$internal}).map(function(e){return e.creator});s.unuse.apply(s,function(e){if(Array.isArray(e))return y(e)}(u)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(u)||g(u)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var f=p(function(e){if(e.appId&&e.apiKey)return[e.appId,e.apiKey];if(!e.transporter)return[e.applicationID,e.apiKey];var t=e.transporter,r=t.headers||t.baseHeaders,n=t.queryParameters||t.baseQueryParameters,i="x-algolia-application-id",a="x-algolia-api-key";return[r[i]||n[i],r[a]||n[a]]}(s.client),2),v=f[0],P=f[1],S=void 0,E=void 0,j=void 0,O=d.queue;if(Array.isArray(O)){var x=["setUserToken","init"].map(function(e){var t=(0,h.s)(O.slice().reverse(),function(t){return p(t,1)[0]===e})||[];return p(t,2)[1]}),F=p(x,2);E=F[0],S=F[1]}return d("getUserToken",null,function(e,t){j=R(t)}),(r||!w(d))&&d("init",m({appId:v,apiKey:P,partial:!0},r)),{$$type:"ais.insights",$$internal:a,$$automatic:o,onStateChange:function(){},subscribe:function(){if(d.shouldAddScript){var e="[insights middleware]: could not load search-insights.js. Please load it manually following https://alg.li/insights-init";try{var t=document.createElement("script");t.async=!0,t.src=b,t.onerror=function(){s.emit("error",Error(e))},document.body.appendChild(t),d.shouldAddScript=!1}catch(t){d.shouldAddScript=!1,s.emit("error",Error(e))}}},started:function(){d("addAlgoliaAgent","insights-middleware"),i=s.mainHelper;var e,u,l,f,g,y,b,O,x,F,q,T,I=d.queue;if(Array.isArray(I)){var D=["setUserToken","init"].map(function(e){var t=(0,h.s)(I.slice().reverse(),function(t){return p(t,1)[0]===e})||[];return p(t,2)[1]}),_=p(D,2);E=_[0],S=_[1]}l=(null===(e=s._initialResults)||void 0===e?void 0:null===(u=e[s.indexName])||void 0===u?void 0:u.state)||{},f=s.mainHelper.state,t={userToken:l.userToken||f.userToken,clickAnalytics:l.clickAnalytics||f.clickAnalytics},o||i.overrideStateWithoutTriggeringChangeEvent(m(m({},i.state),{},{clickAnalytics:!0})),a||s.scheduleSearch();var C=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=R(e);if(r){var n=i.state.userToken;t?a():setTimeout(a,0)}function a(){i.overrideStateWithoutTriggeringChangeEvent(m(m({},i.state),{},{userToken:r})),n&&n!==e&&s.scheduleSearch()}};function k(e){C(e,!0),d("setUserToken",e)}var A=void 0;A=function(e){if(("undefined"==typeof document?"undefined":c(document))==="object"&&"string"==typeof document.cookie)for(var t="".concat(e,"="),r=document.cookie.split(";"),n=0;n<r.length;n++){for(var i=r[n];" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(t))return i.substring(t.length,i.length)}}("_ALGOLIA")||"anonymous-".concat("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}));var N=t.userToken;null!=r&&r.userToken&&(x=r.userToken),x?k(x):N?k(N):j?k(j):E?k(E):A&&(k(A),(null!=r&&r.useCookie||null!==(F=S)&&void 0!==F&&F.useCookie)&&(g=A,y=(null==r?void 0:r.cookieDuration)||(null===(q=S)||void 0===q?void 0:q.cookieDuration),(b=new Date).setTime(b.getTime()+(y||15552e6)),O="expires=".concat(b.toUTCString()),document.cookie="_ALGOLIA=".concat(g,";").concat(O,";path=/"))),d("onUserTokenChange",function(e){return C(e,!0)},{immediate:!0});var H=d;w(d)&&(H=function(e,t){return d(e,t,{headers:{"X-Algolia-Application-Id":v,"X-Algolia-API-Key":P}})});var U=new Set;s.mainHelper.derivedHelpers[0].on("result",function(e){var t=e.results;t&&(!t.queryID||t.queryID!==T)&&(T=t.queryID,U.clear())}),s.sendEventToInsights=function(e){if(n)n(e,H);else if(e.insightsMethod){if("viewedObjectIDs"===e.insightsMethod){var t=e.payload,r=t.objectIDs.filter(function(e){return!U.has(e)});if(0===r.length)return;r.forEach(function(e){return U.add(e)}),t.objectIDs=r}e.payload.algoliaSource=["instantsearch"],o&&e.payload.algoliaSource.push("instantsearch-automatic"),"internal"===e.eventModifier&&e.payload.algoliaSource.push("instantsearch-internal"),H(e.insightsMethod,e.payload)}}},unsubscribe:function(){d("onUserTokenChange",void 0),s.sendEventToInsights=l.Z,i&&t&&(i.overrideStateWithoutTriggeringChangeEvent(m(m({},i.state),t)),s.scheduleSearch())}}}}function w(e){var t=p((e.version||"").split(".").map(Number),2),r=t[0],n=t[1];return r>=3||2===r&&n>=6||1===r&&n>=10}function R(e){if(e)return"number"==typeof e?e.toString():e}var S=r(37925),E=r(81850);function j(e){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function O(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,F(n.key),n)}}function x(e,t,r){return(t=F(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){var t=function(e,t){if("object"!=j(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==j(t)?t:String(t)}var q=function(e){e&&(window.document.title=e)},T=function(){var e,t;function r(e){var t=this,n=e.windowTitle,i=e.writeDelay,a=e.createURL,s=e.parseURL,o=e.getLocation,c=e.start,l=e.dispose,h=e.push,f=e.cleanUrlOnDispose;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),x(this,"$$type","ais.browser"),x(this,"windowTitle",void 0),x(this,"writeDelay",void 0),x(this,"_createURL",void 0),x(this,"parseURL",void 0),x(this,"getLocation",void 0),x(this,"writeTimer",void 0),x(this,"_onPopState",void 0),x(this,"inPopState",!1),x(this,"isDisposed",!1),x(this,"latestAcknowledgedHistory",0),x(this,"_start",void 0),x(this,"_dispose",void 0),x(this,"_push",void 0),x(this,"_cleanUrlOnDispose",void 0),this.windowTitle=n,this.writeTimer=void 0,this.writeDelay=void 0===i?400:i,this._createURL=a,this.parseURL=s,this.getLocation=o,this._start=c,this._dispose=l,this._push=h,this._cleanUrlOnDispose=void 0===f||f,u(function(e){var r=e.window;q(t.windowTitle&&t.windowTitle(t.read())),t.latestAcknowledgedHistory=r.history.length})}return e=[{key:"read",value:function(){return this.parseURL({qsModule:E,location:this.getLocation()})}},{key:"write",value:function(e){var t=this;u(function(r){var n=r.window,i=t.createURL(e),a=t.windowTitle&&t.windowTitle(e);t.writeTimer&&clearTimeout(t.writeTimer),t.writeTimer=setTimeout(function(){q(a),t.shouldWrite(i)&&(t._push?t._push(i):n.history.pushState(e,a||"",i),t.latestAcknowledgedHistory=n.history.length),t.inPopState=!1,t.writeTimer=void 0},t.writeDelay)})}},{key:"onUpdate",value:function(e){var t=this;this._start&&this._start(function(){e(t.read())}),this._onPopState=function(){t.writeTimer&&(clearTimeout(t.writeTimer),t.writeTimer=void 0),t.inPopState=!0,e(t.read())},u(function(e){e.window.addEventListener("popstate",t._onPopState)})}},{key:"createURL",value:function(e){return this._createURL({qsModule:E,routeState:e,location:this.getLocation()})}},{key:"dispose",value:function(){var e=this;this._dispose&&this._dispose(),this.isDisposed=!0,u(function(t){var r=t.window;e._onPopState&&r.removeEventListener("popstate",e._onPopState)}),this.writeTimer&&clearTimeout(this.writeTimer),this._cleanUrlOnDispose&&this.write({})}},{key:"start",value:function(){this.isDisposed=!1}},{key:"shouldWrite",value:function(e){var t=this;return u(function(r){var n=r.window;if(t.isDisposed&&!t._cleanUrlOnDispose)return!1;var i=!(t.isDisposed&&t.latestAcknowledgedHistory!==n.history.length);return!t.inPopState&&i&&e!==n.location.href})}}],O(r.prototype,e),t&&O(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function I(e){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var D=["configure"];function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){k(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function k(e,t,r){var n;return(n=function(e,t){if("object"!=I(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==I(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function A(e){return e.configure,function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,D)}function N(e){return e!==Object(e)}function H(e){return(H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){B(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function B(e,t,r){var n;return(n=function(e,t){if("object"!=H(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=H(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==H(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var L=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.router,r=void 0===t?function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.createURL,r=void 0===t?function(e){var t=e.qsModule,r=e.routeState,n=e.location,i=n.protocol,a=n.hostname,s=n.port,o=void 0===s?"":s,c=n.pathname,u=n.hash,l=t.stringify(r),h=""===o?"":":".concat(o);return l?"".concat(i,"//").concat(a).concat(h).concat(c,"?").concat(l).concat(u):"".concat(i,"//").concat(a).concat(h).concat(c).concat(u)}:t,n=e.parseURL,i=e.writeDelay,a=e.windowTitle,s=e.getLocation;return new T({createURL:r,parseURL:void 0===n?function(e){var t=e.qsModule,r=e.location;return t.parse(r.search.slice(1),{arrayLimit:99})}:n,writeDelay:void 0===i?400:i,windowTitle:a,getLocation:void 0===s?function(){return u(function(e){return e.window.location},{fallback:function(){throw Error("You need to provide `getLocation` to the `history` router in environments where `window` does not exist.")}})}:s,start:e.start,dispose:e.dispose,push:e.push,cleanUrlOnDispose:e.cleanUrlOnDispose})}():t,n=e.stateMapping,i=void 0===n?{$$type:"ais.simple",stateToRoute:function(e){return Object.keys(e).reduce(function(t,r){return C(C({},t),{},k({},r,A(e[r])))},{})},routeToState:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,r){return C(C({},t),{},k({},r,A(e[r])))},{})}}:n,a=e.$$internal,s=void 0!==a&&a;return function(e){var t=e.instantSearchInstance;t._createURL=function(e){var n=0===t.mainIndex.getWidgets().length?t._initialUiState:t.mainIndex.getWidgetUiState({}),a=Object.keys(e).reduce(function(t,r){return Q(Q({},t),{},B({},r,e[r]))},n),s=i.stateToRoute(a);return r.createURL(s)};var n=void 0,a=t._initialUiState;return{$$type:"ais.router({router:".concat(r.$$type||"__unknown__",", stateMapping:").concat(i.$$type||"__unknown__","})"),$$internal:s,onStateChange:function(e){var t=e.uiState,a=i.stateToRoute(t);(void 0===n||!function e(t,r){if(t===r)return!0;if(N(t)||N(r)||"function"==typeof t||"function"==typeof r)return t===r;if(Object.keys(t).length!==Object.keys(r).length)return!1;for(var n=0,i=Object.keys(t);n<i.length;n++){var a=i[n];if(!(a in r)||!e(t[a],r[a]))return!1}return!0}(n,a))&&(r.write(a),n=a)},subscribe:function(){t._initialUiState=Q(Q({},a),i.routeToState(r.read())),r.onUpdate(function(e){t.mainIndex.getWidgets().length>0&&t.setUiState(i.routeToState(e))})},started:function(){var e;null===(e=r.start)||void 0===e||e.call(r)},unsubscribe:function(){r.dispose()}}}},W=r(94739),$=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.descendantName,n=t.modifierName;return"".concat("ais","-").concat(e).concat(r?"-".concat(r):"").concat(n?"--".concat(n):"")}};function V(e,t){return(Array.isArray(t)?t:t.split(".")).reduce(function(e,t){return e&&e[t]},e)}var G=r(61159),M=$("Highlight");function K(e){var t=G.Rn.highlightPreTag,r=G.Rn.highlightPostTag;return e.map(function(e){return e.isHighlighted?t+e.value+r:e.value}).join("")}var z=r(39639),J=new RegExp(/\w/i);function Z(e){return(Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function X(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=Z(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=Z(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Z(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ee(e){return e.some(function(e){return e.isHighlighted})?e.map(function(t,r){var n,i,a,s,o;return X(X({},t),{},{isHighlighted:(a=e[r],s=(null===(n=e[r+1])||void 0===n?void 0:n.isHighlighted)||!0,o=(null===(i=e[r-1])||void 0===i?void 0:i.isHighlighted)||!0,J.test((0,z.A)(a.value))||o!==s?!a.isHighlighted:!o)})}):e.map(function(e){return X(X({},e),{},{isHighlighted:!1})})}function et(e){var t=G.Rn.highlightPostTag,r=G.Rn.highlightPreTag,n=e.split(r),i=n.shift(),a=i?[{value:i,isHighlighted:!1}]:[];return n.forEach(function(e){var r=e.split(t);a.push({value:r[0],isHighlighted:!0}),""!==r[1]&&a.push({value:r[1],isHighlighted:!1})}),a}var er=$("ReverseHighlight"),en=$("Snippet"),ei=$("ReverseSnippet"),ea=r(90142);function es(e){return(es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eo(e){return(eo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ec(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ec(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eo(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eo(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ec(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var el=r(83880),eh=Promise.resolve();function ef(e){var t=null,r=!1,n=function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];null===t&&(t=eh.then(function(){if(t=null,r){r=!1;return}e.apply(void 0,i)}))};return n.wait=function(){if(null===t)throw Error("The deferred function should be called before calling `wait()`");return t},n.cancel=function(){null!==t&&(r=!0)},n}var ed=r(95332);function em(e){return(em="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ep(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ey(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eg(Object(r),!0).forEach(function(t){ev(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ev(e,t,r){var n;return(n=function(e,t){if("object"!=em(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=em(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==em(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eb(e){var t=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=0;return e.replace(/%s/g,function(){return encodeURIComponent(r[i++])})};return Object.keys(e).map(function(r){var n;return t("%s=%s",r,(n=e[r],"[object Object]"===Object.prototype.toString.call(n)||"[object Array]"===Object.prototype.toString.call(n))?JSON.stringify(e[r]):e[r])}).join("&")}function eP(e){return(eP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ew(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ew(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eP(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ew(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eS(e,t){var r=e[t.getIndexId()]||{};t.getHelper().setState(t.getWidgetSearchParameters(t.getHelper().state,{uiState:r})),t.getWidgets().filter(ed.J).forEach(function(t){return eS(e,t)})}function eE(e){return(eE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ej(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ej(Object(r),!0).forEach(function(t){eI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ej(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ex(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eD(n.key),n)}}function eF(e,t){return(eF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eq(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function eT(e){return(eT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eI(e,t,r){return(t=eD(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eD(e){var t=function(e,t){if("object"!=eE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eE(t)?t:String(t)}var e_=(0,el.K)({name:"instantsearch"});function eC(){return"#"}var ek={preserveSharedStateOnUnmount:!1,persistHierarchicalRootCount:!1},eA=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eF(e,t)}(a,e);var t,r,n,i=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=eT(a);if(t){var n=eT(this).constructor;e=Reflect.construct(r,arguments,n)}else e=r.apply(this,arguments);return function(e,t){if(t&&("object"===eE(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return eq(e)}(this,e)});function a(e){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,a),eI(eq(t=i.call(this)),"client",void 0),eI(eq(t),"indexName",void 0),eI(eq(t),"compositionID",void 0),eI(eq(t),"insightsClient",void 0),eI(eq(t),"onStateChange",null),eI(eq(t),"future",void 0),eI(eq(t),"helper",void 0),eI(eq(t),"mainHelper",void 0),eI(eq(t),"mainIndex",void 0),eI(eq(t),"started",void 0),eI(eq(t),"templatesConfig",void 0),eI(eq(t),"renderState",{}),eI(eq(t),"_stalledSearchDelay",void 0),eI(eq(t),"_searchStalledTimer",void 0),eI(eq(t),"_initialUiState",void 0),eI(eq(t),"_initialResults",void 0),eI(eq(t),"_createURL",void 0),eI(eq(t),"_searchFunction",void 0),eI(eq(t),"_mainHelperSearch",void 0),eI(eq(t),"_hasSearchWidget",!1),eI(eq(t),"_hasRecommendWidget",!1),eI(eq(t),"_insights",void 0),eI(eq(t),"middleware",[]),eI(eq(t),"sendEventToInsights",void 0),eI(eq(t),"status","idle"),eI(eq(t),"error",void 0),eI(eq(t),"scheduleSearch",ef(function(){t.started&&t.mainHelper.search()})),eI(eq(t),"scheduleRender",ef(function(){var e,r=!(arguments.length>0)||void 0===arguments[0]||arguments[0];!(null!==(e=t.mainHelper)&&void 0!==e&&e.hasPendingRequests())&&(clearTimeout(t._searchStalledTimer),t._searchStalledTimer=null,r&&(t.status="idle",t.error=void 0)),t.mainIndex.render({instantSearchInstance:eq(t)}),t.emit("render")})),eI(eq(t),"onInternalStateChange",ef(function(){var e=t.mainIndex.getWidgetUiState({});t.middleware.forEach(function(t){t.instance.onStateChange({uiState:e})})})),t.setMaxListeners(100);var t,r=e.indexName,n=e.compositionID,s=e.numberLocale,o=e.initialUiState,c=e.routing,h=void 0===c?null:c,f=e.insights,d=void 0===f?void 0:f,m=e.searchFunction,p=e.stalledSearchDelay,g=e.searchClient,y=void 0===g?null:g,v=e.insightsClient,b=void 0===v?null:v,w=e.onStateChange,R=e.future,E=void 0===R?eO(eO({},ek),e.future||{}):R;if(null===y)throw Error(e_("The `searchClient` option is required."));if("function"!=typeof y.search)throw Error("The `searchClient` must implement a `search` method.\n\nSee: https://www.algolia.com/doc/guides/building-search-ui/going-further/backend-search/in-depth/backend-instantsearch/js/");if("function"==typeof y.addAlgoliaAgent&&y.addAlgoliaAgent("instantsearch.js (".concat("4.77.1",")")),b&&"function"!=typeof b)throw Error(e_("The `insightsClient` option should be a function."));if(t.client=y,t.future=E,t.insightsClient=b,t.indexName=void 0===r?"":r,t.compositionID=n,t.helper=null,t.mainHelper=null,t.mainIndex=(0,W.Z)({indexName:t.compositionID||t.indexName}),t.onStateChange=void 0===w?null:w,t.started=!1,t.templatesConfig={helpers:{formatNumber:function(e,t){return Number(t(e)).toLocaleString(s)},highlight:function(e,t){try{var r,n,i,a,s,o,c,u,l,h=JSON.parse(e);return t((n=(r=eu(eu({},h),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,s=r.hit,o=r.cssClasses,c=void 0===o?{}:o,u=(V(s._highlightResult,n)||{}).value,l=M({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),(void 0===u?"":u).replace(RegExp(G.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(G.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\nThe highlight helper expects a JSON object of the format:\n{ "attribute": "name", "highlightedTagName": "mark" }')}},reverseHighlight:function(e,t){try{var r,n,i,a,s,o,c,u,l,h=JSON.parse(e);return t((n=(r=eu(eu({},h),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,s=r.hit,o=r.cssClasses,c=void 0===o?{}:o,u=(V(s._highlightResult,n)||{}).value,l=er({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),K(ee(et(void 0===u?"":u))).replace(RegExp(G.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(G.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\n  The reverseHighlight helper expects a JSON object of the format:\n  { "attribute": "name", "highlightedTagName": "mark" }')}},snippet:function(e,t){try{var r,n,i,a,s,o,c,u,l,h=JSON.parse(e);return t((n=(r=eu(eu({},h),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,s=r.hit,o=r.cssClasses,c=void 0===o?{}:o,u=(V(s._snippetResult,n)||{}).value,l=en({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),(void 0===u?"":u).replace(RegExp(G.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(G.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\nThe snippet helper expects a JSON object of the format:\n{ "attribute": "name", "highlightedTagName": "mark" }')}},reverseSnippet:function(e,t){try{var r,n,i,a,s,o,c,u,l,h=JSON.parse(e);return t((n=(r=eu(eu({},h),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,s=r.hit,o=r.cssClasses,c=void 0===o?{}:o,u=(V(s._snippetResult,n)||{}).value,l=ei({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),K(ee(et(void 0===u?"":u))).replace(RegExp(G.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(G.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\n  The reverseSnippet helper expects a JSON object of the format:\n  { "attribute": "name", "highlightedTagName": "mark" }')}},insights:function(e,t){try{var r,n=JSON.parse(e),i=n.method,a=n.payload;return t((r=eu({objectIDs:[this.objectID]},a),function(e){var t,r=e.method,n=e.payload;if("object"!==es(n))throw Error("The insights helper expects the payload to be an object.");try{t=(0,ea.a)(n)}catch(e){throw Error("Could not JSON serialize the payload object.")}return'data-insights-method="'.concat(r,'" data-insights-payload="').concat(t,'"')}({method:i,payload:r})))}catch(e){throw Error('\nThe insights helper expects a JSON object of the format:\n{ "method": "method-name", "payload": { "eventName": "name of the event" } }')}}},compileOptions:{}},t._stalledSearchDelay=void 0===p?200:p,t._searchStalledTimer=null,t._createURL=eC,t._initialUiState=void 0===o?{}:o,t._initialResults=null,t._insights=d,m&&(t._searchFunction=m),t.sendEventToInsights=l.Z,h){var j="boolean"==typeof h?{}:h;j.$$internal=!0,t.use(L(j))}if(d){var O="boolean"==typeof d?{}:d;O.$$internal=!0,t.use(P(O))}return u(function(e){var t,r;return(null===(t=e.window.navigator)||void 0===t?void 0:null===(r=t.userAgent)||void 0===r?void 0:r.indexOf("Algolia Crawler"))>-1},{fallback:function(){return!1}})&&t.use(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.$$internal,r=void 0!==t&&t;return function(e){var t=e.instantSearchInstance,n={widgets:[]},i=document.createElement("meta"),a=document.querySelector("head");return i.name="instantsearch:widgets",{$$type:"ais.metadata",$$internal:r,onStateChange:function(){},subscribe:function(){setTimeout(function(){var e=t.client;n.ua=e.transporter&&e.transporter.userAgent?e.transporter.userAgent.value:e._ua,function e(t,r,n){var i=(0,S.q)(r,r.mainIndex,r._initialUiState);t.forEach(function(t){var a={};if(t.getWidgetRenderState){var s=t.getWidgetRenderState(i);s&&s.widgetParams&&(a=s.widgetParams)}var o=Object.keys(a).filter(function(e){return void 0!==a[e]});n.widgets.push({type:t.$$type,widgetType:t.$$widgetType,params:o}),"ais.index"===t.$$type&&e(t.getWidgets(),r,n)})}(t.mainIndex.getWidgets(),t,n),t.middleware.forEach(function(e){return n.widgets.push({middleware:!0,type:e.instance.$$type,internal:e.instance.$$internal})}),i.content=JSON.stringify(n),a.appendChild(i)},0)},started:function(){},unsubscribe:function(){i.remove()}}}}({$$internal:!0})),t}return r=[{key:"_isSearchStalled",get:function(){return"stalled"===this.status}},{key:"use",value:function(){for(var e=this,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r.map(function(t){var r=eO({$$type:"__unknown__",$$internal:!1,subscribe:l.Z,started:l.Z,unsubscribe:l.Z,onStateChange:l.Z},t({instantSearchInstance:e}));return e.middleware.push({creator:t,instance:r}),r});return this.started&&i.forEach(function(e){e.subscribe(),e.started()}),this}},{key:"unuse",value:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.middleware.filter(function(e){return t.includes(e.creator)}).forEach(function(e){return e.instance.unsubscribe()}),this.middleware=this.middleware.filter(function(e){return!t.includes(e.creator)}),this}},{key:"EXPERIMENTAL_use",value:function(){return this.use.apply(this,arguments)}},{key:"addWidget",value:function(e){return this.addWidgets([e])}},{key:"addWidgets",value:function(e){if(!Array.isArray(e))throw Error(e_("The `addWidgets` method expects an array of widgets. Please use `addWidget`."));if(e.some(function(e){return"function"!=typeof e.init&&"function"!=typeof e.render}))throw Error(e_("The widget definition expects a `render` and/or an `init` method."));if(this.compositionID&&e.some(ed.J))throw Error(e_("The `index` widget cannot be used with a composition-based InstantSearch implementation."));return this.mainIndex.addWidgets(e),this}},{key:"removeWidget",value:function(e){return this.removeWidgets([e])}},{key:"removeWidgets",value:function(e){if(!Array.isArray(e))throw Error(e_("The `removeWidgets` method expects an array of widgets. Please use `removeWidget`."));if(e.some(function(e){return"function"!=typeof e.dispose}))throw Error(e_("The widget definition expects a `dispose` method."));return this.mainIndex.removeWidgets(e),this}},{key:"start",value:function(){var e=this;if(this.started)throw Error(e_("The `start` method has already been called once."));var t=this.mainHelper||o(this.client,this.indexName,void 0,{persistHierarchicalRootCount:this.future.persistHierarchicalRootCount});if(this.compositionID&&(t.searchForFacetValues=t.searchForCompositionFacetValues.bind(t)),t.search=function(){return e.status="loading",e.scheduleRender(!1),e._hasSearchWidget&&(e.compositionID?t.searchWithComposition():t.searchOnlyWithDerivedHelpers()),e._hasRecommendWidget&&t.recommend(),t},this._searchFunction){var r={search:function(){return new Promise(l.Z)}};this._mainHelperSearch=t.search.bind(t),t.search=function(){var n=e.mainIndex.getHelper(),i=o(r,n.state.index,n.state);return i.once("search",function(t){var r=t.state;n.overrideStateWithoutTriggeringChangeEvent(r),e._mainHelperSearch()}),i.on("change",function(e){var t=e.state;n.setState(t)}),e._searchFunction(i),t}}if(t.on("error",function(t){var r=t.error;if(!(r instanceof Error)){var n=r;r=Object.keys(n).reduce(function(e,t){return e[t]=n[t],e},Error(n.message))}r.error=r,e.error=r,e.status="error",e.scheduleRender(!1),e.emit("error",r)}),this.mainHelper=t,this.middleware.forEach(function(e){e.instance.subscribe()}),this.mainIndex.init({instantSearchInstance:this,parent:null,uiState:this._initialUiState}),this._initialResults){(function(e,t){if(t&&("transporter"in e&&!e._cacheHydrated||e._useCache&&"function"==typeof e.addAlgoliaAgent)){var r=[Object.keys(t).reduce(function(e,r){var n=t[r],i=n.state,a=n.requestParams,s=n.results,o=s&&i?s.map(function(e,t){return ey({indexName:i.index||e.index},null!=a&&a[t]||e.params?{params:eb((null==a?void 0:a[t])||e.params.split("&").reduce(function(e,t){var r,n=function(e){if(Array.isArray(e))return e}(r=t.split("="))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(r,2)||function(e,t){if(e){if("string"==typeof e)return ep(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ep(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=n[0],a=n[1];return e[i]=a?decodeURIComponent(a):"",e},{}))}:{})}):[];return e.concat(o)},[])],n=Object.keys(t).reduce(function(e,r){var n=t[r].results;return n?e.concat(n):e},[]);if("transporter"in e&&!e._cacheHydrated){e._cacheHydrated=!0;var i=e.search.bind(e);e.search=function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];var s=Array.isArray(t)?t.map(function(e){return ey(ey({},e),{},{params:eb(e.params)})}):eb(t.requestBody.params);return e.transporter.responsesCache.get({method:"search",args:[s].concat(n)},function(){return i.apply(void 0,[t].concat(n))})},e.transporter.responsesCache.set({method:"search",args:r},{results:n})}if(!("transporter"in e)){var a="/1/indexes/*/queries_body_".concat(JSON.stringify({requests:r}));e.cache=ey(ey({},e.cache),{},ev({},a,JSON.stringify({results:Object.keys(t).map(function(e){return t[e].results})})))}}})(this.client,this._initialResults),n=this.mainHelper,a=Object.keys(i=this._initialResults).reduce(function(e,t){var r=i[t];return r.recommendResults?eR(eR({},e),r.recommendResults.results):e},{}),n._recommendCache=a;var n,i,a,s=this.scheduleSearch;this.scheduleSearch=ef(l.Z),ef(function(){e.scheduleSearch=s})()}else this.mainIndex.getWidgets().length>0&&this.scheduleSearch();this.helper=this.mainIndex.getHelper(),this.started=!0,this.middleware.forEach(function(e){e.instance.started()}),void 0===this._insights&&t.derivedHelpers[0].once("result",function(){e.mainIndex.getScopedResults().some(function(e){var t=e.results;return null==t?void 0:t._automaticInsights})&&e.use(P({$$internal:!0,$$automatic:!0}))})}},{key:"dispose",value:function(){var e;this.scheduleSearch.cancel(),this.scheduleRender.cancel(),clearTimeout(this._searchStalledTimer),this.removeWidgets(this.mainIndex.getWidgets()),this.mainIndex.dispose(),this.started=!1,this.removeAllListeners(),null===(e=this.mainHelper)||void 0===e||e.removeAllListeners(),this.mainHelper=null,this.helper=null,this.middleware.forEach(function(e){e.instance.unsubscribe()})}},{key:"scheduleStalledRender",value:function(){var e=this;this._searchStalledTimer||(this._searchStalledTimer=setTimeout(function(){e.status="stalled",e.scheduleRender()},this._stalledSearchDelay))}},{key:"setUiState",value:function(e){var t=this,r=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!this.mainHelper)throw Error(e_("The `start` method needs to be called before `setUiState`."));this.mainIndex.refreshUiState();var n="function"==typeof e?e(this.mainIndex.getWidgetUiState({})):e;this.onStateChange&&r?this.onStateChange({uiState:n,setUiState:function(e){eS("function"==typeof e?e(n):e,t.mainIndex),t.scheduleSearch(),t.onInternalStateChange()}}):(eS(n,this.mainIndex),this.scheduleSearch(),this.onInternalStateChange())}},{key:"getUiState",value:function(){return this.started&&this.mainIndex.refreshUiState(),this.mainIndex.getWidgetUiState({})}},{key:"createURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.started)throw Error(e_("The `start` method needs to be called before `createURL`."));return this._createURL(e)}},{key:"refresh",value:function(){if(!this.mainHelper)throw Error(e_("The `start` method needs to be called before `refresh`."));this.mainHelper.clearCache().search()}}],ex(a.prototype,r),n&&ex(a,n),Object.defineProperty(a,"prototype",{writable:!1}),a}(s),eN=r(22362),eH="7.15.1",eU=r(77414),eQ=r(36577),eB=r(93248),eL=r(81970),eW=r(76999),e$=r(25566);function eV(e){return(eV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eG(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eV(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eK=["react (".concat(n.version,")"),"react-instantsearch (".concat(eH,")"),"react-instantsearch-core (".concat(eH,")")],ez="react-instantsearch-server (".concat(eH,")");function eJ(e,t){"function"==typeof e.addAlgoliaAgent&&t.filter(Boolean).forEach(function(t){e.addAlgoliaAgent(t)})}var eZ=["children"];function eY(e){var t=e.children,r=function(e){var t=(0,eQ.N)(),r=(0,eB.a)(),i=(0,eL.s)(),a=(0,eW.P)(),s=null==i?void 0:i.initialResults,o=(0,n.useRef)(e),c=r||s||a,u=(0,n.useRef)(null);if(i&&(u=i.ssrSearchRef),null===u.current){var l,h,f,d=new eA(e);d._schedule=function(e){d._schedule.queue.push(e),clearTimeout(d._schedule.timer),d._schedule.timer=setTimeout(function(){d._schedule.queue.forEach(function(e){e()}),d._schedule.queue=[]},0)},d._schedule.queue=[],c&&(d._initialResults=s||{}),eJ(e.searchClient,[].concat(eK,[r&&ez,(f="undefined"!=typeof window&&(null===(l=window.next)||void 0===l?void 0:l.version)||(void 0!==e$?null===(h=e$.env)||void 0===h?void 0:"":void 0))?"next.js (".concat(f,")"):null])),c&&d.start(),r&&r.notifyServer({search:d}),e.routing,u.current=d}var m,p=u.current,g=o.current;g.indexName!==e.indexName&&(p.helper.setIndex(e.indexName||"").search(),o.current=e),g.searchClient!==e.searchClient&&(eJ(e.searchClient,[].concat(eK,[r&&ez])),p.mainHelper.setClient(e.searchClient).search(),o.current=e),g.onStateChange!==e.onStateChange&&(p.onStateChange=e.onStateChange,o.current=e),g.searchFunction!==e.searchFunction&&(p._searchFunction=e.searchFunction,o.current=e),g.stalledSearchDelay!==e.stalledSearchDelay&&(p._stalledSearchDelay=null!==(m=e.stalledSearchDelay)&&void 0!==m?m:200,o.current=e),(0,eU.J)(g.future,e.future)||(p.future=eM(eM({},ek),e.future),o.current=e);var y=(0,n.useRef)(null);return(0,eN.useSyncExternalStore)((0,n.useCallback)(function(){var e=u.current;return null===y.current?e.started||(e.start(),t()):(clearTimeout(y.current),e._preventWidgetCleanup=!1),function(){clearTimeout(e._schedule.timer),y.current=setTimeout(function(){e.dispose()}),e._preventWidgetCleanup=!0}},[t]),function(){return u.current},function(){return u.current})}(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,eZ));return r.started?n.createElement(a.Z.Provider,{value:r},n.createElement(i.Z.Provider,{value:r.mainIndex},t)):null}},11923:function(e,t,r){"use strict";r.d(t,{O:function(){return O}});var n=r(83880),i=r(14803),a=r(97886),s=r(90142);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e){var t,r=e.getIndex,n=e.widgetType,i=(e.methodName,e.args),a=e.instantSearchInstance;if(1===i.length&&"object"===l(i[0]))return[i[0]];var s=function(e){if(Array.isArray(e))return e}(t=i[0].split(":"))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(t,2)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=s[0],h=s[1],f=i[1],d=i[2],m=i[3]||{};if(!f||("click"===o||"conversion"===o)&&!d)return[];var p=Array.isArray(f)?f:[f];if(0===p.length)return[];var g=p[0].__queryID,y=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=[],n=0;n<Math.ceil(e.length/t);n++)r.push(e.slice(n*t,(n+1)*t));return r}(p),v=y.map(function(e){return e.map(function(e){return e.objectID})}),b=y.map(function(e){return e.map(function(e){return e.__position})});return"view"===o?"idle"!==a.status?[]:y.map(function(e,t){return{insightsMethod:"viewedObjectIDs",widgetType:n,eventType:o,payload:c({eventName:d||"Hits Viewed",index:r(),objectIDs:v[t]},m),hits:e,eventModifier:h}}):"click"===o?y.map(function(e,t){return{insightsMethod:"clickedObjectIDsAfterSearch",widgetType:n,eventType:o,payload:c({eventName:d||"Hit Clicked",index:r(),queryID:g,objectIDs:v[t],positions:b[t]},m),hits:e,eventModifier:h}}):"conversion"===o?y.map(function(e,t){return{insightsMethod:"convertedObjectIDsAfterSearch",widgetType:n,eventType:o,payload:c({eventName:d||"Hit Converted",index:r(),queryID:g,objectIDs:v[t]},m),hits:e,eventModifier:h}}):[]}var f=r(61159);function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach(function(t){R(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function R(e,t,r){var n;return(n=function(e,t){if("object"!=b(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=b(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==b(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var S=(0,n.K)({name:"hits",connector:!0}),E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Z;return(0,a._)(e,S()),function(r){var n,i,a=r||{},o=a.escapeHTML,c=void 0===o||o,u=a.transformItems,l=void 0===u?function(e){return e}:u;return{$$type:"ais.hits",init:function(t){e(w(w({},this.getWidgetRenderState(t)),{},{instantSearchInstance:t.instantSearchInstance}),!0)},render:function(t){var r=this.getWidgetRenderState(t);e(w(w({},r),{},{instantSearchInstance:t.instantSearchInstance}),!1),r.sendEvent("view:internal",r.items)},getRenderState:function(e,t){return w(w({},e),{},{hits:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t,a,o,u,d,m,g,y,b,P,w,R,S,E,j,O,x,F,q=e.results,T=e.helper,I=e.instantSearchInstance;if(n||(a=(t={instantSearchInstance:I,getIndex:function(){return T.getIndex()},widgetType:this.$$type}).instantSearchInstance,o=t.getIndex,u=t.widgetType,d={},m=void 0,n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];h({widgetType:u,getIndex:o,methodName:"sendEvent",args:t,instantSearchInstance:a}).forEach(function(e){"click"===e.eventType&&"internal"===e.eventModifier&&d[e.eventType]||(d[e.eventType]=!0,a.sendEventToInsights(e))}),clearTimeout(m),m=setTimeout(function(){d={}},0)}),i||(y=(g={getIndex:function(){return T.getIndex()},widgetType:this.$$type,instantSearchInstance:I}).getIndex,b=g.widgetType,P=g.instantSearchInstance,i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=h({widgetType:b,getIndex:y,methodName:"bindEvent",args:t,instantSearchInstance:P});return n.length?"data-insights-event=".concat((0,s.a)(n)):""}),!q)return{hits:[],items:[],results:void 0,banner:void 0,sendEvent:n,bindEvent:i,widgetParams:r};c&&q.hits.length>0&&(q.hits=(0,f.mY)(q.hits));var D=l((w=q.hits,R=q.page,S=q.hitsPerPage,E=w.map(function(e,t){return p(p({},e),{},{__position:S*R+t+1})}),(j=q.queryID)?E.map(function(e){return v(v({},e),{},{__queryID:j})}):E),{results:q}),_=null===(O=q.renderingContent)||void 0===O?void 0:null===(x=O.widgets)||void 0===x?void 0:null===(F=x.banners)||void 0===F?void 0:F[0];return{hits:D,items:D,results:q,banner:_,sendEvent:n,bindEvent:i,widgetParams:r}},dispose:function(e){var r=e.state;return(t(),c)?r.setQueryParameters(Object.keys(f.dg).reduce(function(e,t){return w(w({},e),{},R({},t,void 0))},{})):r},getWidgetSearchParameters:function(e,t){return c?e.setQueryParameters(f.dg):e}}}},j=r(39531);function O(e,t){return(0,j.B)(E,e,t)}},63639:function(e,t,r){"use strict";r.d(t,{l:function(){return d}});var n=r(83880),i=r(14803),a=r(97886);function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=(0,n.K)({name:"search-box",connector:!0}),l=function(e,t){return t(e)},h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Z;return(0,a._)(e,u()),function(r){var n,i,a=(r||{}).queryHook,s=void 0===a?l:a;return{$$type:"ais.searchBox",init:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(e){var r=e.state;return t(),r.setQueryParameter("query",void 0)},getRenderState:function(e,t){return c(c({},e),{},{searchBox:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t=e.helper,a=e.instantSearchInstance,o=e.state;return n||(n=function(e){s(e,function(e){return t.setQuery(e).search()})},i=function(){t.setQuery("").search()}),{query:o.query||"",refine:n,clear:i,widgetParams:r,isSearchStalled:"stalled"===a.status}},getWidgetUiState:function(e,t){var r=t.searchParameters.query||"";return""===r||e&&e.query===r?e:c(c({},e),{},{query:r})},getWidgetSearchParameters:function(e,t){var r=t.uiState;return e.setQueryParameter("query",r.query||"")}}}},f=r(39531);function d(e,t){return(0,f.B)(h,e,t)}},1873:function(e,t,r){"use strict";r.d(t,{V:function(){return f}});var n=r(83880),i=r(14803),a=r(97886);function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=(0,n.K)({name:"stats",connector:!0}),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Z;return(0,a._)(e,u()),function(r){return{$$type:"ais.stats",init:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(){t()},getRenderState:function(e,t){return c(c({},e),{},{stats:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t=e.results,n=e.state;return t?{hitsPerPage:t.hitsPerPage,nbHits:t.nbHits,nbSortedHits:t.nbSortedHits,areHitsSorted:void 0!==t.appliedRelevancyStrictness&&t.appliedRelevancyStrictness>0&&t.nbSortedHits!==t.nbHits,nbPages:t.nbPages,page:t.page,processingTimeMS:t.processingTimeMS,query:t.query,widgetParams:r}:{hitsPerPage:n.hitsPerPage,nbHits:0,nbSortedHits:void 0,areHitsSorted:!1,nbPages:0,page:n.page||0,processingTimeMS:-1,query:n.query||"",widgetParams:r}}}}},h=r(39531);function f(e,t){return(0,h.B)(l,e,t)}},39531:function(e,t,r){"use strict";r.d(t,{B:function(){return b}});var n=r(2265),i=r(77414),a=r(88613),s=r(72701),o=r(29613),c=r(93248),u=r(81970),l=r(81182),h=r(63220);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var d=["instantSearchInstance","widgetParams"],m=["widgetParams"];function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function b(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},f=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},g=(0,c.a)(),b=(0,u.s)(),P=(0,o.z)(),w=(0,s.a)(),R=(0,l.q)(r),S=(0,l.q)(f),E=(0,n.useRef)(!0),j=(0,n.useRef)(null),O=(0,n.useRef)(P.status),x=(0,n.useMemo)(function(){return y(y({},e(function(e,t){if(t){E.current=!0;return}if(E.current){var r=e.instantSearchInstance,n=(e.widgetParams,v(e,d));(0,i.J)(n,j.current,function(e,t){return(null==e?void 0:e.constructor)===Function&&(null==t?void 0:t.constructor)===Function})&&r.status===O.current||(T(n),j.current=n,O.current=r.status)}},function(){E.current=!1})(R)),S)},[e,R,S]),F=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(function(){if(x.getWidgetRenderState){var e,t=w.getHelper(),r=w.getWidgetUiState({})[w.getIndexId()];t.state=(null===(e=x.getWidgetSearchParameters)||void 0===e?void 0:e.call(x,t.state,{uiState:r}))||t.state;var n=(0,a.E)(w),i=n.results,s=n.scopedResults,o=n.recommendResults,c=x.getWidgetRenderState({helper:t,parent:w,instantSearchInstance:P,results:"recommend"===x.dependsOn&&o&&b?o[b.recommendIdx.current++]:i,scopedResults:s,state:t.state,renderState:P.renderState,templatesConfig:P.templatesConfig,createURL:w.createURL,searchMetadata:{isSearchStalled:"stalled"===P.status},status:P.status,error:P.error});return c.widgetParams,v(c,m)}return{}}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(t,2)||function(e,t){if(e){if("string"==typeof e)return p(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),q=F[0],T=F[1];return(0,h.F)({widget:x,parentIndex:w,props:R,shouldSsr:!!g}),q}},54148:function(e,t,r){"use strict";r.d(t,{b:function(){return f}});var n=r(2265),i=r(29613),a=r(97366),s=r(95332),o=r(88613),c=r(72701);function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return h(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f(){var e,t,r,h,f,d,m,p,g,y,v,b,P,w,R,S,E,j,O,x=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},F=x.catchError,q=(0,i.z)(),T=(e=(0,i.z)(),r=(t=(0,c.a)()).getIndexId(),f=(h=l((0,n.useState)(function(){return e.getUiState()}),2))[0],d=h[1],m=f[r],g=(p=l((0,n.useState)(function(){return e.renderState}),2))[0],y=p[1],v=g[r]||{},b=(0,n.useCallback)(function(t){e.setUiState(t)},[e]),P=(0,n.useCallback)(function(e){t.setIndexUiState(e)},[t]),(0,n.useEffect)(function(){function t(){d(e.getUiState()),y(e.renderState)}return e.addListener("render",t),function(){e.removeListener("render",t)}},[e]),{uiState:f,setUiState:b,indexUiState:m,setIndexUiState:P,renderState:g,indexRenderState:v}),I=T.uiState,D=T.setUiState,_=T.indexUiState,C=T.setIndexUiState,k=T.renderState,A=T.indexRenderState,N=(w=(0,i.z)(),R=(0,c.a)(),j=(E=function(e){if(Array.isArray(e))return e}(S=(0,n.useState)(function(){var e=(0,o.E)(R);return{results:e.results,scopedResults:e.scopedResults}}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(S,2)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,t)}}(S,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],O=E[1],(0,n.useEffect)(function(){function e(){var e=R.getResults();if(null!==e)O({results:e,scopedResults:R.getScopedResults()});else if(0===w.mainIndex.getIndexName().length){var t=w.mainIndex.getWidgets().find(s.J);t&&O({results:(0,o.E)(R).results,scopedResults:t.getScopedResults()})}}return w.addListener("render",e),function(){w.removeListener("render",e)}},[w,R]),j),H=N.results,U=N.scopedResults,Q=(0,n.useCallback)(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return q.use.apply(q,t),function(){q.unuse.apply(q,t)}},[q]),B=(0,n.useCallback)(function(){q.refresh()},[q]);return(0,a.L)(function(){if(F){var e=function(){};return q.addListener("error",e),function(){return q.removeListener("error",e)}}return function(){}},[q,F]),{results:H,scopedResults:U,uiState:I,setUiState:D,indexUiState:_,setIndexUiState:C,renderState:k,indexRenderState:A,addMiddlewares:Q,refresh:B,status:q.status,error:q.error}}},20783:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=(0,r(2265).createContext)(null)},76194:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=(0,r(2265).createContext)(null)},77414:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{J:function(){return function e(t,r,a){var s,o;if(null!=a&&a(t,r)||t===r)return!0;if(t&&r&&(s=t.constructor)===r.constructor){if(s===Date)return t.getTime()===r.getTime();if(s===RegExp)return t.toString()===r.toString();if(s===Array){if((o=t.length)===r.length)for(;o--&&e(t[o],r[o],a););return -1===o}if(!s||"object"===n(t)){for(s in o=0,t)if(i.call(t,s)&&++o&&!i.call(r,s)||!(s in r)||!e(t[s],r[s],a))return!1;return Object.keys(r).length===o}}return t!=t&&r!=r}}});var i=Object.prototype.hasOwnProperty},88613:function(e,t,r){"use strict";r.d(t,{E:function(){return c}});var n=r(54015);function i(e){var t,r,i;return new n.SearchResults(e,[{query:null!==(t=e.query)&&void 0!==t?t:"",page:null!==(r=e.page)&&void 0!==r?r:0,hitsPerPage:null!==(i=e.hitsPerPage)&&void 0!==i?i:20,hits:[],nbHits:0,nbPages:0,params:"",exhaustiveNbHits:!0,exhaustiveFacetsCount:!0,processingTimeMS:0,index:e.index}],{__isArtificial:!0})}function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=e.getHelper(),r=e.getResults()||i(t.state),n=e.getScopedResults().map(function(t){var n=t.indexId===e.getIndexId()?r:i(t.helper.state);return o(o({},t),{},{results:t.results||n})});return{results:r,scopedResults:n,recommendResults:t.lastRecommendResults}}},44737:function(e,t,r){"use strict";function n(e,t){if(!e)throw Error("Invariant failed")}r.d(t,{k:function(){return n}})},36577:function(e,t,r){"use strict";r.d(t,{N:function(){return a}});var n=r(2265);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(){var e;return(function(e){if(Array.isArray(e))return e}(e=(0,n.useReducer)(function(e){return e+1},0))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(e,2)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,t)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[1]}},72701:function(e,t,r){"use strict";r.d(t,{a:function(){return s}});var n=r(2265),i=r(20783),a=r(44737);function s(){var e=(0,n.useContext)(i.Z);return(0,a.k)(null!==e,"The <Index> component must be used within <InstantSearch>."),e}},29613:function(e,t,r){"use strict";r.d(t,{z:function(){return s}});var n=r(2265),i=r(76194),a=r(44737);function s(){var e=(0,n.useContext)(i.Z);return(0,a.k)(null!==e,"Hooks must be used inside the <InstantSearch> component.\n\nThey are not compatible with the `react-instantsearch-core@6.x` and `react-instantsearch-dom` packages, so make sure to use the <InstantSearch> component from `react-instantsearch-core@7.x`."),e}},81970:function(e,t,r){"use strict";r.d(t,{s:function(){return a}});var n=r(2265),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},93248:function(e,t,r){"use strict";r.d(t,{a:function(){return a}});var n=r(2265),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},97366:function(e,t,r){"use strict";r.d(t,{L:function(){return i}});var n=r(2265),i="undefined"!=typeof window?n.useLayoutEffect:n.useEffect},76999:function(e,t,r){"use strict";r.d(t,{P:function(){return a}});var n=r(2265),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},81182:function(e,t,r){"use strict";r.d(t,{q:function(){return s}});var n=r(2265),i=r(77414);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e){var t,r=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(function(){return e}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(t,2)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),s=r[0],o=r[1];return(0,i.J)(s,e)||o(e),s}},63220:function(e,t,r){"use strict";r.d(t,{F:function(){return l}});var n=r(2265),i=r.t(n,2),a=r(77414),s=i.use,o=r(29613),c=r(97366),u=r(76999);function l(e){var t,r,i=e.widget,l=e.parentIndex,h=e.props,f=e.shouldSsr,d=(0,u.P)(),m=(0,n.useRef)(h);(0,n.useEffect)(function(){m.current=h},[h]);var p=(0,n.useRef)(i);(0,n.useEffect)(function(){p.current=i},[i]);var g=(0,n.useRef)(null),y=f&&!l.getWidgets().includes(i),v=(0,o.z)();(0,c.L)(function(){var e=p.current;return g.current?(clearTimeout(g.current),(0,a.J)(h,m.current)||(l.removeWidgets([e]),l.addWidgets([i]))):f||l.addWidgets([i]),function(){g.current=setTimeout(function(){v._schedule(function(){v._preventWidgetCleanup||l.removeWidgets([e])})})}},[l,i,f,v,h]),(y||(null==d?void 0:null===(t=d.current)||void 0===t?void 0:t.status)==="pending")&&l.addWidgets([i]),"undefined"==typeof window&&null!=d&&d.current&&"ais.index"!==i.$$type&&(s(d.current),"ais.dynamicWidgets"!==i.$$type&&null!==(r=v.helper)&&void 0!==r&&r.lastResults&&s(d.current))}},39310:function(e,t,r){"use strict";r.d(t,{m:function(){return b}});var n,i,a,s,o=r(14749),c=r(70444),u=r(3415),l=["classNames","hits","itemComponent","sendEvent","emptyComponent","banner","bannerComponent"],h=r(2265),f=r(11923),d=["escapeHTML","transformItems","hitComponent","bannerComponent"],m=["hit","index"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function g(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e){var t=e.hit;return h.createElement("div",{style:{wordBreak:"break-all"}},JSON.stringify(t).slice(0,100),"…")}var v=(i=({createElement:a=(n={createElement:h.createElement,Fragment:h.Fragment}).createElement,Fragment:n.Fragment}).createElement,s=function(e){var t=e.classNames,r=e.banner;return r.image.urls[0].url?i("aside",{className:(0,u.cx)("ais-Hits-banner",t.bannerRoot)},r.link?i("a",{className:(0,u.cx)("ais-Hits-banner-link",t.bannerLink),href:r.link.url,target:r.link.target},i("img",{className:(0,u.cx)("ais-Hits-banner-image",t.bannerImage),src:r.image.urls[0].url,alt:r.image.title})):i("img",{className:(0,u.cx)("ais-Hits-banner-image",t.bannerImage),src:r.image.urls[0].url,alt:r.image.title})):null},function(e){var t=e.classNames,r=void 0===t?{}:t,n=e.hits,i=e.itemComponent,h=e.sendEvent,f=e.emptyComponent,d=e.banner,m=e.bannerComponent,p=function(e,t){if(null==e)return{};var r,n,i=(0,c.Z)(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,l);return a("div",(0,o.Z)({},p,{className:(0,u.cx)("ais-Hits",r.root,0===n.length&&(0,u.cx)("ais-Hits--empty",r.emptyRoot),p.className)}),d&&(m?a(m,{className:(0,u.cx)("ais-Hits-banner",r.bannerRoot),banner:d}):a(s,{classNames:r,banner:d})),0===n.length&&f?a(f,null):a("ol",{className:(0,u.cx)("ais-Hits-list",r.list)},n.map(function(e,t){return a(i,{key:e.objectID,hit:e,index:t,className:(0,u.cx)("ais-Hits-item",r.item),onClick:function(){h("click:internal",e,"Hit Clicked")},onAuxClick:function(){h("click:internal",e,"Hit Clicked")}})})))});function b(e){var t=e.escapeHTML,r=e.transformItems,n=e.hitComponent,i=void 0===n?y:n,a=e.bannerComponent,s=g(e,d),o=(0,f.O)({escapeHTML:t,transformItems:r},{$$widgetType:"ais.hits"}),c=o.hits,u=o.banner,l=o.sendEvent,b=(0,h.useMemo)(function(){return function(e){var t=e.hit,r=(e.index,g(e,m));return h.createElement("li",p({key:t.objectID},r),h.createElement(i,{hit:t,sendEvent:l}))}},[i,l]);return h.createElement(v,p({},s,{hits:c,sendEvent:l,itemComponent:b,banner:u,bannerComponent:!1===a?function(){return null}:a}))}},69723:function(e,t,r){"use strict";r.d(t,{R:function(){return P}});var n=r(2265),i=r(63639),a=r(3415),s=["formRef","inputRef","isSearchStalled","onChange","onReset","onSubmit","placeholder","value","autoFocus","resetIconComponent","submitIconComponent","loadingIconComponent","classNames","translations"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var c=n.createElement("path",{d:"M26.804 29.01c-2.832 2.34-6.465 3.746-10.426 3.746C7.333 32.756 0 25.424 0 16.378 0 7.333 7.333 0 16.378 0c9.046 0 16.378 7.333 16.378 16.378 0 3.96-1.406 7.594-3.746 10.426l10.534 10.534c.607.607.61 1.59-.004 2.202-.61.61-1.597.61-2.202.004L26.804 29.01zm-10.426.627c7.323 0 13.26-5.936 13.26-13.26 0-7.32-5.937-13.257-13.26-13.257C9.056 3.12 3.12 9.056 3.12 16.378c0 7.323 5.936 13.26 13.258 13.26z"});function u(e){var t=e.classNames;return n.createElement("svg",{className:(0,a.cx)("ais-SearchBox-submitIcon",t.submitIcon),width:"10",height:"10",viewBox:"0 0 40 40","aria-hidden":"true"},c)}var l=n.createElement("path",{d:"M8.114 10L.944 2.83 0 1.885 1.886 0l.943.943L10 8.113l7.17-7.17.944-.943L20 1.886l-.943.943-7.17 7.17 7.17 7.17.943.944L18.114 20l-.943-.943-7.17-7.17-7.17 7.17-.944.943L0 18.114l.943-.943L8.113 10z"});function h(e){var t=e.classNames;return n.createElement("svg",{className:(0,a.cx)("ais-SearchBox-resetIcon",t.resetIcon),viewBox:"0 0 20 20",width:"10",height:"10","aria-hidden":"true"},l)}var f=n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},n.createElement("circle",{strokeOpacity:".5",cx:"18",cy:"18",r:"18"}),n.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},n.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"}))));function d(e){var t=e.classNames;return n.createElement("svg",{"aria-label":"Results are loading",width:"16",height:"16",viewBox:"0 0 38 38",stroke:"#444",className:(0,a.cx)("ais-SearchBox-loadingIcon",t.loadingIcon),"aria-hidden":"true"},f)}function m(e){var t=e.formRef,r=e.inputRef,i=e.isSearchStalled,c=e.onChange,l=e.onReset,f=e.onSubmit,m=e.placeholder,p=e.value,g=e.autoFocus,y=e.resetIconComponent,v=e.submitIconComponent,b=e.loadingIconComponent,P=e.classNames,w=void 0===P?{}:P,R=e.translations,S=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,s);return n.createElement("div",o({},S,{className:(0,a.cx)("ais-SearchBox",w.root,S.className)}),n.createElement("form",{ref:t,action:"",className:(0,a.cx)("ais-SearchBox-form",w.form),noValidate:!0,onSubmit:function(e){e.preventDefault(),e.stopPropagation(),f&&f(e),r.current&&r.current.blur()},onReset:function(e){e.preventDefault(),e.stopPropagation(),l(e),r.current&&r.current.focus()},role:"search"},n.createElement("input",{ref:r,className:(0,a.cx)("ais-SearchBox-input",w.input),"aria-label":"Search",autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",placeholder:void 0===m?"":m,spellCheck:!1,maxLength:512,type:"search",value:p,onChange:c,onCompositionEnd:c,autoFocus:g}),n.createElement("button",{className:(0,a.cx)("ais-SearchBox-submit",w.submit),type:"submit",title:R.submitButtonTitle},n.createElement(void 0===v?u:v,{classNames:w})),n.createElement("button",{className:(0,a.cx)("ais-SearchBox-reset",w.reset),type:"reset",title:R.resetButtonTitle,hidden:0===p.length||i},n.createElement(void 0===y?h:y,{classNames:w})),n.createElement("span",{className:(0,a.cx)("ais-SearchBox-loadingIndicator",w.loadingIndicator),hidden:!i},n.createElement(void 0===b?d:b,{classNames:w}))))}function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var g=["queryHook","searchAsYouType","ignoreCompositionEvents","translations"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function P(e){var t,r=e.queryHook,a=e.searchAsYouType,s=void 0===a||a,o=e.ignoreCompositionEvents,c=void 0!==o&&o,u=e.translations,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,g),h=(0,i.l)({queryHook:r},{$$widgetType:"ais.searchBox"}),f=h.query,d=h.refine,P=h.isSearchStalled,w=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(f))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw i}}return o}}(t,2)||function(e,t){if(e){if("string"==typeof e)return b(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),R=w[0],S=w[1],E=(0,n.useRef)(null);function j(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];S(e),s&&!(c&&t)&&d(e)}f!==R&&document.activeElement!==E.current&&S(f);var O={inputRef:E,isSearchStalled:P,onChange:function(e){j(e.currentTarget.value,e.nativeEvent.isComposing)},onReset:function(){j(""),s||d("")},onSubmit:function(e){s||d(R),l.onSubmit&&l.onSubmit(e)},value:R,translations:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({submitButtonTitle:"Submit the search query",resetButtonTitle:"Clear the search query"},u)};return n.createElement(m,y({},l,O))}},87606:function(e,t,r){"use strict";r.d(t,{h:function(){return P}});var n=r(2265),i=r(83880),a=r(14803),s=r(97886),o=r(20148);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var h=(0,i.K)({name:"sort-by",connector:!0}),f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.Z;(0,s._)(e,h());var r={};return function(n){var i=n||{},a=i.items,s=i.transformItems,c=void 0===s?function(e){return e}:s;if(!Array.isArray(a))throw Error(h("The `items` option expects an array of objects."));return{$$type:"ais.sortBy",init:function(t){var r=t.instantSearchInstance,n=this.getWidgetRenderState(t),i=n.currentRefinement;(0,o.s)(a,function(e){return e.value===i}),e(l(l({},n),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(l(l({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(e){var n=e.state;return t(),r.initialIndex?n.setIndex(r.initialIndex):n},getRenderState:function(e,t){return l(l({},e),{},{sortBy:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t=e.results,i=e.helper,s=e.state,o=e.parent;!r.initialIndex&&o&&(r.initialIndex=o.getIndexName()),r.setIndex||(r.setIndex=function(e){i.setIndex(e).search()});var u=!t||0===t.nbHits;return{currentRefinement:s.index,options:c(a,{results:t}),refine:r.setIndex,hasNoResults:u,canRefine:!u&&a.length>0,widgetParams:n}},getWidgetUiState:function(e,t){var n=t.searchParameters.index;return l(l({},e),{},{sortBy:n!==r.initialIndex?n:void 0})},getWidgetSearchParameters:function(e,t){var n=t.uiState;return e.setQueryParameter("index",n.sortBy||r.initialIndex||e.index)}}}},d=r(39531),m=r(3415),p=["items","value","onChange","classNames"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y(e){var t=e.items,r=e.value,i=e.onChange,a=void 0===i?function(){}:i,s=e.classNames,o=void 0===s?{}:s,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p);return n.createElement("div",g({},c,{className:(0,m.cx)("ais-SortBy",o.root,c.className)}),n.createElement("select",{className:(0,m.cx)("ais-SortBy-select",o.select),onChange:function(e){return a(e.target.value)},value:r,"aria-label":"Sort results by"},t.map(function(e){return n.createElement("option",{className:(0,m.cx)("ais-SortBy-option",o.option),key:e.value,value:e.value},e.label)})))}var v=["items","transformItems"];function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function P(e){var t=e.items,r=e.transformItems,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,v),a=(0,d.B)(f,{items:t,transformItems:r},{$$widgetType:"ais.sortBy"}),s=a.currentRefinement,o=a.options,c=a.refine;return n.createElement(y,b({},i,{items:o,value:s,onChange:c}))}}}]);