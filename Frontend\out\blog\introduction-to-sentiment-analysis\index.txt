3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","introduction-to-sentiment-analysis","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","introduction-to-sentiment-analysis","d"],{"children":["__PAGE__?{\"blogDetails\":\"introduction-to-sentiment-analysis\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","introduction-to-sentiment-analysis","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
12:T455,<p>With advancements in technology and fields like deep learning, sentiment analysis is becoming more and more common for companies that want to gauge their customers’ sentiments.</p><p>Today, businesses use <a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;">natural language processing</span></a>, statistical analysis, and text analysis to identify the sentiment and classify words into positive, negative, and neutral categories.&nbsp;</p><p>The best companies understand the importance of understanding their customers’ sentiments – what they are saying, what they mean and how they are saying. You can use sentiment analysis to identify customer sentiment in comments, reviews, tweets, or social media platforms where people mention your brand.&nbsp;</p><p>As sentiment analysis is the domain of understanding emotions using software, we have prepared a complete guide to understand ‘what is sentiment analysis?’, its tools, and different classifications and use cases.&nbsp;</p>13:T66f,<p>Sentiment analysis can be defined as analyzing the positive or negative sentiment of the customer in text. The contextual analysis of identifying information helps businesses understand their customers’ social sentiment by monitoring online conversations.&nbsp;</p><p>As customers express their reviews and thoughts about the brand more openly than ever before, sentiment analysis has become a powerful tool to monitor and understand online conversations. Analyzing customer feedback and reviews automatically through survey responses or social media discussions allows you to learn what makes your customer happy or disappointed. Further, you can use this analysis to tailor your products and services to meet your customer’s needs and make your brand successful.&nbsp;</p><p><span style="font-family:Arial;">Recent advancements in </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">AI solutions</span></a><span style="font-family:Arial;"> have increased the efficiency of sentiment analysis algorithms. </span>You can creatively use advanced <a href="https://marutitech.com/artificial-intelligence-and-machine-learning/" target="_blank" rel="noopener">artificial intelligence and machine learning</a> tools for doing research and draw out the analysis.</p><p>For example, sentiment analysis can help you to automatically analyze 5000+ reviews about your brand by discovering whether your customer is happy or not satisfied by your pricing plans and customer services. Therefore, you can say that the application of sentiment is endless.</p>14:Td6b,<p>The sentiment analysis process mainly focuses on polarity, i.e., positive, negative, or neutral. Apart from polarity, it also considers the feelings and emotions(happy, sad, angry, etc.), intentions(interested or not interested), or urgency(urgent or not urgent) of the text.&nbsp;</p><p><img src="https://cdn.marutitech.com/993a41de-types_of_sentiment_analysis_copy.png" alt="Types of Sentiment Analysis" srcset="https://cdn.marutitech.com/993a41de-types_of_sentiment_analysis_copy.png 1000w, https://cdn.marutitech.com/993a41de-types_of_sentiment_analysis_copy-768x637.png 768w, https://cdn.marutitech.com/993a41de-types_of_sentiment_analysis_copy-705x584.png 705w, https://cdn.marutitech.com/993a41de-types_of_sentiment_analysis_copy-450x373.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>Depending on how you interpret customer feedback, you can classify them and meet your sentiment analysis. However, below are some of the popular sentiment analysis classifications:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Fine-grained Sentiment Analysis</strong></span></h3><p>If your business requires the polarity precisions, then you can classify your polarity categories into the following parts:</p><ul><li>Very positive&nbsp;</li><li>Positive&nbsp;</li><li>Neutral&nbsp;</li><li>Negative&nbsp;</li><li>Very Negative&nbsp;</li></ul><p>For polarity analysis, you can use the 5-star ratings as a customer review where very positive refers to a five-star rating and very negative refers to a one-star rating.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Emotion Detection</strong></span></h3><p>This type of sentiment analysis helps to detect customer emotions like happiness, disappointment, anger, sadness, etc. Here, you can use sentiment lexicons or complex <a href="https://marutitech.com/predictive-analytics-models-algorithms/" target="_blank" rel="noopener"><span style="color:#f05443;">machine learning algorithms</span></a> to identify the customer’s feelings.&nbsp;</p><p>One of the disadvantages of using sentiment lexicons is that people tend to express emotions in different ways. So, it may be confusing to understand human emotion clearly while using it.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Aspect-based Sentiment Analysis</strong></span></h3><p>Let’s say that you are analyzing customer sentiment using fine-grained analysis. You want to identify the particular aspect or features for which people are mentioning positive or negative reviews. Here, aspect-based sentiment analysis comes into play.&nbsp;</p><p>For instance, in the review “The camera quality of this phone is getting worse with time,” an aspect-based classifier will determine that the review expresses a negative opinion from the customer for the phone’s camera feature.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Multilingual Sentiment Analysis</strong></span></h3><p>Multilingual sentiment analysis is complex compared to others as it includes many preprocessing and resources available online (i.e., sentiment lexicons). Businesses value the feedback of the customer regardless of their geography or language. Therefore, multilingual sentiment analysis helps you identify customer sentiment irrespective of location or language difference.&nbsp;</p>15:T7a3,<p>The most crucial advantage of sentiment analysis is that it enables you to understand the sentiment of your customers towards your brand. Your products and services can be improved, and you can make more informed decisions by automatically analyzing the customers’ feelings and opinions through social media conversations, reviews, surveys, and more.&nbsp;</p><p>According to the survey, 90% of the world’s data is unstructured. Especially in businesses, emails, tickets, chats, social media conversions, and documents are generated daily. Therefore, it is hard to analyze all this vast data in a timely and efficient manner.</p><p>Let us look at the overall benefits of sentiment analysis in detail:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Sort Data at Scale&nbsp;</strong></span></h3><p>There is too much business data to analyze daily. Can you imagine sorting all these documents, tweets, customer support conversations, or surveys manually? Sentiment analysis will help your business to process all this massive data efficiently and cost-effectively.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Real-Time Analysis&nbsp;</strong></span></h3><p>Is your angry customer about to churn? Is a PR crisis on social media escalating? Sentiment analysis will help you handle these situations by identifying critical real-time situations and taking necessary action right away.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Consistent Criteria&nbsp;</strong></span></h3><p>According to research, customers only agree for 60-65% while determining the sentiment of the particular text. Tagging text is highly subjective, influenced by thoughts and beliefs, and also includes personal experience. Therefore, you can apply criteria and filters to all your data, improve their accuracy, and gain better insights using sentiment analysis.&nbsp;</p>16:T14ba,<p>Sentiment analysis works with the help of natural language processing and machine learning algorithms by automatically identifying the customer’s emotions behind the online conversations and feedback.&nbsp;</p><p>Depending on the amount of data and accuracy you need in your result, you can implement different sentiment analysis models and algorithms accordingly. Therefore, sentiment analysis algorithms comprise one of the three buckets below.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp;1. Rule-Based Approach&nbsp;</strong></span></h3><p>The rule-based system performs sentiment analysis based on manually crafted rules to identify polarity, subjectivity, or the subject of an opinion.&nbsp;</p><p>These rules contain different natural language processing techniques developed in computational linguistics like stemming tokenization, parsing, lexicons(list of words and expressions), or part of speech tagging.&nbsp;</p><p>For instance, you define two lists of polarized words, i.e., negative words(bad, worst, ugly, etc.) and positive words(good, best, beautiful, etc.). You have to count the number of positive and negative words in the text. If the number of positive words is greater than negative words, the text returns the positive sentiment and vice versa. If the number of negative and positive words is equal, then the text returns the neutral sentiment.&nbsp;</p><p>Since the rule-based system does not consider how words are combined in the sequence, this system is very naive. However, new rules can be added to support the new expression and vocabulary of the system by using more advanced processing techniques. But these will also add complexity to the design and affect the previous results.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Automatic Approach</strong></span></h3><p>Unlike rule-based systems, the automatic approach works on machine learning techniques, which rely on manually crafted rules. Here, the sentiment analysis system consists of a classification problem where the input will be the text to be analyzed. It will return a polarity if the text, for example, is positive, negative, or neutral.&nbsp;</p><p><img src="https://cdn.marutitech.com/774d5b31-untitled-2-min.png" alt="how does sentiment analysis work ?" srcset="https://cdn.marutitech.com/774d5b31-untitled-2-min.png 1452w, https://cdn.marutitech.com/774d5b31-untitled-2-min-768x696.png 768w, https://cdn.marutitech.com/774d5b31-untitled-2-min-705x639.png 705w, https://cdn.marutitech.com/774d5b31-untitled-2-min-450x408.png 450w" sizes="(max-width: 1452px) 100vw, 1452px" width="1452"></p><h4><span style="font-size:18px;"><i>a]Training and Prediction&nbsp;</i></span></h4><p>In the training process, your model links with a particular input(i.e., text) to the corresponding output based on the test sample. The feature extractor will help to transfer the input to the feature vector. These pairs of feature vectors and the tags provided are transferred to the machine learning algorithm to generate a model.</p><p>In the prediction process, the feature extractor transforms the unidentified text inputs into feature vectors. Further, these feature vectors generate the predicted tags like positive, negative, and neutral.&nbsp;</p><h4><span style="font-size:18px;"><i>b]Feature Extraction from Input Text</i></span></h4><p>Machine learning text classifiers will transform the text extraction using the classical approach of bag-of-words or bag-of-n-grams with their frequency. A new feature extraction system is created on word embeddings known as word vectors.&nbsp;</p><p>This kind of representation helps to improve the performance of classifiers by making it possible for words with similar meanings to have similar presentations.&nbsp;</p><h4><strong>Classification Algorithms&nbsp;</strong></h4><p>Various classification algorithms involve statistical modelings like naive Bayes, support vector machines, deep learning, or logistic regression. Let us discuss them in detail below:</p><ul><li><strong>Naive Bayes:</strong> It is a family of probabilistic algorithms that predict the category of a text by using the Bayes theorem.&nbsp;</li><li><strong>Support Vector Machines:</strong> It is a non-probabilistic model that uses a representation of the input text as a point in multi-dimensional space. Different text categories map to distinct regions within the space because the new texts are categorized based on the similarity with the existing text and the region they are mapping.</li><li><strong>Deep Learning:</strong> A family of algorithms that attempts to mimic the human brain with the help of artificial neural networks to process the data.&nbsp;</li><li><strong>Linear Regression:</strong> A family of algorithms in statistics that helps to predict some value (y) for a given set of features (x).&nbsp;</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Hybrid Approaches</strong></span></h3><p>The hybrid model is the combination of elements of the rule-based approach and automatic approach into one system. A massive advantage of this approach is that the results are often more accurate and precise than the rule-based and automated approaches.&nbsp;</p>17:T11e3,<p>The above approaches were good enough to implement the sentiment analysis but very hard to elaborate on. Therefore, a machine learning approach was introduced to apply the sentiment analysis model effectively and carry out word representations in a vector space.</p><p><span style="font-family:Arial;">It would help if you considered connecting with a </span><a href="https://marutitech.com/natural-language-processing-services/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Arial;">Natural Language Processing consulting</span></a><span style="font-family:Arial;"> service to educate yourself on the latest NLP trends.</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Word Representations in a Vector Space</strong></span></h3><h3><i>Feature Extraction&nbsp;</i></h3><p>Firstly, you must represent your sentences in a vector space while building a deep learning sentiment analysis model. <a href="https://medium.com/@samuelpilcer/sentiment-analysis-frequency-based-models-288c0accdd12" target="_blank" rel="noopener">Frequency-based methods</a> represent a sentence either by bag-of-words (list of the words that appear in the sentence with their frequencies) or by term frequency-inverse document frequency vector (the word frequencies in your sentences weights with their frequencies in the entire corpus).&nbsp;</p><p>These methods are beneficial for long texts. For instance, you can efficiently classify a newspaper article or a book by its most frequently used words. But if the sentences are short, the results will not be so accurate. At the same time, the sentence structure is also essential to identify while analyzing the sentiments because tf-IDF models rarely capture the negations, concessions, and amplification. For example, the text “Excellent camera but bad battery life.” will have the same effect as “Bad camera but excellent battery life.”</p><h3><i>Word Vectors</i></h3><p>When you represent the text with vectors, the vectors consider both the words and the semantic structure of the text. You must define every word with an n-feature vector and represent the sentence with an n*length matrix. For instance, you can create a vector of the same size as the vocabulary, and to describe the ith word, use 1 and 0 elsewhere.</p><p><a href="https://cs.stanford.edu/~quocle/paragraph_vector.pdf" target="_blank" rel="noopener">Tomas Mikolov</a> created a new way to represent words in a vector space. He trains the neural network model on a vast corpus that defines the term “ants” by the hidden layer’s output vector. These word vectors capture the semantic information as it captures enough data to analyze the statistical repartition of the word that follows “ant” in the sentence.&nbsp;</p><p>The exact process is followed here, i.e., an index vector represents every word. This small vector is the input of a convolution neural network. Further, it is integrated into the <a href="https://marutitech.com/ai-visual-inspection-for-defect-detection/#3_Develop_Deep_Learning_Model" target="_blank" rel="noopener">deep learning model</a> as a hidden layer of linear neurons and converts these significant vectors into small parts.&nbsp;</p><p>Therefore, the model trains as a whole so that the word vectors you use are enough to fit the sentiment information of the word, i.e. the features you get capture enough data on the terms to predict the sentiment of the text.</p><p><i>Click here to learn about </i><a href="https://marutitech.com/predictive-maintenance-machine-learning-techniques/" target="_blank" rel="noopener"><i>predictive maintenance machine learning techniques</i></a><i>.</i></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Sentence Representation</strong></span></h3><p>You have to build the representation of the sentence that considers words of the text and the semantic structure. The easiest method is to create a matrix and superpose of these word vectors that represent the text.&nbsp;</p><p>According to <a href="https://cs.stanford.edu/~quocle/paragraph_vector.pdf" target="_blank" rel="noopener">Tomas Mikolov</a>, you can also do this by the method called Doc2Vec. Here, he modifies the neural network used for the Word2Vec and takes input as a word vector and vector that depends on the sentence. Later, this word vector is considered a parameter to the model and optimized using gradient descent. By doing this, you will have a set of features for every sentence that represents the structure of the sentence.&nbsp;</p>18:T141a,<p>As discussed earlier, the customer writing positive or negative sentiment will differ by the composition of words in their reviews. Therefore, feeding the logistic regression to these vectors and training the regression to predict the feelings from the given text is one of the best sentiment analysis methods, especially using the fine-grained classification.&nbsp;</p><p>It is not an easy task to build the document vector for the given sentence. You have to run a gradient descent algorithm to search for the right coefficient for this vector in every sentence. Therefore, the Doc2Vec classification needs a significant hardware investment that takes much longer to process than other sentiment analysis methods where the preprocessing is a shorter algorithm.&nbsp;</p><h3><span style="font-family:Raleway, sans-serif;font-size:18px;"><strong>Convolution Neural Networks</strong></span></h3><p>For text classification, another method you can consider combines a multilayer <a href="https://marutitech.com/computer-vision-neural-networks/" target="_blank" rel="noopener">computer vision neural network</a> with a convolution layer, dense layers of neurons with sigmoid activation function, and additional layers designed to prevent overfitting. Convolutional layers are a technique designed for <a href="https://marutitech.com/services/artificial-intelligence-consulting/computer-vision/" target="_blank" rel="noopener">computer vision services</a>, and it helps to improve the accuracy of <a href="https://marutitech.com/working-image-recognition/" target="_blank" rel="noopener">image recognition</a> and object detection models.&nbsp;</p><p>The basic idea is to apply the convolutions to the image and the set of filters and consider this new image as input to the next layer. Depending on the filer you use, the output image will smooth the edges, capture them, or sharpen the key patterns. You will build highly relevant features to feed the next layer of the model by training the filter’s coefficients.&nbsp;</p><p>These features tend to work like local patches that practice compositionality. The model’s training will automatically practice the best patches depending on the classification problem you wish to solve.&nbsp;</p><h3><span style="font-family:Raleway, sans-serif;font-size:18px;"><strong>Applications in Natural Language Processing</strong></span></h3><p><img src="https://cdn.marutitech.com/Applications_in_Natural_Language_Processing_45fc8c789c.png" alt="Applications in Natural Language Processing" srcset="https://cdn.marutitech.com/thumbnail_Applications_in_Natural_Language_Processing_45fc8c789c.png 245w,https://cdn.marutitech.com/small_Applications_in_Natural_Language_Processing_45fc8c789c.png 500w," sizes="100vw"></p><p>Natural language processing is a popular model which people often try to apply in various other fields like <a href="https://marutitech.com/use-cases-of-natural-language-processing-in-healthcare/" target="_blank" rel="noopener">NLP in healthcare</a>, retail, advertising, manufacturing, automotive, etc. For NLP tasks like sentiment analysis, you have to build a word vector and convolve the image developed by juxtaposing these vectors for creating relevant features.&nbsp;</p><p>Eventually, the filters will allow you to highlight the intensely positive or negative words in the text. It will also help you understand the relationship between negations and what follows. It will also capture the relevant data about how the words follow each other and learn particular words or n-grams that contain the sentiment information.&nbsp;</p><p>Further, it ultimately connects the deep neural network with the outputs of these convolutions and selects the best feature for classifying the sentence’s sentiment.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Long Short Term Memory</strong></span></h3><p>Implementing the long short term memory (LSTM) is a fascinating architecture to process natural language. It starts reading the sentence from the first word to the last word. Later after processing each word, it tries to figure out the sentiment of the sentence.&nbsp;</p><p>For example, if the sentence is “The camera is worst, and battery backup is bad.” Here, the model will read “The,” then “camera,” then “worst,” “battery,” then “backup,” and at last “bad.” It will consider a vector that represents what comes before memory and a partial output. For instance, it will consider the sentence as negative halfway and update the process with more data.</p><p><img src="https://cdn.marutitech.com/Long_Short_Term_Memory_836126bacb.png" alt="Long Short Term Memory" srcset="https://cdn.marutitech.com/thumbnail_Long_Short_Term_Memory_836126bacb.png 245w,https://cdn.marutitech.com/small_Long_Short_Term_Memory_836126bacb.png 500w," sizes="100vw"></p><p>The above image accurately shows the sentiment analysis process in detail. It is very efficient at speech recognition and translation processes. But at the same time, it slows down the evaluation process considerably. So it should be implemented with care.&nbsp;</p>19:T1a20,<p>Sentiment analysis is a difficult task in natural language processing as even humans struggle to analyze sentiments accurately. Let’s look at some challenges of machine-based sentiment analysis:&nbsp;</p><p><img src="https://cdn.marutitech.com/Challenges_Faced_During_Sentiment_Analysis_c7719897c6.png" alt="Challenges Faced During Sentiment Analysis" srcset="https://cdn.marutitech.com/thumbnail_Challenges_Faced_During_Sentiment_Analysis_c7719897c6.png 156w,https://cdn.marutitech.com/small_Challenges_Faced_During_Sentiment_Analysis_c7719897c6.png 500w,https://cdn.marutitech.com/medium_Challenges_Faced_During_Sentiment_Analysis_c7719897c6.png 749w,https://cdn.marutitech.com/large_Challenges_Faced_During_Sentiment_Analysis_c7719897c6.png 999w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Subjectivity and Tone</strong></span></h3><p>As you know, there are two types of text available, i.e., subjective and objective. Subjective texts contain explicit sentiments, while the objective text does not. For example, you want to analyze the sentiment of the following two texts:</p><p><i>The phone is nice</i></p><p><i>The phone is blue</i></p><p>You will say that the sentiments are positive for the first and neutral for the second. Here, all text predicates should not be treated differently regarding how they create the sentiment. In this example, nice is more subjective in comparison to blue.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Context and Polarity</strong></span><strong>&nbsp;</strong></h3><p>Analyzing sentiment without context gets difficult as machines cannot learn about contexts if it is not trained explicitly. The most crucial disadvantage that arises from context is changing in polarity. Check out the below responses to a survey:</p><p><i>Everything of it</i></p><p><i>Absolutely nothing!</i></p><p>Consider the question, “What did you like about this phone?” The first response will be positive, and the second response will be negative. Now consider the question, “What did you dislike about this phone?” The negative verb “dislike” in the given question will change the sentiment analysis of the text.</p><p>If you consider the tiniest part of the context in the input text, you will need many preprocessing and postprocessing methods.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Irony and Sarcasm</strong></span></h3><p>When it comes to sarcasm, people tend to express their negative sentiments using affirmative words, making it difficult for machines to detect and understand the context of the situation and genuine emotions.&nbsp;</p><p>For instance, consider the question, “Did you enjoy your trip with us?”</p><p><i>Yeah, sure. So enjoyable!</i></p><p><i>Not one, but many!</i></p><p>If you consider the first response, the exclamation mark displays negation, correct? The challenge here is that there is no textual cue to help the machine understand the sentiment because “yeah” and “sure” are often considered positive or neutral.&nbsp;</p><p>However, suppose you consider the second response. In that case, sentiment is positive, but you will also develop many different contexts expressed in negative sentiment.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Comparisons</strong></span></h3><p>It is quite a challenge to tackle the comparisons while analyzing the sentiment of the text. For instance,&nbsp;</p><p><i>This product is better than others.</i></p><p><i>The new shop is far from the old one.</i></p><p><i>I like blue more than orange.</i></p><p>The first response specifies that it is positive. But what about the second and third responses? As mentioned above, context can make a difference in the sentiments of the sentence. In the second response, if the “old one” is considered useless, it becomes a lot easier to classify it.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Emojis</strong></span></h3><p>According to <a href="https://hal-amu.archives-ouvertes.fr/hal-01529708/document" target="_blank" rel="noopener">Guibon</a>, there are two types of emojis, i.e., Western emojis (:D) containing only one or two characters, and Eastern emojis (¯ \ <i>(ツ)</i> / ¯), which have more characters of vertical nature.</p><p>Emojis play a prominent role in sentiment analysis, especially while working with tweets. When it comes to analyzing tweets, you will have to pay more attention to character-level and word-level at the same time. And for this purpose, a lot of preprocessing might be needed.</p><p>For example, you must preprocess the tweets and convert the eastern emojis and western emojis into tokens. Further, whitelist them, which will improve your sentiment analysis performance.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Defining Neutral&nbsp;</strong></span></h3><p>When performing accurate sentiment analysis, defining the category of neutral is the most challenging task. As mentioned earlier, you have to define your types by classifying positive, negative, and neutral sentiment analysis. In this case, determining the neutral tag is the most critical and challenging problem. Since tagging data requires consistency for accurate results, a good definition of the problem is a must.</p><p>Here are some examples to define neutral texts:</p><ol><li><strong>Objective texts:</strong> As objective texts do not contain explicit sentiments, you can include them in the neutral category.</li><li><strong>Irrelevant information:</strong> You can tag the irrelevant data of your text as neutral if you haven’t preprocessed your text. But it is recommended to do this only if this data does not affect overall performance. Because sometimes, you may add unnecessary data(noise) to your categorization, and performance can worsen.&nbsp;</li><li><strong>Wishes Texts:</strong> You can consider some wishes like “I wish the camera had more clarity” as neutral texts. But at the same time, the texts including comparisons like “I wish the camera were better” are pretty difficult to categorize.</li></ol><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. Human Annotator Accuracy&nbsp;</strong></span></h3><p>Sentiment Analysis is quite a difficult task, whether it’s a machine or a human. When it comes to sentiment analysis, the inter-annotator agreement is very low. And since the machines learn from the humans by the data they feed, sentiment analysis classifiers are not as accurate as other types.</p>1a:T1eec,<p><img src="https://cdn.marutitech.com/Application_of_Sentiment_Analysis_db6548d619.png" alt="Application of Sentiment Analysis" srcset="https://cdn.marutitech.com/thumbnail_Application_of_Sentiment_Analysis_db6548d619.png 180w,https://cdn.marutitech.com/small_Application_of_Sentiment_Analysis_db6548d619.png 500w,https://cdn.marutitech.com/medium_Application_of_Sentiment_Analysis_db6548d619.png 750w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Brand Monitoring</strong></span><strong>&nbsp;</strong></h3><p>A brand is not defined by the product it manufactures. It depends on how you build a brand by online marketing, social campaigning, content marketing, and customer support services. Getting full 360 views of how your customers view your product, company, or brand is one of the most important uses of sentiment analysis.&nbsp;</p><p>Sentiment analysis enables you to quantify the perception of potential customers. Analyzing social media and surveys, you can get key insights about how your business is doing right or wrong for your customers.&nbsp;</p><p>Companies tend to use sentiment analysis as a powerful weapon to measure the impact of their products and campaigns on their customers and stakeholders. Brand monitoring allows you to have a wealth of insights from the conversions about your brand in the market. Sentiment analysis enables you to automatically categorize the urgency of all brand mentions and further route them to the designated team.&nbsp;</p><p>Keeping the feedback of the customer in knowledge, you can develop more appealing branding techniques and marketing strategies that can help make quick transitions.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Customer Service</strong></span><strong>&nbsp;</strong></h3><p>Customer service companies often use sentiment analysis to automatically classify their user incoming calls into “urgent” and “not urgent” classes. The classification is based on the sentiments of the emails or proactively identifying the calls of frustrated customers.&nbsp;</p><p>The customer expects their experience with the companies to be intuitive, personal, and immediate. Therefore, the service providers focus more on the urgent calls to resolve users’ issues and thereby maintain their brand value. Therefore, analyze customer support interactions to make sure that your employees are following the appropriate process. Moreover, increase the efficiency of your services so that customers aren’t left waiting for support for longer periods.&nbsp;</p><p>As the customer service sector has become more automated using machine learning, understanding customers’ sentiments has become more critical than ever before. For the same reason, companies are opting for <a href="https://marutitech.com/nlp-based-chatbot/" target="_blank" rel="noopener">NLP-based chatbots</a> as their first line of customer support to better grasp context and intent of the conversations.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Finance and Stock Monitoring</strong></span></h3><p>It is said that “Be fearful when others are greedy and be greedy when others are fearful.” But here, the question that arises: how do you know if others are fearful or greedy? Well, here, you can make use of the sentiment analysis technique. Making investments, especially in the business world, is quite tricky. The stocks and market are always on the edge of risks, but they can be condensed if you do correct research before investing.</p><p>For instance, if you are looking to invest in the automobile industry and are confused about choosing between company X and company Y, you can look at the sentiments received from the company for their latest products. It will help you to find the one that is performing better in the market.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Business Intelligence Buildup</strong></span></h3><p>Digital marketing plays a prominent role in business. Social media often displays the reactions and reviews of the product. When you are available with the sentiment data of your company and new products, it is a lot easier to estimate your customer retention rate.</p><p>Sentiment analysis enables you to determine how your product performs in the market and what else is needed to improve your sales. You can also analyze the responses received from your competitors. Based on the survey generated, you can satisfy your customer’s needs in a better way. You can make immediate decisions that will help you to adjust to the present market situation.</p><p>Business intelligence is all about staying dynamic. Therefore, sentiment analysis gives you the liberty to run your business effectively. For example, if you come up with a big idea, you can test and analyze it before bringing life to it.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Enhancing the Customer Experience</strong></span></h3><p>A satisfying customer experience means a higher chance of returning the customers. A successful business knows that it is important to take care of how they deliver compared to what they deliver.</p><p>Brand Monitoring offers us unfiltered and invaluable information on customer sentiment. However, you can also put this analysis on customer support interactions and surveys.</p><p><a href="https://www.netpromoter.com/know/" target="_blank" rel="noopener">NPS</a> (Net Promoter Score) surveys help you gain feedback for your business with the simple question: Will you recommend this brand, product, or service to your friend or family? The output is a single score on the number scale. Businesses use these sentiment scores to analyze the customer as promoters, detractors, and passives.</p><p>Here the goal is to find the overall customer experience and elevate your customer to promoter level. Theoretically, include the phases as: will buy more, stay longer and refer to another customer.</p><p>The next step in the NPS survey is to ask survey participants to leave the score and seek open-ended responses, i.e., qualitative data. Qualitative surveys are far more challenging to analyze. Still, with the help of sentiment analysis, these texts can be classified into multiple categories, which offer further insights into customers’ opinions.</p><p>As mentioned earlier, the experience of the customers can either be positive, negative, or neutral. Depending on the customers’ reviews, you can categorize the data according to its sentiments. This classification will help you properly implement the product changes, customer support, services, etc.</p><p>Also, remember that getting a positive response to your product is not always enough. The customer support services of your company should always be impeccable irrespective of how phenomenal your services are.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Market Research and Analysis</strong></span></h3><p>Business intelligence uses sentiment analysis to understand the subjective reasons why customers are or are not responding to something, whether the product, user experience, or customer support.</p><p>Sentiment analysis will enable you to have all kinds of market research and competitive analysis. It can make a huge difference whether you are exploring a new market or seeking an edge on the competition.&nbsp;</p><p>You can review your product online and compare them to your competition. You can also analyze the negative points of your competitors and use them to your advantage.</p><p>Sentiment analysis is used in sociology, psychology, and political science to analyze trends, opinions, ideological bias, gauge reaction, etc. A lot of these sentiment analysis applications are already up and running.</p>1b:T4b6,<p>The era of getting valuable insights from surveys and social media has peaked due to the advancement of technology. Therefore, it is time for your business to be in touch with the pulse of what your customers are feeling. Companies are using intelligent classifiers like contextual semantic search and sentiment analysis to leverage the power of data and get the deepest insights.</p><p>Formulate business strategies, exceed customer expectations, generate leads, build marketing campaigns, and open up new avenues for growth through <a href="https://marutitech.com/natural-language-processing-services/" target="_blank" rel="noopener">natural language processing solutions</a>.</p><p><a href="https://marutitech.com/" target="_blank" rel="noopener">Maruti Techlabs’</a> developers help you model human language and recognize the underlying meaning behind the words said or the action performed. We take communication beyond words and help to interpret human language and behavior.</p><p>Are you looking to interpret customer sentiments for increasing brand value? Drop us a note <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener">here</a>, and we’ll take it from there.</p>1c:Tb5c,<p>The healthcare industry is fast realizing the importance of data, collecting information from EHRs, sensors, and other sources. However, the struggle to make sense of the data collected in the process might rage on for years. Since the healthcare system has started adopting cutting-edge technologies, there is a vast amount of data collected in silos. Healthcare organizations want to digitize processes, but not unnecessarily disrupt established clinical workflows. Therefore, we now have as much as 80 percent of data unstructured and of poor quality.&nbsp;This brings us to a pertinent challenge of data extraction and utilization in the healthcare space through <a href="https://marutitech.com/nlp-for-electronic-healthcare-record/" target="_blank" rel="noopener"><span style="color:#f05443;">NLP in Healthcare</span></a>.</p><p><a href="https://marutitech.com/case-study/covid-awareness-whatsapp-chatbot/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/dc6b1819-artboard-2-copy.png" alt="NLP in Healthcare" srcset="https://cdn.marutitech.com/dc6b1819-artboard-2-copy.png 2421w, https://cdn.marutitech.com/dc6b1819-artboard-2-copy-768x121.png 768w, https://cdn.marutitech.com/dc6b1819-artboard-2-copy-1500x236.png 1500w, https://cdn.marutitech.com/dc6b1819-artboard-2-copy-705x111.png 705w, https://cdn.marutitech.com/dc6b1819-artboard-2-copy-450x71.png 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><p>This data as it is today, and given the amount of time and effort it would need for humans to read and reformat it, is unusable. Thus, we cannot yet make effective decisions in healthcare through analytics because of the form our data is in.&nbsp;Therefore, there is a higher need to leverage this unstructured data as we shift from fee-for-service healthcare model to value-based care.</p><p>This is where Natural Language Processing, a subcategory of Artificial Intelligence can come in. <a href="https://marutitech.com/nlp-based-chatbot/" target="_blank" rel="noopener">NLP based chatbots</a>&nbsp;already possess the capabilities of well and truly mimicking&nbsp;human behavior and executing a myriad of tasks. When it comes to implementing the same on a much larger use case, like a hospital – it can be used to parse information and extract critical strings of data, thereby offering an opportunity for us to leverage&nbsp;unstructured data.</p><p><img src="https://cdn.marutitech.com/NLP-in-Healthcare-1-New-logo.jpg" alt="NLP-in-Healthcare"></p><p>This augmentation could save healthcare organizations precious money and time by automating&nbsp;quality reporting and creating patient registries.&nbsp;Let’s explore the factors driving <a href="https://marutitech.com/nlp-for-electronic-healthcare-record/" target="_blank" rel="noopener"><span style="color:#f05443;">NLP in Healthcare</span></a> and its possible benefits to the industry.</p>1d:T22e3,<p>Studies show that Natural Language Processing in Healthcare is expected to grow from <a href="https://www.marketsandmarkets.com/Market-Reports/healthcare-lifesciences-nlp-market-131821021.html" target="_blank" rel="noopener">USD&nbsp;1030.2 million in 2016 to USD 2650.2 million in 2021</a>, at a CAGR of 20.8 percent during the&nbsp;forecast period.</p><p>NLP, a branch of AI, aims at primarily reducing the distance between the capabilities of a&nbsp;human and a machine. As it beginning to get more and more traction in the healthcare space, providers are focusing on developing solutions that can understand, analyze, and generate languages can humans can understand.</p><p>There is a further need for voice recognition systems that can automatically respond to queries&nbsp;from patients and healthcare users. There are many more drivers of NLP in Healthcare as elucidated below –</p><p><img src="https://cdn.marutitech.com/NLP-in-Healthcare-2-New-logo.jpg" alt="NLP-in-Healthcare"></p><ul><li><strong>Handle the Surge in Clinical Data</strong></li></ul><p>The increased use of patient health record systems and the digital transformation of medicine&nbsp;has led to a spike in the volume of data available with healthcare organizations. The need to&nbsp;make sense out of this data and draw credible insights happens to be a major driver.</p><ul><li><strong>Support Value-Based Care and Population Health Management</strong></li></ul><p>The shift in business models and outcome expectations is driving the need for better use of&nbsp;unstructured data. Traditional health information systems have been focusing on deriving value&nbsp;from the 20 percent of healthcare data that comes in structured formats through clinical&nbsp;channels.</p><p>For advanced patient health record systems, managed care, PHM applications, and analytics&nbsp;and reporting, there is an urgent need to tap into the reservoir of unstructured information that is&nbsp;only getting piled up with healthcare organizations.</p><p><a href="https://marutitech.com/nlp-for-electronic-healthcare-record/" target="_blank" rel="noopener"><span style="color:#f05443;">NLP in Healthcare</span></a> could solve these challenges through a number of use cases. Let’s explore a couple of them:</p><ol><li><strong>Improving Clinical Documentation</strong> – Electronic Health Record solutions often have a complex structure, so that documenting data in them is a hassle. With speech-to-text dictation, data can be automatically captured at the point of care, freeing up physicians from the tedious task of documenting care delivery.</li><li><strong>Making CAC more Efficient</strong> – Computer-assisted coding can be improved in so many ways with NLP. CAC extracts information about procedures to capture codes and maximize claims. This can truly help HCOs make the shift from fee-for-service to a value-based model, thereby improving the patient experience significantly.</li></ol><ul><li><strong>Improve Patient-Provider Interactions with EHR</strong></li></ul><p>Patients in this day and age need undivided attention from their healthcare providers. This&nbsp;leaves doctors feeling overwhelmed and burned out as they have to offer personalized services&nbsp;while also managing burdensome documentation including billing services.</p><p>Studies have shown how a majority of care professionals experience burnout at their&nbsp;workplaces. Integrating NLP with electronic health record systems will help take off workload&nbsp;from doctors and make analysis easier.&nbsp;Already, virtual assistants such as <a href="https://www.mobihealthnews.com/content/how-voice-assistant-can-be-constant-companion-hospital-bound-patients" target="_blank" rel="noopener">Siri, Cortana, and Alexa</a> have made it into healthcare&nbsp;organizations, working as administrative aids, helping with customer service tasks and help&nbsp;desk responsibilities.</p><p>Soon, NLP in Healthcare might make virtual assistants cross over to the clinical side of the&nbsp;healthcare industry as ordering assistants or medical scribes.</p><p><a href="https://marutitech.com/case-study/medical-record-processing-using-nlp/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/<EMAIL>" alt="Case Study - Medical Record Processing using NLP" srcset="https://cdn.marutitech.com/<EMAIL> 2421w, https://cdn.marutitech.com/<EMAIL> 768w, https://cdn.marutitech.com/<EMAIL> 1500w, https://cdn.marutitech.com/<EMAIL> 705w, https://cdn.marutitech.com/<EMAIL> 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><ul><li><strong>Empower Patients with Health Literacy</strong></li></ul><p>With <a href="https://chatbotsmagazine.com/is-conversational-ai-the-future-of-healthcare-658a3d8e9dd5" target="_blank" rel="noopener">conversational AI already being a success within the healthcare space</a>, a key use-case and benefit of implementing this technology is the ability to help patients understand their symptoms and gain more&nbsp;knowledge about their conditions. By becoming more aware of their health conditions, patients&nbsp;can make informed decisions, and keep their health on track by interacting with an intelligent <a href="https://wotnot.io/healthcare-chatbot/" target="_blank" rel="noopener">helathcare chatbot</a>.</p><p><a href="https://healthitanalytics.com/news/natural-language-processing-could-translate-ehr-jargon-for-patients" target="_blank" rel="noopener">In a 2017 study</a>, researchers used NLP solutions to match clinical terms from their documents&nbsp;with their layman language counterparts. By doing so, they aimed to improve patient EHR&nbsp;understanding and the patient portal experience.&nbsp;Natural Language Processing in healthcare could boost patients’ understanding of EHR portals,&nbsp;opening up opportunities to make them more aware of their health.</p><ul><li><strong>Address the Need for Higher Quality of Healthcare</strong></li></ul><p>NLP can be the front-runner&nbsp;in assessing and improving the quality of healthcare by measuring&nbsp;physician performance and identifying gaps in care delivery.</p><p>Research has shown that artificial intelligence in healthcare can ease the process of physician&nbsp;assessment and automate patient diagnosis, reducing the time and human effort needed in&nbsp;carrying out routine tasks such as patient diagnosis. NLP in healthcare can also identify and mitigate potential errors in care delivery. <a href="https://healthitanalytics.com/news/ehr-natural-language-processing-identifies-care-guideline-adherence" target="_blank" rel="noopener">A study&nbsp;showed that NLP could also be utilized in measuring the quality of healthcare and monitor&nbsp;adherence to clinical guidelines</a>.</p><ul><li><strong>Identify Patients who Need Improved Care</strong></li></ul><p>Machine Learning and NLP tools have the capabilities needed to detect patients with complex health conditions who have a history of mental health or substance abuse and need improved care. Factors such as food insecurity and housing instability can deter the treatment protocols, thereby compelling these patients to incur more cost in their lifetime.</p><p>The data of a patient’s social status and demography is often hard to locate than their clinical&nbsp;information since it is usually in an unstructured format. NLP can help solve this problem.&nbsp;NLP can also be used to improve care coordination with patients who have behavioral health&nbsp;conditions. Both, Natural Language Processing &amp; Machine Learning can be utilized to mine patient data and detect those that are at risk of&nbsp;falling through any gaps in the healthcare system.</p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/<EMAIL>" alt="Factors Behind NLP in Healthcare" srcset="https://cdn.marutitech.com/<EMAIL> 2421w, https://cdn.marutitech.com/<EMAIL> 768w, https://cdn.marutitech.com/<EMAIL> 1500w, https://cdn.marutitech.com/<EMAIL> 705w, https://cdn.marutitech.com/<EMAIL> 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><p>Since the healthcare industry generates both structured&nbsp;and unstructured data, it is crucial for healthcare organizations to refine both before&nbsp;implementing <a href="https://marutitech.com/nlp-for-electronic-healthcare-record/" target="_blank" rel="noopener"><span style="color:#f05443;">NLP in healthcare</span></a>.</p>1e:T842,<p>Natural Language Processing in the healthcare industry can help enhance the accuracy and&nbsp;completeness of EHRs by transforming the free text into standardized data. This could also&nbsp;make documentation easier by allowing care providers to dictate notes as NLP turns it into&nbsp;documented data.</p><p><img src="https://cdn.marutitech.com/NLP-in-Healthcare-3-New-logo.jpg" alt="NLP-in-Healthcare"></p><p>Computer-aided coding is another excellent benefit of NLP in healthcare. It can be viewed as a&nbsp;silver bullet for the issues of adding significant detail and introducing specificity in clinical documentation.&nbsp;For providers in need of a point-of-care solution for highly complex patient issues, NLP can be&nbsp;used for decision support. An often-quoted example and an epitome of NLP in healthcare is IBM&nbsp;Watson. It has a massive appetite for academic literature and growing expertise in clinical&nbsp;decision support for precision medicine and cancer care. In 2014, IBM Watson was used to&nbsp;investigating how NLP and Machine Learning could be used to flag patients with heart diseases&nbsp;and help clinicians take the first step in care delivery.</p><p>Natural Language Processing algorithms were applied to patient data and several risk factors&nbsp;were automatically detected from the notes in the medical records.&nbsp;Since there is this explosion of data in healthcare which pertains not only to genomes but&nbsp;everything else, the industry needs to find the best way to extract relevant information from it&nbsp;and bring it together to help clinicians base their decisions on facts and insights.</p><p><span style="font-family:Arial;">Developing, testing, and deploying NLP-based solutions can prove to be a cumbersome task and might need external assistance from a </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Arial;">Natural Language Processing services and solutions</span></a><span style="font-family:Arial;"> company.</span></p>1f:T1027,<p>NLP in Healthcare is still not up to snuff, but the industry is willing to put in the effort to make&nbsp;advancements. Semantic big data analytics and <a href="https://marutitech.com/cognitive-computing-features-scope-limitations/" target="_blank" rel="noopener">cognitive computing</a> projects, which have&nbsp;foundations in NLP, are seeing significant investments in healthcare from some recognizable players.</p><p><img src="https://cdn.marutitech.com/NLP-in-Healthcare-4-New-logo.jpg" alt="NLP-in-Healthcare"></p><p>Allied Market Research has predicted that the cognitive computing market will be worth <a href="https://www.alliedmarketresearch.com/press-release/cognitive-computing-market-is-expected-to-reach-137-billion-globally-by-2020-allied-market-research.html" target="_blank" rel="noopener">USD</a>&nbsp;<a href="https://www.alliedmarketresearch.com/press-release/cognitive-computing-market-is-expected-to-reach-137-billion-globally-by-2020-allied-market-research.html" target="_blank" rel="noopener">13.7 billion across industries by 2020</a>. The same company has projected spending of <a href="https://www.prnewswire.com/news-releases/text-analytics-market-is-expected-to-reach-65-billion-by-2020---allied-market-research-*********.html" target="_blank" rel="noopener">USD 6.5</a>&nbsp;<a href="https://www.prnewswire.com/news-releases/text-analytics-market-is-expected-to-reach-65-billion-by-2020---allied-market-research-*********.html" target="_blank" rel="noopener">billion on text analytics by 2020</a>.&nbsp;Eventually, natural language processing tools might be able to bridge the gap between the&nbsp;insurmountable volume of data in healthcare generated every day and the limited cognitive&nbsp;capacity of the human brain.</p><p>The technology has found applications in healthcare ranging from the most cutting-edge solutions in&nbsp;precision medicine applications to the <a href="https://marutitech.com/nlp-contract-management-analysis/" target="_blank" rel="noopener">NLP contract management analysis</a> and coding a claim for reimbursement or billing. The technology has far and wide implications on the healthcare industry, should it be brought to&nbsp;fruition. However, the key to the success of introducing this technology will be to develop algorithms that&nbsp;are intelligent, accurate, and specific to ground-level issues in the industry.&nbsp;NLP will have to meet the dual goals of data extraction and data presentation so that patients&nbsp;can have an accurate record of their health in terms they can understand.&nbsp;If that happens, there are no bars to the improvement in physical efficiency we will witness within the healthcare space.</p><p><a href="https://marutitech.com/case-study/appointment-booking-chatbot-for-hospital/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/c48a18b5-artboard-2.png" alt="NLP in Healthcare" srcset="https://cdn.marutitech.com/c48a18b5-artboard-2.png 2421w, https://cdn.marutitech.com/c48a18b5-artboard-2-768x121.png 768w, https://cdn.marutitech.com/c48a18b5-artboard-2-1500x236.png 1500w, https://cdn.marutitech.com/c48a18b5-artboard-2-705x111.png 705w, https://cdn.marutitech.com/c48a18b5-artboard-2-450x71.png 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><p>At Maruti Techlabs, we are truly committed to transforming the healthcare space by building solutions like contextual AI assistants as we realize that conversations with patients or internally at hospitals are rarely just one question and answer. Our chatbot solutions and NLP models have helped leading hospitals within India and abroad, overhaul their patient and staff experience through use cases like automation of appointment booking, feedback collection, optimization of internal process like medical coding and data assessment as well as data entry. It has been truly exhilarating for us to see our clients &amp; partners go live with their <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">chatbots</a> and AI based models, enhance &amp; train over time, and meet their organizational goals.</p>20:T9e5,<p>NLP involves communication through speech and text. Speech communication is the recent innovation that includes chatbots and other voice-based bots. These are designed as human language personal assistant. You may be already familiar with the personal assistant found on iPhone’s Siri – a personal assistant that just communicates like a human and can assign her almost any task you wish to do. Instructions like calling a friend, find restaurants and events, check weather and so on. The list is endless. It may even tweet and update your status on facebook just like a human friend with incredible intelligence.</p><figure class="image"><img src="https://cdn.marutitech.com/123_1_91bc40f28e.png" alt="NLP involves communication" srcset="https://cdn.marutitech.com/thumbnail_123_1_91bc40f28e.png 245w,https://cdn.marutitech.com/small_123_1_91bc40f28e.png 500w,https://cdn.marutitech.com/medium_123_1_91bc40f28e.png 750w," sizes="100vw"><figcaption>Ref – https://www.upwork.com/hiring/for-clients/artificial-intelligence-and-natural-language-processing-in-big-data/</figcaption></figure><p>This technology binds human-computer relationship, and leaps and bounds benefit business houses. &nbsp;Although machine learning the natural language is far away from the dominating human realms, but human intelligence is exploring the new heights, and we may see the new age of Artificial Intelligence (AI) is getting closer in achieving perfection and in the near future we might see the dynamic use of NLP in various forms.</p><p>NLP has strengthened the interactions with the search engines that allowed the queries to be assessed faster and in an efficient manner. The most important part of it is, it understands the query given to it and fetches accurate results.</p><p>NLP has given rise to voice search that becomes increasingly popular these days. And Google <a href="https://googleblog.blogspot.in/2014/10/omg-mobile-voice-survey-reveals-teens.html" target="_blank" rel="noopener">study </a>of 2014 reveals 55% of teens and 41% of adults in the US use voice search more than once a day.&nbsp; In 2016, Google <a href="https://searchengineland.com/google-reveals-20-percent-queries-voice-queries-249917" target="_blank" rel="noopener">announced </a>that 20 percent of its mobile app and Android devices are voice searches. All these numbers are bound to increase drastically in the coming years. It is the increasing speed of computer processing that made the voice search a possibility and now an increasing popularity.</p>21:T60f,<p>E-Commerce sales in <a href="https://www.statista.com/statistics/272391/us-retail-e-commerce-sales-forecast/" target="_blank" rel="noopener">2017 </a>in the United States amounted to almost 453 billion US dollars, and it is expected to grow by 779.5 billion US dollars in 2021. The opportunities are wide open as people prefer online shopping more than the brick and mortar and that’s primarily because of the benefits available are plentiful.</p><p>Few challenges still remain unsolved though the issues are addressed, and improved considerably with the invention of latest technologies like the NLP. Below are some of the questions that come to us when we try to bridge customer expectations into an actual sale.</p><ul><li>How to convert the search ratios into actual sales?</li><li>Do customers keep visiting the websites?</li><li>How well your app and site respond to customer queries?</li><li>Do the searches match the query?</li><li>Does your search engine understand smartly enough to keep the customer engaging?</li></ul><p>Not only these questions are addressed, but the solutions can be taken to the new level with NLP. Computers now can understand what exactly customers mean when they type a word or phrase or speak in the search field.&nbsp; Text processing is now more filtered, clean and noise-free so that it can be readily analyzable without having to go through a backhand processing. NLP helps the unstructured text data into a standardization form. This enables the search results to be swifter and with utmost precision.</p>22:T1d33,<p><strong>1.Text Recognition</strong></p><p>Recognizes the text, character and converts them into data and stores it in its database. The ability to read the text and converts into a machine-encoded text is one of the methods of language processing that is been used by search engines for many years. With the help of NLP, things are far easier than it was before in terms speed and accuracy. No more mere search results but it give you the answers to the question posed by the customers.</p><p><strong>2.Semantics</strong></p><p>We, humans, understand the word in the context of the sentence spoken or written and we do it more efficiently and effortlessly. But to teach the computer the context in which the sentence is spoken or written is quite a task. Machines do not understand what and the why.</p><p>As we all know that training makes us perfect in something we do, the same theory applies here as well to the computer world. They have been given a lot of unstructured data for semantic analysis and through powerful algorithms; computers are becoming more powerful and getting better at understanding the particular word in reference to the context or scenario to comprehend the phrase.</p><p><strong>3.Sentiment Analysis</strong></p><p>When do you know what exactly means ‘happy customer experience’ is, which is very much subjective. Even, if we find out the ways to get into it, how to teach the systems to understand the emotions of the text? Yes, things are still in the primitive stage to evaluating the customer views that may be made available to us through our research team. Customer feedbacks, answers to queries, their likes, and dislikes, their choice and preferences in the coming festival seasons, holidaying trends, better product ideas, their expectations with regard to the product and services, etc., will amount to a huge unstructured data.</p><p>To analyze the enormous amount of unstructured data and interpret the outcome of such reviews requires huge manpower. But with computers now tuned to AI, customer’s emotional responses, analysis, and findings are marked as a positive, negative or neutral outcome. It would be easier for the computers to understand the simple answers or interactions. However, complex responses complicate the whole comprehension of machine learning. But there are several methods to segregate the complicated words from complex sentence patterns to determine the exact meaning of the sentences. <a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Arial;">Natural Language Processing implementation</span></a><span style="font-family:Arial;"> can be complex, often requiring collaboration between data scientists, machine learning engineers, and domain experts. </span>Thus, this further provides a high level of accuracy in predicting the ambiguous phrases in simpler ways.</p><p><strong>4.Personalized Bots</strong></p><p>An organization may reap maximum benefits using NLP in designing personal assistants. Shopping can be more fun than ever. These assistants have the ability to keep the customer interested and bring on their screen exactly what they require to shop. It analyses recent searches you made, past purchase behavior to bring out seamless shopping experience.</p><p>NLP would be able to make machine talking to a human in the easiest possible way. <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">Chatbots technology may be used in business</a> houses to extract information from past data that might help taking big business decisions. The powerful technology also helps you forecast the revenues based on the statistical data in a flash. The insights delivered by the chatbots may transform your business into a formidable platform to find the right answers to leap into the future.</p><p><strong>5.Customer Service Centers Dynamics</strong></p><p>Automation is the mantra for transforming call centers without a need for a human agent. AI is making the pathway to the future for handling customer interactions. All those forward-thinking b-houses can be benefitted through real-time dynamics that efficiently improve loyalty and brand name and its reputation to new heights.</p><p>Several thousand or more or even infinite calls can be attended through a single server that fetches the query in a flash and responds to the customer or transfer calls to the respective departments with the help of embedded intelligence such as NLP. There will be no more hearing such as ‘press 1’ ‘press 2’ etc.</p><p>AI aims to improve the customer service reputation and reduce dissatisfaction among customers.&nbsp; Only AI has the speed and power to boost purchasing cycle as it sends alerts and, intriguing offers based on the certain patterns that are highly valuable to retain customers and urges them to revisit the apps time and again.</p><p>Social interactions, messaging can be made fully operational through chatbots leveraging the time and space. These interactions can be made 24×7 and even be designed to solve issues instantly rather than sending an email requesting to process the issue within 2 business working days. These are challenging but creative that is sure to win customer support in an attempt to reach out to them to provide unmatched service.</p><p>It is worth reading the Zendesk <a href="https://www.zendesk.com/resources/customer-service-and-lifetime-customer-value/" target="_blank" rel="noopener">survey</a> that illuminates us how interaction with customer ending on a happy note has a great impact on purchase behavior. This impact is purely based on the past interactions. If there is a negative response, 95 percent of those unsatisfied customers are likely to share their bad experiences. Another drawback of traditional call centers revealed as 74 percent of the people complained that they have to explain their problem multiple times as the call center calls are not diverted to one single person. More shocking is one bad customer service interaction can cost you a customer as 66 percent stopped buying after one bad experience during customer service interaction.</p><p><strong>6.Cost Effective</strong></p><p>Imagine if your entire workforce needs to be trained to the new technology and the dynamic of it can significantly have an impact on business operations, then you ultimately end up paying 1000s of dollars to let the technology do the talking. But if the new technology that brings in with it the intelligence that has the automation platforms programmed to own industry knowledge, why not implement them. That business intelligence requires training once as and when the upgrade is released. It is a powerful feature that every business houses need to own.</p><p><strong>7.Information Discovery Magician</strong></p><p>Business constantly requires updated information’s about the customer reviews on their products, their behavioral trends, fair ratings of their recently launched products etc., can illuminate the management to get things going their way. Information gathered through poll surveys, emails pop-ups, social media posts, blog posts, phone calls and comments about products on different web interfaces are managed by applications powered by AI. The quest for knowledge never ceases and information gathered is analyzed and interpreted in an accurate manner.</p>23:T1095,<p>Natural Language Processing is a based on <a href="https://marutitech.com/top-8-deep-learning-frameworks/" target="_blank" rel="noopener">deep learning</a> that enables computers to acquire meaning from inputs given by users. In the context of bots, it assesses the intent of the input from the users and then creates responses based on contextual analysis similar to a human being.</p><p>Say you have a chatbot for customer support, it is very likely that users will try to ask questions that go beyond the bot’s scope and throw it off. This can be resolved by having default responses in place, however, it isn’t exactly possible to predict the kind of questions a user may ask or the manner in which they will be raised.</p><p>When it comes to Natural Language Processing, developers can train the bot on multiple interactions and conversations it will go through as well as providing multiple examples of content it will come in contact with as that tends to give it a much wider basis with which it can further assess and interpret queries more effectively.</p><p>So, while training the bot sounds like a very tedious process, the results are very much worth it. <a href="https://www.finextra.com/newsarticle/30513/rbs-gives-ai-a-helping-hand-with-hybrid-bots" target="_blank" rel="noopener">Royal Bank of Scotland uses NLP in their chatbots</a>&nbsp;to enhance customer experience through text analysis to interpret the trends from the customer feedback in multiple forms like surveys, call center discussions, complaints or emails. It helps them identify the root cause of the customer’s dissatisfaction and help them improve their services according to that.</p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/<EMAIL>" alt="NLP based chatbot" srcset="https://cdn.marutitech.com/<EMAIL> 2421w, https://cdn.marutitech.com/<EMAIL> 768w, https://cdn.marutitech.com/<EMAIL> 1500w, https://cdn.marutitech.com/<EMAIL> 705w, https://cdn.marutitech.com/<EMAIL> 450w" sizes="(max-width: 2421px) 100vw, 2421px" width="2421"></a></p><p><strong>Best NLP Approach</strong></p><p>The best approach towards NLP that is a blend of Machine Learning and Fundamental Meaning for maximizing the outcomes. Machine Learning only is at the core of many NLP platforms, however, the amalgamation of fundamental meaning and Machine Learning helps to make efficient NLP based chatbots. Machine Language is used to train the bots which leads it to continuous learning for natural language processing (NLP) and <a href="https://marutitech.com/advantages-of-natural-language-generation/" target="_blank" rel="noopener">natural language generation (NLG)</a>. Both ML and FM has its own benefits and shortcomings as well. Best features of both the approaches are ideal for resolving the real-world business problems.</p><p>Here’s what an NLP based bot entails &nbsp;–</p><ol><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Lesser false positive outcomes through accurate interpretation</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Identify user input failures and resolve conflicts using statistical modeling</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Use comprehensive communication for user responses</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Learn faster to address the development gaps</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Achieve natural language capability through lesser training data inputs</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Ability to re-purpose&nbsp;the input training data for future learnings</span></li><li><span style="font-family:Raleway, sans-serif;font-size:16px;">Provide simple corrective actions for the false positives</span></li></ol><p><img src="https://cdn.marutitech.com/1_Mtech-1.png" alt="nlp-based-chatbots"></p>24:T1163,<p>NLP engines extensively use Machine Learning to parse user input in order to take out the necessary entities and understand user intent. NLP based <a href="https://wotnot.io/" target="_blank" rel="noopener">chatbots</a> can parse multiple user intents to minimize the failures.</p><p><strong>Intent Recognition –</strong></p><p>User inputs through a chatbot are broken and compiled into a user intent through few words. For e.g., “search for a pizza corner in Seattle which offers deep dish margherita”.</p><p>NLP analyses complete sentence through the understanding of the meaning of the words, positioning, conjugation, plurality, and many other factors that human speech can have. Thus, it breaks down the complete sentence or a paragraph to a simpler one like – search for pizza to begin with followed by other search factors from the speech to better understand the intent of the user.</p><p>This attribute also facilitates <a href="https://marutitech.com/nlp-contract-management-analysis/" target="_blank" rel="noopener">NLP contract management and analysis</a>.</p><p><strong>Dealing with Entity –</strong></p><p>Entities can be fields, data or words related to date, time, place, location, description, a synonym of a word, a person, an item, a number or anything that specifies an object. The chatbots are able to identify words from users, matches the available entities or collects additional entities of needed to complete a task.</p><p><strong>Capitalization of Nouns –</strong></p><p>NLP enabled chatbots remove capitalization from the common nouns and recognize the proper nouns from speech/user input.</p><p><strong>Expansion &amp; Transfer of vocabulary –</strong></p><p>NLP enables bots to continuously add new synonyms and uses Machine Learning to expand chatbot vocabulary while also transfer vocabulary from one bot to the next.</p><p><strong>Tense of the Verbs –</strong></p><p>AI chatbots understand different tense and conjugation of the verbs through the tenses.</p><p><strong>Contractions –</strong></p><p>Bots with NLP can expand the contractions and simplify the tasks removing apostrophes in between the words.</p><p>Other than these, there are many capabilities that NLP enabled bots possesses, such as – document analysis, machine translations, distinguish contents and more.</p><p>NLP engines rely on the following elements in order to process queries –</p><ul><li><strong>Intent</strong> – The central concept of constructing a <a href="https://marutitech.com/trends-need-to-know-about-conversational-marketing/" target="_blank" rel="noopener"><span style="color:#f05443;">conversational</span></a> user interface and it is identified as the task a user wants to achieve or the problem statement a user is looking to solve.</li><li><strong>Utterance – </strong>The various different instances of sentences that a user may give as input to the chatbot as when they are referring to an intent.</li><li><strong>Entity</strong>. They include all characteristics and details pertinent to the user’s intent. This can range from location, date, time, etc.</li><li><strong>Context</strong>. This helps in saving and share different parameters over the entirety of the user’s session.</li><li><strong>Session</strong>. This essentially covers the start and end points of a user’s conversation.</li></ul><p>There are many NLP engines available in the market right from <a href="https://dialogflow.com/" target="_blank" rel="noopener">Google’s Dialogflow</a> (previously known as API.ai), <a href="https://wit.ai/" target="_blank" rel="noopener">Wit.ai</a>, <a href="https://www.ibm.com/watson/services/conversation/" target="_blank" rel="noopener">Watson Conversation Service</a>, <a href="https://aws.amazon.com/lex/" target="_blank" rel="noopener">Lex</a> and more. Some services provide an all in one solution while some focus on resolving one single issue.</p><p><img src="https://cdn.marutitech.com/3_Mtech.png" alt="nlp-based-chatbot"></p><p>At its core, the crux of natural language processing lies in understanding input and translating it into language that can be understood between computers. To extract intents, parameters and the main context from utterances and transform it into a piece of <a href="https://marutitech.com/big-data-analysis-structured-unstructured-data/" target="_blank" rel="noopener">structured data</a> while also calling APIs is the job of NLP engines.</p>25:T12bd,<p>There are many <a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">different types of chatbots</a> created for various purposes like FAQ, customer service, virtual assistance and much more. Chatbots without NLP rely majorly on pre-fed static information &amp; are naturally less equipped to handle human languages that have variations in emotions, intent, and sentiments to express each specific query.</p><p>Let’s check out the reasons that your chatbot should have NLP in it –</p><p><strong>1.Overcoming the challenges of language variations –</strong></p><p>The problem with the approach of pre-fed static content is that languages have an infinite number of variations in expressing a specific statement. There are uncountable ways a user can produce a statement to express an emotion. Researchers have worked long and hard to make the systems interpret the language of a human being.</p><p><span style="font-family:Arial;">Through </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Arial;">Natural Language Processing implementation</span></a><span style="font-family:Arial;">, it is possible to make a connection between the incoming text from a human being and the system-generated response.</span> This response can be anything starting from a simple answer to a query, action based on customer request or store any information from the customer to the system database. NLP can differentiate between the different type of requests generated by a human being and thereby enhance customer experience substantially.</p><ul><li>NLP based chatbots are smart to understand the language semantics, text structures, and speech phrases. Therefore, it empowers you to analyze a vast amount of unstructured data and make sense.</li><li>NLP is capable of understanding the morphemes across languages which makes a bot more capable of understanding different nuances.</li><li>NLP gives chatbots the ability to understand and interpret slangs and learn abbreviation continuously like a human being while also understanding various emotions through sentiment analysis.</li></ul><p><strong>2.Shift in focus on more important tasks</strong></p><p>Generally many different roles &amp; resources are deployed in order to make an organization function, however, that entails repetition of manual tasks across different verticals like customer service, human resources, catalog management or invoice processing. <a href="https://marutitech.com/artificial-intelligence-for-customer-service-2/" target="_blank" rel="noopener">NLP based chatbots reduce the human efforts in operations like customer service</a> or invoice processing dramatically so that these operations require fewer resources with increased employee efficiency.</p><p>Now, employees can focus on mission critical tasks and tasks that impact the business positively in a far more creative manner as opposed to losing time on tedious repeated tasks every day. You can use NLP based chatbots for internal use as well especially for Human Resources and IT Helpdesk.</p><p><strong>3.Increased profitability due to reduced cost</strong></p><p>Costing is the essential aspect for any business to grow and increase profitability. NLP based chatbots can significantly assist in cutting down costs associated with manpower and other resources entangled in repetitive tasks as well as costs on customer retention, while&nbsp;improving efficiency and streamlining workflows.</p><p><strong>4.Higher efficient systems lead to customer satisfaction</strong></p><p>Millennials today want an instant response and instant solutions for their queries. NLP helps chatbots understand, analyze and prioritize the questions according to the complexity &amp; this enables bots to respond to customer queries faster than a human being. Faster responses help in building customer trust and subsequently, more business.</p><p>You’ll experience an increased customer retention rate after using chatbots. It reduces the effort and cost of acquiring a new customer each time by increasing loyalty of the existing ones. Chatbots give the customers the time and attention they want to make them feel important and happy.</p><p><strong>5.Market Research and Analysis for making impactful business decisions</strong></p><p>You can get or generate a considerable amount of versatile and unstructured content just from social media. NLP helps in structuring the unstructured content and draw meaning from it. You can easily understand the meaning or idea behind the customer reviews, inputs, comments or queries. You can get a glimpse at how the user is feeling about your services or your brand.</p>26:T524,<p><img src="https://cdn.marutitech.com/2_Mtech.png" alt="nlp-based-chatbot"></p><p>NLP based chatbots can help enhance your business processes and elevate customer experience to the next level while also increasing overall growth and profitability. It provides technological advantages to stay competitive in the market-saving time, effort and costs that further leads to increased customer satisfaction and increased engagements in your business.</p><p>Although NLP, NLU and NLG isn’t exactly at par with human language comprehension, given its subtleties and contextual reliance; <a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">an intelligent chatbot</a> can imitate that level of understanding and analysis fairly well. Within semi restricted contexts, a bot can execute quite well when it comes to assessing the user’s objective &amp; accomplish required tasks in the form of a self-service interaction.</p><p>At the end of the day, with NLP based chatbots, the result is significant when it comes to cutting down on operational costs for customer support through immediate responses with zero down time, round the clock and consistent execution from an “employee” that is new for an extremely short time frame and already well-versed in multiple languages.</p>2:[null,["$","$L11",null,{"blogData":{"data":[{"id":167,"attributes":{"createdAt":"2022-09-14T11:16:47.961Z","updatedAt":"2025-06-16T10:42:06.896Z","publishedAt":"2022-09-14T12:52:59.375Z","title":"Introduction to Sentiment Analysis: Concept, Working, and Application","description":"Understand emotions using software. Here's the complete guide on sentiment analysis, its working and application. ","type":"Artificial Intelligence and Machine Learning","slug":"introduction-to-sentiment-analysis","content":[{"id":13527,"title":null,"description":"$12","twitter_link":null,"twitter_link_text":null},{"id":13528,"title":"What is Sentiment Analysis?","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13529,"title":"Types of Sentiment Analysis","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13530,"title":"Importance of Sentiment Analysis","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13531,"title":"How Does Sentiment Analysis Work?","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13532,"title":"Sentiment Analysis: Machine Learning Approach","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13533,"title":"Pros and Cons of Sentiment Analysis","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13534,"title":"Challenges Faced During Sentiment Analysis","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13535,"title":"Application of Sentiment Analysis","description":"$1a","twitter_link":null,"twitter_link_text":null},{"id":13536,"title":"Conclusion","description":"$1b","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":385,"attributes":{"name":"d315fc9a-sentimentanalysis-min (1).jpg","alternativeText":"d315fc9a-sentimentanalysis-min (1).jpg","caption":"d315fc9a-sentimentanalysis-min (1).jpg","width":1000,"height":667,"formats":{"small":{"name":"small_d315fc9a-sentimentanalysis-min (1).jpg","hash":"small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":334,"size":41.53,"sizeInBytes":41528,"url":"https://cdn.marutitech.com//small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"thumbnail":{"name":"thumbnail_d315fc9a-sentimentanalysis-min (1).jpg","hash":"thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":234,"height":156,"size":12.76,"sizeInBytes":12764,"url":"https://cdn.marutitech.com//thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"medium":{"name":"medium_d315fc9a-sentimentanalysis-min (1).jpg","hash":"medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":500,"size":81.13,"sizeInBytes":81134,"url":"https://cdn.marutitech.com//medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"}},"hash":"d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","size":130.31,"url":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:01.279Z","updatedAt":"2024-12-16T11:45:01.279Z"}}},"audio_file":{"data":null},"suggestions":{"id":1934,"blogs":{"data":[{"id":154,"attributes":{"createdAt":"2022-09-13T11:53:26.556Z","updatedAt":"2025-06-16T10:42:05.490Z","publishedAt":"2022-09-13T12:13:03.080Z","title":"Unlocking the Power of NLP in Healthcare: A Comprehensive Review","description":"Get an overview of how Natural Language Processing (NLP) can be used in the healthcare sector.","type":"Artificial Intelligence and Machine Learning","slug":"nlp-in-healthcare","content":[{"id":13464,"title":null,"description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13465,"title":"Driving Factors Behind NLP in Healthcare","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13466,"title":"How Would Healthcare Benefit from NLP Integration?","description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":13467,"title":"What the Future of NLP in Healthcare Looks Like","description":"$1f","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":375,"attributes":{"name":"6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","alternativeText":"6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","caption":"6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","width":1000,"height":563,"formats":{"small":{"name":"small_6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","hash":"small_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":17.09,"sizeInBytes":17088,"url":"https://cdn.marutitech.com//small_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5.jpg"},"medium":{"name":"medium_6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","hash":"medium_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":32.14,"sizeInBytes":32144,"url":"https://cdn.marutitech.com//medium_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5.jpg"},"thumbnail":{"name":"thumbnail_6-Driving-Factors-Behind-NLP-in-Healthcare.jpg","hash":"thumbnail_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":5.87,"sizeInBytes":5870,"url":"https://cdn.marutitech.com//thumbnail_6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5.jpg"}},"hash":"6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5","ext":".jpg","mime":"image/jpeg","size":49.91,"url":"https://cdn.marutitech.com//6_Driving_Factors_Behind_NLP_in_Healthcare_4933c3fac5.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:44:26.628Z","updatedAt":"2024-12-16T11:44:26.628Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":178,"attributes":{"createdAt":"2022-09-14T11:21:23.795Z","updatedAt":"2025-06-16T10:42:08.501Z","publishedAt":"2022-09-14T12:51:46.148Z","title":"What is NLP? And 7 Reasons why everyone in Retail should use it","description":"Get in-depth knowledge on how NLP can affect retail industry.  ","type":"Artificial Intelligence and Machine Learning","slug":"what-nlp-reasons-everyone-retail-use-it","content":[{"id":13633,"title":null,"description":"<p><a href=\"https://marutitech.com/how-is-natural-language-processing-applied-in-business/\" target=\"_blank\" rel=\"noopener\">Natural Language Processing</a> (NLP) is one of the attempts of adding a ‘human touch’ in the computer-driven world. Frankly speaking, it worked out wonders so far. NLP technology falls under the umbrella of Artificial Intelligence (AI). NLP is coded to act like a human to communicate and respond to user’s query smartly to achieve better customer satisfaction or even get cart checkout conversion rates higher.</p><p>NLP is an inbuilt and powerful technology that helps users find the exact products on the shopping websites without having to choose from different options available from the static searches. Even a dynamic search that is provided on the website suggests the words even before we type may not be all that interesting with the coming age of Artificial Intelligence (AI).</p>","twitter_link":null,"twitter_link_text":null},{"id":13634,"title":"Technology innovations in communication and interactions","description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13635,"title":"Searching for the perfect match","description":"<p>When people search for the product online, the exact and closest matches appear on the screen. The product description played a vital role in marketing the product and helps improve sales to a considerable ratio.</p><p>Now with the app world, everything is individualized. Preferences based on search history, recommendations based on sales history, notifications, etc., and give users a delightful experience. As the usage of smartphones and tablets increases day by day, mobile-optimized websites and apps are gaining momentum to give users online shopping experience a fulfilling one.</p>","twitter_link":null,"twitter_link_text":null},{"id":13636,"title":"Challenges in Retail industry","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13637,"title":"7 different ways NLP can help retailers","description":"$22","twitter_link":null,"twitter_link_text":null},{"id":13638,"title":"Conclusion","description":"<p>Businesses would be greatly benefitted from these in-depth insights that are powered by AI are surely find customer satisfaction ratio in the upward curve leading to the increase in revenue curve as well. More innovations like <a href=\"https://marutitech.com/nlp-contract-management-analysis/\" target=\"_blank\" rel=\"noopener\">NLP contract management</a> can transform business operations. We just wait and watch how the AI unfolds in the coming years.</p>","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":3627,"attributes":{"name":"What is NLP.webp","alternativeText":"What is NLP","caption":null,"width":5096,"height":3397,"formats":{"thumbnail":{"name":"thumbnail_What is NLP.webp","hash":"thumbnail_What_is_NLP_0d52b86f53","ext":".webp","mime":"image/webp","path":null,"width":234,"height":156,"size":9.74,"sizeInBytes":9744,"url":"https://cdn.marutitech.com/thumbnail_What_is_NLP_0d52b86f53.webp"},"small":{"name":"small_What is NLP.webp","hash":"small_What_is_NLP_0d52b86f53","ext":".webp","mime":"image/webp","path":null,"width":500,"height":333,"size":28.72,"sizeInBytes":28720,"url":"https://cdn.marutitech.com/small_What_is_NLP_0d52b86f53.webp"},"large":{"name":"large_What is NLP.webp","hash":"large_What_is_NLP_0d52b86f53","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":667,"size":70.54,"sizeInBytes":70540,"url":"https://cdn.marutitech.com/large_What_is_NLP_0d52b86f53.webp"},"medium":{"name":"medium_What is NLP.webp","hash":"medium_What_is_NLP_0d52b86f53","ext":".webp","mime":"image/webp","path":null,"width":750,"height":500,"size":48.92,"sizeInBytes":48924,"url":"https://cdn.marutitech.com/medium_What_is_NLP_0d52b86f53.webp"}},"hash":"What_is_NLP_0d52b86f53","ext":".webp","mime":"image/webp","size":581.87,"url":"https://cdn.marutitech.com/What_is_NLP_0d52b86f53.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-08T08:44:52.825Z","updatedAt":"2025-05-08T08:44:52.825Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":201,"attributes":{"createdAt":"2022-09-14T11:28:56.499Z","updatedAt":"2025-06-16T10:42:11.331Z","publishedAt":"2022-09-15T06:04:22.120Z","title":"What is NLP? Why does your business need an NLP based chatbot?","description":"Understand the basics of NLP and how it can be used to create an NLP-based chatbot for your business.","type":"Bot Development","slug":"nlp-based-chatbot","content":[{"id":13771,"title":null,"description":"<p>With chatbots becoming more and more prevalent over the last couple years, they have gone on to serve multiple different use cases across industries in the form of scripted &amp; linear conversations with a predetermined output. Although that has served the purpose with multiple use cases, today, with the advent of <a href=\"https://marutitech.com/artificial-intelligence-and-machine-learning/\" target=\"_blank\" rel=\"noopener\">AI and Machine Learning</a>, it has become imperative for businesses to develop and deploy an NLP based chatbot that assesses, analyzes and communicates with its users just like a human in order to offer an unparalleled experience.&nbsp;<strong>&nbsp;</strong></p>","twitter_link":null,"twitter_link_text":null},{"id":13772,"title":"What is Natural Language Processing (NLP)?","description":"$23","twitter_link":null,"twitter_link_text":null},{"id":13773,"title":"What can NLP Engines do?","description":"$24","twitter_link":null,"twitter_link_text":null},{"id":13774,"title":"What can chatbots with NLP do to your business?","description":"$25","twitter_link":null,"twitter_link_text":null},{"id":13775,"title":"Conclusion","description":"$26","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":506,"attributes":{"name":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","alternativeText":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","caption":"nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","width":9170,"height":3840,"formats":{"thumbnail":{"name":"thumbnail_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"thumbnail_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":103,"size":5.89,"sizeInBytes":5886,"url":"https://cdn.marutitech.com//thumbnail_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"large":{"name":"large_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"large_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":419,"size":43.67,"sizeInBytes":43667,"url":"https://cdn.marutitech.com//large_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"small":{"name":"small_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"small_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":209,"size":17.03,"sizeInBytes":17030,"url":"https://cdn.marutitech.com//small_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"},"medium":{"name":"medium_nlp-natural-language-processing-cognitive-computing-technology-concept (1).jpg","hash":"medium_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":314,"size":29.52,"sizeInBytes":29524,"url":"https://cdn.marutitech.com//medium_nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg"}},"hash":"nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015","ext":".jpg","mime":"image/jpeg","size":785.07,"url":"https://cdn.marutitech.com//nlp_natural_language_processing_cognitive_computing_technology_concept_1_d0691e3015.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:36.013Z","updatedAt":"2024-12-16T11:53:36.013Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1934,"title":"Machine Learning Model Accelerates Healthcare Record Processing by 87%","link":"https://marutitech.com/case-study/medical-record-processing-using-nlp/","cover_image":{"data":{"id":675,"attributes":{"name":"2.png","alternativeText":"2.png","caption":"2.png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":21,"sizeInBytes":21002,"url":"https://cdn.marutitech.com//thumbnail_2_d22fbc1184.png"},"small":{"name":"small_2.png","hash":"small_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":73.7,"sizeInBytes":73702,"url":"https://cdn.marutitech.com//small_2_d22fbc1184.png"},"medium":{"name":"medium_2.png","hash":"medium_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":163.79,"sizeInBytes":163790,"url":"https://cdn.marutitech.com//medium_2_d22fbc1184.png"},"large":{"name":"large_2.png","hash":"large_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":292.75,"sizeInBytes":292746,"url":"https://cdn.marutitech.com//large_2_d22fbc1184.png"}},"hash":"2_d22fbc1184","ext":".png","mime":"image/png","size":93.1,"url":"https://cdn.marutitech.com//2_d22fbc1184.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-31T09:40:15.084Z","updatedAt":"2024-12-31T09:40:15.084Z"}}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]},"seo":{"id":2164,"title":"Introduction to Sentiment Analysis: Concept, Working, and Application","description":"Track your company's brand perception and better your services using sentiment analysis. Learn more on sentiment analysis and its applications.","type":"article","url":"https://marutitech.com/introduction-to-sentiment-analysis/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":385,"attributes":{"name":"d315fc9a-sentimentanalysis-min (1).jpg","alternativeText":"d315fc9a-sentimentanalysis-min (1).jpg","caption":"d315fc9a-sentimentanalysis-min (1).jpg","width":1000,"height":667,"formats":{"small":{"name":"small_d315fc9a-sentimentanalysis-min (1).jpg","hash":"small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":334,"size":41.53,"sizeInBytes":41528,"url":"https://cdn.marutitech.com//small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"thumbnail":{"name":"thumbnail_d315fc9a-sentimentanalysis-min (1).jpg","hash":"thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":234,"height":156,"size":12.76,"sizeInBytes":12764,"url":"https://cdn.marutitech.com//thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"medium":{"name":"medium_d315fc9a-sentimentanalysis-min (1).jpg","hash":"medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":500,"size":81.13,"sizeInBytes":81134,"url":"https://cdn.marutitech.com//medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"}},"hash":"d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","size":130.31,"url":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:01.279Z","updatedAt":"2024-12-16T11:45:01.279Z"}}}},"image":{"data":{"id":385,"attributes":{"name":"d315fc9a-sentimentanalysis-min (1).jpg","alternativeText":"d315fc9a-sentimentanalysis-min (1).jpg","caption":"d315fc9a-sentimentanalysis-min (1).jpg","width":1000,"height":667,"formats":{"small":{"name":"small_d315fc9a-sentimentanalysis-min (1).jpg","hash":"small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":334,"size":41.53,"sizeInBytes":41528,"url":"https://cdn.marutitech.com//small_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"thumbnail":{"name":"thumbnail_d315fc9a-sentimentanalysis-min (1).jpg","hash":"thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":234,"height":156,"size":12.76,"sizeInBytes":12764,"url":"https://cdn.marutitech.com//thumbnail_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"},"medium":{"name":"medium_d315fc9a-sentimentanalysis-min (1).jpg","hash":"medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":500,"size":81.13,"sizeInBytes":81134,"url":"https://cdn.marutitech.com//medium_d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"}},"hash":"d315fc9a_sentimentanalysis_min_1_4051c3c1eb","ext":".jpg","mime":"image/jpeg","size":130.31,"url":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:01.279Z","updatedAt":"2024-12-16T11:45:01.279Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
27:T698,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/introduction-to-sentiment-analysis/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/introduction-to-sentiment-analysis/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/introduction-to-sentiment-analysis/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/introduction-to-sentiment-analysis/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/introduction-to-sentiment-analysis/#webpage","url":"https://marutitech.com/introduction-to-sentiment-analysis/","inLanguage":"en-US","name":"Introduction to Sentiment Analysis: Concept, Working, and Application","isPartOf":{"@id":"https://marutitech.com/introduction-to-sentiment-analysis/#website"},"about":{"@id":"https://marutitech.com/introduction-to-sentiment-analysis/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/introduction-to-sentiment-analysis/#primaryimage","url":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/introduction-to-sentiment-analysis/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Track your company's brand perception and better your services using sentiment analysis. Learn more on sentiment analysis and its applications."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Introduction to Sentiment Analysis: Concept, Working, and Application"}],["$","meta","3",{"name":"description","content":"Track your company's brand perception and better your services using sentiment analysis. Learn more on sentiment analysis and its applications."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$27"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/introduction-to-sentiment-analysis/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"Introduction to Sentiment Analysis: Concept, Working, and Application"}],["$","meta","9",{"property":"og:description","content":"Track your company's brand perception and better your services using sentiment analysis. Learn more on sentiment analysis and its applications."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/introduction-to-sentiment-analysis/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"Introduction to Sentiment Analysis: Concept, Working, and Application"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"Introduction to Sentiment Analysis: Concept, Working, and Application"}],["$","meta","19",{"name":"twitter:description","content":"Track your company's brand perception and better your services using sentiment analysis. Learn more on sentiment analysis and its applications."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//d315fc9a_sentimentanalysis_min_1_4051c3c1eb.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
