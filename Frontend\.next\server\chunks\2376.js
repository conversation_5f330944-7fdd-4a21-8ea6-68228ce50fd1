"use strict";exports.id=2376,exports.ids=[2376],exports.modules={29064:(t,n,e)=>{var r;t=e.nmd(t),(function(){var i,o="Expected a function",a="__lodash_hash_undefined__",u="__lodash_placeholder__",c=1/0,l=0/0,f=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],s="[object Arguments]",h="[object Array]",d="[object Boolean]",p="[object Date]",v="[object Error]",g="[object Function]",y="[object GeneratorFunction]",_="[object Map]",b="[object Number]",m="[object Object]",x="[object Promise]",w="[object RegExp]",M="[object Set]",T="[object String]",A="[object Symbol]",k="[object WeakMap]",S="[object ArrayBuffer]",E="[object DataView]",N="[object Float32Array]",C="[object Float64Array]",P="[object Int8Array]",R="[object Int16Array]",O="[object Int32Array]",j="[object Uint8Array]",z="[object Uint8ClampedArray]",D="[object Uint16Array]",L="[object Uint32Array]",I=/\b__p \+= '';/g,$=/\b(__p \+=) '' \+/g,B=/(__e\(.*?\)|\b__t\)) \+\n'';/g,V=/&(?:amp|lt|gt|quot|#39);/g,U=/[&<>"']/g,F=RegExp(V.source),q=RegExp(U.source),G=/<%-([\s\S]+?)%>/g,Y=/<%([\s\S]+?)%>/g,H=/<%=([\s\S]+?)%>/g,W=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Z=/^\w*$/,X=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,K=/[\\^$.*+?()[\]{}|]/g,J=RegExp(K.source),Q=/^\s+/,tt=/\s/,tn=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,te=/\{\n\/\* \[wrapped with (.+)\] \*/,tr=/,? & /,ti=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,to=/[()=,{}\[\]\/\s]/,ta=/\\(\\)?/g,tu=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tc=/\w*$/,tl=/^[-+]0x[0-9a-f]+$/i,tf=/^0b[01]+$/i,ts=/^\[object .+?Constructor\]$/,th=/^0o[0-7]+$/i,td=/^(?:0|[1-9]\d*)$/,tp=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,tv=/($^)/,tg=/['\n\r\u2028\u2029\\]/g,ty="\ud800-\udfff",t_="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",tb="\\u2700-\\u27bf",tm="a-z\\xdf-\\xf6\\xf8-\\xff",tx="A-Z\\xc0-\\xd6\\xd8-\\xde",tw="\\ufe0e\\ufe0f",tM="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",tT="['’]",tA="["+tM+"]",tk="["+t_+"]",tS="["+tm+"]",tE="[^"+ty+tM+"\\d+"+tb+tm+tx+"]",tN="\ud83c[\udffb-\udfff]",tC="[^"+ty+"]",tP="(?:\ud83c[\udde6-\uddff]){2}",tR="[\ud800-\udbff][\udc00-\udfff]",tO="["+tx+"]",tj="\\u200d",tz="(?:"+tS+"|"+tE+")",tD="(?:"+tT+"(?:d|ll|m|re|s|t|ve))?",tL="(?:"+tT+"(?:D|LL|M|RE|S|T|VE))?",tI="(?:"+tk+"|"+tN+")?",t$="["+tw+"]?",tB="(?:"+tj+"(?:"+[tC,tP,tR].join("|")+")"+t$+tI+")*",tV=t$+tI+tB,tU="(?:"+["["+tb+"]",tP,tR].join("|")+")"+tV,tF="(?:"+[tC+tk+"?",tk,tP,tR,"["+ty+"]"].join("|")+")",tq=RegExp(tT,"g"),tG=RegExp(tk,"g"),tY=RegExp(tN+"(?="+tN+")|"+tF+tV,"g"),tH=RegExp([tO+"?"+tS+"+"+tD+"(?="+[tA,tO,"$"].join("|")+")","(?:"+tO+"|"+tE+")+"+tL+"(?="+[tA,tO+tz,"$"].join("|")+")",tO+"?"+tz+"+"+tD,tO+"+"+tL,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])","\\d+",tU].join("|"),"g"),tW=RegExp("["+tj+ty+t_+tw+"]"),tZ=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,tX=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],tK=-1,tJ={};tJ[N]=tJ[C]=tJ[P]=tJ[R]=tJ[O]=tJ[j]=tJ[z]=tJ[D]=tJ[L]=!0,tJ[s]=tJ[h]=tJ[S]=tJ[d]=tJ[E]=tJ[p]=tJ[v]=tJ[g]=tJ[_]=tJ[b]=tJ[m]=tJ[w]=tJ[M]=tJ[T]=tJ[k]=!1;var tQ={};tQ[s]=tQ[h]=tQ[S]=tQ[E]=tQ[d]=tQ[p]=tQ[N]=tQ[C]=tQ[P]=tQ[R]=tQ[O]=tQ[_]=tQ[b]=tQ[m]=tQ[w]=tQ[M]=tQ[T]=tQ[A]=tQ[j]=tQ[z]=tQ[D]=tQ[L]=!0,tQ[v]=tQ[g]=tQ[k]=!1;var t0={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},t1=parseFloat,t2=parseInt,t6="object"==typeof global&&global&&global.Object===Object&&global,t3="object"==typeof self&&self&&self.Object===Object&&self,t5=t6||t3||Function("return this")(),t4=n&&!n.nodeType&&n,t8=t4&&t&&!t.nodeType&&t,t7=t8&&t8.exports===t4,t9=t7&&t6.process,nt=function(){try{var t=t8&&t8.require&&t8.require("util").types;if(t)return t;return t9&&t9.binding&&t9.binding("util")}catch(t){}}(),nn=nt&&nt.isArrayBuffer,ne=nt&&nt.isDate,nr=nt&&nt.isMap,ni=nt&&nt.isRegExp,no=nt&&nt.isSet,na=nt&&nt.isTypedArray;function nu(t,n,e){switch(e.length){case 0:return t.call(n);case 1:return t.call(n,e[0]);case 2:return t.call(n,e[0],e[1]);case 3:return t.call(n,e[0],e[1],e[2])}return t.apply(n,e)}function nc(t,n,e,r){for(var i=-1,o=null==t?0:t.length;++i<o;){var a=t[i];n(r,a,e(a),t)}return r}function nl(t,n){for(var e=-1,r=null==t?0:t.length;++e<r&&!1!==n(t[e],e,t););return t}function nf(t,n){for(var e=-1,r=null==t?0:t.length;++e<r;)if(!n(t[e],e,t))return!1;return!0}function ns(t,n){for(var e=-1,r=null==t?0:t.length,i=0,o=[];++e<r;){var a=t[e];n(a,e,t)&&(o[i++]=a)}return o}function nh(t,n){return!!(null==t?0:t.length)&&nw(t,n,0)>-1}function nd(t,n,e){for(var r=-1,i=null==t?0:t.length;++r<i;)if(e(n,t[r]))return!0;return!1}function np(t,n){for(var e=-1,r=null==t?0:t.length,i=Array(r);++e<r;)i[e]=n(t[e],e,t);return i}function nv(t,n){for(var e=-1,r=n.length,i=t.length;++e<r;)t[i+e]=n[e];return t}function ng(t,n,e,r){var i=-1,o=null==t?0:t.length;for(r&&o&&(e=t[++i]);++i<o;)e=n(e,t[i],i,t);return e}function ny(t,n,e,r){var i=null==t?0:t.length;for(r&&i&&(e=t[--i]);i--;)e=n(e,t[i],i,t);return e}function n_(t,n){for(var e=-1,r=null==t?0:t.length;++e<r;)if(n(t[e],e,t))return!0;return!1}var nb=nk("length");function nm(t,n,e){var r;return e(t,function(t,e,i){if(n(t,e,i))return r=e,!1}),r}function nx(t,n,e,r){for(var i=t.length,o=e+(r?1:-1);r?o--:++o<i;)if(n(t[o],o,t))return o;return -1}function nw(t,n,e){return n==n?function(t,n,e){for(var r=e-1,i=t.length;++r<i;)if(t[r]===n)return r;return -1}(t,n,e):nx(t,nT,e)}function nM(t,n,e,r){for(var i=e-1,o=t.length;++i<o;)if(r(t[i],n))return i;return -1}function nT(t){return t!=t}function nA(t,n){var e=null==t?0:t.length;return e?nN(t,n)/e:l}function nk(t){return function(n){return null==n?i:n[t]}}function nS(t){return function(n){return null==t?i:t[n]}}function nE(t,n,e,r,i){return i(t,function(t,i,o){e=r?(r=!1,t):n(e,t,i,o)}),e}function nN(t,n){for(var e,r=-1,o=t.length;++r<o;){var a=n(t[r]);i!==a&&(e=i===e?a:e+a)}return e}function nC(t,n){for(var e=-1,r=Array(t);++e<t;)r[e]=n(e);return r}function nP(t){return t?t.slice(0,nH(t)+1).replace(Q,""):t}function nR(t){return function(n){return t(n)}}function nO(t,n){return np(n,function(n){return t[n]})}function nj(t,n){return t.has(n)}function nz(t,n){for(var e=-1,r=t.length;++e<r&&nw(n,t[e],0)>-1;);return e}function nD(t,n){for(var e=t.length;e--&&nw(n,t[e],0)>-1;);return e}var nL=nS({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),nI=nS({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function n$(t){return"\\"+t0[t]}function nB(t){return tW.test(t)}function nV(t){var n=-1,e=Array(t.size);return t.forEach(function(t,r){e[++n]=[r,t]}),e}function nU(t,n){return function(e){return t(n(e))}}function nF(t,n){for(var e=-1,r=t.length,i=0,o=[];++e<r;){var a=t[e];(a===n||a===u)&&(t[e]=u,o[i++]=e)}return o}function nq(t){var n=-1,e=Array(t.size);return t.forEach(function(t){e[++n]=t}),e}function nG(t){return nB(t)?function(t){for(var n=tY.lastIndex=0;tY.test(t);)++n;return n}(t):nb(t)}function nY(t){return nB(t)?t.match(tY)||[]:t.split("")}function nH(t){for(var n=t.length;n--&&tt.test(t.charAt(n)););return n}var nW=nS({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),nZ=function t(n){var e,r,tt,ty,t_=(n=null==n?t5:nZ.defaults(t5.Object(),n,nZ.pick(t5,tX))).Array,tb=n.Date,tm=n.Error,tx=n.Function,tw=n.Math,tM=n.Object,tT=n.RegExp,tA=n.String,tk=n.TypeError,tS=t_.prototype,tE=tx.prototype,tN=tM.prototype,tC=n["__core-js_shared__"],tP=tE.toString,tR=tN.hasOwnProperty,tO=0,tj=(e=/[^.]+$/.exec(tC&&tC.keys&&tC.keys.IE_PROTO||""))?"Symbol(src)_1."+e:"",tz=tN.toString,tD=tP.call(tM),tL=t5._,tI=tT("^"+tP.call(tR).replace(K,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),t$=t7?n.Buffer:i,tB=n.Symbol,tV=n.Uint8Array,tU=t$?t$.allocUnsafe:i,tF=nU(tM.getPrototypeOf,tM),tY=tM.create,tW=tN.propertyIsEnumerable,t0=tS.splice,t6=tB?tB.isConcatSpreadable:i,t3=tB?tB.iterator:i,t4=tB?tB.toStringTag:i,t8=function(){try{var t=ip(tM,"defineProperty");return t({},"",{}),t}catch(t){}}(),t9=n.clearTimeout!==t5.clearTimeout&&n.clearTimeout,nt=tb&&tb.now!==t5.Date.now&&tb.now,nb=n.setTimeout!==t5.setTimeout&&n.setTimeout,nS=tw.ceil,nX=tw.floor,nK=tM.getOwnPropertySymbols,nJ=t$?t$.isBuffer:i,nQ=n.isFinite,n0=tS.join,n1=nU(tM.keys,tM),n2=tw.max,n6=tw.min,n3=tb.now,n5=n.parseInt,n4=tw.random,n8=tS.reverse,n7=ip(n,"DataView"),n9=ip(n,"Map"),et=ip(n,"Promise"),en=ip(n,"Set"),ee=ip(n,"WeakMap"),er=ip(tM,"create"),ei=ee&&new ee,eo={},ea=i$(n7),eu=i$(n9),ec=i$(et),el=i$(en),ef=i$(ee),es=tB?tB.prototype:i,eh=es?es.valueOf:i,ed=es?es.toString:i;function ep(t){if(oY(t)&&!oz(t)&&!(t instanceof e_)){if(t instanceof ey)return t;if(tR.call(t,"__wrapped__"))return iB(t)}return new ey(t)}var ev=function(){function t(){}return function(n){if(!oG(n))return{};if(tY)return tY(n);t.prototype=n;var e=new t;return t.prototype=i,e}}();function eg(){}function ey(t,n){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=i}function e_(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=4294967295,this.__views__=[]}function eb(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}function em(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}function ex(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}function ew(t){var n=-1,e=null==t?0:t.length;for(this.__data__=new ex;++n<e;)this.add(t[n])}function eM(t){var n=this.__data__=new em(t);this.size=n.size}function eT(t,n){var e=oz(t),r=!e&&oj(t),i=!e&&!r&&o$(t),o=!e&&!r&&!i&&o0(t),a=e||r||i||o,u=a?nC(t.length,tA):[],c=u.length;for(var l in t)(n||tR.call(t,l))&&!(a&&("length"==l||i&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||ix(l,c)))&&u.push(l);return u}function eA(t){var n=t.length;return n?t[rl(0,n-1)]:i}function ek(t,n,e){(i===e||oP(t[n],e))&&(i!==e||n in t)||eP(t,n,e)}function eS(t,n,e){var r=t[n];tR.call(t,n)&&oP(r,e)&&(i!==e||n in t)||eP(t,n,e)}function eE(t,n){for(var e=t.length;e--;)if(oP(t[e][0],n))return e;return -1}function eN(t,n,e,r){return eI(t,function(t,i,o){n(r,t,e(t),o)}),r}function eC(t,n){return t&&rV(n,ah(n),t)}function eP(t,n,e){"__proto__"==n&&t8?t8(t,n,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[n]=e}function eR(t,n){for(var e=-1,r=n.length,o=t_(r),a=null==t;++e<r;)o[e]=a?i:au(t,n[e]);return o}function eO(t,n,e){return t==t&&(i!==e&&(t=t<=e?t:e),i!==n&&(t=t>=n?t:n)),t}function ej(t,n,e,r,o,a){var u,c=1&n,l=2&n,f=4&n;if(e&&(u=o?e(t,r,o,a):e(t)),i!==u)return u;if(!oG(t))return t;var h=oz(t);if(h){if(v=t.length,x=new t.constructor(v),v&&"string"==typeof t[0]&&tR.call(t,"index")&&(x.index=t.index,x.input=t.input),u=x,!c)return rB(t,u)}else{var v,x,k,I,$,B=iy(t),V=B==g||B==y;if(o$(t))return rj(t,c);if(B==m||B==s||V&&!o){if(u=l||V?{}:ib(t),!c)return l?(k=($=u)&&rV(t,ad(t),$),rV(t,ig(t),k)):(I=eC(u,t),rV(t,iv(t),I))}else{if(!tQ[B])return o?t:{};u=function(t,n,e){var r,i,o=t.constructor;switch(n){case S:return rz(t);case d:case p:return new o(+t);case E:return r=e?rz(t.buffer):t.buffer,new t.constructor(r,t.byteOffset,t.byteLength);case N:case C:case P:case R:case O:case j:case z:case D:case L:return rD(t,e);case _:return new o;case b:case T:return new o(t);case w:return(i=new t.constructor(t.source,tc.exec(t))).lastIndex=t.lastIndex,i;case M:return new o;case A:return eh?tM(eh.call(t)):{}}}(t,B,c)}}a||(a=new eM);var U=a.get(t);if(U)return U;a.set(t,u),oK(t)?t.forEach(function(r){u.add(ej(r,n,e,r,t,a))}):oH(t)&&t.forEach(function(r,i){u.set(i,ej(r,n,e,i,t,a))});var F=f?l?ia:io:l?ad:ah,q=h?i:F(t);return nl(q||t,function(r,i){q&&(r=t[i=r]),eS(u,i,ej(r,n,e,i,t,a))}),u}function ez(t,n,e){var r=e.length;if(null==t)return!r;for(t=tM(t);r--;){var o=e[r],a=n[o],u=t[o];if(i===u&&!(o in t)||!a(u))return!1}return!0}function eD(t,n,e){if("function"!=typeof t)throw new tk(o);return iR(function(){t.apply(i,e)},n)}function eL(t,n,e,r){var i=-1,o=nh,a=!0,u=t.length,c=[],l=n.length;if(!u)return c;e&&(n=np(n,nR(e))),r?(o=nd,a=!1):n.length>=200&&(o=nj,a=!1,n=new ew(n));t:for(;++i<u;){var f=t[i],s=null==e?f:e(f);if(f=r||0!==f?f:0,a&&s==s){for(var h=l;h--;)if(n[h]===s)continue t;c.push(f)}else o(n,s,r)||c.push(f)}return c}ep.templateSettings={escape:G,evaluate:Y,interpolate:H,variable:"",imports:{_:ep}},ep.prototype=eg.prototype,ep.prototype.constructor=ep,ey.prototype=ev(eg.prototype),ey.prototype.constructor=ey,e_.prototype=ev(eg.prototype),e_.prototype.constructor=e_,eb.prototype.clear=function(){this.__data__=er?er(null):{},this.size=0},eb.prototype.delete=function(t){var n=this.has(t)&&delete this.__data__[t];return this.size-=n?1:0,n},eb.prototype.get=function(t){var n=this.__data__;if(er){var e=n[t];return e===a?i:e}return tR.call(n,t)?n[t]:i},eb.prototype.has=function(t){var n=this.__data__;return er?i!==n[t]:tR.call(n,t)},eb.prototype.set=function(t,n){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=er&&i===n?a:n,this},em.prototype.clear=function(){this.__data__=[],this.size=0},em.prototype.delete=function(t){var n=this.__data__,e=eE(n,t);return!(e<0)&&(e==n.length-1?n.pop():t0.call(n,e,1),--this.size,!0)},em.prototype.get=function(t){var n=this.__data__,e=eE(n,t);return e<0?i:n[e][1]},em.prototype.has=function(t){return eE(this.__data__,t)>-1},em.prototype.set=function(t,n){var e=this.__data__,r=eE(e,t);return r<0?(++this.size,e.push([t,n])):e[r][1]=n,this},ex.prototype.clear=function(){this.size=0,this.__data__={hash:new eb,map:new(n9||em),string:new eb}},ex.prototype.delete=function(t){var n=ih(this,t).delete(t);return this.size-=n?1:0,n},ex.prototype.get=function(t){return ih(this,t).get(t)},ex.prototype.has=function(t){return ih(this,t).has(t)},ex.prototype.set=function(t,n){var e=ih(this,t),r=e.size;return e.set(t,n),this.size+=e.size==r?0:1,this},ew.prototype.add=ew.prototype.push=function(t){return this.__data__.set(t,a),this},ew.prototype.has=function(t){return this.__data__.has(t)},eM.prototype.clear=function(){this.__data__=new em,this.size=0},eM.prototype.delete=function(t){var n=this.__data__,e=n.delete(t);return this.size=n.size,e},eM.prototype.get=function(t){return this.__data__.get(t)},eM.prototype.has=function(t){return this.__data__.has(t)},eM.prototype.set=function(t,n){var e=this.__data__;if(e instanceof em){var r=e.__data__;if(!n9||r.length<199)return r.push([t,n]),this.size=++e.size,this;e=this.__data__=new ex(r)}return e.set(t,n),this.size=e.size,this};var eI=rq(eY),e$=rq(eH,!0);function eB(t,n){var e=!0;return eI(t,function(t,r,i){return e=!!n(t,r,i)}),e}function eV(t,n,e){for(var r=-1,o=t.length;++r<o;){var a=t[r],u=n(a);if(null!=u&&(i===c?u==u&&!oQ(u):e(u,c)))var c=u,l=a}return l}function eU(t,n){var e=[];return eI(t,function(t,r,i){n(t,r,i)&&e.push(t)}),e}function eF(t,n,e,r,i){var o=-1,a=t.length;for(e||(e=im),i||(i=[]);++o<a;){var u=t[o];n>0&&e(u)?n>1?eF(u,n-1,e,r,i):nv(i,u):r||(i[i.length]=u)}return i}var eq=rG(),eG=rG(!0);function eY(t,n){return t&&eq(t,n,ah)}function eH(t,n){return t&&eG(t,n,ah)}function eW(t,n){return ns(n,function(n){return oU(t[n])})}function eZ(t,n){n=rP(n,t);for(var e=0,r=n.length;null!=t&&e<r;)t=t[iI(n[e++])];return e&&e==r?t:i}function eX(t,n,e){var r=n(t);return oz(t)?r:nv(r,e(t))}function eK(t){return null==t?i===t?"[object Undefined]":"[object Null]":t4&&t4 in tM(t)?function(t){var n=tR.call(t,t4),e=t[t4];try{t[t4]=i;var r=!0}catch(t){}var o=tz.call(t);return r&&(n?t[t4]=e:delete t[t4]),o}(t):tz.call(t)}function eJ(t,n){return t>n}function eQ(t,n){return null!=t&&tR.call(t,n)}function e0(t,n){return null!=t&&n in tM(t)}function e1(t,n,e){for(var r=e?nd:nh,o=t[0].length,a=t.length,u=a,c=t_(a),l=1/0,f=[];u--;){var s=t[u];u&&n&&(s=np(s,nR(n))),l=n6(s.length,l),c[u]=!e&&(n||o>=120&&s.length>=120)?new ew(u&&s):i}s=t[0];var h=-1,d=c[0];t:for(;++h<o&&f.length<l;){var p=s[h],v=n?n(p):p;if(p=e||0!==p?p:0,!(d?nj(d,v):r(f,v,e))){for(u=a;--u;){var g=c[u];if(!(g?nj(g,v):r(t[u],v,e)))continue t}d&&d.push(v),f.push(p)}}return f}function e2(t,n,e){n=rP(n,t);var r=null==(t=iN(t,n))?t:t[iI(iK(n))];return null==r?i:nu(r,t,e)}function e6(t){return oY(t)&&eK(t)==s}function e3(t,n,e,r,o){return t===n||(null!=t&&null!=n&&(oY(t)||oY(n))?function(t,n,e,r,o,a){var u=oz(t),c=oz(n),l=u?h:iy(t),f=c?h:iy(n);l=l==s?m:l,f=f==s?m:f;var g=l==m,y=f==m,x=l==f;if(x&&o$(t)){if(!o$(n))return!1;u=!0,g=!1}if(x&&!g)return a||(a=new eM),u||o0(t)?ir(t,n,e,r,o,a):function(t,n,e,r,i,o,a){switch(e){case E:if(t.byteLength!=n.byteLength||t.byteOffset!=n.byteOffset)break;t=t.buffer,n=n.buffer;case S:if(t.byteLength!=n.byteLength||!o(new tV(t),new tV(n)))break;return!0;case d:case p:case b:return oP(+t,+n);case v:return t.name==n.name&&t.message==n.message;case w:case T:return t==n+"";case _:var u=nV;case M:var c=1&r;if(u||(u=nq),t.size!=n.size&&!c)break;var l=a.get(t);if(l)return l==n;r|=2,a.set(t,n);var f=ir(u(t),u(n),r,i,o,a);return a.delete(t),f;case A:if(eh)return eh.call(t)==eh.call(n)}return!1}(t,n,l,e,r,o,a);if(!(1&e)){var k=g&&tR.call(t,"__wrapped__"),N=y&&tR.call(n,"__wrapped__");if(k||N){var C=k?t.value():t,P=N?n.value():n;return a||(a=new eM),o(C,P,e,r,a)}}return!!x&&(a||(a=new eM),function(t,n,e,r,o,a){var u=1&e,c=io(t),l=c.length;if(l!=io(n).length&&!u)return!1;for(var f=l;f--;){var s=c[f];if(!(u?s in n:tR.call(n,s)))return!1}var h=a.get(t),d=a.get(n);if(h&&d)return h==n&&d==t;var p=!0;a.set(t,n),a.set(n,t);for(var v=u;++f<l;){var g=t[s=c[f]],y=n[s];if(r)var _=u?r(y,g,s,n,t,a):r(g,y,s,t,n,a);if(!(i===_?g===y||o(g,y,e,r,a):_)){p=!1;break}v||(v="constructor"==s)}if(p&&!v){var b=t.constructor,m=n.constructor;b!=m&&"constructor"in t&&"constructor"in n&&!("function"==typeof b&&b instanceof b&&"function"==typeof m&&m instanceof m)&&(p=!1)}return a.delete(t),a.delete(n),p}(t,n,e,r,o,a))}(t,n,e,r,e3,o):t!=t&&n!=n)}function e5(t,n,e,r){var o=e.length,a=o,u=!r;if(null==t)return!a;for(t=tM(t);o--;){var c=e[o];if(u&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++o<a;){var l=(c=e[o])[0],f=t[l],s=c[1];if(u&&c[2]){if(i===f&&!(l in t))return!1}else{var h=new eM;if(r)var d=r(f,s,l,t,n,h);if(!(i===d?e3(s,f,3,r,h):d))return!1}}return!0}function e4(t){return!(!oG(t)||tj&&tj in t)&&(oU(t)?tI:ts).test(i$(t))}function e8(t){return"function"==typeof t?t:null==t?aI:"object"==typeof t?oz(t)?re(t[0],t[1]):rn(t):aH(t)}function e7(t){if(!ik(t))return n1(t);var n=[];for(var e in tM(t))tR.call(t,e)&&"constructor"!=e&&n.push(e);return n}function e9(t,n){return t<n}function rt(t,n){var e=-1,r=oL(t)?t_(t.length):[];return eI(t,function(t,i,o){r[++e]=n(t,i,o)}),r}function rn(t){var n=id(t);return 1==n.length&&n[0][2]?iS(n[0][0],n[0][1]):function(e){return e===t||e5(e,t,n)}}function re(t,n){var e;return iM(t)&&(e=n)==e&&!oG(e)?iS(iI(t),n):function(e){var r=au(e,t);return i===r&&r===n?ac(e,t):e3(n,r,3)}}function rr(t,n,e,r,o){t!==n&&eq(n,function(a,u){if(o||(o=new eM),oG(a))(function(t,n,e,r,o,a,u){var c=iC(t,e),l=iC(n,e),f=u.get(l);if(f){ek(t,e,f);return}var s=a?a(c,l,e+"",t,n,u):i,h=i===s;if(h){var d=oz(l),p=!d&&o$(l),v=!d&&!p&&o0(l);s=l,d||p||v?oz(c)?s=c:oI(c)?s=rB(c):p?(h=!1,s=rj(l,!0)):v?(h=!1,s=rD(l,!0)):s=[]:oZ(l)||oj(l)?(s=c,oj(c)?s=o7(c):(!oG(c)||oU(c))&&(s=ib(l))):h=!1}h&&(u.set(l,s),o(s,l,r,a,u),u.delete(l)),ek(t,e,s)})(t,n,u,e,rr,r,o);else{var c=r?r(iC(t,u),a,u+"",t,n,o):i;i===c&&(c=a),ek(t,u,c)}},ad)}function ri(t,n){var e=t.length;if(e)return ix(n+=n<0?e:0,e)?t[n]:i}function ro(t,n,e){n=n.length?np(n,function(t){return oz(t)?function(n){return eZ(n,1===t.length?t[0]:t)}:t}):[aI];var r=-1;return n=np(n,nR(is())),function(t,n){var e=t.length;for(t.sort(n);e--;)t[e]=t[e].value;return t}(rt(t,function(t,e,i){return{criteria:np(n,function(n){return n(t)}),index:++r,value:t}}),function(t,n){return function(t,n,e){for(var r=-1,i=t.criteria,o=n.criteria,a=i.length,u=e.length;++r<a;){var c=rL(i[r],o[r]);if(c){if(r>=u)return c;return c*("desc"==e[r]?-1:1)}}return t.index-n.index}(t,n,e)})}function ra(t,n,e){for(var r=-1,i=n.length,o={};++r<i;){var a=n[r],u=eZ(t,a);e(u,a)&&rh(o,rP(a,t),u)}return o}function ru(t,n,e,r){var i=r?nM:nw,o=-1,a=n.length,u=t;for(t===n&&(n=rB(n)),e&&(u=np(t,nR(e)));++o<a;)for(var c=0,l=n[o],f=e?e(l):l;(c=i(u,f,c,r))>-1;)u!==t&&t0.call(u,c,1),t0.call(t,c,1);return t}function rc(t,n){for(var e=t?n.length:0,r=e-1;e--;){var i=n[e];if(e==r||i!==o){var o=i;ix(i)?t0.call(t,i,1):rM(t,i)}}return t}function rl(t,n){return t+nX(n4()*(n-t+1))}function rf(t,n){var e="";if(!t||n<1||n>9007199254740991)return e;do n%2&&(e+=t),(n=nX(n/2))&&(t+=t);while(n);return e}function rs(t,n){return iO(iE(t,n,aI),t+"")}function rh(t,n,e,r){if(!oG(t))return t;n=rP(n,t);for(var o=-1,a=n.length,u=a-1,c=t;null!=c&&++o<a;){var l=iI(n[o]),f=e;if("__proto__"===l||"constructor"===l||"prototype"===l)break;if(o!=u){var s=c[l];f=r?r(s,l,c):i,i===f&&(f=oG(s)?s:ix(n[o+1])?[]:{})}eS(c,l,f),c=c[l]}return t}var rd=ei?function(t,n){return ei.set(t,n),t}:aI,rp=t8?function(t,n){return t8(t,"toString",{configurable:!0,enumerable:!1,value:az(n),writable:!0})}:aI;function rv(t,n,e){var r=-1,i=t.length;n<0&&(n=-n>i?0:i+n),(e=e>i?i:e)<0&&(e+=i),i=n>e?0:e-n>>>0,n>>>=0;for(var o=t_(i);++r<i;)o[r]=t[r+n];return o}function rg(t,n){var e;return eI(t,function(t,r,i){return!(e=n(t,r,i))}),!!e}function ry(t,n,e){var r=0,i=null==t?r:t.length;if("number"==typeof n&&n==n&&i<=2147483647){for(;r<i;){var o=r+i>>>1,a=t[o];null!==a&&!oQ(a)&&(e?a<=n:a<n)?r=o+1:i=o}return i}return r_(t,n,aI,e)}function r_(t,n,e,r){var o=0,a=null==t?0:t.length;if(0===a)return 0;for(var u=(n=e(n))!=n,c=null===n,l=oQ(n),f=i===n;o<a;){var s=nX((o+a)/2),h=e(t[s]),d=i!==h,p=null===h,v=h==h,g=oQ(h);if(u)var y=r||v;else y=f?v&&(r||d):c?v&&d&&(r||!p):l?v&&d&&!p&&(r||!g):!p&&!g&&(r?h<=n:h<n);y?o=s+1:a=s}return n6(a,4294967294)}function rb(t,n){for(var e=-1,r=t.length,i=0,o=[];++e<r;){var a=t[e],u=n?n(a):a;if(!e||!oP(u,c)){var c=u;o[i++]=0===a?0:a}}return o}function rm(t){return"number"==typeof t?t:oQ(t)?l:+t}function rx(t){if("string"==typeof t)return t;if(oz(t))return np(t,rx)+"";if(oQ(t))return ed?ed.call(t):"";var n=t+"";return"0"==n&&1/t==-c?"-0":n}function rw(t,n,e){var r=-1,i=nh,o=t.length,a=!0,u=[],c=u;if(e)a=!1,i=nd;else if(o>=200){var l=n?null:r4(t);if(l)return nq(l);a=!1,i=nj,c=new ew}else c=n?[]:u;t:for(;++r<o;){var f=t[r],s=n?n(f):f;if(f=e||0!==f?f:0,a&&s==s){for(var h=c.length;h--;)if(c[h]===s)continue t;n&&c.push(s),u.push(f)}else i(c,s,e)||(c!==u&&c.push(s),u.push(f))}return u}function rM(t,n){return n=rP(n,t),null==(t=iN(t,n))||delete t[iI(iK(n))]}function rT(t,n,e,r){return rh(t,n,e(eZ(t,n)),r)}function rA(t,n,e,r){for(var i=t.length,o=r?i:-1;(r?o--:++o<i)&&n(t[o],o,t););return e?rv(t,r?0:o,r?o+1:i):rv(t,r?o+1:0,r?i:o)}function rk(t,n){var e=t;return e instanceof e_&&(e=e.value()),ng(n,function(t,n){return n.func.apply(n.thisArg,nv([t],n.args))},e)}function rS(t,n,e){var r=t.length;if(r<2)return r?rw(t[0]):[];for(var i=-1,o=t_(r);++i<r;)for(var a=t[i],u=-1;++u<r;)u!=i&&(o[i]=eL(o[i]||a,t[u],n,e));return rw(eF(o,1),n,e)}function rE(t,n,e){for(var r=-1,o=t.length,a=n.length,u={};++r<o;){var c=r<a?n[r]:i;e(u,t[r],c)}return u}function rN(t){return oI(t)?t:[]}function rC(t){return"function"==typeof t?t:aI}function rP(t,n){return oz(t)?t:iM(t,n)?[t]:iL(o9(t))}function rR(t,n,e){var r=t.length;return e=i===e?r:e,!n&&e>=r?t:rv(t,n,e)}var rO=t9||function(t){return t5.clearTimeout(t)};function rj(t,n){if(n)return t.slice();var e=t.length,r=tU?tU(e):new t.constructor(e);return t.copy(r),r}function rz(t){var n=new t.constructor(t.byteLength);return new tV(n).set(new tV(t)),n}function rD(t,n){var e=n?rz(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)}function rL(t,n){if(t!==n){var e=i!==t,r=null===t,o=t==t,a=oQ(t),u=i!==n,c=null===n,l=n==n,f=oQ(n);if(!c&&!f&&!a&&t>n||a&&u&&l&&!c&&!f||r&&u&&l||!e&&l||!o)return 1;if(!r&&!a&&!f&&t<n||f&&e&&o&&!r&&!a||c&&e&&o||!u&&o||!l)return -1}return 0}function rI(t,n,e,r){for(var i=-1,o=t.length,a=e.length,u=-1,c=n.length,l=n2(o-a,0),f=t_(c+l),s=!r;++u<c;)f[u]=n[u];for(;++i<a;)(s||i<o)&&(f[e[i]]=t[i]);for(;l--;)f[u++]=t[i++];return f}function r$(t,n,e,r){for(var i=-1,o=t.length,a=-1,u=e.length,c=-1,l=n.length,f=n2(o-u,0),s=t_(f+l),h=!r;++i<f;)s[i]=t[i];for(var d=i;++c<l;)s[d+c]=n[c];for(;++a<u;)(h||i<o)&&(s[d+e[a]]=t[i++]);return s}function rB(t,n){var e=-1,r=t.length;for(n||(n=t_(r));++e<r;)n[e]=t[e];return n}function rV(t,n,e,r){var o=!e;e||(e={});for(var a=-1,u=n.length;++a<u;){var c=n[a],l=r?r(e[c],t[c],c,e,t):i;i===l&&(l=t[c]),o?eP(e,c,l):eS(e,c,l)}return e}function rU(t,n){return function(e,r){var i=oz(e)?nc:eN,o=n?n():{};return i(e,t,is(r,2),o)}}function rF(t){return rs(function(n,e){var r=-1,o=e.length,a=o>1?e[o-1]:i,u=o>2?e[2]:i;for(a=t.length>3&&"function"==typeof a?(o--,a):i,u&&iw(e[0],e[1],u)&&(a=o<3?i:a,o=1),n=tM(n);++r<o;){var c=e[r];c&&t(n,c,r,a)}return n})}function rq(t,n){return function(e,r){if(null==e)return e;if(!oL(e))return t(e,r);for(var i=e.length,o=n?i:-1,a=tM(e);(n?o--:++o<i)&&!1!==r(a[o],o,a););return e}}function rG(t){return function(n,e,r){for(var i=-1,o=tM(n),a=r(n),u=a.length;u--;){var c=a[t?u:++i];if(!1===e(o[c],c,o))break}return n}}function rY(t){return function(n){var e=nB(n=o9(n))?nY(n):i,r=e?e[0]:n.charAt(0),o=e?rR(e,1).join(""):n.slice(1);return r[t]()+o}}function rH(t){return function(n){return ng(aR(aT(n).replace(tq,"")),t,"")}}function rW(t){return function(){var n=arguments;switch(n.length){case 0:return new t;case 1:return new t(n[0]);case 2:return new t(n[0],n[1]);case 3:return new t(n[0],n[1],n[2]);case 4:return new t(n[0],n[1],n[2],n[3]);case 5:return new t(n[0],n[1],n[2],n[3],n[4]);case 6:return new t(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new t(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var e=ev(t.prototype),r=t.apply(e,n);return oG(r)?r:e}}function rZ(t){return function(n,e,r){var o=tM(n);if(!oL(n)){var a=is(e,3);n=ah(n),e=function(t){return a(o[t],t,o)}}var u=t(n,e,r);return u>-1?o[a?n[u]:u]:i}}function rX(t){return ii(function(n){var e=n.length,r=e,a=ey.prototype.thru;for(t&&n.reverse();r--;){var u=n[r];if("function"!=typeof u)throw new tk(o);if(a&&!c&&"wrapper"==ic(u))var c=new ey([],!0)}for(r=c?r:e;++r<e;){var l=ic(u=n[r]),f="wrapper"==l?iu(u):i;c=f&&iT(f[0])&&424==f[1]&&!f[4].length&&1==f[9]?c[ic(f[0])].apply(c,f[3]):1==u.length&&iT(u)?c[l]():c.thru(u)}return function(){var t=arguments,r=t[0];if(c&&1==t.length&&oz(r))return c.plant(r).value();for(var i=0,o=e?n[i].apply(this,t):r;++i<e;)o=n[i].call(this,o);return o}})}function rK(t,n,e,r,o,a,u,c,l,f){var s=128&n,h=1&n,d=2&n,p=24&n,v=512&n,g=d?i:rW(t);return function y(){for(var _=arguments.length,b=t_(_),m=_;m--;)b[m]=arguments[m];if(p)var x=il(y),w=function(t,n){for(var e=t.length,r=0;e--;)t[e]===n&&++r;return r}(b,x);if(r&&(b=rI(b,r,o,p)),a&&(b=r$(b,a,u,p)),_-=w,p&&_<f){var M=nF(b,x);return r3(t,n,rK,y.placeholder,e,b,M,c,l,f-_)}var T=h?e:this,A=d?T[t]:t;return _=b.length,c?b=function(t,n){for(var e=t.length,r=n6(n.length,e),o=rB(t);r--;){var a=n[r];t[r]=ix(a,e)?o[a]:i}return t}(b,c):v&&_>1&&b.reverse(),s&&l<_&&(b.length=l),this&&this!==t5&&this instanceof y&&(A=g||rW(A)),A.apply(T,b)}}function rJ(t,n){return function(e,r){var i,o;return i=n(r),o={},eY(e,function(n,e,r){t(o,i(n),e,r)}),o}}function rQ(t,n){return function(e,r){var o;if(i===e&&i===r)return n;if(i!==e&&(o=e),i!==r){if(i===o)return r;"string"==typeof e||"string"==typeof r?(e=rx(e),r=rx(r)):(e=rm(e),r=rm(r)),o=t(e,r)}return o}}function r0(t){return ii(function(n){return n=np(n,nR(is())),rs(function(e){var r=this;return t(n,function(t){return nu(t,r,e)})})})}function r1(t,n){var e=(n=i===n?" ":rx(n)).length;if(e<2)return e?rf(n,t):n;var r=rf(n,nS(t/nG(n)));return nB(n)?rR(nY(r),0,t).join(""):r.slice(0,t)}function r2(t){return function(n,e,r){return r&&"number"!=typeof r&&iw(n,e,r)&&(e=r=i),n=o3(n),i===e?(e=n,n=0):e=o3(e),r=i===r?n<e?1:-1:o3(r),function(t,n,e,r){for(var i=-1,o=n2(nS((n-t)/(e||1)),0),a=t_(o);o--;)a[r?o:++i]=t,t+=e;return a}(n,e,r,t)}}function r6(t){return function(n,e){return"string"==typeof n&&"string"==typeof e||(n=o8(n),e=o8(e)),t(n,e)}}function r3(t,n,e,r,o,a,u,c,l,f){var s=8&n,h=s?u:i,d=s?i:u,p=s?a:i,v=s?i:a;n|=s?32:64,4&(n&=~(s?64:32))||(n&=-4);var g=[t,n,o,p,h,v,d,c,l,f],y=e.apply(i,g);return iT(t)&&iP(y,g),y.placeholder=r,ij(y,t,n)}function r5(t){var n=tw[t];return function(t,e){if(t=o8(t),(e=null==e?0:n6(o5(e),292))&&nQ(t)){var r=(o9(t)+"e").split("e");return+((r=(o9(n(r[0]+"e"+(+r[1]+e)))+"e").split("e"))[0]+"e"+(+r[1]-e))}return n(t)}}var r4=en&&1/nq(new en([,-0]))[1]==c?function(t){return new en(t)}:aF;function r8(t){return function(n){var e,r,i=iy(n);return i==_?nV(n):i==M?(e=-1,r=Array(n.size),n.forEach(function(t){r[++e]=[t,t]}),r):np(t(n),function(t){return[t,n[t]]})}}function r7(t,n,e,r,a,c,l,f){var s=2&n;if(!s&&"function"!=typeof t)throw new tk(o);var h=r?r.length:0;if(h||(n&=-97,r=a=i),l=i===l?l:n2(o5(l),0),f=i===f?f:o5(f),h-=a?a.length:0,64&n){var d=r,p=a;r=a=i}var v=s?i:iu(t),g=[t,n,e,r,a,d,p,c,l,f];if(v&&function(t,n){var e=t[1],r=n[1],i=e|r,o=i<131,a=128==r&&8==e||128==r&&256==e&&t[7].length<=n[8]||384==r&&n[7].length<=n[8]&&8==e;if(o||a){1&r&&(t[2]=n[2],i|=1&e?0:4);var c=n[3];if(c){var l=t[3];t[3]=l?rI(l,c,n[4]):c,t[4]=l?nF(t[3],u):n[4]}(c=n[5])&&(l=t[5],t[5]=l?r$(l,c,n[6]):c,t[6]=l?nF(t[5],u):n[6]),(c=n[7])&&(t[7]=c),128&r&&(t[8]=null==t[8]?n[8]:n6(t[8],n[8])),null==t[9]&&(t[9]=n[9]),t[0]=n[0],t[1]=i}}(g,v),t=g[0],n=g[1],e=g[2],r=g[3],a=g[4],(f=g[9]=i===g[9]?s?0:t.length:n2(g[9]-h,0))||!(24&n)||(n&=-25),n&&1!=n)8==n||16==n?(y=t,_=n,b=f,m=rW(y),R=function t(){for(var n=arguments.length,e=t_(n),r=n,o=il(t);r--;)e[r]=arguments[r];var a=n<3&&e[0]!==o&&e[n-1]!==o?[]:nF(e,o);return(n-=a.length)<b?r3(y,_,rK,t.placeholder,i,e,a,i,i,b-n):nu(this&&this!==t5&&this instanceof t?m:y,this,e)}):32!=n&&33!=n||a.length?R=rK.apply(i,g):(x=t,w=n,M=e,T=r,A=1&w,k=rW(x),R=function t(){for(var n=-1,e=arguments.length,r=-1,i=T.length,o=t_(i+e),a=this&&this!==t5&&this instanceof t?k:x;++r<i;)o[r]=T[r];for(;e--;)o[r++]=arguments[++n];return nu(a,A?M:this,o)});else var y,_,b,m,x,w,M,T,A,k,S,E,N,C,P,R=(S=t,E=n,N=e,C=1&E,P=rW(S),function t(){return(this&&this!==t5&&this instanceof t?P:S).apply(C?N:this,arguments)});return ij((v?rd:iP)(R,g),t,n)}function r9(t,n,e,r){return i===t||oP(t,tN[e])&&!tR.call(r,e)?n:t}function it(t,n,e,r,o,a){return oG(t)&&oG(n)&&(a.set(n,t),rr(t,n,i,it,a),a.delete(n)),t}function ie(t){return oZ(t)?i:t}function ir(t,n,e,r,o,a){var u=1&e,c=t.length,l=n.length;if(c!=l&&!(u&&l>c))return!1;var f=a.get(t),s=a.get(n);if(f&&s)return f==n&&s==t;var h=-1,d=!0,p=2&e?new ew:i;for(a.set(t,n),a.set(n,t);++h<c;){var v=t[h],g=n[h];if(r)var y=u?r(g,v,h,n,t,a):r(v,g,h,t,n,a);if(i!==y){if(y)continue;d=!1;break}if(p){if(!n_(n,function(t,n){if(!nj(p,n)&&(v===t||o(v,t,e,r,a)))return p.push(n)})){d=!1;break}}else if(!(v===g||o(v,g,e,r,a))){d=!1;break}}return a.delete(t),a.delete(n),d}function ii(t){return iO(iE(t,i,iY),t+"")}function io(t){return eX(t,ah,iv)}function ia(t){return eX(t,ad,ig)}var iu=ei?function(t){return ei.get(t)}:aF;function ic(t){for(var n=t.name+"",e=eo[n],r=tR.call(eo,n)?e.length:0;r--;){var i=e[r],o=i.func;if(null==o||o==t)return i.name}return n}function il(t){return(tR.call(ep,"placeholder")?ep:t).placeholder}function is(){var t=ep.iteratee||a$;return t=t===a$?e8:t,arguments.length?t(arguments[0],arguments[1]):t}function ih(t,n){var e,r=t.__data__;return("string"==(e=typeof n)||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==n:null===n)?r["string"==typeof n?"string":"hash"]:r.map}function id(t){for(var n=ah(t),e=n.length;e--;){var r=n[e],i=t[r];n[e]=[r,i,i==i&&!oG(i)]}return n}function ip(t,n){var e=null==t?i:t[n];return e4(e)?e:i}var iv=nK?function(t){return null==t?[]:ns(nK(t=tM(t)),function(n){return tW.call(t,n)})}:aX,ig=nK?function(t){for(var n=[];t;)nv(n,iv(t)),t=tF(t);return n}:aX,iy=eK;function i_(t,n,e){n=rP(n,t);for(var r=-1,i=n.length,o=!1;++r<i;){var a=iI(n[r]);if(!(o=null!=t&&e(t,a)))break;t=t[a]}return o||++r!=i?o:!!(i=null==t?0:t.length)&&oq(i)&&ix(a,i)&&(oz(t)||oj(t))}function ib(t){return"function"!=typeof t.constructor||ik(t)?{}:ev(tF(t))}function im(t){return oz(t)||oj(t)||!!(t6&&t&&t[t6])}function ix(t,n){var e=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==e||"symbol"!=e&&td.test(t))&&t>-1&&t%1==0&&t<n}function iw(t,n,e){if(!oG(e))return!1;var r=typeof n;return("number"==r?!!(oL(e)&&ix(n,e.length)):"string"==r&&n in e)&&oP(e[n],t)}function iM(t,n){if(oz(t))return!1;var e=typeof t;return!!("number"==e||"symbol"==e||"boolean"==e||null==t||oQ(t))||Z.test(t)||!W.test(t)||null!=n&&t in tM(n)}function iT(t){var n=ic(t),e=ep[n];if("function"!=typeof e||!(n in e_.prototype))return!1;if(t===e)return!0;var r=iu(e);return!!r&&t===r[0]}(n7&&iy(new n7(new ArrayBuffer(1)))!=E||n9&&iy(new n9)!=_||et&&iy(et.resolve())!=x||en&&iy(new en)!=M||ee&&iy(new ee)!=k)&&(iy=function(t){var n=eK(t),e=n==m?t.constructor:i,r=e?i$(e):"";if(r)switch(r){case ea:return E;case eu:return _;case ec:return x;case el:return M;case ef:return k}return n});var iA=tC?oU:aK;function ik(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||tN)}function iS(t,n){return function(e){return null!=e&&e[t]===n&&(i!==n||t in tM(e))}}function iE(t,n,e){return n=n2(i===n?t.length-1:n,0),function(){for(var r=arguments,i=-1,o=n2(r.length-n,0),a=t_(o);++i<o;)a[i]=r[n+i];i=-1;for(var u=t_(n+1);++i<n;)u[i]=r[i];return u[n]=e(a),nu(t,this,u)}}function iN(t,n){return n.length<2?t:eZ(t,rv(n,0,-1))}function iC(t,n){if(("constructor"!==n||"function"!=typeof t[n])&&"__proto__"!=n)return t[n]}var iP=iz(rd),iR=nb||function(t,n){return t5.setTimeout(t,n)},iO=iz(rp);function ij(t,n,e){var r,i,o=n+"";return iO(t,function(t,n){var e=n.length;if(!e)return t;var r=e-1;return n[r]=(e>1?"& ":"")+n[r],n=n.join(e>2?", ":" "),t.replace(tn,"{\n/* [wrapped with "+n+"] */\n")}(o,(r=(i=o.match(te))?i[1].split(tr):[],nl(f,function(t){var n="_."+t[0];e&t[1]&&!nh(r,n)&&r.push(n)}),r.sort())))}function iz(t){var n=0,e=0;return function(){var r=n3(),o=16-(r-e);if(e=r,o>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(i,arguments)}}function iD(t,n){var e=-1,r=t.length,o=r-1;for(n=i===n?r:n;++e<n;){var a=rl(e,o),u=t[a];t[a]=t[e],t[e]=u}return t.length=n,t}var iL=(tt=(r=oA(function(t){var n=[];return 46===t.charCodeAt(0)&&n.push(""),t.replace(X,function(t,e,r,i){n.push(r?i.replace(ta,"$1"):e||t)}),n},function(t){return 500===tt.size&&tt.clear(),t})).cache,r);function iI(t){if("string"==typeof t||oQ(t))return t;var n=t+"";return"0"==n&&1/t==-c?"-0":n}function i$(t){if(null!=t){try{return tP.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function iB(t){if(t instanceof e_)return t.clone();var n=new ey(t.__wrapped__,t.__chain__);return n.__actions__=rB(t.__actions__),n.__index__=t.__index__,n.__values__=t.__values__,n}var iV=rs(function(t,n){return oI(t)?eL(t,eF(n,1,oI,!0)):[]}),iU=rs(function(t,n){var e=iK(n);return oI(e)&&(e=i),oI(t)?eL(t,eF(n,1,oI,!0),is(e,2)):[]}),iF=rs(function(t,n){var e=iK(n);return oI(e)&&(e=i),oI(t)?eL(t,eF(n,1,oI,!0),i,e):[]});function iq(t,n,e){var r=null==t?0:t.length;if(!r)return -1;var i=null==e?0:o5(e);return i<0&&(i=n2(r+i,0)),nx(t,is(n,3),i)}function iG(t,n,e){var r=null==t?0:t.length;if(!r)return -1;var o=r-1;return i!==e&&(o=o5(e),o=e<0?n2(r+o,0):n6(o,r-1)),nx(t,is(n,3),o,!0)}function iY(t){return(null==t?0:t.length)?eF(t,1):[]}function iH(t){return t&&t.length?t[0]:i}var iW=rs(function(t){var n=np(t,rN);return n.length&&n[0]===t[0]?e1(n):[]}),iZ=rs(function(t){var n=iK(t),e=np(t,rN);return n===iK(e)?n=i:e.pop(),e.length&&e[0]===t[0]?e1(e,is(n,2)):[]}),iX=rs(function(t){var n=iK(t),e=np(t,rN);return(n="function"==typeof n?n:i)&&e.pop(),e.length&&e[0]===t[0]?e1(e,i,n):[]});function iK(t){var n=null==t?0:t.length;return n?t[n-1]:i}var iJ=rs(iQ);function iQ(t,n){return t&&t.length&&n&&n.length?ru(t,n):t}var i0=ii(function(t,n){var e=null==t?0:t.length,r=eR(t,n);return rc(t,np(n,function(t){return ix(t,e)?+t:t}).sort(rL)),r});function i1(t){return null==t?t:n8.call(t)}var i2=rs(function(t){return rw(eF(t,1,oI,!0))}),i6=rs(function(t){var n=iK(t);return oI(n)&&(n=i),rw(eF(t,1,oI,!0),is(n,2))}),i3=rs(function(t){var n=iK(t);return n="function"==typeof n?n:i,rw(eF(t,1,oI,!0),i,n)});function i5(t){if(!(t&&t.length))return[];var n=0;return t=ns(t,function(t){if(oI(t))return n=n2(t.length,n),!0}),nC(n,function(n){return np(t,nk(n))})}function i4(t,n){if(!(t&&t.length))return[];var e=i5(t);return null==n?e:np(e,function(t){return nu(n,i,t)})}var i8=rs(function(t,n){return oI(t)?eL(t,n):[]}),i7=rs(function(t){return rS(ns(t,oI))}),i9=rs(function(t){var n=iK(t);return oI(n)&&(n=i),rS(ns(t,oI),is(n,2))}),ot=rs(function(t){var n=iK(t);return n="function"==typeof n?n:i,rS(ns(t,oI),i,n)}),on=rs(i5),oe=rs(function(t){var n=t.length,e=n>1?t[n-1]:i;return e="function"==typeof e?(t.pop(),e):i,i4(t,e)});function or(t){var n=ep(t);return n.__chain__=!0,n}function oi(t,n){return n(t)}var oo=ii(function(t){var n=t.length,e=n?t[0]:0,r=this.__wrapped__,o=function(n){return eR(n,t)};return!(n>1)&&!this.__actions__.length&&r instanceof e_&&ix(e)?((r=r.slice(e,+e+(n?1:0))).__actions__.push({func:oi,args:[o],thisArg:i}),new ey(r,this.__chain__).thru(function(t){return n&&!t.length&&t.push(i),t})):this.thru(o)}),oa=rU(function(t,n,e){tR.call(t,e)?++t[e]:eP(t,e,1)}),ou=rZ(iq),oc=rZ(iG);function ol(t,n){return(oz(t)?nl:eI)(t,is(n,3))}function of(t,n){return(oz(t)?function(t,n){for(var e=null==t?0:t.length;e--&&!1!==n(t[e],e,t););return t}:e$)(t,is(n,3))}var os=rU(function(t,n,e){tR.call(t,e)?t[e].push(n):eP(t,e,[n])}),oh=rs(function(t,n,e){var r=-1,i="function"==typeof n,o=oL(t)?t_(t.length):[];return eI(t,function(t){o[++r]=i?nu(n,t,e):e2(t,n,e)}),o}),od=rU(function(t,n,e){eP(t,e,n)});function op(t,n){return(oz(t)?np:rt)(t,is(n,3))}var ov=rU(function(t,n,e){t[e?0:1].push(n)},function(){return[[],[]]}),og=rs(function(t,n){if(null==t)return[];var e=n.length;return e>1&&iw(t,n[0],n[1])?n=[]:e>2&&iw(n[0],n[1],n[2])&&(n=[n[0]]),ro(t,eF(n,1),[])}),oy=nt||function(){return t5.Date.now()};function o_(t,n,e){return n=e?i:n,n=t&&null==n?t.length:n,r7(t,128,i,i,i,i,n)}function ob(t,n){var e;if("function"!=typeof n)throw new tk(o);return t=o5(t),function(){return--t>0&&(e=n.apply(this,arguments)),t<=1&&(n=i),e}}var om=rs(function(t,n,e){var r=1;if(e.length){var i=nF(e,il(om));r|=32}return r7(t,r,n,e,i)}),ox=rs(function(t,n,e){var r=3;if(e.length){var i=nF(e,il(ox));r|=32}return r7(n,r,t,e,i)});function ow(t,n,e){var r,a,u,c,l,f,s=0,h=!1,d=!1,p=!0;if("function"!=typeof t)throw new tk(o);function v(n){var e=r,o=a;return r=a=i,s=n,c=t.apply(o,e)}function g(t){var e=t-f,r=t-s;return i===f||e>=n||e<0||d&&r>=u}function y(){var t,e,r,i=oy();if(g(i))return _(i);l=iR(y,(t=i-f,e=i-s,r=n-t,d?n6(r,u-e):r))}function _(t){return(l=i,p&&r)?v(t):(r=a=i,c)}function b(){var t,e=oy(),o=g(e);if(r=arguments,a=this,f=e,o){if(i===l)return s=t=f,l=iR(y,n),h?v(t):c;if(d)return rO(l),l=iR(y,n),v(f)}return i===l&&(l=iR(y,n)),c}return n=o8(n)||0,oG(e)&&(h=!!e.leading,u=(d="maxWait"in e)?n2(o8(e.maxWait)||0,n):u,p="trailing"in e?!!e.trailing:p),b.cancel=function(){i!==l&&rO(l),s=0,r=f=a=l=i},b.flush=function(){return i===l?c:_(oy())},b}var oM=rs(function(t,n){return eD(t,1,n)}),oT=rs(function(t,n,e){return eD(t,o8(n)||0,e)});function oA(t,n){if("function"!=typeof t||null!=n&&"function"!=typeof n)throw new tk(o);var e=function(){var r=arguments,i=n?n.apply(this,r):r[0],o=e.cache;if(o.has(i))return o.get(i);var a=t.apply(this,r);return e.cache=o.set(i,a)||o,a};return e.cache=new(oA.Cache||ex),e}function ok(t){if("function"!=typeof t)throw new tk(o);return function(){var n=arguments;switch(n.length){case 0:return!t.call(this);case 1:return!t.call(this,n[0]);case 2:return!t.call(this,n[0],n[1]);case 3:return!t.call(this,n[0],n[1],n[2])}return!t.apply(this,n)}}oA.Cache=ex;var oS=rs(function(t,n){var e=(n=1==n.length&&oz(n[0])?np(n[0],nR(is())):np(eF(n,1),nR(is()))).length;return rs(function(r){for(var i=-1,o=n6(r.length,e);++i<o;)r[i]=n[i].call(this,r[i]);return nu(t,this,r)})}),oE=rs(function(t,n){var e=nF(n,il(oE));return r7(t,32,i,n,e)}),oN=rs(function(t,n){var e=nF(n,il(oN));return r7(t,64,i,n,e)}),oC=ii(function(t,n){return r7(t,256,i,i,i,n)});function oP(t,n){return t===n||t!=t&&n!=n}var oR=r6(eJ),oO=r6(function(t,n){return t>=n}),oj=e6(function(){return arguments}())?e6:function(t){return oY(t)&&tR.call(t,"callee")&&!tW.call(t,"callee")},oz=t_.isArray,oD=nn?nR(nn):function(t){return oY(t)&&eK(t)==S};function oL(t){return null!=t&&oq(t.length)&&!oU(t)}function oI(t){return oY(t)&&oL(t)}var o$=nJ||aK,oB=ne?nR(ne):function(t){return oY(t)&&eK(t)==p};function oV(t){if(!oY(t))return!1;var n=eK(t);return n==v||"[object DOMException]"==n||"string"==typeof t.message&&"string"==typeof t.name&&!oZ(t)}function oU(t){if(!oG(t))return!1;var n=eK(t);return n==g||n==y||"[object AsyncFunction]"==n||"[object Proxy]"==n}function oF(t){return"number"==typeof t&&t==o5(t)}function oq(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}function oG(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}function oY(t){return null!=t&&"object"==typeof t}var oH=nr?nR(nr):function(t){return oY(t)&&iy(t)==_};function oW(t){return"number"==typeof t||oY(t)&&eK(t)==b}function oZ(t){if(!oY(t)||eK(t)!=m)return!1;var n=tF(t);if(null===n)return!0;var e=tR.call(n,"constructor")&&n.constructor;return"function"==typeof e&&e instanceof e&&tP.call(e)==tD}var oX=ni?nR(ni):function(t){return oY(t)&&eK(t)==w},oK=no?nR(no):function(t){return oY(t)&&iy(t)==M};function oJ(t){return"string"==typeof t||!oz(t)&&oY(t)&&eK(t)==T}function oQ(t){return"symbol"==typeof t||oY(t)&&eK(t)==A}var o0=na?nR(na):function(t){return oY(t)&&oq(t.length)&&!!tJ[eK(t)]},o1=r6(e9),o2=r6(function(t,n){return t<=n});function o6(t){if(!t)return[];if(oL(t))return oJ(t)?nY(t):rB(t);if(t3&&t[t3])return function(t){for(var n,e=[];!(n=t.next()).done;)e.push(n.value);return e}(t[t3]());var n=iy(t);return(n==_?nV:n==M?nq:ax)(t)}function o3(t){return t?(t=o8(t))===c||t===-c?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}function o5(t){var n=o3(t),e=n%1;return n==n?e?n-e:n:0}function o4(t){return t?eO(o5(t),0,4294967295):0}function o8(t){if("number"==typeof t)return t;if(oQ(t))return l;if(oG(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=oG(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=nP(t);var e=tf.test(t);return e||th.test(t)?t2(t.slice(2),e?2:8):tl.test(t)?l:+t}function o7(t){return rV(t,ad(t))}function o9(t){return null==t?"":rx(t)}var at=rF(function(t,n){if(ik(n)||oL(n)){rV(n,ah(n),t);return}for(var e in n)tR.call(n,e)&&eS(t,e,n[e])}),an=rF(function(t,n){rV(n,ad(n),t)}),ae=rF(function(t,n,e,r){rV(n,ad(n),t,r)}),ar=rF(function(t,n,e,r){rV(n,ah(n),t,r)}),ai=ii(eR),ao=rs(function(t,n){t=tM(t);var e=-1,r=n.length,o=r>2?n[2]:i;for(o&&iw(n[0],n[1],o)&&(r=1);++e<r;)for(var a=n[e],u=ad(a),c=-1,l=u.length;++c<l;){var f=u[c],s=t[f];(i===s||oP(s,tN[f])&&!tR.call(t,f))&&(t[f]=a[f])}return t}),aa=rs(function(t){return t.push(i,it),nu(av,i,t)});function au(t,n,e){var r=null==t?i:eZ(t,n);return i===r?e:r}function ac(t,n){return null!=t&&i_(t,n,e0)}var al=rJ(function(t,n,e){null!=n&&"function"!=typeof n.toString&&(n=tz.call(n)),t[n]=e},az(aI)),af=rJ(function(t,n,e){null!=n&&"function"!=typeof n.toString&&(n=tz.call(n)),tR.call(t,n)?t[n].push(e):t[n]=[e]},is),as=rs(e2);function ah(t){return oL(t)?eT(t):e7(t)}function ad(t){return oL(t)?eT(t,!0):function(t){if(!oG(t))return function(t){var n=[];if(null!=t)for(var e in tM(t))n.push(e);return n}(t);var n=ik(t),e=[];for(var r in t)"constructor"==r&&(n||!tR.call(t,r))||e.push(r);return e}(t)}var ap=rF(function(t,n,e){rr(t,n,e)}),av=rF(function(t,n,e,r){rr(t,n,e,r)}),ag=ii(function(t,n){var e={};if(null==t)return e;var r=!1;n=np(n,function(n){return n=rP(n,t),r||(r=n.length>1),n}),rV(t,ia(t),e),r&&(e=ej(e,7,ie));for(var i=n.length;i--;)rM(e,n[i]);return e}),ay=ii(function(t,n){return null==t?{}:ra(t,n,function(n,e){return ac(t,e)})});function a_(t,n){if(null==t)return{};var e=np(ia(t),function(t){return[t]});return n=is(n),ra(t,e,function(t,e){return n(t,e[0])})}var ab=r8(ah),am=r8(ad);function ax(t){return null==t?[]:nO(t,ah(t))}var aw=rH(function(t,n,e){return n=n.toLowerCase(),t+(e?aM(n):n)});function aM(t){return aP(o9(t).toLowerCase())}function aT(t){return(t=o9(t))&&t.replace(tp,nL).replace(tG,"")}var aA=rH(function(t,n,e){return t+(e?"-":"")+n.toLowerCase()}),ak=rH(function(t,n,e){return t+(e?" ":"")+n.toLowerCase()}),aS=rY("toLowerCase"),aE=rH(function(t,n,e){return t+(e?"_":"")+n.toLowerCase()}),aN=rH(function(t,n,e){return t+(e?" ":"")+aP(n)}),aC=rH(function(t,n,e){return t+(e?" ":"")+n.toUpperCase()}),aP=rY("toUpperCase");function aR(t,n,e){if(t=o9(t),n=e?i:n,i===n){var r;return(r=t,tZ.test(r))?t.match(tH)||[]:t.match(ti)||[]}return t.match(n)||[]}var aO=rs(function(t,n){try{return nu(t,i,n)}catch(t){return oV(t)?t:new tm(t)}}),aj=ii(function(t,n){return nl(n,function(n){eP(t,n=iI(n),om(t[n],t))}),t});function az(t){return function(){return t}}var aD=rX(),aL=rX(!0);function aI(t){return t}function a$(t){return e8("function"==typeof t?t:ej(t,1))}var aB=rs(function(t,n){return function(e){return e2(e,t,n)}}),aV=rs(function(t,n){return function(e){return e2(t,e,n)}});function aU(t,n,e){var r=ah(n),i=eW(n,r);null!=e||oG(n)&&(i.length||!r.length)||(e=n,n=t,t=this,i=eW(n,ah(n)));var o=!(oG(e)&&"chain"in e)||!!e.chain,a=oU(t);return nl(i,function(e){var r=n[e];t[e]=r,a&&(t.prototype[e]=function(){var n=this.__chain__;if(o||n){var e=t(this.__wrapped__);return(e.__actions__=rB(this.__actions__)).push({func:r,args:arguments,thisArg:t}),e.__chain__=n,e}return r.apply(t,nv([this.value()],arguments))})}),t}function aF(){}var aq=r0(np),aG=r0(nf),aY=r0(n_);function aH(t){return iM(t)?nk(iI(t)):function(n){return eZ(n,t)}}var aW=r2(),aZ=r2(!0);function aX(){return[]}function aK(){return!1}var aJ=rQ(function(t,n){return t+n},0),aQ=r5("ceil"),a0=rQ(function(t,n){return t/n},1),a1=r5("floor"),a2=rQ(function(t,n){return t*n},1),a6=r5("round"),a3=rQ(function(t,n){return t-n},0);return ep.after=function(t,n){if("function"!=typeof n)throw new tk(o);return t=o5(t),function(){if(--t<1)return n.apply(this,arguments)}},ep.ary=o_,ep.assign=at,ep.assignIn=an,ep.assignInWith=ae,ep.assignWith=ar,ep.at=ai,ep.before=ob,ep.bind=om,ep.bindAll=aj,ep.bindKey=ox,ep.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return oz(t)?t:[t]},ep.chain=or,ep.chunk=function(t,n,e){n=(e?iw(t,n,e):i===n)?1:n2(o5(n),0);var r=null==t?0:t.length;if(!r||n<1)return[];for(var o=0,a=0,u=t_(nS(r/n));o<r;)u[a++]=rv(t,o,o+=n);return u},ep.compact=function(t){for(var n=-1,e=null==t?0:t.length,r=0,i=[];++n<e;){var o=t[n];o&&(i[r++]=o)}return i},ep.concat=function(){var t=arguments.length;if(!t)return[];for(var n=t_(t-1),e=arguments[0],r=t;r--;)n[r-1]=arguments[r];return nv(oz(e)?rB(e):[e],eF(n,1))},ep.cond=function(t){var n=null==t?0:t.length,e=is();return t=n?np(t,function(t){if("function"!=typeof t[1])throw new tk(o);return[e(t[0]),t[1]]}):[],rs(function(e){for(var r=-1;++r<n;){var i=t[r];if(nu(i[0],this,e))return nu(i[1],this,e)}})},ep.conforms=function(t){var n,e;return e=ah(n=ej(t,1)),function(t){return ez(t,n,e)}},ep.constant=az,ep.countBy=oa,ep.create=function(t,n){var e=ev(t);return null==n?e:eC(e,n)},ep.curry=function t(n,e,r){e=r?i:e;var o=r7(n,8,i,i,i,i,i,e);return o.placeholder=t.placeholder,o},ep.curryRight=function t(n,e,r){e=r?i:e;var o=r7(n,16,i,i,i,i,i,e);return o.placeholder=t.placeholder,o},ep.debounce=ow,ep.defaults=ao,ep.defaultsDeep=aa,ep.defer=oM,ep.delay=oT,ep.difference=iV,ep.differenceBy=iU,ep.differenceWith=iF,ep.drop=function(t,n,e){var r=null==t?0:t.length;return r?rv(t,(n=e||i===n?1:o5(n))<0?0:n,r):[]},ep.dropRight=function(t,n,e){var r=null==t?0:t.length;return r?rv(t,0,(n=r-(n=e||i===n?1:o5(n)))<0?0:n):[]},ep.dropRightWhile=function(t,n){return t&&t.length?rA(t,is(n,3),!0,!0):[]},ep.dropWhile=function(t,n){return t&&t.length?rA(t,is(n,3),!0):[]},ep.fill=function(t,n,e,r){var o=null==t?0:t.length;return o?(e&&"number"!=typeof e&&iw(t,n,e)&&(e=0,r=o),function(t,n,e,r){var o=t.length;for((e=o5(e))<0&&(e=-e>o?0:o+e),(r=i===r||r>o?o:o5(r))<0&&(r+=o),r=e>r?0:o4(r);e<r;)t[e++]=n;return t}(t,n,e,r)):[]},ep.filter=function(t,n){return(oz(t)?ns:eU)(t,is(n,3))},ep.flatMap=function(t,n){return eF(op(t,n),1)},ep.flatMapDeep=function(t,n){return eF(op(t,n),c)},ep.flatMapDepth=function(t,n,e){return e=i===e?1:o5(e),eF(op(t,n),e)},ep.flatten=iY,ep.flattenDeep=function(t){return(null==t?0:t.length)?eF(t,c):[]},ep.flattenDepth=function(t,n){return(null==t?0:t.length)?eF(t,n=i===n?1:o5(n)):[]},ep.flip=function(t){return r7(t,512)},ep.flow=aD,ep.flowRight=aL,ep.fromPairs=function(t){for(var n=-1,e=null==t?0:t.length,r={};++n<e;){var i=t[n];r[i[0]]=i[1]}return r},ep.functions=function(t){return null==t?[]:eW(t,ah(t))},ep.functionsIn=function(t){return null==t?[]:eW(t,ad(t))},ep.groupBy=os,ep.initial=function(t){return(null==t?0:t.length)?rv(t,0,-1):[]},ep.intersection=iW,ep.intersectionBy=iZ,ep.intersectionWith=iX,ep.invert=al,ep.invertBy=af,ep.invokeMap=oh,ep.iteratee=a$,ep.keyBy=od,ep.keys=ah,ep.keysIn=ad,ep.map=op,ep.mapKeys=function(t,n){var e={};return n=is(n,3),eY(t,function(t,r,i){eP(e,n(t,r,i),t)}),e},ep.mapValues=function(t,n){var e={};return n=is(n,3),eY(t,function(t,r,i){eP(e,r,n(t,r,i))}),e},ep.matches=function(t){return rn(ej(t,1))},ep.matchesProperty=function(t,n){return re(t,ej(n,1))},ep.memoize=oA,ep.merge=ap,ep.mergeWith=av,ep.method=aB,ep.methodOf=aV,ep.mixin=aU,ep.negate=ok,ep.nthArg=function(t){return t=o5(t),rs(function(n){return ri(n,t)})},ep.omit=ag,ep.omitBy=function(t,n){return a_(t,ok(is(n)))},ep.once=function(t){return ob(2,t)},ep.orderBy=function(t,n,e,r){return null==t?[]:(oz(n)||(n=null==n?[]:[n]),oz(e=r?i:e)||(e=null==e?[]:[e]),ro(t,n,e))},ep.over=aq,ep.overArgs=oS,ep.overEvery=aG,ep.overSome=aY,ep.partial=oE,ep.partialRight=oN,ep.partition=ov,ep.pick=ay,ep.pickBy=a_,ep.property=aH,ep.propertyOf=function(t){return function(n){return null==t?i:eZ(t,n)}},ep.pull=iJ,ep.pullAll=iQ,ep.pullAllBy=function(t,n,e){return t&&t.length&&n&&n.length?ru(t,n,is(e,2)):t},ep.pullAllWith=function(t,n,e){return t&&t.length&&n&&n.length?ru(t,n,i,e):t},ep.pullAt=i0,ep.range=aW,ep.rangeRight=aZ,ep.rearg=oC,ep.reject=function(t,n){return(oz(t)?ns:eU)(t,ok(is(n,3)))},ep.remove=function(t,n){var e=[];if(!(t&&t.length))return e;var r=-1,i=[],o=t.length;for(n=is(n,3);++r<o;){var a=t[r];n(a,r,t)&&(e.push(a),i.push(r))}return rc(t,i),e},ep.rest=function(t,n){if("function"!=typeof t)throw new tk(o);return rs(t,n=i===n?n:o5(n))},ep.reverse=i1,ep.sampleSize=function(t,n,e){return n=(e?iw(t,n,e):i===n)?1:o5(n),(oz(t)?function(t,n){return iD(rB(t),eO(n,0,t.length))}:function(t,n){var e=ax(t);return iD(e,eO(n,0,e.length))})(t,n)},ep.set=function(t,n,e){return null==t?t:rh(t,n,e)},ep.setWith=function(t,n,e,r){return r="function"==typeof r?r:i,null==t?t:rh(t,n,e,r)},ep.shuffle=function(t){return(oz(t)?function(t){return iD(rB(t))}:function(t){return iD(ax(t))})(t)},ep.slice=function(t,n,e){var r=null==t?0:t.length;return r?(e&&"number"!=typeof e&&iw(t,n,e)?(n=0,e=r):(n=null==n?0:o5(n),e=i===e?r:o5(e)),rv(t,n,e)):[]},ep.sortBy=og,ep.sortedUniq=function(t){return t&&t.length?rb(t):[]},ep.sortedUniqBy=function(t,n){return t&&t.length?rb(t,is(n,2)):[]},ep.split=function(t,n,e){return(e&&"number"!=typeof e&&iw(t,n,e)&&(n=e=i),e=i===e?4294967295:e>>>0)?(t=o9(t))&&("string"==typeof n||null!=n&&!oX(n))&&!(n=rx(n))&&nB(t)?rR(nY(t),0,e):t.split(n,e):[]},ep.spread=function(t,n){if("function"!=typeof t)throw new tk(o);return n=null==n?0:n2(o5(n),0),rs(function(e){var r=e[n],i=rR(e,0,n);return r&&nv(i,r),nu(t,this,i)})},ep.tail=function(t){var n=null==t?0:t.length;return n?rv(t,1,n):[]},ep.take=function(t,n,e){return t&&t.length?rv(t,0,(n=e||i===n?1:o5(n))<0?0:n):[]},ep.takeRight=function(t,n,e){var r=null==t?0:t.length;return r?rv(t,(n=r-(n=e||i===n?1:o5(n)))<0?0:n,r):[]},ep.takeRightWhile=function(t,n){return t&&t.length?rA(t,is(n,3),!1,!0):[]},ep.takeWhile=function(t,n){return t&&t.length?rA(t,is(n,3)):[]},ep.tap=function(t,n){return n(t),t},ep.throttle=function(t,n,e){var r=!0,i=!0;if("function"!=typeof t)throw new tk(o);return oG(e)&&(r="leading"in e?!!e.leading:r,i="trailing"in e?!!e.trailing:i),ow(t,n,{leading:r,maxWait:n,trailing:i})},ep.thru=oi,ep.toArray=o6,ep.toPairs=ab,ep.toPairsIn=am,ep.toPath=function(t){return oz(t)?np(t,iI):oQ(t)?[t]:rB(iL(o9(t)))},ep.toPlainObject=o7,ep.transform=function(t,n,e){var r=oz(t),i=r||o$(t)||o0(t);if(n=is(n,4),null==e){var o=t&&t.constructor;e=i?r?new o:[]:oG(t)&&oU(o)?ev(tF(t)):{}}return(i?nl:eY)(t,function(t,r,i){return n(e,t,r,i)}),e},ep.unary=function(t){return o_(t,1)},ep.union=i2,ep.unionBy=i6,ep.unionWith=i3,ep.uniq=function(t){return t&&t.length?rw(t):[]},ep.uniqBy=function(t,n){return t&&t.length?rw(t,is(n,2)):[]},ep.uniqWith=function(t,n){return n="function"==typeof n?n:i,t&&t.length?rw(t,i,n):[]},ep.unset=function(t,n){return null==t||rM(t,n)},ep.unzip=i5,ep.unzipWith=i4,ep.update=function(t,n,e){return null==t?t:rT(t,n,rC(e))},ep.updateWith=function(t,n,e,r){return r="function"==typeof r?r:i,null==t?t:rT(t,n,rC(e),r)},ep.values=ax,ep.valuesIn=function(t){return null==t?[]:nO(t,ad(t))},ep.without=i8,ep.words=aR,ep.wrap=function(t,n){return oE(rC(n),t)},ep.xor=i7,ep.xorBy=i9,ep.xorWith=ot,ep.zip=on,ep.zipObject=function(t,n){return rE(t||[],n||[],eS)},ep.zipObjectDeep=function(t,n){return rE(t||[],n||[],rh)},ep.zipWith=oe,ep.entries=ab,ep.entriesIn=am,ep.extend=an,ep.extendWith=ae,aU(ep,ep),ep.add=aJ,ep.attempt=aO,ep.camelCase=aw,ep.capitalize=aM,ep.ceil=aQ,ep.clamp=function(t,n,e){return i===e&&(e=n,n=i),i!==e&&(e=(e=o8(e))==e?e:0),i!==n&&(n=(n=o8(n))==n?n:0),eO(o8(t),n,e)},ep.clone=function(t){return ej(t,4)},ep.cloneDeep=function(t){return ej(t,5)},ep.cloneDeepWith=function(t,n){return ej(t,5,n="function"==typeof n?n:i)},ep.cloneWith=function(t,n){return ej(t,4,n="function"==typeof n?n:i)},ep.conformsTo=function(t,n){return null==n||ez(t,n,ah(n))},ep.deburr=aT,ep.defaultTo=function(t,n){return null==t||t!=t?n:t},ep.divide=a0,ep.endsWith=function(t,n,e){t=o9(t),n=rx(n);var r=t.length,o=e=i===e?r:eO(o5(e),0,r);return(e-=n.length)>=0&&t.slice(e,o)==n},ep.eq=oP,ep.escape=function(t){return(t=o9(t))&&q.test(t)?t.replace(U,nI):t},ep.escapeRegExp=function(t){return(t=o9(t))&&J.test(t)?t.replace(K,"\\$&"):t},ep.every=function(t,n,e){var r=oz(t)?nf:eB;return e&&iw(t,n,e)&&(n=i),r(t,is(n,3))},ep.find=ou,ep.findIndex=iq,ep.findKey=function(t,n){return nm(t,is(n,3),eY)},ep.findLast=oc,ep.findLastIndex=iG,ep.findLastKey=function(t,n){return nm(t,is(n,3),eH)},ep.floor=a1,ep.forEach=ol,ep.forEachRight=of,ep.forIn=function(t,n){return null==t?t:eq(t,is(n,3),ad)},ep.forInRight=function(t,n){return null==t?t:eG(t,is(n,3),ad)},ep.forOwn=function(t,n){return t&&eY(t,is(n,3))},ep.forOwnRight=function(t,n){return t&&eH(t,is(n,3))},ep.get=au,ep.gt=oR,ep.gte=oO,ep.has=function(t,n){return null!=t&&i_(t,n,eQ)},ep.hasIn=ac,ep.head=iH,ep.identity=aI,ep.includes=function(t,n,e,r){t=oL(t)?t:ax(t),e=e&&!r?o5(e):0;var i=t.length;return e<0&&(e=n2(i+e,0)),oJ(t)?e<=i&&t.indexOf(n,e)>-1:!!i&&nw(t,n,e)>-1},ep.indexOf=function(t,n,e){var r=null==t?0:t.length;if(!r)return -1;var i=null==e?0:o5(e);return i<0&&(i=n2(r+i,0)),nw(t,n,i)},ep.inRange=function(t,n,e){var r,o,a;return n=o3(n),i===e?(e=n,n=0):e=o3(e),(r=t=o8(t))>=n6(o=n,a=e)&&r<n2(o,a)},ep.invoke=as,ep.isArguments=oj,ep.isArray=oz,ep.isArrayBuffer=oD,ep.isArrayLike=oL,ep.isArrayLikeObject=oI,ep.isBoolean=function(t){return!0===t||!1===t||oY(t)&&eK(t)==d},ep.isBuffer=o$,ep.isDate=oB,ep.isElement=function(t){return oY(t)&&1===t.nodeType&&!oZ(t)},ep.isEmpty=function(t){if(null==t)return!0;if(oL(t)&&(oz(t)||"string"==typeof t||"function"==typeof t.splice||o$(t)||o0(t)||oj(t)))return!t.length;var n=iy(t);if(n==_||n==M)return!t.size;if(ik(t))return!e7(t).length;for(var e in t)if(tR.call(t,e))return!1;return!0},ep.isEqual=function(t,n){return e3(t,n)},ep.isEqualWith=function(t,n,e){var r=(e="function"==typeof e?e:i)?e(t,n):i;return i===r?e3(t,n,i,e):!!r},ep.isError=oV,ep.isFinite=function(t){return"number"==typeof t&&nQ(t)},ep.isFunction=oU,ep.isInteger=oF,ep.isLength=oq,ep.isMap=oH,ep.isMatch=function(t,n){return t===n||e5(t,n,id(n))},ep.isMatchWith=function(t,n,e){return e="function"==typeof e?e:i,e5(t,n,id(n),e)},ep.isNaN=function(t){return oW(t)&&t!=+t},ep.isNative=function(t){if(iA(t))throw new tm("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return e4(t)},ep.isNil=function(t){return null==t},ep.isNull=function(t){return null===t},ep.isNumber=oW,ep.isObject=oG,ep.isObjectLike=oY,ep.isPlainObject=oZ,ep.isRegExp=oX,ep.isSafeInteger=function(t){return oF(t)&&t>=-9007199254740991&&t<=9007199254740991},ep.isSet=oK,ep.isString=oJ,ep.isSymbol=oQ,ep.isTypedArray=o0,ep.isUndefined=function(t){return i===t},ep.isWeakMap=function(t){return oY(t)&&iy(t)==k},ep.isWeakSet=function(t){return oY(t)&&"[object WeakSet]"==eK(t)},ep.join=function(t,n){return null==t?"":n0.call(t,n)},ep.kebabCase=aA,ep.last=iK,ep.lastIndexOf=function(t,n,e){var r=null==t?0:t.length;if(!r)return -1;var o=r;return i!==e&&(o=(o=o5(e))<0?n2(r+o,0):n6(o,r-1)),n==n?function(t,n,e){for(var r=e+1;r--&&t[r]!==n;);return r}(t,n,o):nx(t,nT,o,!0)},ep.lowerCase=ak,ep.lowerFirst=aS,ep.lt=o1,ep.lte=o2,ep.max=function(t){return t&&t.length?eV(t,aI,eJ):i},ep.maxBy=function(t,n){return t&&t.length?eV(t,is(n,2),eJ):i},ep.mean=function(t){return nA(t,aI)},ep.meanBy=function(t,n){return nA(t,is(n,2))},ep.min=function(t){return t&&t.length?eV(t,aI,e9):i},ep.minBy=function(t,n){return t&&t.length?eV(t,is(n,2),e9):i},ep.stubArray=aX,ep.stubFalse=aK,ep.stubObject=function(){return{}},ep.stubString=function(){return""},ep.stubTrue=function(){return!0},ep.multiply=a2,ep.nth=function(t,n){return t&&t.length?ri(t,o5(n)):i},ep.noConflict=function(){return t5._===this&&(t5._=tL),this},ep.noop=aF,ep.now=oy,ep.pad=function(t,n,e){t=o9(t);var r=(n=o5(n))?nG(t):0;if(!n||r>=n)return t;var i=(n-r)/2;return r1(nX(i),e)+t+r1(nS(i),e)},ep.padEnd=function(t,n,e){t=o9(t);var r=(n=o5(n))?nG(t):0;return n&&r<n?t+r1(n-r,e):t},ep.padStart=function(t,n,e){t=o9(t);var r=(n=o5(n))?nG(t):0;return n&&r<n?r1(n-r,e)+t:t},ep.parseInt=function(t,n,e){return e||null==n?n=0:n&&(n=+n),n5(o9(t).replace(Q,""),n||0)},ep.random=function(t,n,e){if(e&&"boolean"!=typeof e&&iw(t,n,e)&&(n=e=i),i===e&&("boolean"==typeof n?(e=n,n=i):"boolean"==typeof t&&(e=t,t=i)),i===t&&i===n?(t=0,n=1):(t=o3(t),i===n?(n=t,t=0):n=o3(n)),t>n){var r=t;t=n,n=r}if(e||t%1||n%1){var o=n4();return n6(t+o*(n-t+t1("1e-"+((o+"").length-1))),n)}return rl(t,n)},ep.reduce=function(t,n,e){var r=oz(t)?ng:nE,i=arguments.length<3;return r(t,is(n,4),e,i,eI)},ep.reduceRight=function(t,n,e){var r=oz(t)?ny:nE,i=arguments.length<3;return r(t,is(n,4),e,i,e$)},ep.repeat=function(t,n,e){return n=(e?iw(t,n,e):i===n)?1:o5(n),rf(o9(t),n)},ep.replace=function(){var t=arguments,n=o9(t[0]);return t.length<3?n:n.replace(t[1],t[2])},ep.result=function(t,n,e){n=rP(n,t);var r=-1,o=n.length;for(o||(o=1,t=i);++r<o;){var a=null==t?i:t[iI(n[r])];i===a&&(r=o,a=e),t=oU(a)?a.call(t):a}return t},ep.round=a6,ep.runInContext=t,ep.sample=function(t){return(oz(t)?eA:function(t){return eA(ax(t))})(t)},ep.size=function(t){if(null==t)return 0;if(oL(t))return oJ(t)?nG(t):t.length;var n=iy(t);return n==_||n==M?t.size:e7(t).length},ep.snakeCase=aE,ep.some=function(t,n,e){var r=oz(t)?n_:rg;return e&&iw(t,n,e)&&(n=i),r(t,is(n,3))},ep.sortedIndex=function(t,n){return ry(t,n)},ep.sortedIndexBy=function(t,n,e){return r_(t,n,is(e,2))},ep.sortedIndexOf=function(t,n){var e=null==t?0:t.length;if(e){var r=ry(t,n);if(r<e&&oP(t[r],n))return r}return -1},ep.sortedLastIndex=function(t,n){return ry(t,n,!0)},ep.sortedLastIndexBy=function(t,n,e){return r_(t,n,is(e,2),!0)},ep.sortedLastIndexOf=function(t,n){if(null==t?0:t.length){var e=ry(t,n,!0)-1;if(oP(t[e],n))return e}return -1},ep.startCase=aN,ep.startsWith=function(t,n,e){return t=o9(t),e=null==e?0:eO(o5(e),0,t.length),n=rx(n),t.slice(e,e+n.length)==n},ep.subtract=a3,ep.sum=function(t){return t&&t.length?nN(t,aI):0},ep.sumBy=function(t,n){return t&&t.length?nN(t,is(n,2)):0},ep.template=function(t,n,e){var r=ep.templateSettings;e&&iw(t,n,e)&&(n=i),t=o9(t),n=ae({},n,r,r9);var o,a,u=ae({},n.imports,r.imports,r9),c=ah(u),l=nO(u,c),f=0,s=n.interpolate||tv,h="__p += '",d=tT((n.escape||tv).source+"|"+s.source+"|"+(s===H?tu:tv).source+"|"+(n.evaluate||tv).source+"|$","g"),p="//# sourceURL="+(tR.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++tK+"]")+"\n";t.replace(d,function(n,e,r,i,u,c){return r||(r=i),h+=t.slice(f,c).replace(tg,n$),e&&(o=!0,h+="' +\n__e("+e+") +\n'"),u&&(a=!0,h+="';\n"+u+";\n__p += '"),r&&(h+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),f=c+n.length,n}),h+="';\n";var v=tR.call(n,"variable")&&n.variable;if(v){if(to.test(v))throw new tm("Invalid `variable` option passed into `_.template`")}else h="with (obj) {\n"+h+"\n}\n";h=(a?h.replace(I,""):h).replace($,"$1").replace(B,"$1;"),h="function("+(v||"obj")+") {\n"+(v?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(o?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+h+"return __p\n}";var g=aO(function(){return tx(c,p+"return "+h).apply(i,l)});if(g.source=h,oV(g))throw g;return g},ep.times=function(t,n){if((t=o5(t))<1||t>9007199254740991)return[];var e=4294967295,r=n6(t,4294967295);n=is(n),t-=4294967295;for(var i=nC(r,n);++e<t;)n(e);return i},ep.toFinite=o3,ep.toInteger=o5,ep.toLength=o4,ep.toLower=function(t){return o9(t).toLowerCase()},ep.toNumber=o8,ep.toSafeInteger=function(t){return t?eO(o5(t),-9007199254740991,9007199254740991):0===t?t:0},ep.toString=o9,ep.toUpper=function(t){return o9(t).toUpperCase()},ep.trim=function(t,n,e){if((t=o9(t))&&(e||i===n))return nP(t);if(!t||!(n=rx(n)))return t;var r=nY(t),o=nY(n),a=nz(r,o),u=nD(r,o)+1;return rR(r,a,u).join("")},ep.trimEnd=function(t,n,e){if((t=o9(t))&&(e||i===n))return t.slice(0,nH(t)+1);if(!t||!(n=rx(n)))return t;var r=nY(t),o=nD(r,nY(n))+1;return rR(r,0,o).join("")},ep.trimStart=function(t,n,e){if((t=o9(t))&&(e||i===n))return t.replace(Q,"");if(!t||!(n=rx(n)))return t;var r=nY(t),o=nz(r,nY(n));return rR(r,o).join("")},ep.truncate=function(t,n){var e=30,r="...";if(oG(n)){var o="separator"in n?n.separator:o;e="length"in n?o5(n.length):e,r="omission"in n?rx(n.omission):r}var a=(t=o9(t)).length;if(nB(t)){var u=nY(t);a=u.length}if(e>=a)return t;var c=e-nG(r);if(c<1)return r;var l=u?rR(u,0,c).join(""):t.slice(0,c);if(i===o)return l+r;if(u&&(c+=l.length-c),oX(o)){if(t.slice(c).search(o)){var f,s=l;for(o.global||(o=tT(o.source,o9(tc.exec(o))+"g")),o.lastIndex=0;f=o.exec(s);)var h=f.index;l=l.slice(0,i===h?c:h)}}else if(t.indexOf(rx(o),c)!=c){var d=l.lastIndexOf(o);d>-1&&(l=l.slice(0,d))}return l+r},ep.unescape=function(t){return(t=o9(t))&&F.test(t)?t.replace(V,nW):t},ep.uniqueId=function(t){var n=++tO;return o9(t)+n},ep.upperCase=aC,ep.upperFirst=aP,ep.each=ol,ep.eachRight=of,ep.first=iH,aU(ep,(ty={},eY(ep,function(t,n){tR.call(ep.prototype,n)||(ty[n]=t)}),ty),{chain:!1}),ep.VERSION="4.17.21",nl(["bind","bindKey","curry","curryRight","partial","partialRight"],function(t){ep[t].placeholder=ep}),nl(["drop","take"],function(t,n){e_.prototype[t]=function(e){e=i===e?1:n2(o5(e),0);var r=this.__filtered__&&!n?new e_(this):this.clone();return r.__filtered__?r.__takeCount__=n6(e,r.__takeCount__):r.__views__.push({size:n6(e,4294967295),type:t+(r.__dir__<0?"Right":"")}),r},e_.prototype[t+"Right"]=function(n){return this.reverse()[t](n).reverse()}}),nl(["filter","map","takeWhile"],function(t,n){var e=n+1,r=1==e||3==e;e_.prototype[t]=function(t){var n=this.clone();return n.__iteratees__.push({iteratee:is(t,3),type:e}),n.__filtered__=n.__filtered__||r,n}}),nl(["head","last"],function(t,n){var e="take"+(n?"Right":"");e_.prototype[t]=function(){return this[e](1).value()[0]}}),nl(["initial","tail"],function(t,n){var e="drop"+(n?"":"Right");e_.prototype[t]=function(){return this.__filtered__?new e_(this):this[e](1)}}),e_.prototype.compact=function(){return this.filter(aI)},e_.prototype.find=function(t){return this.filter(t).head()},e_.prototype.findLast=function(t){return this.reverse().find(t)},e_.prototype.invokeMap=rs(function(t,n){return"function"==typeof t?new e_(this):this.map(function(e){return e2(e,t,n)})}),e_.prototype.reject=function(t){return this.filter(ok(is(t)))},e_.prototype.slice=function(t,n){t=o5(t);var e=this;return e.__filtered__&&(t>0||n<0)?new e_(e):(t<0?e=e.takeRight(-t):t&&(e=e.drop(t)),i!==n&&(e=(n=o5(n))<0?e.dropRight(-n):e.take(n-t)),e)},e_.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},e_.prototype.toArray=function(){return this.take(4294967295)},eY(e_.prototype,function(t,n){var e=/^(?:filter|find|map|reject)|While$/.test(n),r=/^(?:head|last)$/.test(n),o=ep[r?"take"+("last"==n?"Right":""):n],a=r||/^find/.test(n);o&&(ep.prototype[n]=function(){var n=this.__wrapped__,u=r?[1]:arguments,c=n instanceof e_,l=u[0],f=c||oz(n),s=function(t){var n=o.apply(ep,nv([t],u));return r&&h?n[0]:n};f&&e&&"function"==typeof l&&1!=l.length&&(c=f=!1);var h=this.__chain__,d=!!this.__actions__.length,p=a&&!h,v=c&&!d;if(!a&&f){n=v?n:new e_(this);var g=t.apply(n,u);return g.__actions__.push({func:oi,args:[s],thisArg:i}),new ey(g,h)}return p&&v?t.apply(this,u):(g=this.thru(s),p?r?g.value()[0]:g.value():g)})}),nl(["pop","push","shift","sort","splice","unshift"],function(t){var n=tS[t],e=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",r=/^(?:pop|shift)$/.test(t);ep.prototype[t]=function(){var t=arguments;if(r&&!this.__chain__){var i=this.value();return n.apply(oz(i)?i:[],t)}return this[e](function(e){return n.apply(oz(e)?e:[],t)})}}),eY(e_.prototype,function(t,n){var e=ep[n];if(e){var r=e.name+"";tR.call(eo,r)||(eo[r]=[]),eo[r].push({name:n,func:e})}}),eo[rK(i,2).name]=[{name:"wrapper",func:i}],e_.prototype.clone=function(){var t=new e_(this.__wrapped__);return t.__actions__=rB(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=rB(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=rB(this.__views__),t},e_.prototype.reverse=function(){if(this.__filtered__){var t=new e_(this);t.__dir__=-1,t.__filtered__=!0}else t=this.clone(),t.__dir__*=-1;return t},e_.prototype.value=function(){var t=this.__wrapped__.value(),n=this.__dir__,e=oz(t),r=n<0,i=e?t.length:0,o=function(t,n,e){for(var r=-1,i=e.length;++r<i;){var o=e[r],a=o.size;switch(o.type){case"drop":t+=a;break;case"dropRight":n-=a;break;case"take":n=n6(n,t+a);break;case"takeRight":t=n2(t,n-a)}}return{start:t,end:n}}(0,i,this.__views__),a=o.start,u=o.end,c=u-a,l=r?u:a-1,f=this.__iteratees__,s=f.length,h=0,d=n6(c,this.__takeCount__);if(!e||!r&&i==c&&d==c)return rk(t,this.__actions__);var p=[];t:for(;c--&&h<d;){for(var v=-1,g=t[l+=n];++v<s;){var y=f[v],_=y.iteratee,b=y.type,m=_(g);if(2==b)g=m;else if(!m){if(1==b)continue t;break t}}p[h++]=g}return p},ep.prototype.at=oo,ep.prototype.chain=function(){return or(this)},ep.prototype.commit=function(){return new ey(this.value(),this.__chain__)},ep.prototype.next=function(){i===this.__values__&&(this.__values__=o6(this.value()));var t=this.__index__>=this.__values__.length,n=t?i:this.__values__[this.__index__++];return{done:t,value:n}},ep.prototype.plant=function(t){for(var n,e=this;e instanceof eg;){var r=iB(e);r.__index__=0,r.__values__=i,n?o.__wrapped__=r:n=r;var o=r;e=e.__wrapped__}return o.__wrapped__=t,n},ep.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof e_){var n=t;return this.__actions__.length&&(n=new e_(this)),(n=n.reverse()).__actions__.push({func:oi,args:[i1],thisArg:i}),new ey(n,this.__chain__)}return this.thru(i1)},ep.prototype.toJSON=ep.prototype.valueOf=ep.prototype.value=function(){return rk(this.__wrapped__,this.__actions__)},ep.prototype.first=ep.prototype.head,t3&&(ep.prototype[t3]=function(){return this}),ep}();t5._=nZ,i!==(r=(function(){return nZ}).call(n,e,n,t))&&(t.exports=r)}).call(void 0)},98453:(t,n,e)=>{e.d(n,{Z:()=>R});var r,i=e(34132),o=e.n(i),a=e(92801),u=e(42714),c=e(19751),l=e(83209);function f(t){if((!r&&0!==r||t)&&u.default){var n=document.createElement("div");n.style.position="absolute",n.style.top="-9999px",n.style.width="50px",n.style.height="50px",n.style.overflow="scroll",document.body.appendChild(n),r=n.offsetWidth-n.clientWidth,document.body.removeChild(n)}return r}var s=e(3729),h=e(68342),d=e(83524),p=e(42887),v=e(26400),g=e(96857),y=e(67378),_=e(70136),b=e(95344);let m=s.forwardRef(({className:t,bsPrefix:n,as:e="div",...r},i)=>(n=(0,_.vE)(n,"modal-body"),(0,b.jsx)(e,{ref:i,className:o()(t,n),...r})));m.displayName="ModalBody";var x=e(51562);let w=s.forwardRef(({bsPrefix:t,className:n,contentClassName:e,centered:r,size:i,fullscreen:a,children:u,scrollable:c,...l},f)=>{t=(0,_.vE)(t,"modal");let s=`${t}-dialog`,h="string"==typeof a?`${t}-fullscreen-${a}`:`${t}-fullscreen`;return(0,b.jsx)("div",{...l,ref:f,className:o()(s,n,i&&`${t}-${i}`,r&&`${s}-centered`,c&&`${s}-scrollable`,a&&h),children:(0,b.jsx)("div",{className:o()(`${t}-content`,e),children:u})})});w.displayName="ModalDialog";let M=w,T=s.forwardRef(({className:t,bsPrefix:n,as:e="div",...r},i)=>(n=(0,_.vE)(n,"modal-footer"),(0,b.jsx)(e,{ref:i,className:o()(t,n),...r})));T.displayName="ModalFooter";var A=e(80620);let k=s.forwardRef(({bsPrefix:t,className:n,closeLabel:e="Close",closeButton:r=!1,...i},a)=>(t=(0,_.vE)(t,"modal-header"),(0,b.jsx)(A.Z,{ref:a,...i,className:o()(n,t),closeLabel:e,closeButton:r})));k.displayName="ModalHeader";let S=(0,e(80232).Z)("h4"),E=s.forwardRef(({className:t,bsPrefix:n,as:e=S,...r},i)=>(n=(0,_.vE)(n,"modal-title"),(0,b.jsx)(e,{ref:i,className:o()(t,n),...r})));function N(t){return(0,b.jsx)(y.Z,{...t,timeout:null})}function C(t){return(0,b.jsx)(y.Z,{...t,timeout:null})}E.displayName="ModalTitle";let P=s.forwardRef(({bsPrefix:t,className:n,style:e,dialogClassName:r,contentClassName:i,children:y,dialogAs:m=M,"data-bs-theme":w,"aria-labelledby":T,"aria-describedby":A,"aria-label":k,show:S=!1,animation:E=!0,backdrop:P=!0,keyboard:R=!0,onEscapeKeyDown:O,onShow:j,onHide:z,container:D,autoFocus:L=!0,enforceFocus:I=!0,restoreFocus:$=!0,restoreFocusOptions:B,onEntered:V,onExit:U,onExiting:F,onEnter:q,onEntering:G,onExited:Y,backdropClassName:H,manager:W,...Z},X)=>{let[K,J]=(0,s.useState)({}),[Q,tt]=(0,s.useState)(!1),tn=(0,s.useRef)(!1),te=(0,s.useRef)(!1),tr=(0,s.useRef)(null),[ti,to]=(0,s.useState)(null),ta=(0,d.Z)(X,to),tu=(0,h.Z)(z),tc=(0,_.SC)();t=(0,_.vE)(t,"modal");let tl=(0,s.useMemo)(()=>({onHide:tu}),[tu]);function tf(){return W||(0,g.t)({isRTL:tc})}function ts(t){if(!u.default)return;let n=tf().getScrollbarWidth()>0,e=t.scrollHeight>(0,c.default)(t).documentElement.clientHeight;J({paddingRight:n&&!e?f():void 0,paddingLeft:!n&&e?f():void 0})}let th=(0,h.Z)(()=>{ti&&ts(ti.dialog)});!function(t){let n=function(t){let n=(0,s.useRef)(t);return n.current=t,n}(t);(0,s.useEffect)(()=>()=>n.current(),[])}(()=>{(0,l.Z)(window,"resize",th),null==tr.current||tr.current()});let td=()=>{tn.current=!0},tp=t=>{tn.current&&ti&&t.target===ti.dialog&&(te.current=!0),tn.current=!1},tv=()=>{tt(!0),tr.current=(0,p.Z)(ti.dialog,()=>{tt(!1)})},tg=t=>{t.target===t.currentTarget&&tv()},ty=t=>{if("static"===P){tg(t);return}if(te.current||t.target!==t.currentTarget){te.current=!1;return}null==z||z()},t_=(0,s.useCallback)(n=>(0,b.jsx)("div",{...n,className:o()(`${t}-backdrop`,H,!E&&"show")}),[E,H,t]),tb={...e,...K};return tb.display="block",(0,b.jsx)(x.Z.Provider,{value:tl,children:(0,b.jsx)(v.Z,{show:S,ref:ta,backdrop:P,container:D,keyboard:!0,autoFocus:L,enforceFocus:I,restoreFocus:$,restoreFocusOptions:B,onEscapeKeyDown:t=>{R?null==O||O(t):(t.preventDefault(),"static"===P&&tv())},onShow:j,onHide:z,onEnter:(t,n)=>{t&&ts(t),null==q||q(t,n)},onEntering:(t,n)=>{null==G||G(t,n),(0,a.ZP)(window,"resize",th)},onEntered:V,onExit:t=>{null==tr.current||tr.current(),null==U||U(t)},onExiting:F,onExited:t=>{t&&(t.style.display=""),null==Y||Y(t),(0,l.Z)(window,"resize",th)},manager:tf(),transition:E?N:void 0,backdropTransition:E?C:void 0,renderBackdrop:t_,renderDialog:e=>(0,b.jsx)("div",{role:"dialog",...e,style:tb,className:o()(n,t,Q&&`${t}-static`,!E&&"show"),onClick:P?ty:void 0,onMouseUp:tp,"data-bs-theme":w,"aria-label":k,"aria-labelledby":T,"aria-describedby":A,children:(0,b.jsx)(m,{...Z,onMouseDown:td,className:r,contentClassName:i,children:y})})})})});P.displayName="Modal";let R=Object.assign(P,{Body:m,Header:k,Title:E,Footer:T,Dialog:M,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},35015:(t,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.CONSTANTS=void 0,n.CONSTANTS={arcTooltipClassname:"gauge-component-arc-tooltip",tickLineClassname:"tick-line",tickValueClassname:"tick-value",valueLabelClassname:"value-text",debugTicksRadius:!1,debugSingleGauge:!1,rangeBetweenCenteredTickValueLabel:[.35,.65]},n.default=n.CONSTANTS},63755:(t,n,e)=>{var r=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},i=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},o=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.validateArcs=n.clearOuterArcs=n.clearArcs=n.redrawArcs=n.getCoordByValue=n.createGradientElement=n.getColors=n.applyGradientColors=n.getArcDataByPercentage=n.getArcDataByValue=n.applyColors=n.setupTooltip=n.setupArcs=n.drawArc=n.setArcData=n.hideTooltip=void 0;var a=o(e(60229)),u=e(43450),c=o(e(63755)),l=function(t){return t&&t.__esModule?t:{default:t}}(e(35015)),f=e(55409),s=e(45553),h=e(29064),d=function(t,n,e){void 0!=n.data.tooltip&&(n.data.tooltip.text!=e.tooltip.current.text()&&(e.tooltip.current.html(n.data.tooltip.text).style("position","absolute").style("display","block").style("opacity",1),p(n.data.tooltip,n.data.color,e)),e.tooltip.current.style("left",t.pageX+15+"px").style("top",t.pageY-10+"px")),void 0!=n.data.onMouseMove&&n.data.onMouseMove(t)},p=function(t,n,e){Object.entries(f.defaultTooltipStyle).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)}),e.tooltip.current.style("background-color",n),void 0!=t.style&&Object.entries(t.style).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)})},v=function(t,e,r,i){i.cancel(),(0,n.hideTooltip)(r),void 0!=e.data.onMouseLeave&&e.data.onMouseLeave(t)};n.hideTooltip=function(t){t.tooltip.current.html(" ").style("display","none")};var g=function(t,n,e){t.target.style.stroke="none"},y=function(t,n){void 0!=n.data.onMouseClick&&n.data.onMouseClick(t)};n.setArcData=function(t){var e,r,i=t.props.arc,o=t.props.minValue,u=t.props.maxValue,c=(null==i?void 0:i.nbSubArcs)||(null===(e=null==i?void 0:i.subArcs)||void 0===e?void 0:e.length)||1,l=(0,n.getColors)(c,t);if((null==i?void 0:i.subArcs)&&!(null==i?void 0:i.nbSubArcs)){var f=0,s=0,h=[],d=[],p=[];null===(r=null==i?void 0:i.subArcs)||void 0===r||r.forEach(function(n,e){var r,c=0,l=0,v=n.limit;if(void 0!=n.length)c=n.length,v=a.getCurrentGaugeValueByPercentage(c+s,t);else if(void 0==n.limit){l=f;var g=void 0,y=null===(r=null==i?void 0:i.subArcs)||void 0===r?void 0:r.slice(e),_=(1-a.calculatePercentage(o,u,f))*100;g||(g=_/Math.max((null==y?void 0:y.length)||1,1)/100),v=f+100*g,c=g}else l=v-f,c=0!==e?a.calculatePercentage(o,u,v)-s:a.calculatePercentage(o,u,l);h.push(c),d.push(v),s=h.reduce(function(t,n){return t+n},0),f=v,void 0!=n.tooltip&&p.push(n.tooltip)});var v=i.subArcs;t.arcData.current=h.map(function(t,n){return{value:t,limit:d[n],color:l[n],showTick:v[n].showTick||!1,tooltip:v[n].tooltip||void 0,onMouseMove:v[n].onMouseMove,onMouseLeave:v[n].onMouseLeave,onMouseClick:v[n].onClick}})}else{var g=u/c;t.arcData.current=Array.from({length:c},function(t,n){return{value:g,limit:(n+1)*g,color:l[n],tooltip:void 0}})}};var _=function(t,e){void 0===e&&(e=void 0);var r,i=void 0!=e?e:a.calculatePercentage(t.props.minValue,t.props.maxValue,t.props.value),o=(0,n.getArcDataByPercentage)(i,t);return[{value:i,color:(null==o?void 0:o.color)||"white"},{value:1-i,color:null===(r=t.props.arc)||void 0===r?void 0:r.emptyColor}]},b=function(t,e){void 0===e&&(e=!1);var r=t.dimensions.current.outerRadius;if(t.props.type==s.GaugeType.Grafana&&e){t.doughnut.current.selectAll(".outerSubArc").remove();var i=(0,u.arc)().outerRadius(r+7).innerRadius(r+2).cornerRadius(0).padAngle(0),o=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(t.arcData.current)).enter().append("g").attr("class","outerSubArc"),a=o.append("path").attr("d",i);(0,n.applyColors)(a,t);var c=(0,h.throttle)(function(n,e){return d(n,e,t)},20);o.on("mouseleave",function(n,e){return v(n,e,t,c)}).on("mouseout",function(n,e){return g(n,e,t)}).on("mousemove",c).on("click",function(t,n){return y(t,n)})}};n.drawArc=function(t,e){void 0===e&&(e=void 0);var r,i,o=t.props.arc,a=o.padding,c=o.cornerRadius,l=t.dimensions.current,f=l.innerRadius,p=l.outerRadius,b={};b=(null===(i=null===(r=t.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient)?[{value:1}]:t.arcData.current,t.props.type==s.GaugeType.Grafana&&(b=_(t,e));var m=t.props.type==s.GaugeType.Grafana?0:a,x=t.props.type==s.GaugeType.Grafana?0:c,w=(0,u.arc)().outerRadius(p).innerRadius(f).cornerRadius(x).padAngle(m),M=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(b)).enter().append("g").attr("class","subArc"),T=M.append("path").attr("d",w);(0,n.applyColors)(T,t);var A=(0,h.throttle)(function(n,e){return d(n,e,t)},20);M.on("mouseleave",function(n,e){return v(n,e,t,A)}).on("mouseout",function(n,e){return g(n,e,t)}).on("mousemove",A).on("click",function(t,n){return y(t,n)})},n.setupArcs=function(t,e){void 0===e&&(e=!1),(0,n.setupTooltip)(t),b(t,e),(0,n.drawArc)(t)},n.setupTooltip=function(t){0!=document.getElementsByClassName(l.default.arcTooltipClassname).length||(0,u.select)("body").append("div").attr("class",l.default.arcTooltipClassname),t.tooltip.current=(0,u.select)(".".concat(l.default.arcTooltipClassname)),t.tooltip.current.on("mouseleave",function(){return c.hideTooltip(t)}).on("mouseout",function(){return c.hideTooltip(t)})},n.applyColors=function(t,e){var r,i;if(null===(i=null===(r=e.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient){var o="subArc-linear-gradient-".concat(Math.random()),a=(0,n.createGradientElement)(e.doughnut.current,o);(0,n.applyGradientColors)(a,e),t.style("fill",function(t){return"url(#".concat(o,")")})}else t.style("fill",function(t){return t.data.color})},n.getArcDataByValue=function(t,n){return n.arcData.current.find(function(n){return t<=n.limit})},n.getArcDataByPercentage=function(t,e){return(0,n.getArcDataByValue)(a.getCurrentGaugeValueByPercentage(t,e),e)},n.applyGradientColors=function(t,n){n.arcData.current.forEach(function(e){var r,i,o,u,c=a.normalize(null==e?void 0:e.limit,null!==(i=null===(r=null==n?void 0:n.props)||void 0===r?void 0:r.minValue)&&void 0!==i?i:0,null!==(u=null===(o=null==n?void 0:n.props)||void 0===o?void 0:o.maxValue)&&void 0!==u?u:100);t.append("stop").attr("offset","".concat(c,"%")).style("stop-color",e.color).style("stop-opacity",1)})},n.getColors=function(t,n){var e,r=n.props.arc,i=[];if(r.colorArray)i=r.colorArray;else{var o=null===(e=r.subArcs)||void 0===e?void 0:e.map(function(t){return t.color});i=(null==o?void 0:o.some(function(t){return void 0!=t}))?o:l.default.defaultColors}if(i||(i=["#fff"]),t===(null==i?void 0:i.length))return i;for(var a=(0,u.scaleLinear)().domain([1,t]).range([i[0],i[i.length-1]]).interpolate(u.interpolateHsl),c=[],f=1;f<=t;f++)c.push(a(f));return c},n.createGradientElement=function(t,n){return t.append("defs").append("linearGradient").attr("id",n).attr("x1","0%").attr("x2","100%").attr("y1","0%").attr("y2","0%")},n.getCoordByValue=function(t,n,e,r,i){void 0===e&&(e="inner"),void 0===r&&(r=0),void 0===i&&(i=1);var o,u=({outer:function(){return n.dimensions.current.outerRadius-r+2},inner:function(){return n.dimensions.current.innerRadius*i-r+9},between:function(){var t=n.dimensions.current.outerRadius-n.dimensions.current.innerRadius;return n.dimensions.current.innerRadius+t-5}})[e]();n.props.type===s.GaugeType.Grafana?u+=5:n.props.type===s.GaugeType.Semicircle&&(u+=-2);var c=a.calculatePercentage(n.props.minValue,n.props.maxValue,t),l=((o={})[s.GaugeType.Grafana]={startAngle:a.degToRad(-23),endAngle:a.degToRad(203)},o[s.GaugeType.Semicircle]={startAngle:a.degToRad(.9),endAngle:a.degToRad(179.1)},o[s.GaugeType.Radial]={startAngle:a.degToRad(-39),endAngle:a.degToRad(219)},o)[n.props.type],f=l.startAngle,h=f+c*(l.endAngle-f),d=[0,-(n.dimensions.current.width/500*1)/2],p=[d[0]-u*Math.cos(h),d[1]-u*Math.sin(h)],v=[n.dimensions.current.outerRadius,n.dimensions.current.outerRadius];return{x:v[0]+p[0],y:v[1]+p[1]}},n.redrawArcs=function(t){(0,n.clearArcs)(t),(0,n.setArcData)(t),(0,n.setupArcs)(t)},n.clearArcs=function(t){t.doughnut.current.selectAll(".subArc").remove()},n.clearOuterArcs=function(t){t.doughnut.current.selectAll(".outerSubArc").remove()},n.validateArcs=function(t){m(t)};var m=function(t){for(var n,e=t.props.minValue,r=t.props.maxValue,i=t.props.arc.subArcs,o=void 0,a=0,u=(null===(n=t.props.arc)||void 0===n?void 0:n.subArcs)||[];a<u.length;a++){var c=u[a].limit;if(void 0!==c){if(c<e||c>r)throw Error("The limit of a subArc must be between the minValue and maxValue. The limit of the subArc is ".concat(c));if(void 0!==o&&c<=o)throw Error("The limit of a subArc must be greater than the limit of the previous subArc. The limit of the subArc is ".concat(c,'. If you\'re trying to specify length in percent of the arc, use property "length". refer to: https://github.com/antoniolago/react-gauge-component'));o=c}}if(i.length>0){var l=i[i.length-1];l.limit<r&&(l.limit=r)}}},98181:(t,n,e)=>{var r=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},i=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},o=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearChart=n.centerGraph=n.calculateRadius=n.updateDimensions=n.renderChart=n.calculateAngles=n.initChart=void 0;var a=e(45553),u=o(e(63755)),c=o(e(55492)),l=o(e(63931));n.initChart=function(t,e){var r,i,o,a,u=t.dimensions.current.angles;if((null===(i=null===(r=t.resizeObserver)||void 0===r?void 0:r.current)||void 0===i?void 0:i.disconnect)&&(null===(a=null===(o=t.resizeObserver)||void 0===o?void 0:o.current)||void 0===a||a.disconnect()),JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value)&&!e){(0,n.renderChart)(t,!1);return}t.container.current.select("svg").remove(),t.svg.current=t.container.current.append("svg"),t.g.current=t.svg.current.append("g"),t.doughnut.current=t.g.current.append("g").attr("class","doughnut"),(0,n.calculateAngles)(t),t.pieChart.current.value(function(t){return t.value}).startAngle(u.startAngle).endAngle(u.endAngle).sort(null),l.addPointerElement(t),(0,n.renderChart)(t,!0)},n.calculateAngles=function(t){var n=t.dimensions.current.angles;t.props.type==a.GaugeType.Semicircle?(n.startAngle=-Math.PI/2+.02,n.endAngle=Math.PI/2-.02):t.props.type==a.GaugeType.Radial?(n.startAngle=-Math.PI/1.37,n.endAngle=Math.PI/1.37):t.props.type==a.GaugeType.Grafana&&(n.startAngle=-Math.PI/1.6,n.endAngle=Math.PI/1.6)},n.renderChart=function(t,e){void 0===e&&(e=!1);var r,i,o,f,s,h,d=t.dimensions,p=t.props.arc,v=t.props.labels;if(e){(0,n.updateDimensions)(t),t.g.current.attr("transform","translate("+d.current.margin.left+", 35)"),(0,n.calculateRadius)(t),t.doughnut.current.attr("transform","translate("+d.current.outerRadius+", "+d.current.outerRadius+")"),t.doughnut.current.on("mouseleave",function(){return u.hideTooltip(t)}).on("mouseout",function(){return u.hideTooltip(t)});var g=p.width;d.current.innerRadius=d.current.outerRadius*(1-g),(0,n.clearChart)(t),u.setArcData(t),u.setupArcs(t,e),c.setupLabels(t),(null===(o=null===(i=t.props)||void 0===i?void 0:i.pointer)||void 0===o?void 0:o.hide)||l.drawPointer(t,e);var y=((r={})[a.GaugeType.Semicircle]=50,r[a.GaugeType.Radial]=55,r[a.GaugeType.Grafana]=55,r),_=t.doughnut.current.node().getBoundingClientRect().height,b=t.container.current.node().getBoundingClientRect().width,m=t.props.type;t.svg.current.attr("width",b).attr("height",_+y[m])}else{var x=JSON.stringify(t.prevProps.current.arc)!==JSON.stringify(t.props.arc),w=JSON.stringify(t.prevProps.current.pointer)!==JSON.stringify(t.props.pointer),M=JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value),T=JSON.stringify(null===(f=t.prevProps.current.labels)||void 0===f?void 0:f.tickLabels)!==JSON.stringify(v.tickLabels);x&&(u.clearArcs(t),u.setArcData(t),u.setupArcs(t,e)),(w||M&&!(null===(h=null===(s=t.props)||void 0===s?void 0:s.pointer)||void 0===h?void 0:h.hide))&&l.drawPointer(t),(x||T)&&(c.clearTicks(t),c.setupTicks(t)),M&&(c.clearValueLabel(t),c.setupValueLabel(t))}},n.updateDimensions=function(t){var n=t.props.marginInPercent,e=t.dimensions,r=t.container.current.node().getBoundingClientRect(),i=r.width,o=r.height;0==e.current.fixedHeight&&(e.current.fixedHeight=o+200);var a="number"==typeof n,u=a?n:n.left,c=a?n:n.right,l=a?n:n.top,f=a?n:n.bottom;e.current.margin.left=i*u,e.current.margin.right=i*c,e.current.width=i-e.current.margin.left-e.current.margin.right,e.current.margin.top=e.current.fixedHeight*l,e.current.margin.bottom=e.current.fixedHeight*f,e.current.height=e.current.width/2-e.current.margin.top-e.current.margin.bottom},n.calculateRadius=function(t){var e=t.dimensions;e.current.width<2*e.current.height?e.current.outerRadius=(e.current.width-e.current.margin.left-e.current.margin.right)/2:e.current.outerRadius=e.current.height-e.current.margin.top-e.current.margin.bottom+35,(0,n.centerGraph)(t)},n.centerGraph=function(t){var n=t.dimensions;n.current.margin.left=n.current.width/2-n.current.outerRadius+n.current.margin.right,t.g.current.attr("transform","translate("+n.current.margin.left+", "+n.current.margin.top+")")},n.clearChart=function(t){c.clearTicks(t),c.clearValueLabel(t),l.clearPointerElement(t),u.clearArcs(t)}},55492:(t,n,e)=>{var r=function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},o=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},a=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.calculateAnchorAndAngleByValue=n.clearTicks=n.clearValueLabel=n.addValueText=n.addText=n.getLabelCoordsByValue=n.addTick=n.addTickValue=n.addTickLine=n.mapTick=n.addArcTicks=n.setupTicks=n.setupValueLabel=n.setupLabels=void 0;var u=a(e(60229)),c=function(t){return t&&t.__esModule?t:{default:t}}(e(35015)),l=e(86714),f=a(e(43450)),s=e(45553),h=e(63755);n.setupLabels=function(t){(0,n.setupValueLabel)(t),(0,n.setupTicks)(t)},n.setupValueLabel=function(t){var e,r=t.props.labels;(null===(e=null==r?void 0:r.valueLabel)||void 0===e?void 0:e.hide)||(0,n.addValueText)(t)},n.setupTicks=function(t){var e,r,i,o,a,u,l,f,s,h=t.props.labels,d=t.props.minValue,p=t.props.maxValue;if(c.default.debugTicksRadius)for(var v=0;v<p;v++){var g=(0,n.mapTick)(v,t);(0,n.addTick)(g,t)}else if(!(null===(e=h.tickLabels)||void 0===e?void 0:e.hideMinMax)){if(!(null===(i=null===(r=h.tickLabels)||void 0===r?void 0:r.ticks)||void 0===i?void 0:i.some(function(t){return t.value==d}))){var y=(0,n.mapTick)(d,t);(0,n.addTick)(y,t)}if(!(null===(a=null===(o=h.tickLabels)||void 0===o?void 0:o.ticks)||void 0===a?void 0:a.some(function(t){return t.value==p}))){var _=(0,n.mapTick)(p,t);(0,n.addTick)(_,t)}}(null===(l=null===(u=h.tickLabels)||void 0===u?void 0:u.ticks)||void 0===l?void 0:l.length)>0&&(null===(s=null===(f=h.tickLabels)||void 0===f?void 0:f.ticks)||void 0===s||s.forEach(function(e){(0,n.addTick)(e,t)})),(0,n.addArcTicks)(t)},n.addArcTicks=function(t){var e;null===(e=t.arcData.current)||void 0===e||e.map(function(t){if(t.showTick)return t.limit}).forEach(function(e){e&&(0,n.addTick)((0,n.mapTick)(e,t),t)})},n.mapTick=function(t,n){var e=n.props.labels.tickLabels;return{value:t,valueConfig:null==e?void 0:e.defaultTickValueConfig,lineConfig:null==e?void 0:e.defaultTickLineConfig}},n.addTickLine=function(t,e){var r,i,o,a,u,s,h,d,p,v,g,y,_,b,m,x,w,M,T,A,k,S=e.props.labels,E=(0,n.calculateAnchorAndAngleByValue)(null==t?void 0:t.value,e),N=(E.tickAnchor,E.angle),C=(null===(r=t.lineConfig)||void 0===r?void 0:r.distanceFromArc)||(null===(o=null===(i=null==S?void 0:S.tickLabels)||void 0===i?void 0:i.defaultTickLineConfig)||void 0===o?void 0:o.distanceFromArc)||0;(null===(u=null===(a=e.props.labels)||void 0===a?void 0:a.tickLabels)||void 0===u?void 0:u.type)=="outer"&&(C=-C);var P=(0,n.getLabelCoordsByValue)(null==t?void 0:t.value,e,C),R=(null===(s=t.lineConfig)||void 0===s?void 0:s.color)||(null===(d=null===(h=null==S?void 0:S.tickLabels)||void 0===h?void 0:h.defaultTickLineConfig)||void 0===d?void 0:d.color)||(null===(p=l.defaultTickLabels.defaultTickLineConfig)||void 0===p?void 0:p.color),O=(null===(v=t.lineConfig)||void 0===v?void 0:v.width)||(null===(y=null===(g=null==S?void 0:S.tickLabels)||void 0===g?void 0:g.defaultTickLineConfig)||void 0===y?void 0:y.width)||(null===(_=l.defaultTickLabels.defaultTickLineConfig)||void 0===_?void 0:_.width),j=(null===(b=t.lineConfig)||void 0===b?void 0:b.length)||(null===(x=null===(m=null==S?void 0:S.tickLabels)||void 0===m?void 0:m.defaultTickLineConfig)||void 0===x?void 0:x.length)||(null===(w=l.defaultTickLabels.defaultTickLineConfig)||void 0===w?void 0:w.length);(null===(M=null==S?void 0:S.tickLabels)||void 0===M?void 0:M.type)=="inner"?(T=P.x+j*Math.cos(N*Math.PI/180),A=P.y+j*Math.sin(N*Math.PI/180)):(T=P.x-j*Math.cos(N*Math.PI/180),A=P.y-j*Math.sin(N*Math.PI/180));var z=f.line();k=[[P.x,P.y],[T,A]],e.g.current.append("path").datum(k).attr("class",c.default.tickLineClassname).attr("d",z).attr("stroke",R).attr("stroke-width",O).attr("fill","none")},n.addTickValue=function(t,e){var i,o,a,l,f,s,h,d,v,g,y,_,b,m,x,w,M,T=e.props.labels,A=e.props.arc.width,k=null==t?void 0:t.value,S=(0,n.calculateAnchorAndAngleByValue)(k,e).tickAnchor,E=27-10*A,N=(null===(i=null==T?void 0:T.tickLabels)||void 0===i?void 0:i.type)=="inner";N?E-=10:E=10*A-10;var C=(null===(o=t.lineConfig)||void 0===o?void 0:o.distanceFromArc)||(null===(l=null===(a=null==T?void 0:T.tickLabels)||void 0===a?void 0:a.defaultTickLineConfig)||void 0===l?void 0:l.distanceFromArc)||0,P=(null===(f=t.lineConfig)||void 0===f?void 0:f.length)||(null===(h=null===(s=null==T?void 0:T.tickLabels)||void 0===s?void 0:s.defaultTickLineConfig)||void 0===h?void 0:h.length)||0;p(t,e)||(N?E+=C+P:(E-=C,E-=P));var R=(0,n.getLabelCoordsByValue)(k,e,E),O=(null===(d=t.valueConfig)||void 0===d?void 0:d.style)||(null===(g=null===(v=null==T?void 0:T.tickLabels)||void 0===v?void 0:v.defaultTickValueConfig)||void 0===g?void 0:g.style)||{};O=r({},O);var j="",z=(null===(y=t.valueConfig)||void 0===y?void 0:y.maxDecimalDigits)||(null===(b=null===(_=null==T?void 0:T.tickLabels)||void 0===_?void 0:_.defaultTickValueConfig)||void 0===b?void 0:b.maxDecimalDigits);j=(null===(m=t.valueConfig)||void 0===m?void 0:m.formatTextValue)?t.valueConfig.formatTextValue(u.floatingNumber(k,z)):(null===(w=null===(x=null==T?void 0:T.tickLabels)||void 0===x?void 0:x.defaultTickValueConfig)||void 0===w?void 0:w.formatTextValue)?T.tickLabels.defaultTickValueConfig.formatTextValue(u.floatingNumber(k,z)):0===e.props.minValue&&100===e.props.maxValue?u.floatingNumber(k,z).toString()+"%":u.floatingNumber(k,z).toString(),(null===(M=null==T?void 0:T.tickLabels)||void 0===M?void 0:M.type)=="inner"?("end"===S&&(R.x+=10),"start"===S&&(R.x-=10)):"middle"===S&&(R.y+=2),"middle"===S?R.y+=0:R.y+=3,O.textAnchor=S,(0,n.addText)(j,R.x,R.y,e,O,c.default.tickValueClassname)},n.addTick=function(t,e){e.props.labels;var r=p(t,e),i=v(t,e);r||(0,n.addTickLine)(t,e),c.default.debugTicksRadius||i||(0,n.addTickValue)(t,e)},n.getLabelCoordsByValue=function(t,n,e){void 0===e&&(e=0);var r,i=n.props.labels,o=n.props.minValue,a=n.props.maxValue,c=null===(r=i.tickLabels)||void 0===r?void 0:r.type,l=(0,h.getCoordByValue)(t,n,c,e,.93),f=l.x,d=l.y;return u.calculatePercentage(o,a,t),n.props.type==s.GaugeType.Radial&&(d+=3),{x:f,y:d}},n.addText=function(t,n,e,r,i,o,a){void 0===a&&(a=0);var u=r.g.current.append("g").attr("class",o).attr("transform","translate(".concat(n,", ").concat(e,")")).append("text").text(t);d(u,i),u.attr("transform","rotate(".concat(a,")"))};var d=function(t,n){Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(u.camelCaseToKebabCase(e),r)}),void 0!=n&&Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(u.camelCaseToKebabCase(e),r)})};n.addValueText=function(t){var e,i,o,a=t.props.labels,l=t.props.value,f=null==a?void 0:a.valueLabel,d="",p=null===(e=null==a?void 0:a.valueLabel)||void 0===e?void 0:e.maxDecimalDigits,v=u.floatingNumber(l,p),g=(null==(d=f.formatTextValue?f.formatTextValue(v):0===t.props.minValue&&100===t.props.maxValue?v.toString()+"%":v.toString())?void 0:d.length)||0,y=g>4?4/g*1.5:1,_=null===(i=null==f?void 0:f.style)||void 0===i?void 0:i.fontSize,b=r({},f.style),m=t.dimensions.current.outerRadius,x=0;b.textAnchor="middle",t.props.type==s.GaugeType.Semicircle?x=t.dimensions.current.outerRadius/1.5+20:t.props.type==s.GaugeType.Radial?x=1.45*t.dimensions.current.outerRadius+20:t.props.type==s.GaugeType.Grafana&&(x=1*t.dimensions.current.outerRadius+20);var w=(t.props.type,s.GaugeType.Radial,.003);y=t.dimensions.current.width*w*y;var M=parseInt(_,10)*y;b.fontSize=M+"px",f.matchColorWithArc&&(b.fill=(null===(o=(0,h.getArcDataByValue)(l,t))||void 0===o?void 0:o.color)||"white"),(0,n.addText)(d,m,x,t,b,c.default.valueLabelClassname)},n.clearValueLabel=function(t){return t.g.current.selectAll(".".concat(c.default.valueLabelClassname)).remove()},n.clearTicks=function(t){t.g.current.selectAll(".".concat(c.default.tickLineClassname)).remove(),t.g.current.selectAll(".".concat(c.default.tickValueClassname)).remove()},n.calculateAnchorAndAngleByValue=function(t,n){var e,r,i=n.props.labels,o=n.props.minValue,a=n.props.maxValue,l=u.calculatePercentage(o,a,t),f=((e={})[s.GaugeType.Grafana]={startAngle:-20,endAngle:220},e[s.GaugeType.Semicircle]={startAngle:0,endAngle:180},e[s.GaugeType.Radial]={startAngle:-42,endAngle:266},e)[n.props.type],h=f.startAngle,d=f.endAngle,p=l>c.default.rangeBetweenCenteredTickValueLabel[0]&&l<c.default.rangeBetweenCenteredTickValueLabel[1],v=(null===(r=null==i?void 0:i.tickLabels)||void 0===r?void 0:r.type)=="inner";return{tickAnchor:p?"middle":l<.5?v?"start":"end":v?"end":"start",angle:h+100*l*d/100}};var p=function(t,n){var e,r,i,o,a=n.props.labels,u=null===(e=l.defaultTickLabels.defaultTickLineConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==a?void 0:a.tickLabels)||void 0===r?void 0:r.defaultTickLineConfig)||void 0===i?void 0:i.hide;void 0!=c&&(u=c);var f=null===(o=t.lineConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(u=f),u},v=function(t,n){var e,r,i,o,a=n.props.labels,u=null===(e=l.defaultTickLabels.defaultTickValueConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==a?void 0:a.tickLabels)||void 0===r?void 0:r.defaultTickValueConfig)||void 0===i?void 0:i.hide;void 0!=c&&(u=c);var f=null===(o=t.valueConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(u=f),u}},63931:(t,n,e)=>{var r=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},i=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},o=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearPointerElement=n.addPointerElement=n.translatePointer=n.drawPointer=void 0;var a=e(43450),u=e(86660),c=e(63755),l=o(e(60229)),f=o(e(63755)),s=e(45553);n.drawPointer=function(t,n){void 0===n&&(n=!1),t.pointer.current.context=h(t);var e,r=t.pointer.current.context,i=r.prevPercent,o=r.currentPercent,u=r.prevProgress,c=t.props.pointer,l=(null===(e=t.prevProps)||void 0===e?void 0:e.current.value)==void 0;(l||n)&&t.props.type!=s.GaugeType.Grafana&&d(t),(!n||l)&&c.animate?t.doughnut.current.transition().delay(c.animationDelay).ease(c.elastic?a.easeElastic:a.easeExpOut).duration(c.animationDuration).tween("progress",function(){var n=(0,a.interpolateNumber)(i,o);return function(e){var r=n(e);g(r,u,t)&&(t.props.type==s.GaugeType.Grafana?(f.clearArcs(t),f.drawArc(t,r)):p(r,t)),t.pointer.current.context.prevProgress=r}}):p(o,t)};var h=function(t){var n,e=t.props.value,r=t.props.pointer,i=r.length,o=t.props.minValue,a=t.props.maxValue;t.pointer.current.context.pointerPath;var c=_(t),f=r.type==u.PointerType.Needle?i:.2,s=[u.PointerType.Needle,u.PointerType.Arrow];return{centerPoint:[0,-c/2],pointerRadius:_(t),pathLength:t.dimensions.current.outerRadius*f,currentPercent:l.calculatePercentage(o,a,e),prevPercent:l.calculatePercentage(o,a,(null===(n=t.prevProps)||void 0===n?void 0:n.current.value)||o),prevProgress:0,pathStr:"",shouldDrawPath:s.includes(r.type),prevColor:""}},d=function(t){var n=t.props.value,e=t.props.pointer,r=t.pointer.current.context,i=r.shouldDrawPath,o=r.centerPoint,a=r.pointerRadius,c=(r.pathStr,r.currentPercent),l=r.prevPercent;i&&(t.pointer.current.context.pathStr=y(t,l||c),t.pointer.current.path=t.pointer.current.element.append("path").attr("d",t.pointer.current.context.pathStr).attr("fill",e.color)),e.type==u.PointerType.Needle?t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",a).attr("fill",e.color):e.type==u.PointerType.Blob&&t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",a).attr("fill",e.baseColor).attr("stroke",e.color).attr("stroke-width",e.strokeWidth*a/10),v(a,n,t)},p=function(t,n){var e,r=n.props.pointer,i=n.pointer.current.context,o=i.pointerRadius,a=i.shouldDrawPath,c=i.prevColor;if(v(o,t,n),a&&n.props.type!=s.GaugeType.Grafana&&n.pointer.current.path.attr("d",y(n,t)),r.type==u.PointerType.Blob){var l=null===(e=f.getArcDataByPercentage(t,n))||void 0===e?void 0:e.color;l!=c&&n.pointer.current.element.select("circle").attr("stroke",l);var h=r.strokeWidth*o/10;n.pointer.current.element.select("circle").attr("stroke-width",h),n.pointer.current.context.prevColor=l}},v=function(t,e,r){var i,o=r.props.pointer.type,a=r.dimensions,f=l.getCurrentGaugeValueByPercentage(e,r);return((i={})[u.PointerType.Needle]=function(){(0,n.translatePointer)(a.current.outerRadius,a.current.outerRadius,r)},i[u.PointerType.Arrow]=function(){var e=(0,c.getCoordByValue)(f,r,"inner",0,.7),i=e.x,o=e.y;i-=1,o+=t-3,(0,n.translatePointer)(i,o,r)},i[u.PointerType.Blob]=function(){var e=(0,c.getCoordByValue)(f,r,"between",0,.75),i=e.x,o=e.y;i-=1,o+=t,(0,n.translatePointer)(i,o,r)},i)[o]()},g=function(t,n,e){return!(1e-4>Math.abs(t-n))&&t!=n&&!(t>1||t<0)},y=function(t,n){var e=t.pointer.current.context,r=e.centerPoint,i=e.pointerRadius,o=e.pathLength,a=l.degToRad(t.props.type==s.GaugeType.Semicircle?0:-42),u=a+n*(l.degToRad(t.props.type==s.GaugeType.Semicircle?180:223)-a),c=[r[0]-o*Math.cos(u),r[1]-o*Math.sin(u)],f=u-Math.PI/2,h=[r[0]-i*Math.cos(f),r[1]-i*Math.sin(f)],d=u+Math.PI/2,p=[r[0]-i*Math.cos(d),r[1]-i*Math.sin(d)];return"M ".concat(h[0]," ").concat(h[1]," L ").concat(c[0]," ").concat(c[1]," L ").concat(p[0]," ").concat(p[1])},_=function(t){var n=t.props.pointer.width;return t.dimensions.current.width/500*n};n.translatePointer=function(t,n,e){return e.pointer.current.element.attr("transform","translate("+t+", "+n+")")},n.addPointerElement=function(t){return t.pointer.current.element=t.g.current.append("g").attr("class","pointer")},n.clearPointerElement=function(t){return t.pointer.current.element.selectAll("*").remove()}},60229:(t,n)=>{var e=function(){return(e=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};Object.defineProperty(n,"__esModule",{value:!0}),n.camelCaseToKebabCase=n.getCurrentGaugeValueByPercentage=n.getCurrentGaugePercentageByValue=n.degToRad=n.normalize=n.floatingNumber=n.percentToRad=n.mergeObjects=n.isEmptyObject=n.calculatePercentage=void 0,n.calculatePercentage=function(t,n,e){return e<t?0:e>n?1:(e-t)/(n-t)},n.isEmptyObject=function(t){return 0===Object.keys(t).length&&t.constructor===Object},n.mergeObjects=function(t,r){var i=e({},t);return Object.keys(r).forEach(function(e){var o=t[e],a=r[e];Array.isArray(o)&&Array.isArray(a)?i[e]=a:"object"==typeof o&&"object"==typeof a?i[e]=(0,n.mergeObjects)(o,a):void 0!==a&&(i[e]=a)}),i},n.percentToRad=function(t,n){return Math.PI/n*t},n.floatingNumber=function(t,n){return void 0===n&&(n=2),Math.round(t*Math.pow(10,n))/Math.pow(10,n)},n.normalize=function(t,n,e){return(t-n)/(e-n)*100},n.degToRad=function(t){return Math.PI/180*t},n.getCurrentGaugePercentageByValue=function(t,e){return(0,n.calculatePercentage)(e.minValue,e.maxValue,t)},n.getCurrentGaugeValueByPercentage=function(t,n){var e=n.props.minValue;return e+t*(n.props.maxValue-e)},n.camelCaseToKebabCase=function(t){return t.replace(/[A-Z]/g,function(t){return"-".concat(t.toLowerCase())})}},89557:(t,n,e)=>{var r=function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},o=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},a=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.GaugeComponent=void 0;var u=a(e(3729)),c=e(43450),l=e(45553),f=a(e(98181)),s=a(e(63755)),h=e(60229),d=e(39620),p=e(86660),v=e(53594),g=function(t){var n=(0,u.useRef)({}),e=(0,u.useRef)({}),i=(0,u.useRef)({}),o=(0,u.useRef)({}),a=(0,u.useRef)(!0),g=(0,u.useRef)(0),y=(0,u.useRef)(r({},p.defaultPointerRef)),_=(0,u.useRef)({}),b=(0,u.useRef)([]),m=(0,u.useRef)((0,c.pie)()),x=(0,u.useRef)(r({},d.defaultDimensions)),w=(0,u.useRef)(t),M=(0,u.useRef)({}),T=(0,u.useRef)({}),A=(0,u.useRef)(null),k={props:w.current,resizeObserver:T,prevProps:M,svg:n,g:i,dimensions:x,doughnut:o,isFirstRun:a,currentProgress:g,pointer:y,container:_,arcData:b,pieChart:m,tooltip:e},S=function(){var n,e,i=r({},l.defaultGaugeProps);k.props=w.current=(0,h.mergeObjects)(i,t),(null===(n=k.props.arc)||void 0===n?void 0:n.width)==(null===(e=l.defaultGaugeProps.arc)||void 0===e?void 0:e.width)&&(w.current.arc.width=(0,v.getArcWidthByType)(k.props.type)),k.props.marginInPercent==l.defaultGaugeProps.marginInPercent&&(w.current.marginInPercent=(0,l.getGaugeMarginByType)(k.props.type)),s.validateArcs(k)},E=function(){var t=JSON.stringify(M.current.arc)!==JSON.stringify(w.current.arc),n=JSON.stringify(M.current.pointer)!==JSON.stringify(w.current.pointer),e=JSON.stringify(M.current.value)!==JSON.stringify(w.current.value),r=JSON.stringify(M.current.minValue)!==JSON.stringify(w.current.minValue),i=JSON.stringify(M.current.maxValue)!==JSON.stringify(w.current.maxValue);return t||n||e||r||i};(0,u.useLayoutEffect)(function(){S(),a.current=(0,h.isEmptyObject)(_.current),a.current&&(_.current=(0,c.select)(A.current)),E()&&f.initChart(k,a.current),k.prevProps.current=w.current},[t]),(0,u.useEffect)(function(){var t=function(){return f.renderChart(k,!0)};return window.addEventListener("resize",t),function(){return window.removeEventListener("resize",t)}},[t]),(0,u.useEffect)(function(){var t=A.current;if(t){var n=new ResizeObserver(function(){f.renderChart(k,!0)});return k.resizeObserver.current=n,t.parentNode&&n.observe(t.parentNode),function(){var t;k.resizeObserver&&(null===(t=k.resizeObserver.current)||void 0===t||t.disconnect(),delete k.resizeObserver.current)}}},[]);var N=t.id,C=t.style,P=t.className;return t.type,u.default.createElement("div",{id:N,className:"".concat(k.props.type,"-gauge").concat(P?" "+P:""),style:C,ref:function(t){return A.current=t}})};n.GaugeComponent=g,n.default=g},53594:(t,n,e)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.defaultArc=n.getArcWidthByType=n.defaultSubArcs=void 0;var r=e(45553);n.defaultSubArcs=[{limit:33,color:"#5BE12C"},{limit:66,color:"#F5CD19"},{color:"#EA4228"}],n.getArcWidthByType=function(t){var n,e=((n={})[r.GaugeType.Grafana]=.25,n[r.GaugeType.Semicircle]=.15,n[r.GaugeType.Radial]=.2,n);return t||(t=r.defaultGaugeProps.type),e[t]},n.defaultArc={padding:.05,width:.25,cornerRadius:7,nbSubArcs:void 0,emptyColor:"#5C5C5C",colorArray:void 0,subArcs:n.defaultSubArcs,gradient:!1}},39620:(t,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.defaultDimensions=n.defaultAngles=n.defaultMargins=void 0,n.defaultMargins={top:0,right:0,bottom:0,left:0},n.defaultAngles={startAngle:0,endAngle:0,startAngleDeg:0,endAngleDeg:0},n.defaultDimensions={width:0,height:0,margin:n.defaultMargins,outerRadius:0,innerRadius:0,angles:n.defaultAngles,fixedHeight:0}},45553:(t,n,e)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.getGaugeMarginByType=n.defaultGaugeProps=n.GaugeType=void 0;var r,i=e(53594),o=e(49719),a=e(86660);(function(t){t.Semicircle="semicircle",t.Radial="radial",t.Grafana="grafana"})(r||(n.GaugeType=r={})),n.defaultGaugeProps={id:"",className:"gauge-component-class",style:{width:"100%"},marginInPercent:.07,value:33,minValue:0,maxValue:100,arc:i.defaultArc,labels:o.defaultLabels,pointer:a.defaultPointer,type:r.Grafana},n.getGaugeMarginByType=function(t){var n;return((n={})[r.Grafana]={top:.12,bottom:0,left:.07,right:.07},n[r.Semicircle]={top:.08,bottom:0,left:.08,right:.08},n[r.Radial]={top:.07,bottom:0,left:.07,right:.07},n)[t]}},49719:(t,n,e)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.defaultLabels=n.defaultValueLabel=void 0;var r=e(86714);n.defaultValueLabel={formatTextValue:void 0,matchColorWithArc:!1,maxDecimalDigits:2,style:{fontSize:"35px",fill:"#fff",textShadow:"black 1px 0.5px 0px, black 0px 0px 0.03em, black 0px 0px 0.01em"},hide:!1},n.defaultLabels={valueLabel:n.defaultValueLabel,tickLabels:r.defaultTickLabels}},86660:(t,n)=>{var e;Object.defineProperty(n,"__esModule",{value:!0}),n.defaultPointer=n.defaultPointerRef=n.defaultPointerContext=n.PointerType=void 0,function(t){t.Needle="needle",t.Blob="blob",t.Arrow="arrow"}(e||(n.PointerType=e={})),n.defaultPointerContext={centerPoint:[0,0],pointerRadius:0,pathLength:0,currentPercent:0,prevPercent:0,prevProgress:0,pathStr:"",shouldDrawPath:!1,prevColor:""},n.defaultPointerRef={element:void 0,path:void 0,context:n.defaultPointerContext},n.defaultPointer={type:e.Needle,color:"#5A5A5A",baseColor:"white",length:.7,width:20,animate:!0,elastic:!1,hide:!1,animationDuration:3e3,animationDelay:100,strokeWidth:8}},86714:(t,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTickLabels=void 0,n.defaultTickLabels={type:"outer",hideMinMax:!1,ticks:[],defaultTickValueConfig:{formatTextValue:void 0,maxDecimalDigits:2,style:{fontSize:"10px",fill:"rgb(173 172 171)"},hide:!1},defaultTickLineConfig:{color:"rgb(173 172 171)",length:7,width:1,distanceFromArc:3,hide:!1}}},55409:(t,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTooltipStyle=void 0,n.defaultTooltipStyle={borderColor:"#5A5A5A",borderStyle:"solid",borderWidth:"1px",borderRadius:"5px",color:"white",padding:"5px",fontSize:"15px",textShadow:"1px 1px 2px black, 0 0 1em black, 0 0 0.2em black"}},82678:(t,n,e)=>{var r=function(t){return t&&t.__esModule?t:{default:t}}(e(89557));r.default,n.ZP=r.default},43450:(t,n,e)=>{let r,i,o,a;function u(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function c(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function l(t){let n,e,r;function i(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>e(t[n],r)?i=n+1:o=n}while(i<o)}return i}return 2!==t.length?(n=u,e=(n,e)=>u(t(n),e),r=(n,e)=>t(n)-e):(n=t===u||t===c?t:f,e=t,r=t),{left:i,center:function(t,n,e=0,o=t.length){let a=i(t,n,e,o-1);return a>e&&r(t[a-1],n)>-r(t[a],n)?a-1:a},right:function(t,r,i=0,o=t.length){if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>=e(t[n],r)?i=n+1:o=n}while(i<o)}return i}}}function f(){return 0}function s(t){return null===t?NaN:+t}e.r(n),e.d(n,{Adder:()=>O,Delaunay:()=>oI,FormatSpecifier:()=>uS,InternMap:()=>D,InternSet:()=>L,Node:()=>sW,Path:()=>iS,Voronoi:()=>oO,ZoomTransform:()=>bq,active:()=>rU,arc:()=>yJ,area:()=>y4,areaRadial:()=>_o,ascending:()=>u,autoType:()=>ai,axisBottom:()=>nf,axisLeft:()=>ns,axisRight:()=>nl,axisTop:()=>nc,bin:()=>tx,bisect:()=>g,bisectCenter:()=>v,bisectLeft:()=>p,bisectRight:()=>d,bisector:()=>l,blob:()=>aY,blur:()=>y,blur2:()=>_,blurImage:()=>b,brush:()=>ic,brushSelection:()=>io,brushX:()=>ia,brushY:()=>iu,buffer:()=>aW,chord:()=>ib,chordDirected:()=>ix,chordTranspose:()=>im,cluster:()=>sV,color:()=>eb,contourDensity:()=>od,contours:()=>oo,count:()=>T,create:()=>yS,creator:()=>nW,cross:()=>E,csv:()=>aQ,csvFormat:()=>o1,csvFormatBody:()=>o2,csvFormatRow:()=>o3,csvFormatRows:()=>o6,csvFormatValue:()=>o5,csvParse:()=>oQ,csvParseRows:()=>o0,cubehelix:()=>i7,cumsum:()=>N,curveBasis:()=>_W,curveBasisClosed:()=>_X,curveBasisOpen:()=>_J,curveBumpX:()=>_l,curveBumpY:()=>_f,curveBundle:()=>_0,curveCardinal:()=>_6,curveCardinalClosed:()=>_5,curveCardinalOpen:()=>_8,curveCatmullRom:()=>bt,curveCatmullRomClosed:()=>be,curveCatmullRomOpen:()=>bi,curveLinear:()=>y2,curveLinearClosed:()=>ba,curveMonotoneX:()=>bd,curveMonotoneY:()=>bp,curveNatural:()=>by,curveStep:()=>bb,curveStepAfter:()=>bx,curveStepBefore:()=>bm,descending:()=>c,deviation:()=>P,difference:()=>t5,disjoint:()=>t4,dispatch:()=>ng,drag:()=>oG,dragDisable:()=>en,dragEnable:()=>ee,dsv:()=>aJ,dsvFormat:()=>oK,easeBack:()=>aB,easeBackIn:()=>aI,easeBackInOut:()=>aB,easeBackOut:()=>a$,easeBounce:()=>aD,easeBounceIn:()=>az,easeBounceInOut:()=>aL,easeBounceOut:()=>aD,easeCircle:()=>aT,easeCircleIn:()=>aw,easeCircleInOut:()=>aT,easeCircleOut:()=>aM,easeCubic:()=>r$,easeCubicIn:()=>rL,easeCubicInOut:()=>r$,easeCubicOut:()=>rI,easeElastic:()=>aF,easeElasticIn:()=>aU,easeElasticInOut:()=>aq,easeElasticOut:()=>aF,easeExp:()=>ax,easeExpIn:()=>ab,easeExpInOut:()=>ax,easeExpOut:()=>am,easeLinear:()=>aa,easePoly:()=>ah,easePolyIn:()=>af,easePolyInOut:()=>ah,easePolyOut:()=>as,easeQuad:()=>al,easeQuadIn:()=>au,easeQuadInOut:()=>al,easeQuadOut:()=>ac,easeSin:()=>ay,easeSinIn:()=>av,easeSinInOut:()=>ay,easeSinOut:()=>ag,every:()=>tQ,extent:()=>R,fcumsum:()=>z,filter:()=>t1,flatGroup:()=>Y,flatRollup:()=>H,forceCenter:()=>a7,forceCollide:()=>us,forceLink:()=>up,forceManyBody:()=>ub,forceRadial:()=>um,forceSimulation:()=>u_,forceX:()=>ux,forceY:()=>uw,format:()=>co,formatDefaultLocale:()=>uj,formatLocale:()=>uO,formatPrefix:()=>ca,formatSpecifier:()=>uk,fsum:()=>j,geoAlbers:()=>sa,geoAlbersUsa:()=>su,geoArea:()=>cb,geoAzimuthalEqualArea:()=>ss,geoAzimuthalEqualAreaRaw:()=>sf,geoAzimuthalEquidistant:()=>sd,geoAzimuthalEquidistantRaw:()=>sh,geoBounds:()=>cI,geoCentroid:()=>cX,geoCircle:()=>c8,geoClipAntimeridian:()=>lc,geoClipCircle:()=>ll,geoClipExtent:()=>ls,geoClipRectangle:()=>lf,geoConicConformal:()=>sb,geoConicConformalRaw:()=>s_,geoConicEqualArea:()=>so,geoConicEqualAreaRaw:()=>si,geoConicEquidistant:()=>sM,geoConicEquidistantRaw:()=>sw,geoContains:()=>lS,geoDistance:()=>lb,geoEqualEarth:()=>sk,geoEqualEarthRaw:()=>sA,geoEquirectangular:()=>sx,geoEquirectangularRaw:()=>sm,geoGnomonic:()=>sE,geoGnomonicRaw:()=>sS,geoGraticule:()=>lC,geoGraticule10:()=>lP,geoIdentity:()=>sN,geoInterpolate:()=>lR,geoLength:()=>lg,geoMercator:()=>sv,geoMercatorRaw:()=>sp,geoNaturalEarth1:()=>sP,geoNaturalEarth1Raw:()=>sC,geoOrthographic:()=>sO,geoOrthographicRaw:()=>sR,geoPath:()=>fJ,geoProjection:()=>sn,geoProjectionMutator:()=>se,geoRotation:()=>c3,geoStereographic:()=>sz,geoStereographicRaw:()=>sj,geoStream:()=>cn,geoTransform:()=>fQ,geoTransverseMercator:()=>sL,geoTransverseMercatorRaw:()=>sD,gray:()=>iZ,greatest:()=>tE,greatestIndex:()=>tG,group:()=>F,groupSort:()=>ti,groups:()=>q,hcl:()=>i3,hierarchy:()=>sF,histogram:()=>tx,hsl:()=>eR,html:()=>a4,image:()=>a1,index:()=>X,indexes:()=>K,interpolate:()=>e3,interpolateArray:()=>eX,interpolateBasis:()=>eI,interpolateBasisClosed:()=>e$,interpolateBlues:()=>yt,interpolateBrBG:()=>gw,interpolateBuGn:()=>gB,interpolateBuPu:()=>gU,interpolateCividis:()=>ys,interpolateCool:()=>yp,interpolateCubehelix:()=>hG,interpolateCubehelixDefault:()=>yh,interpolateCubehelixLong:()=>hY,interpolateDate:()=>eJ,interpolateDiscrete:()=>hR,interpolateGnBu:()=>gq,interpolateGreens:()=>ye,interpolateGreys:()=>yi,interpolateHcl:()=>hU,interpolateHclLong:()=>hF,interpolateHsl:()=>hI,interpolateHslLong:()=>h$,interpolateHue:()=>hO,interpolateInferno:()=>yA,interpolateLab:()=>hB,interpolateMagma:()=>yT,interpolateNumber:()=>eQ,interpolateNumberArray:()=>eW,interpolateObject:()=>e0,interpolateOrRd:()=>gY,interpolateOranges:()=>yf,interpolatePRGn:()=>gT,interpolatePiYG:()=>gk,interpolatePlasma:()=>yk,interpolatePuBu:()=>gX,interpolatePuBuGn:()=>gW,interpolatePuOr:()=>gE,interpolatePuRd:()=>gJ,interpolatePurples:()=>ya,interpolateRainbow:()=>yg,interpolateRdBu:()=>gC,interpolateRdGy:()=>gR,interpolateRdPu:()=>g0,interpolateRdYlBu:()=>gj,interpolateRdYlGn:()=>gD,interpolateReds:()=>yc,interpolateRgb:()=>eq,interpolateRgbBasis:()=>eY,interpolateRgbBasisClosed:()=>eH,interpolateRound:()=>hj,interpolateSinebow:()=>ym,interpolateSpectral:()=>gI,interpolateString:()=>e6,interpolateTransformCss:()=>rS,interpolateTransformSvg:()=>rE,interpolateTurbo:()=>yx,interpolateViridis:()=>yM,interpolateWarm:()=>yd,interpolateYlGn:()=>g3,interpolateYlGnBu:()=>g2,interpolateYlOrBr:()=>g4,interpolateYlOrRd:()=>g7,interpolateZoom:()=>hD,interrupt:()=>rw,intersection:()=>t8,interval:()=>bV,isoFormat:()=>b$,isoParse:()=>bB,json:()=>a6,lab:()=>iX,lch:()=>i6,least:()=>tF,leastIndex:()=>tq,line:()=>y5,lineRadial:()=>_i,link:()=>_p,linkHorizontal:()=>_v,linkRadial:()=>_y,linkVertical:()=>_g,local:()=>yN,map:()=>t2,matcher:()=>nw,max:()=>tw,maxIndex:()=>tM,mean:()=>tj,median:()=>tz,medianIndex:()=>tD,merge:()=>tL,min:()=>tT,minIndex:()=>tA,mode:()=>tI,namespace:()=>nD,namespaces:()=>nz,nice:()=>tb,now:()=>ru,pack:()=>hi,packEnclose:()=>s0,packSiblings:()=>he,pairs:()=>t$,partition:()=>hf,path:()=>iE,pathRound:()=>iN,permute:()=>tt,pie:()=>y9,piecewise:()=>hH,pointRadial:()=>_a,pointer:()=>e4,pointers:()=>yP,polygonArea:()=>hZ,polygonCentroid:()=>hX,polygonContains:()=>h0,polygonHull:()=>hQ,polygonLength:()=>h1,precisionFixed:()=>uz,precisionPrefix:()=>uD,precisionRound:()=>uL,quadtree:()=>ur,quantile:()=>tN,quantileIndex:()=>tP,quantileSorted:()=>tC,quantize:()=>hW,quickselect:()=>tk,radialArea:()=>_o,radialLine:()=>_i,randomBates:()=>h7,randomBernoulli:()=>dn,randomBeta:()=>di,randomBinomial:()=>da,randomCauchy:()=>dc,randomExponential:()=>h9,randomGamma:()=>dr,randomGeometric:()=>de,randomInt:()=>h3,randomIrwinHall:()=>h8,randomLcg:()=>dh,randomLogNormal:()=>h4,randomLogistic:()=>dl,randomNormal:()=>h5,randomPareto:()=>dt,randomPoisson:()=>df,randomUniform:()=>h6,randomWeibull:()=>du,range:()=>tV,rank:()=>tU,reduce:()=>t6,reverse:()=>t3,rgb:()=>eM,ribbon:()=>iB,ribbonArrow:()=>iV,rollup:()=>W,rollups:()=>Z,scaleBand:()=>dy,scaleDiverging:()=>function t(){var n=dN(go()(dx));return n.copy=function(){return ge(n,t())},dp.apply(n,arguments)},scaleDivergingLog:()=>function t(){var n=dL(go()).domain([.1,1,10]);return n.copy=function(){return ge(n,t()).base(n.base())},dp.apply(n,arguments)},scaleDivergingPow:()=>ga,scaleDivergingSqrt:()=>gu,scaleDivergingSymlog:()=>function t(){var n=dB(go());return n.copy=function(){return ge(n,t()).constant(n.constant())},dp.apply(n,arguments)},scaleIdentity:()=>function t(n){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(n=Array.from(t,db),r):n.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return t(n).unknown(e)},n=arguments.length?Array.from(n,db):[0,1],dN(r)},scaleImplicit:()=>dv,scaleLinear:()=>function t(){var n=dS();return n.copy=function(){return dA(n,t())},dd.apply(n,arguments),dN(n)},scaleLog:()=>function t(){let n=dL(dk()).domain([1,10]);return n.copy=()=>dA(n,t()).base(n.base()),dd.apply(n,arguments),n},scaleOrdinal:()=>dg,scalePoint:()=>d_,scalePow:()=>dG,scaleQuantile:()=>function t(){var n,e=[],r=[],i=[];function o(){var t=0,n=Math.max(1,r.length);for(i=Array(n-1);++t<n;)i[t-1]=tC(e,t/n);return a}function a(t){return null==t||isNaN(t=+t)?n:r[g(i,t)]}return a.invertExtent=function(t){var n=r.indexOf(t);return n<0?[NaN,NaN]:[n>0?i[n-1]:e[0],n<i.length?i[n]:e[e.length-1]]},a.domain=function(t){if(!arguments.length)return e.slice();for(let n of(e=[],t))null==n||isNaN(n=+n)||e.push(n);return e.sort(u),o()},a.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},a.unknown=function(t){return arguments.length?(n=t,a):n},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(e).range(r).unknown(n)},dd.apply(a,arguments)},scaleQuantize:()=>function t(){var n,e=0,r=1,i=1,o=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[g(o,t,0,i)]:n}function c(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*r-(t-i)*e)/(i+1);return u}return u.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},u.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var n=a.indexOf(t);return n<0?[NaN,NaN]:n<1?[e,o[0]]:n>=i?[o[i-1],r]:[o[n-1],o[n]]},u.unknown=function(t){return arguments.length&&(n=t),u},u.thresholds=function(){return o.slice()},u.copy=function(){return t().domain([e,r]).range(a).unknown(n)},dd.apply(dN(u),arguments)},scaleRadial:()=>function t(){var n,e=dS(),r=[0,1],i=!1;function o(t){var r,o=Math.sign(r=e(t))*Math.sqrt(Math.abs(r));return isNaN(o)?n:i?Math.round(o):o}return o.invert=function(t){return e.invert(dH(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((r=Array.from(t,db)).map(dH)),o):r.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t(e.domain(),r).round(i).clamp(e.clamp()).unknown(n)},dd.apply(o,arguments),dN(o)},scaleSequential:()=>function t(){var n=dN(gn()(dx));return n.copy=function(){return ge(n,t())},dp.apply(n,arguments)},scaleSequentialLog:()=>function t(){var n=dL(gn()).domain([1,10]);return n.copy=function(){return ge(n,t()).base(n.base())},dp.apply(n,arguments)},scaleSequentialPow:()=>gr,scaleSequentialQuantile:()=>function t(){var n=[],e=dx;function r(t){if(null!=t&&!isNaN(t=+t))return e((g(n,t,1)-1)/(n.length-1))}return r.domain=function(t){if(!arguments.length)return n.slice();for(let e of(n=[],t))null==e||isNaN(e=+e)||n.push(e);return n.sort(u),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return n.map((t,r)=>e(r/(n.length-1)))},r.quantiles=function(t){return Array.from({length:t+1},(e,r)=>tN(n,r/t))},r.copy=function(){return t(e).domain(n)},dp.apply(r,arguments)},scaleSequentialSqrt:()=>gi,scaleSequentialSymlog:()=>function t(){var n=dB(gn());return n.copy=function(){return ge(n,t()).constant(n.constant())},dp.apply(n,arguments)},scaleSqrt:()=>dY,scaleSymlog:()=>function t(){var n=dB(dk());return n.copy=function(){return dA(n,t()).constant(n.constant())},dd.apply(n,arguments)},scaleThreshold:()=>function t(){var n,e=[.5],r=[0,1],i=1;function o(t){return null!=t&&t<=t?r[g(e,t,0,i)]:n}return o.domain=function(t){return arguments.length?(i=Math.min((e=Array.from(t)).length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),i=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t().domain(e).range(r).unknown(n)},dd.apply(o,arguments)},scaleTime:()=>v9,scaleUtc:()=>gt,scan:()=>tY,schemeAccent:()=>gf,schemeBlues:()=>g9,schemeBrBG:()=>gx,schemeBuGn:()=>g$,schemeBuPu:()=>gV,schemeCategory10:()=>gl,schemeDark2:()=>gs,schemeGnBu:()=>gF,schemeGreens:()=>yn,schemeGreys:()=>yr,schemeObservable10:()=>gh,schemeOrRd:()=>gG,schemeOranges:()=>yl,schemePRGn:()=>gM,schemePaired:()=>gd,schemePastel1:()=>gp,schemePastel2:()=>gv,schemePiYG:()=>gA,schemePuBu:()=>gZ,schemePuBuGn:()=>gH,schemePuOr:()=>gS,schemePuRd:()=>gK,schemePurples:()=>yo,schemeRdBu:()=>gN,schemeRdGy:()=>gP,schemeRdPu:()=>gQ,schemeRdYlBu:()=>gO,schemeRdYlGn:()=>gz,schemeReds:()=>yu,schemeSet1:()=>gg,schemeSet2:()=>gy,schemeSet3:()=>g_,schemeSpectral:()=>gL,schemeTableau10:()=>gb,schemeYlGn:()=>g6,schemeYlGnBu:()=>g1,schemeYlOrBr:()=>g5,schemeYlOrRd:()=>g8,select:()=>n4,selectAll:()=>yR,selection:()=>n5,selector:()=>n_,selectorAll:()=>nx,shuffle:()=>tH,shuffler:()=>tW,some:()=>t0,sort:()=>tn,stack:()=>bk,stackOffsetDiverging:()=>bE,stackOffsetExpand:()=>bS,stackOffsetNone:()=>bw,stackOffsetSilhouette:()=>bN,stackOffsetWiggle:()=>bC,stackOrderAppearance:()=>bP,stackOrderAscending:()=>bO,stackOrderDescending:()=>bz,stackOrderInsideOut:()=>bD,stackOrderNone:()=>bM,stackOrderReverse:()=>bL,stratify:()=>hg,style:()=>nI,subset:()=>nn,sum:()=>tZ,superset:()=>t9,svg:()=>a8,symbol:()=>_q,symbolAsterisk:()=>_b,symbolCircle:()=>_m,symbolCross:()=>_x,symbolDiamond:()=>_T,symbolDiamond2:()=>_A,symbolPlus:()=>_k,symbolSquare:()=>_S,symbolSquare2:()=>_E,symbolStar:()=>_R,symbolTimes:()=>_V,symbolTriangle:()=>_j,symbolTriangle2:()=>_D,symbolWye:()=>_B,symbolX:()=>_V,symbols:()=>_U,symbolsFill:()=>_U,symbolsStroke:()=>_F,text:()=>aX,thresholdFreedmanDiaconis:()=>tR,thresholdScott:()=>tO,thresholdSturges:()=>tm,tickFormat:()=>dE,tickIncrement:()=>ty,tickStep:()=>t_,ticks:()=>tg,timeDay:()=>d9,timeDays:()=>pt,timeFormat:()=>ta,timeFormatDefaultLocale:()=>v5,timeFormatLocale:()=>pK,timeFriday:()=>ps,timeFridays:()=>p_,timeHour:()=>d5,timeHours:()=>d4,timeInterval:()=>dX,timeMillisecond:()=>dK,timeMilliseconds:()=>dJ,timeMinute:()=>d1,timeMinutes:()=>d2,timeMonday:()=>pu,timeMondays:()=>pp,timeMonth:()=>pz,timeMonths:()=>pD,timeParse:()=>tu,timeSaturday:()=>ph,timeSaturdays:()=>pb,timeSecond:()=>dQ,timeSeconds:()=>d0,timeSunday:()=>pa,timeSundays:()=>pd,timeThursday:()=>pf,timeThursdays:()=>py,timeTickInterval:()=>pH,timeTicks:()=>pY,timeTuesday:()=>pc,timeTuesdays:()=>pv,timeWednesday:()=>pl,timeWednesdays:()=>pg,timeWeek:()=>pa,timeWeeks:()=>pd,timeYear:()=>p$,timeYears:()=>pB,timeout:()=>rv,timer:()=>rf,timerFlush:()=>rs,transition:()=>rz,transpose:()=>tX,tree:()=>hM,treemap:()=>hE,treemapBinary:()=>hN,treemapDice:()=>hl,treemapResquarify:()=>hP,treemapSlice:()=>hT,treemapSliceDice:()=>hC,treemapSquarify:()=>hS,tsv:()=>a0,tsvFormat:()=>o9,tsvFormatBody:()=>at,tsvFormatRow:()=>ae,tsvFormatRows:()=>an,tsvFormatValue:()=>ar,tsvParse:()=>o8,tsvParseRows:()=>o7,union:()=>ne,unixDay:()=>pr,unixDays:()=>pi,utcDay:()=>pn,utcDays:()=>pe,utcFormat:()=>tc,utcFriday:()=>pk,utcFridays:()=>pO,utcHour:()=>d8,utcHours:()=>d7,utcMillisecond:()=>dK,utcMilliseconds:()=>dJ,utcMinute:()=>d6,utcMinutes:()=>d3,utcMonday:()=>pw,utcMondays:()=>pN,utcMonth:()=>pL,utcMonths:()=>pI,utcParse:()=>tl,utcSaturday:()=>pS,utcSaturdays:()=>pj,utcSecond:()=>dQ,utcSeconds:()=>d0,utcSunday:()=>px,utcSundays:()=>pE,utcThursday:()=>pA,utcThursdays:()=>pR,utcTickInterval:()=>pG,utcTicks:()=>pq,utcTuesday:()=>pM,utcTuesdays:()=>pC,utcWednesday:()=>pT,utcWednesdays:()=>pP,utcWeek:()=>px,utcWeeks:()=>pE,utcYear:()=>pV,utcYears:()=>pU,variance:()=>C,window:()=>nL,xml:()=>a5,zip:()=>tJ,zoom:()=>b1,zoomIdentity:()=>bG,zoomTransform:()=>bY});let h=l(u),d=h.right,p=h.left,v=l(s).center,g=d;function y(t,n){if(!((n=+n)>=0))throw RangeError("invalid r");let e=t.length;if(!((e=Math.floor(e))>=0))throw RangeError("invalid length");if(!e||!n)return t;let r=M(n),i=t.slice();return r(t,i,0,e,1),r(i,t,0,e,1),r(t,i,0,e,1),t}let _=m(M),b=m(function(t){let n=M(t);return(t,e,r,i,o)=>{n(t,e,(r<<=2)+0,(i<<=2)+0,o<<=2),n(t,e,r+1,i+1,o),n(t,e,r+2,i+2,o),n(t,e,r+3,i+3,o)}});function m(t){return function(n,e,r=e){if(!((e=+e)>=0))throw RangeError("invalid rx");if(!((r=+r)>=0))throw RangeError("invalid ry");let{data:i,width:o,height:a}=n;if(!((o=Math.floor(o))>=0))throw RangeError("invalid width");if(!((a=Math.floor(void 0!==a?a:i.length/o))>=0))throw RangeError("invalid height");if(!o||!a||!e&&!r)return n;let u=e&&t(e),c=r&&t(r),l=i.slice();return u&&c?(x(u,l,i,o,a),x(u,i,l,o,a),x(u,l,i,o,a),w(c,i,l,o,a),w(c,l,i,o,a),w(c,i,l,o,a)):u?(x(u,i,l,o,a),x(u,l,i,o,a),x(u,i,l,o,a)):c&&(w(c,i,l,o,a),w(c,l,i,o,a),w(c,i,l,o,a)),n}}function x(t,n,e,r,i){for(let o=0,a=r*i;o<a;)t(n,e,o,o+=r,1)}function w(t,n,e,r,i){for(let o=0,a=r*i;o<r;++o)t(n,e,o,o+a,r)}function M(t){let n=Math.floor(t);if(n===t)return function(t){let n=2*t+1;return(e,r,i,o,a)=>{if(!((o-=a)>=i))return;let u=t*r[i],c=a*t;for(let t=i,n=i+c;t<n;t+=a)u+=r[Math.min(o,t)];for(let t=i,l=o;t<=l;t+=a)u+=r[Math.min(o,t+c)],e[t]=u/n,u-=r[Math.max(i,t-c)]}}(t);let e=t-n,r=2*t+1;return(t,i,o,a,u)=>{if(!((a-=u)>=o))return;let c=n*i[o],l=u*n,f=l+u;for(let t=o,n=o+l;t<n;t+=u)c+=i[Math.min(a,t)];for(let n=o,s=a;n<=s;n+=u)c+=i[Math.min(a,n+l)],t[n]=(c+e*(i[Math.max(o,n-f)]+i[Math.min(a,n+f)]))/r,c-=i[Math.max(o,n-l)]}}function T(t,n){let e=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&++e;else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(i=+i)>=i&&++e}return e}function A(t){return 0|t.length}function k(t){return!(t>0)}function S(t){return"object"!=typeof t||"length"in t?t:Array.from(t)}function E(...t){var n;let e="function"==typeof t[t.length-1]&&(n=t.pop(),t=>n(...t)),r=(t=t.map(S)).map(A),i=t.length-1,o=Array(i+1).fill(0),a=[];if(i<0||r.some(k))return a;for(;;){a.push(o.map((n,e)=>t[e][n]));let n=i;for(;++o[n]===r[n];){if(0===n)return e?a.map(e):a;o[n--]=0}}}function N(t,n){var e=0,r=0;return Float64Array.from(t,void 0===n?t=>e+=+t||0:i=>e+=+n(i,r++,t)||0)}function C(t,n){let e,r=0,i=0,o=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(e=n-i,i+=e/++r,o+=e*(n-i));else{let a=-1;for(let u of t)null!=(u=n(u,++a,t))&&(u=+u)>=u&&(e=u-i,i+=e/++r,o+=e*(u-i))}if(r>1)return o/(r-1)}function P(t,n){let e=C(t,n);return e?Math.sqrt(e):e}function R(t,n){let e,r;if(void 0===n)for(let n of t)null!=n&&(void 0===e?n>=n&&(e=r=n):(e>n&&(e=n),r<n&&(r=n)));else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(void 0===e?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}class O{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){let n=this._partials,e=0;for(let r=0;r<this._n&&r<32;r++){let i=n[r],o=t+i,a=Math.abs(t)<Math.abs(i)?t-(o-i):i-(o-t);a&&(n[e++]=a),t=o}return n[e]=t,this._n=e+1,this}valueOf(){let t=this._partials,n=this._n,e,r,i,o=0;if(n>0){for(o=t[--n];n>0&&(o=(e=o)+(r=t[--n]),!(i=r-(o-e))););n>0&&(i<0&&t[n-1]<0||i>0&&t[n-1]>0)&&(e=o+(r=2*i),r==e-o&&(o=e))}return o}}function j(t,n){let e=new O;if(void 0===n)for(let n of t)(n=+n)&&e.add(n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&e.add(i)}return+e}function z(t,n){let e=new O,r=-1;return Float64Array.from(t,void 0===n?t=>e.add(+t||0):i=>e.add(+n(i,++r,t)||0))}class D extends Map{constructor(t,n=V){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(let[n,e]of t)this.set(n,e)}get(t){return super.get(I(this,t))}has(t){return super.has(I(this,t))}set(t,n){return super.set($(this,t),n)}delete(t){return super.delete(B(this,t))}}class L extends Set{constructor(t,n=V){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(let n of t)this.add(n)}has(t){return super.has(I(this,t))}add(t){return super.add($(this,t))}delete(t){return super.delete(B(this,t))}}function I({_intern:t,_key:n},e){let r=n(e);return t.has(r)?t.get(r):e}function $({_intern:t,_key:n},e){let r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}function B({_intern:t,_key:n},e){let r=n(e);return t.has(r)&&(e=t.get(r),t.delete(r)),e}function V(t){return null!==t&&"object"==typeof t?t.valueOf():t}function U(t){return t}function F(t,...n){return Q(t,U,U,n)}function q(t,...n){return Q(t,Array.from,U,n)}function G(t,n){for(let e=1,r=n.length;e<r;++e)t=t.flatMap(t=>t.pop().map(([n,e])=>[...t,n,e]));return t}function Y(t,...n){return G(q(t,...n),n)}function H(t,n,...e){return G(Z(t,n,...e),e)}function W(t,n,...e){return Q(t,U,n,e)}function Z(t,n,...e){return Q(t,Array.from,n,e)}function X(t,...n){return Q(t,U,J,n)}function K(t,...n){return Q(t,Array.from,J,n)}function J(t){if(1!==t.length)throw Error("duplicate key");return t[0]}function Q(t,n,e,r){return function t(i,o){if(o>=r.length)return e(i);let a=new D,u=r[o++],c=-1;for(let t of i){let n=u(t,++c,i),e=a.get(n);e?e.push(t):a.set(n,[t])}for(let[n,e]of a)a.set(n,t(e,o));return n(a)}(t,0)}function tt(t,n){return Array.from(n,n=>t[n])}function tn(t,...n){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");t=Array.from(t);let[e]=n;if(e&&2!==e.length||n.length>1){let r=Uint32Array.from(t,(t,n)=>n);return n.length>1?(n=n.map(n=>t.map(n)),r.sort((t,e)=>{for(let r of n){let n=tr(r[t],r[e]);if(n)return n}})):(e=t.map(e),r.sort((t,n)=>tr(e[t],e[n]))),tt(t,r)}return t.sort(te(e))}function te(t=u){if(t===u)return tr;if("function"!=typeof t)throw TypeError("compare is not a function");return(n,e)=>{let r=t(n,e);return r||0===r?r:(0===t(e,e))-(0===t(n,n))}}function tr(t,n){return(null==t||!(t>=t))-(null==n||!(n>=n))||(t<n?-1:t>n?1:0)}function ti(t,n,e){return(2!==n.length?tn(W(t,n,e),([t,n],[e,r])=>u(n,r)||u(t,e)):tn(F(t,e),([t,e],[r,i])=>n(e,i)||u(t,r))).map(([t])=>t)}var to,ta,tu,tc,tl,tf=Array.prototype,ts=tf.slice;tf.map;let th=Math.sqrt(50),td=Math.sqrt(10),tp=Math.sqrt(2);function tv(t,n,e){let r,i,o;let a=(n-t)/Math.max(0,e),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=th?10:c>=td?5:c>=tp?2:1;return(u<0?(r=Math.round(t*(o=Math.pow(10,-u)/l)),i=Math.round(n*o),r/o<t&&++r,i/o>n&&--i,o=-o):(r=Math.round(t/(o=Math.pow(10,u)*l)),i=Math.round(n/o),r*o<t&&++r,i*o>n&&--i),i<r&&.5<=e&&e<2)?tv(t,n,2*e):[r,i,o]}function tg(t,n,e){if(n=+n,t=+t,!((e=+e)>0))return[];if(t===n)return[t];let r=n<t,[i,o,a]=r?tv(n,t,e):tv(t,n,e);if(!(o>=i))return[];let u=o-i+1,c=Array(u);if(r){if(a<0)for(let t=0;t<u;++t)c[t]=-((o-t)/a);else for(let t=0;t<u;++t)c[t]=(o-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((i+t)/a);else for(let t=0;t<u;++t)c[t]=(i+t)*a;return c}function ty(t,n,e){return tv(t=+t,n=+n,e=+e)[2]}function t_(t,n,e){n=+n,t=+t,e=+e;let r=n<t,i=r?ty(n,t,e):ty(t,n,e);return(r?-1:1)*(i<0?-(1/i):i)}function tb(t,n,e){let r;for(;;){let i=ty(t,n,e);if(i===r||0===i||!isFinite(i))return[t,n];i>0?(t=Math.floor(t/i)*i,n=Math.ceil(n/i)*i):i<0&&(t=Math.ceil(t*i)/i,n=Math.floor(n*i)/i),r=i}}function tm(t){return Math.max(1,Math.ceil(Math.log(T(t))/Math.LN2)+1)}function tx(){var t=U,n=R,e=tm;function r(r){Array.isArray(r)||(r=Array.from(r));var i,o,a,u=r.length,c=Array(u);for(i=0;i<u;++i)c[i]=t(r[i],i,r);var l=n(c),f=l[0],s=l[1],h=e(c,f,s);if(!Array.isArray(h)){let t=s,e=+h;if(n===R&&([f,s]=tb(f,s,e)),(h=tg(f,s,e))[0]<=f&&(a=ty(f,s,e)),h[h.length-1]>=s){if(t>=s&&n===R){let t=ty(f,s,e);isFinite(t)&&(t>0?s=(Math.floor(s/t)+1)*t:t<0&&(s=-((Math.ceil(-(s*t))+1)/t)))}else h.pop()}}for(var d=h.length,p=0,v=d;h[p]<=f;)++p;for(;h[v-1]>s;)--v;(p||v<d)&&(h=h.slice(p,v),d=v-p);var y,_=Array(d+1);for(i=0;i<=d;++i)(y=_[i]=[]).x0=i>0?h[i-1]:f,y.x1=i<d?h[i]:s;if(isFinite(a)){if(a>0)for(i=0;i<u;++i)null!=(o=c[i])&&f<=o&&o<=s&&_[Math.min(d,Math.floor((o-f)/a))].push(r[i]);else if(a<0){for(i=0;i<u;++i)if(null!=(o=c[i])&&f<=o&&o<=s){let t=Math.floor((f-o)*a);_[Math.min(d,t+(h[t]<=o))].push(r[i])}}}else for(i=0;i<u;++i)null!=(o=c[i])&&f<=o&&o<=s&&_[g(h,o,0,d)].push(r[i]);return _}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:()=>n,r):t},r.domain=function(t){var e;return arguments.length?(n="function"==typeof t?t:(e=[t[0],t[1]],()=>e),r):n},r.thresholds=function(t){var n;return arguments.length?(e="function"==typeof t?t:(n=Array.isArray(t)?ts.call(t):t,()=>n),r):e},r}function tw(t,n){let e;if(void 0===n)for(let n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e<i||void 0===e&&i>=i)&&(e=i)}return e}function tM(t,n){let e;let r=-1,i=-1;if(void 0===n)for(let n of t)++i,null!=n&&(e<n||void 0===e&&n>=n)&&(e=n,r=i);else for(let o of t)null!=(o=n(o,++i,t))&&(e<o||void 0===e&&o>=o)&&(e=o,r=i);return r}function tT(t,n){let e;if(void 0===n)for(let n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e>i||void 0===e&&i>=i)&&(e=i)}return e}function tA(t,n){let e;let r=-1,i=-1;if(void 0===n)for(let n of t)++i,null!=n&&(e>n||void 0===e&&n>=n)&&(e=n,r=i);else for(let o of t)null!=(o=n(o,++i,t))&&(e>o||void 0===e&&o>=o)&&(e=o,r=i);return r}function tk(t,n,e=0,r=1/0,i){if(n=Math.floor(n),e=Math.floor(Math.max(0,e)),r=Math.floor(Math.min(t.length-1,r)),!(e<=n&&n<=r))return t;for(i=void 0===i?tr:te(i);r>e;){if(r-e>600){let o=r-e+1,a=n-e+1,u=Math.log(o),c=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*c*(o-c)/o)*(a-o/2<0?-1:1),f=Math.max(e,Math.floor(n-a*c/o+l)),s=Math.min(r,Math.floor(n+(o-a)*c/o+l));tk(t,n,f,s,i)}let o=t[n],a=e,u=r;for(tS(t,e,n),i(t[r],o)>0&&tS(t,e,r);a<u;){for(tS(t,a,u),++a,--u;0>i(t[a],o);)++a;for(;i(t[u],o)>0;)--u}0===i(t[e],o)?tS(t,e,u):tS(t,++u,r),u<=n&&(e=u+1),n<=u&&(r=u-1)}return t}function tS(t,n,e){let r=t[n];t[n]=t[e],t[e]=r}function tE(t,n=u){let e;let r=!1;if(1===n.length){let i;for(let o of t){let t=n(o);(r?u(t,i)>0:0===u(t,t))&&(e=o,i=t,r=!0)}}else for(let i of t)(r?n(i,e)>0:0===n(i,i))&&(e=i,r=!0);return e}function tN(t,n,e){if(!(!(r=(t=Float64Array.from(function*(t,n){if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(yield n);else{let e=-1;for(let r of t)null!=(r=n(r,++e,t))&&(r=+r)>=r&&(yield r)}}(t,e))).length)||isNaN(n=+n))){if(n<=0||r<2)return tT(t);if(n>=1)return tw(t);var r,i=(r-1)*n,o=Math.floor(i),a=tw(tk(t,o).subarray(0,o+1));return a+(tT(t.subarray(o+1))-a)*(i-o)}}function tC(t,n,e=s){if(!(!(r=t.length)||isNaN(n=+n))){if(n<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),a=+e(t[o],o,t);return a+(+e(t[o+1],o+1,t)-a)*(i-o)}}function tP(t,n,e=s){if(!isNaN(n=+n)){if(r=Float64Array.from(t,(n,r)=>s(e(t[r],r,t))),n<=0)return tA(r);if(n>=1)return tM(r);var r,i=Uint32Array.from(t,(t,n)=>n),o=r.length-1,a=Math.floor(o*n);return tk(i,a,0,o,(t,n)=>tr(r[t],r[n])),(a=tE(i.subarray(0,a+1),t=>r[t]))>=0?a:-1}}function tR(t,n,e){let r=T(t),i=tN(t,.75)-tN(t,.25);return r&&i?Math.ceil((e-n)/(2*i*Math.pow(r,-1/3))):1}function tO(t,n,e){let r=T(t),i=P(t);return r&&i?Math.ceil((e-n)*Math.cbrt(r)/(3.49*i)):1}function tj(t,n){let e=0,r=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(++e,r+=n);else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}function tz(t,n){return tN(t,.5,n)}function tD(t,n){return tP(t,.5,n)}function tL(t){return Array.from(function*(t){for(let n of t)yield*n}(t))}function tI(t,n){let e;let r=new D;if(void 0===n)for(let n of t)null!=n&&n>=n&&r.set(n,(r.get(n)||0)+1);else{let e=-1;for(let i of t)null!=(i=n(i,++e,t))&&i>=i&&r.set(i,(r.get(i)||0)+1)}let i=0;for(let[t,n]of r)n>i&&(i=n,e=t);return e}function t$(t,n=tB){let e;let r=[],i=!1;for(let o of t)i&&r.push(n(e,o)),e=o,i=!0;return r}function tB(t,n){return[t,n]}function tV(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=Array(i);++r<i;)o[r]=t+r*e;return o}function tU(t,n=u){let e,r;if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");let i=Array.from(t),o=new Float64Array(i.length);2!==n.length&&(i=i.map(n),n=u);let a=(t,e)=>n(i[t],i[e]);return(t=Uint32Array.from(i,(t,n)=>n)).sort(n===u?(t,n)=>tr(i[t],i[n]):te(a)),t.forEach((t,n)=>{let i=a(t,void 0===e?t:e);i>=0?((void 0===e||i>0)&&(e=t,r=n),o[t]=r):o[t]=NaN}),o}function tF(t,n=u){let e;let r=!1;if(1===n.length){let i;for(let o of t){let t=n(o);(r?0>u(t,i):0===u(t,t))&&(e=o,i=t,r=!0)}}else for(let i of t)(r?0>n(i,e):0===n(i,i))&&(e=i,r=!0);return e}function tq(t,n=u){let e;if(1===n.length)return tA(t,n);let r=-1,i=-1;for(let o of t)++i,(r<0?0===n(o,o):0>n(o,e))&&(e=o,r=i);return r}function tG(t,n=u){let e;if(1===n.length)return tM(t,n);let r=-1,i=-1;for(let o of t)++i,(r<0?0===n(o,o):n(o,e)>0)&&(e=o,r=i);return r}function tY(t,n){let e=tq(t,n);return e<0?void 0:e}let tH=tW(Math.random);function tW(t){return function(n,e=0,r=n.length){let i=r-(e=+e);for(;i;){let r=t()*i--|0,o=n[i+e];n[i+e]=n[r+e],n[r+e]=o}return n}}function tZ(t,n){let e=0;if(void 0===n)for(let n of t)(n=+n)&&(e+=n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&(e+=i)}return e}function tX(t){if(!(i=t.length))return[];for(var n=-1,e=tT(t,tK),r=Array(e);++n<e;)for(var i,o=-1,a=r[n]=Array(i);++o<i;)a[o]=t[o][n];return r}function tK(t){return t.length}function tJ(){return tX(arguments)}function tQ(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(!n(r,++e,t))return!1;return!0}function t0(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(n(r,++e,t))return!0;return!1}function t1(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=[],r=-1;for(let i of t)n(i,++r,t)&&e.push(i);return e}function t2(t,n){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");if("function"!=typeof n)throw TypeError("mapper is not a function");return Array.from(t,(e,r)=>n(e,r,t))}function t6(t,n,e){if("function"!=typeof n)throw TypeError("reducer is not a function");let r=t[Symbol.iterator](),i,o,a=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++a}for(;{done:i,value:o}=r.next(),!i;)e=n(e,o,++a,t);return e}function t3(t){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");return Array.from(t).reverse()}function t5(t,...n){for(let e of(t=new L(t),n))for(let n of e)t.delete(n);return t}function t4(t,n){let e=n[Symbol.iterator](),r=new L;for(let n of t){let t,i;if(r.has(n))return!1;for(;({value:t,done:i}=e.next())&&!i;){if(Object.is(n,t))return!1;r.add(t)}}return!0}function t8(t,...n){t=new L(t),n=n.map(t7);n:for(let e of t)for(let r of n)if(!r.has(e)){t.delete(e);continue n}return t}function t7(t){return t instanceof L?t:new L(t)}function t9(t,n){let e=t[Symbol.iterator](),r=new Set;for(let t of n){let n,i;let o=nt(t);if(!r.has(o))for(;{value:n,done:i}=e.next();){if(i)return!1;let t=nt(n);if(r.add(t),Object.is(o,t))break}}return!0}function nt(t){return null!==t&&"object"==typeof t?t.valueOf():t}function nn(t,n){return t9(n,t)}function ne(...t){let n=new L;for(let e of t)for(let t of e)n.add(t);return n}function nr(t){return t}function ni(t){return"translate("+t+",0)"}function no(t){return"translate(0,"+t+")"}function na(){return!this.__axis}function nu(t,n){var e=[],r=null,i=null,o=6,a=6,u=3,c=.5,l=1===t||4===t?-1:1,f=4===t||2===t?"x":"y",s=1===t||3===t?ni:no;function h(h){var d=null==r?n.ticks?n.ticks.apply(n,e):n.domain():r,p=null==i?n.tickFormat?n.tickFormat.apply(n,e):nr:i,v=Math.max(o,0)+u,g=n.range(),y=+g[0]+c,_=+g[g.length-1]+c,b=(n.bandwidth?function(t,n){return n=Math.max(0,t.bandwidth()-2*n)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}:function(t){return n=>+t(n)})(n.copy(),c),m=h.selection?h.selection():h,x=m.selectAll(".domain").data([null]),w=m.selectAll(".tick").data(d,n).order(),M=w.exit(),T=w.enter().append("g").attr("class","tick"),A=w.select("line"),k=w.select("text");x=x.merge(x.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),w=w.merge(T),A=A.merge(T.append("line").attr("stroke","currentColor").attr(f+"2",l*o)),k=k.merge(T.append("text").attr("fill","currentColor").attr(f,l*v).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),h!==m&&(x=x.transition(h),w=w.transition(h),A=A.transition(h),k=k.transition(h),M=M.transition(h).attr("opacity",1e-6).attr("transform",function(t){return isFinite(t=b(t))?s(t+c):this.getAttribute("transform")}),T.attr("opacity",1e-6).attr("transform",function(t){var n=this.parentNode.__axis;return s((n&&isFinite(n=n(t))?n:b(t))+c)})),M.remove(),x.attr("d",4===t||2===t?a?"M"+l*a+","+y+"H"+c+"V"+_+"H"+l*a:"M"+c+","+y+"V"+_:a?"M"+y+","+l*a+"V"+c+"H"+_+"V"+l*a:"M"+y+","+c+"H"+_),w.attr("opacity",1).attr("transform",function(t){return s(b(t)+c)}),A.attr(f+"2",l*o),k.attr(f,l*v).text(p),m.filter(na).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),m.each(function(){this.__axis=b})}return h.scale=function(t){return arguments.length?(n=t,h):n},h.ticks=function(){return e=Array.from(arguments),h},h.tickArguments=function(t){return arguments.length?(e=null==t?[]:Array.from(t),h):e.slice()},h.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),h):r&&r.slice()},h.tickFormat=function(t){return arguments.length?(i=t,h):i},h.tickSize=function(t){return arguments.length?(o=a=+t,h):o},h.tickSizeInner=function(t){return arguments.length?(o=+t,h):o},h.tickSizeOuter=function(t){return arguments.length?(a=+t,h):a},h.tickPadding=function(t){return arguments.length?(u=+t,h):u},h.offset=function(t){return arguments.length?(c=+t,h):c},h}function nc(t){return nu(1,t)}function nl(t){return nu(2,t)}function nf(t){return nu(3,t)}function ns(t){return nu(4,t)}var nh={value:()=>{}};function nd(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new np(r)}function np(t){this._=t}function nv(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=nh,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}np.prototype=nd.prototype={constructor:np,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,a=i.length;if(arguments.length<2){for(;++o<a;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<a;)if(e=(t=i[o]).type)r[e]=nv(r[e],t.name,n);else if(null==n)for(e in r)r[e]=nv(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new np(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};let ng=nd;function ny(){}function n_(t){return null==t?ny:function(){return this.querySelector(t)}}function nb(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function nm(){return[]}function nx(t){return null==t?nm:function(){return this.querySelectorAll(t)}}function nw(t){return function(){return this.matches(t)}}function nM(t){return function(n){return n.matches(t)}}var nT=Array.prototype.find;function nA(){return this.firstElementChild}var nk=Array.prototype.filter;function nS(){return Array.from(this.children)}function nE(t){return Array(t.length)}function nN(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function nC(t,n,e,r,i,o){for(var a,u=0,c=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new nN(t,o[u]);for(;u<c;++u)(a=n[u])&&(i[u]=a)}function nP(t,n,e,r,i,o,a){var u,c,l,f=new Map,s=n.length,h=o.length,d=Array(s);for(u=0;u<s;++u)(c=n[u])&&(d[u]=l=a.call(c,c.__data__,u,n)+"",f.has(l)?i[u]=c:f.set(l,c));for(u=0;u<h;++u)l=a.call(t,o[u],u,o)+"",(c=f.get(l))?(r[u]=c,c.__data__=o[u],f.delete(l)):e[u]=new nN(t,o[u]);for(u=0;u<s;++u)(c=n[u])&&f.get(d[u])===c&&(i[u]=c)}function nR(t){return t.__data__}function nO(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}nN.prototype={constructor:nN,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var nj="http://www.w3.org/1999/xhtml";let nz={svg:"http://www.w3.org/2000/svg",xhtml:nj,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function nD(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),nz.hasOwnProperty(n)?{space:nz[n],local:t}:t}function nL(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function nI(t,n){return t.style.getPropertyValue(n)||nL(t).getComputedStyle(t,null).getPropertyValue(n)}function n$(t){return t.trim().split(/^|\s+/)}function nB(t){return t.classList||new nV(t)}function nV(t){this._node=t,this._names=n$(t.getAttribute("class")||"")}function nU(t,n){for(var e=nB(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function nF(t,n){for(var e=nB(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function nq(){this.textContent=""}function nG(){this.innerHTML=""}function nY(){this.nextSibling&&this.parentNode.appendChild(this)}function nH(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function nW(t){var n=nD(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===nj&&n.documentElement.namespaceURI===nj?n.createElement(t):n.createElementNS(e,t)}})(n)}function nZ(){return null}function nX(){var t=this.parentNode;t&&t.removeChild(this)}function nK(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function nJ(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function nQ(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function n0(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function n1(t,n,e){var r=nL(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}nV.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var n2=[null];function n6(t,n){this._groups=t,this._parents=n}function n3(){return new n6([[document.documentElement]],n2)}n6.prototype=n3.prototype={constructor:n6,select:function(t){"function"!=typeof t&&(t=n_(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,a,u=n[i],c=u.length,l=r[i]=Array(c),f=0;f<c;++f)(o=u[f])&&(a=t.call(o,o.__data__,f,u))&&("__data__"in o&&(a.__data__=o.__data__),l[f]=a);return new n6(r,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){return nb(n.apply(this,arguments))}}else t=nx(t);for(var e=this._groups,r=e.length,i=[],o=[],a=0;a<r;++a)for(var u,c=e[a],l=c.length,f=0;f<l;++f)(u=c[f])&&(i.push(t.call(u,u.__data__,f,c)),o.push(u));return new n6(i,o)},selectChild:function(t){var n;return this.select(null==t?nA:(n="function"==typeof t?t:nM(t),function(){return nT.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?nS:(n="function"==typeof t?t:nM(t),function(){return nk.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=nw(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,c=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&c.push(o);return new n6(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,nR);var e=n?nP:nC,r=this._parents,i=this._groups;"function"!=typeof t&&(_=t,t=function(){return _});for(var o=i.length,a=Array(o),u=Array(o),c=Array(o),l=0;l<o;++l){var f=r[l],s=i[l],h=s.length,d="object"==typeof(y=t.call(f,f&&f.__data__,l,r))&&"length"in y?y:Array.from(y),p=d.length,v=u[l]=Array(p),g=a[l]=Array(p);e(f,s,v,g,c[l]=Array(h),d,n);for(var y,_,b,m,x=0,w=0;x<p;++x)if(b=v[x]){for(x>=w&&(w=x+1);!(m=g[w])&&++w<p;);b._next=m||null}}return(a=new n6(a,r))._enter=u,a._exit=c,a},enter:function(){return new n6(this._enter||this._groups.map(nE),this._parents)},exit:function(){return new n6(this._exit||this._groups.map(nE),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,a=Math.min(i,o),u=Array(i),c=0;c<a;++c)for(var l,f=e[c],s=r[c],h=f.length,d=u[c]=Array(h),p=0;p<h;++p)(l=f[p]||s[p])&&(d[p]=l);for(;c<i;++c)u[c]=e[c];return new n6(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=nO);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var a,u=e[o],c=u.length,l=i[o]=Array(c),f=0;f<c;++f)(a=u[f])&&(l[f]=a);l.sort(n)}return new n6(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e=nD(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):nI(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=n$(t+"");if(arguments.length<2){for(var r=nB(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?nU:nF)(this,t)}}:n?function(t){return function(){nU(this,t)}}:function(t){return function(){nF(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?nq:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?nG:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(nY)},lower:function(){return this.each(nH)},append:function(t){var n="function"==typeof t?t:nW(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:nW(t),r=null==n?nZ:"function"==typeof n?n:n_(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(nX)},clone:function(t){return this.select(t?nJ:nK)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var c,l=0,f=u.length;l<f;++l)for(r=0,c=u[l];r<a;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value}return}for(r=0,u=n?n0:nQ;r<a;++r)this.each(u(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return n1(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return n1(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let n5=n3;function n4(t){return"string"==typeof t?new n6([[document.querySelector(t)]],[document.documentElement]):new n6([[t]],n2)}let n8={passive:!1},n7={capture:!0,passive:!1};function n9(t){t.stopImmediatePropagation()}function et(t){t.preventDefault(),t.stopImmediatePropagation()}function en(t){var n=t.document.documentElement,e=n4(t).on("dragstart.drag",et,n7);"onselectstart"in n?e.on("selectstart.drag",et,n7):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function ee(t,n){var e=t.document.documentElement,r=n4(t).on("dragstart.drag",null);n&&(r.on("click.drag",et,n7),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}function er(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function ei(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function eo(){}var ea="\\s*([+-]?\\d+)\\s*",eu="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ec="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",el=/^#([0-9a-f]{3,8})$/,ef=RegExp(`^rgb\\(${ea},${ea},${ea}\\)$`),es=RegExp(`^rgb\\(${ec},${ec},${ec}\\)$`),eh=RegExp(`^rgba\\(${ea},${ea},${ea},${eu}\\)$`),ed=RegExp(`^rgba\\(${ec},${ec},${ec},${eu}\\)$`),ep=RegExp(`^hsl\\(${eu},${ec},${ec}\\)$`),ev=RegExp(`^hsla\\(${eu},${ec},${ec},${eu}\\)$`),eg={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ey(){return this.rgb().formatHex()}function e_(){return this.rgb().formatRgb()}function eb(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=el.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?em(n):3===e?new eT(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?ex(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?ex(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=ef.exec(t))?new eT(n[1],n[2],n[3],1):(n=es.exec(t))?new eT(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=eh.exec(t))?ex(n[1],n[2],n[3],n[4]):(n=ed.exec(t))?ex(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=ep.exec(t))?eC(n[1],n[2]/100,n[3]/100,1):(n=ev.exec(t))?eC(n[1],n[2]/100,n[3]/100,n[4]):eg.hasOwnProperty(t)?em(eg[t]):"transparent"===t?new eT(NaN,NaN,NaN,0):null}function em(t){return new eT(t>>16&255,t>>8&255,255&t,1)}function ex(t,n,e,r){return r<=0&&(t=n=e=NaN),new eT(t,n,e,r)}function ew(t){return(t instanceof eo||(t=eb(t)),t)?new eT((t=t.rgb()).r,t.g,t.b,t.opacity):new eT}function eM(t,n,e,r){return 1==arguments.length?ew(t):new eT(t,n,e,null==r?1:r)}function eT(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function eA(){return`#${eN(this.r)}${eN(this.g)}${eN(this.b)}`}function ek(){let t=eS(this.opacity);return`${1===t?"rgb(":"rgba("}${eE(this.r)}, ${eE(this.g)}, ${eE(this.b)}${1===t?")":`, ${t})`}`}function eS(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function eE(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function eN(t){return((t=eE(t))<16?"0":"")+t.toString(16)}function eC(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new eO(t,n,e,r)}function eP(t){if(t instanceof eO)return new eO(t.h,t.s,t.l,t.opacity);if(t instanceof eo||(t=eb(t)),!t)return new eO;if(t instanceof eO)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,c=(o+i)/2;return u?(a=n===o?(e-r)/u+(e<r)*6:e===o?(r-n)/u+2:(n-e)/u+4,u/=c<.5?o+i:2-o-i,a*=60):u=c>0&&c<1?0:a,new eO(a,u,c,t.opacity)}function eR(t,n,e,r){return 1==arguments.length?eP(t):new eO(t,n,e,null==r?1:r)}function eO(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function ej(t){return(t=(t||0)%360)<0?t+360:t}function ez(t){return Math.max(0,Math.min(1,t||0))}function eD(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function eL(t,n,e,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*n+(4-6*o+3*a)*e+(1+3*t+3*o-3*a)*r+a*i)/6}function eI(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<n-1?t[r+2]:2*o-i;return eL((e-r/n)*n,a,i,o,u)}}function e$(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],a=t[(r+1)%n],u=t[(r+2)%n];return eL((e-r/n)*n,i,o,a,u)}}er(eo,eb,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ey,formatHex:ey,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return eP(this).formatHsl()},formatRgb:e_,toString:e_}),er(eT,eM,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new eT(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new eT(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new eT(eE(this.r),eE(this.g),eE(this.b),eS(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:eA,formatHex:eA,formatHex8:function(){return`#${eN(this.r)}${eN(this.g)}${eN(this.b)}${eN((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ek,toString:ek})),er(eO,eR,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new eO(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new eO(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new eT(eD(t>=240?t-240:t+120,i,r),eD(t,i,r),eD(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new eO(ej(this.h),ez(this.s),ez(this.l),eS(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=eS(this.opacity);return`${1===t?"hsl(":"hsla("}${ej(this.h)}, ${100*ez(this.s)}%, ${100*ez(this.l)}%${1===t?")":`, ${t})`}`}}));let eB=t=>()=>t;function eV(t,n){return function(e){return t+e*n}}function eU(t,n){var e=n-t;return e?eV(t,e>180||e<-180?e-360*Math.round(e/360):e):eB(isNaN(t)?n:t)}function eF(t,n){var e=n-t;return e?eV(t,e):eB(isNaN(t)?n:t)}let eq=function t(n){var e,r=1==(e=+(e=n))?eF:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):eB(isNaN(t)?n:t)};function i(t,n){var e=r((t=eM(t)).r,(n=eM(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),a=eF(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return i.gamma=t,i}(1);function eG(t){return function(n){var e,r,i=n.length,o=Array(i),a=Array(i),u=Array(i);for(e=0;e<i;++e)r=eM(n[e]),o[e]=r.r||0,a[e]=r.g||0,u[e]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}var eY=eG(eI),eH=eG(e$);function eW(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}}function eZ(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function eX(t,n){return(eZ(n)?eW:eK)(t,n)}function eK(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=Array(i),a=Array(r);for(e=0;e<i;++e)o[e]=e3(t[e],n[e]);for(;e<r;++e)a[e]=n[e];return function(t){for(e=0;e<i;++e)a[e]=o[e](t);return a}}function eJ(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function eQ(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function e0(t,n){var e,r={},i={};for(e in(null===t||"object"!=typeof t)&&(t={}),(null===n||"object"!=typeof n)&&(n={}),n)e in t?r[e]=e3(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var e1=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,e2=RegExp(e1.source,"g");function e6(t,n){var e,r,i,o,a,u=e1.lastIndex=e2.lastIndex=0,c=-1,l=[],f=[];for(t+="",n+="";(i=e1.exec(t))&&(o=e2.exec(n));)(a=o.index)>u&&(a=n.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(i=i[0])===(o=o[0])?l[c]?l[c]+=o:l[++c]=o:(l[++c]=null,f.push({i:c,x:eQ(i,o)})),u=e2.lastIndex;return u<n.length&&(a=n.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?f[0]?(e=f[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=f.length,function(t){for(var e,r=0;r<n;++r)l[(e=f[r]).i]=e.x(t);return l.join("")})}function e3(t,n){var e,r=typeof n;return null==n||"boolean"===r?eB(n):("number"===r?eQ:"string"===r?(e=eb(n))?(n=e,eq):e6:n instanceof eb?eq:n instanceof Date?eJ:eZ(n)?eW:Array.isArray(n)?eK:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?e0:eQ)(t,n)}function e5(t){let n;for(;n=t.sourceEvent;)t=n;return t}function e4(t,n){if(t=e5(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}var e8,e7,e9=0,rt=0,rn=0,re=0,rr=0,ri=0,ro="object"==typeof performance&&performance.now?performance:Date,ra=function(t){setTimeout(t,17)};function ru(){return rr||(ra(rc),rr=ro.now()+ri)}function rc(){rr=0}function rl(){this._call=this._time=this._next=null}function rf(t,n,e){var r=new rl;return r.restart(t,n,e),r}function rs(){ru(),++e9;for(var t,n=e8;n;)(t=rr-n._time)>=0&&n._call.call(void 0,t),n=n._next;--e9}function rh(){rr=(re=ro.now())+ri,e9=rt=0;try{rs()}finally{e9=0,function(){for(var t,n,e=e8,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:e8=n);e7=t,rp(r)}(),rr=0}}function rd(){var t=ro.now(),n=t-re;n>1e3&&(ri-=n,re=t)}function rp(t){!e9&&(rt&&(rt=clearTimeout(rt)),t-rr>24?(t<1/0&&(rt=setTimeout(rh,t-ro.now()-ri)),rn&&(rn=clearInterval(rn))):(rn||(re=ro.now(),rn=setInterval(rd,1e3)),e9=1,ra(rh)))}function rv(t,n,e){var r=new rl;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}rl.prototype=rf.prototype={constructor:rl,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?ru():+e)+(null==n?0:+n),this._next||e7===this||(e7?e7._next=this:e8=this,e7=this),this._call=t,this._time=e,rp()},stop:function(){this._call&&(this._call=null,this._time=1/0,rp())}};var rg=ng("start","end","cancel","interrupt"),ry=[];function r_(t,n,e,r,i,o){var a=t.__transition;if(a){if(e in a)return}else t.__transition={};(function(t,n,e){var r,i=t.__transition;function o(c){var l,f,s,h;if(1!==e.state)return u();for(l in i)if((h=i[l]).name===e.name){if(3===h.state)return rv(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[l]):+l<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[l])}if(rv(function(){3===e.state&&(e.state=4,e.timer.restart(a,e.delay,e.time),a(c))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(l=0,e.state=3,r=Array(s=e.tween.length),f=-1;l<s;++l)(h=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++f]=h);r.length=f+1}}function a(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(u),e.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),u())}function u(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=rf(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)})(t,e,{name:n,index:r,group:i,on:rg,tween:ry,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function rb(t,n){var e=rx(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function rm(t,n){var e=rx(t,n);if(e.state>3)throw Error("too late; already running");return e}function rx(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function rw(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){a=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}a&&delete t.__transition}}var rM=180/Math.PI,rT={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function rA(t,n,e,r,i,o){var a,u,c;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,c/=u),t*r<n*e&&(t=-t,n=-n,c=-c,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*rM,skewX:Math.atan(c)*rM,scaleX:a,scaleY:u}}function rk(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,c,l,f,s=[],h=[];return o=t(o),a=t(a),function(t,r,i,o,a,u){if(t!==i||r!==o){var c=a.push("translate(",null,n,null,e);u.push({i:c-4,x:eQ(t,i)},{i:c-2,x:eQ(r,o)})}else(i||o)&&a.push("translate("+i+n+o+e)}(o.translateX,o.translateY,a.translateX,a.translateY,s,h),(u=o.rotate)!==(c=a.rotate)?(u-c>180?c+=360:c-u>180&&(u+=360),h.push({i:s.push(i(s)+"rotate(",null,r)-2,x:eQ(u,c)})):c&&s.push(i(s)+"rotate("+c+r),(l=o.skewX)!==(f=a.skewX)?h.push({i:s.push(i(s)+"skewX(",null,r)-2,x:eQ(l,f)}):f&&s.push(i(s)+"skewX("+f+r),function(t,n,e,r,o,a){if(t!==e||n!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:eQ(t,e)},{i:u-2,x:eQ(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,s,h),o=a=null,function(t){for(var n,e=-1,r=h.length;++e<r;)s[(n=h[e]).i]=n.x(t);return s.join("")}}}var rS=rk(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?rT:rA(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),rE=rk(function(t){return null==t?rT:(ce||(ce=document.createElementNS("http://www.w3.org/2000/svg","g")),ce.setAttribute("transform",t),t=ce.transform.baseVal.consolidate())?rA((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):rT},", ",")",")");function rN(t,n,e){var r=t._id;return t.each(function(){var t=rm(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return rx(t,r).value[n]}}function rC(t,n){var e;return("number"==typeof n?eQ:n instanceof eb?eq:(e=eb(n))?(n=e,eq):e6)(t,n)}var rP=n5.prototype.constructor;function rR(t){return function(){this.style.removeProperty(t)}}var rO=0;function rj(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function rz(t){return n5().transition(t)}var rD=n5.prototype;function rL(t){return t*t*t}function rI(t){return--t*t*t+1}function r$(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}rj.prototype=rz.prototype={constructor:rj,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=n_(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,c,l=r[a],f=l.length,s=o[a]=Array(f),h=0;h<f;++h)(u=l[h])&&(c=t.call(u,u.__data__,h,l))&&("__data__"in u&&(c.__data__=u.__data__),s[h]=c,r_(s[h],n,e,h,s,rx(u,e)));return new rj(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=nx(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var c,l=r[u],f=l.length,s=0;s<f;++s)if(c=l[s]){for(var h,d=t.call(c,c.__data__,s,l),p=rx(c,e),v=0,g=d.length;v<g;++v)(h=d[v])&&r_(h,n,e,v,d,p);o.push(d),a.push(c)}return new rj(o,a,n,e)},selectChild:rD.selectChild,selectChildren:rD.selectChildren,filter:function(t){"function"!=typeof t&&(t=nw(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,c=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&c.push(o);return new rj(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var c,l=n[u],f=e[u],s=l.length,h=a[u]=Array(s),d=0;d<s;++d)(c=l[d]||f[d])&&(h[d]=c);for(;u<r;++u)a[u]=n[u];return new rj(a,this._parents,this._name,this._id)},selection:function(){return new rP(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++rO,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],c=u.length,l=0;l<c;++l)if(a=u[l]){var f=rx(a,n);r_(a,t,e,l,u,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new rj(r,this._parents,t,e)},call:rD.call,nodes:rD.nodes,node:rD.node,size:rD.size,empty:rD.empty,each:rD.each,on:function(t,n){var e,r,i,o=this._id;return arguments.length<2?rx(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?rb:rm,function(){var a=i(this,o),u=a.on;u!==e&&(r=(e=u).copy()).on(t,n),a.on=r}))},attr:function(t,n){var e=nD(t),r="transform"===e?rE:rC;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var a,u,c=e(this);return null==c?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=c+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,c))}}:function(t,n,e){var r,i,o;return function(){var a,u,c=e(this);return null==c?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=c+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,c))}})(e,r,rN(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=n(r=a,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=n(r=a,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=nD(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,a,u,c,l,f,s,h,d,p,v,g,y,_,b,m,x,w,M,T="transform"==(t+="")?rS:rC;return null==n?this.styleTween(t,(r=t,function(){var t=nI(this,r),n=(this.style.removeProperty(r),nI(this,r));return t===n?null:t===i&&n===o?a:a=T(i=t,o=n)})).on("end.style."+t,rR(t)):"function"==typeof n?this.styleTween(t,(u=t,c=rN(this,"style."+t,n),function(){var t=nI(this,u),n=c(this),e=n+"";return null==n&&(this.style.removeProperty(u),e=n=nI(this,u)),t===e?null:t===l&&e===f?s:(f=e,s=T(l=t,n))})).each((h=this._id,b="end."+(_="style."+(d=t)),function(){var t=rm(this,h),n=t.on,e=null==t.value[_]?y||(y=rR(d)):void 0;(n!==p||g!==e)&&(v=(p=n).copy()).on(b,g=e),t.on=v})):this.styleTween(t,(m=t,M=n+"",function(){var t=nI(this,m);return t===M?null:t===x?w:w=T(x=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=rN(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=rx(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=rm(this,t),o=i.tween;if(o!==e){r=e=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===n){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=rm(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:n,value:e},c=0,l=i.length;c<l;++c)if(i[c].name===n){i[c]=u;break}c===l&&i.push(u)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){rb(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){rb(this,t).delay=n}})(n,t)):rx(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){rm(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){rm(this,t).duration=n}})(n,t)):rx(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){rm(this,t).ease=n}}(n,t)):rx(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();rm(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,a){var u={value:a},c={value:function(){0==--i&&o()}};e.each(function(){var e=rm(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(c)),e.on=n}),0===i&&o()})},[Symbol.iterator]:rD[Symbol.iterator]};var rB={time:null,delay:0,duration:250,ease:r$};n5.prototype.interrupt=function(t){return this.each(function(){rw(this,t)})},n5.prototype.transition=function(t){var n,e;t instanceof rj?(n=t._id,t=t._name):(n=++rO,(e=rB).time=ru(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],c=u.length,l=0;l<c;++l)(a=u[l])&&r_(a,t,n,l,u,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(a,n));return new rj(r,this._parents,t,n)};var rV=[null];function rU(t,n){var e,r,i=t.__transition;if(i){for(r in n=null==n?null:n+"",i)if((e=i[r]).state>1&&e.name===n)return new rj([[t]],rV,n,+r)}return null}let rF=t=>()=>t;function rq(t,{sourceEvent:n,target:e,selection:r,mode:i,dispatch:o}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function rG(t){t.preventDefault(),t.stopImmediatePropagation()}var rY={name:"drag"},rH={name:"space"},rW={name:"handle"},rZ={name:"center"};let{abs:rX,max:rK,min:rJ}=Math;function rQ(t){return[+t[0],+t[1]]}function r0(t){return[rQ(t[0]),rQ(t[1])]}var r1={name:"x",handles:["w","e"].map(r9),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},r2={name:"y",handles:["n","s"].map(r9),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},r6={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(r9),input:function(t){return null==t?null:r0(t)},output:function(t){return t}},r3={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},r5={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},r4={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},r8={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},r7={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function r9(t){return{type:t}}function it(t){return!t.ctrlKey&&!t.button}function ie(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function ir(){return navigator.maxTouchPoints||"ontouchstart"in this}function ii(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function io(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function ia(){return il(r1)}function iu(){return il(r2)}function ic(){return il(r6)}function il(t){var n,e=ie,r=it,i=ir,o=!0,a=ng("start","brush","end"),u=6;function c(n){var e=n.property("__brush",v).selectAll(".overlay").data([r9("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",r3.overlay).merge(e).each(function(){var t=ii(this).extent;n4(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),n.selectAll(".selection").data([r9("selection")]).enter().append("rect").attr("class","selection").attr("cursor",r3.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=n.selectAll(".handle").data(t.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return r3[t.type]}),n.each(l).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",h).filter(i).on("touchstart.brush",h).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",p).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function l(){var t=n4(this),n=ii(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-u/2:n[0][0]-u/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-u/2:n[0][1]-u/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+u:u}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+u:u})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function f(t,n,e){var r=t.__brush.emitter;return!r||e&&r.clean?new s(t,n,e):r}function s(t,n,e){this.that=t,this.args=n,this.state=t.__brush,this.active=0,this.clean=e}function h(e){if((!n||e.touches)&&r.apply(this,arguments)){var i,a,u,c,s,h,d,p,v,g,y,_=this,b=e.target.__data__.type,m=(o&&e.metaKey?b="overlay":b)==="selection"?rY:o&&e.altKey?rZ:rW,x=t===r2?null:r8[b],w=t===r1?null:r7[b],M=ii(_),T=M.extent,A=M.selection,k=T[0][0],S=T[0][1],E=T[1][0],N=T[1][1],C=0,P=0,R=x&&w&&o&&e.shiftKey,O=Array.from(e.touches||[e],t=>{let n=t.identifier;return(t=e4(t,_)).point0=t.slice(),t.identifier=n,t});rw(_);var j=f(_,arguments,!0).beforestart();if("overlay"===b){A&&(v=!0);let n=[O[0],O[1]||O[0]];M.selection=A=[[i=t===r2?k:rJ(n[0][0],n[1][0]),u=t===r1?S:rJ(n[0][1],n[1][1])],[s=t===r2?E:rK(n[0][0],n[1][0]),d=t===r1?N:rK(n[0][1],n[1][1])]],O.length>1&&$(e)}else i=A[0][0],u=A[0][1],s=A[1][0],d=A[1][1];a=i,c=u,h=s,p=d;var z=n4(_).attr("pointer-events","none"),D=z.selectAll(".overlay").attr("cursor",r3[b]);if(e.touches)j.moved=I,j.ended=B;else{var L=n4(e.view).on("mousemove.brush",I,!0).on("mouseup.brush",B,!0);o&&L.on("keydown.brush",function(t){switch(t.keyCode){case 16:R=x&&w;break;case 18:m===rW&&(x&&(s=h-C*x,i=a+C*x),w&&(d=p-P*w,u=c+P*w),m=rZ,$(t));break;case 32:(m===rW||m===rZ)&&(x<0?s=h-C:x>0&&(i=a-C),w<0?d=p-P:w>0&&(u=c-P),m=rH,D.attr("cursor",r3.selection),$(t));break;default:return}rG(t)},!0).on("keyup.brush",function(t){switch(t.keyCode){case 16:R&&(g=y=R=!1,$(t));break;case 18:m===rZ&&(x<0?s=h:x>0&&(i=a),w<0?d=p:w>0&&(u=c),m=rW,$(t));break;case 32:m===rH&&(t.altKey?(x&&(s=h-C*x,i=a+C*x),w&&(d=p-P*w,u=c+P*w),m=rZ):(x<0?s=h:x>0&&(i=a),w<0?d=p:w>0&&(u=c),m=rW),D.attr("cursor",r3[b]),$(t));break;default:return}rG(t)},!0),en(e.view)}l.call(_),j.start(e,m.name)}function I(t){for(let n of t.changedTouches||[t])for(let t of O)t.identifier===n.identifier&&(t.cur=e4(n,_));if(R&&!g&&!y&&1===O.length){let t=O[0];rX(t.cur[0]-t[0])>rX(t.cur[1]-t[1])?y=!0:g=!0}for(let t of O)t.cur&&(t[0]=t.cur[0],t[1]=t.cur[1]);v=!0,rG(t),$(t)}function $(t){var n;let e=O[0],r=e.point0;switch(C=e[0]-r[0],P=e[1]-r[1],m){case rH:case rY:x&&(C=rK(k-i,rJ(E-s,C)),a=i+C,h=s+C),w&&(P=rK(S-u,rJ(N-d,P)),c=u+P,p=d+P);break;case rW:O[1]?(x&&(a=rK(k,rJ(E,O[0][0])),h=rK(k,rJ(E,O[1][0])),x=1),w&&(c=rK(S,rJ(N,O[0][1])),p=rK(S,rJ(N,O[1][1])),w=1)):(x<0?(C=rK(k-i,rJ(E-i,C)),a=i+C,h=s):x>0&&(C=rK(k-s,rJ(E-s,C)),a=i,h=s+C),w<0?(P=rK(S-u,rJ(N-u,P)),c=u+P,p=d):w>0&&(P=rK(S-d,rJ(N-d,P)),c=u,p=d+P));break;case rZ:x&&(a=rK(k,rJ(E,i-C*x)),h=rK(k,rJ(E,s+C*x))),w&&(c=rK(S,rJ(N,u-P*w)),p=rK(S,rJ(N,d+P*w)))}h<a&&(x*=-1,n=i,i=s,s=n,n=a,a=h,h=n,b in r5&&D.attr("cursor",r3[b=r5[b]])),p<c&&(w*=-1,n=u,u=d,d=n,n=c,c=p,p=n,b in r4&&D.attr("cursor",r3[b=r4[b]])),M.selection&&(A=M.selection),g&&(a=A[0][0],h=A[1][0]),y&&(c=A[0][1],p=A[1][1]),(A[0][0]!==a||A[0][1]!==c||A[1][0]!==h||A[1][1]!==p)&&(M.selection=[[a,c],[h,p]],l.call(_),j.brush(t,m.name))}function B(t){var e;if(function(t){t.stopImmediatePropagation()}(t),t.touches){if(t.touches.length)return;n&&clearTimeout(n),n=setTimeout(function(){n=null},500)}else ee(t.view,v),L.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);z.attr("pointer-events","all"),D.attr("cursor",r3.overlay),M.selection&&(A=M.selection),((e=A)[0][0]===e[1][0]||e[0][1]===e[1][1])&&(M.selection=null,l.call(_)),j.end(t,m.name)}}function d(t){f(this,arguments).moved(t)}function p(t){f(this,arguments).ended(t)}function v(){var n=this.__brush||{selection:null};return n.extent=r0(e.apply(this,arguments)),n.dim=t,n}return c.move=function(n,e,r){n.tween?n.on("start.brush",function(t){f(this,arguments).beforestart().start(t)}).on("interrupt.brush end.brush",function(t){f(this,arguments).end(t)}).tween("brush",function(){var n=this,r=n.__brush,i=f(n,arguments),o=r.selection,a=t.input("function"==typeof e?e.apply(this,arguments):e,r.extent),u=e3(o,a);function c(t){r.selection=1===t&&null===a?null:u(t),l.call(n),i.brush()}return null!==o&&null!==a?c:c(1)}):n.each(function(){var n=arguments,i=this.__brush,o=t.input("function"==typeof e?e.apply(this,n):e,i.extent),a=f(this,n).beforestart();rw(this),i.selection=null===o?null:o,l.call(this),a.start(r).brush(r).end(r)})},c.clear=function(t,n){c.move(t,null,n)},s.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(t,n){return this.starting?(this.starting=!1,this.emit("start",t,n)):this.emit("brush",t),this},brush:function(t,n){return this.emit("brush",t,n),this},end:function(t,n){return 0==--this.active&&(delete this.state.emitter,this.emit("end",t,n)),this},emit:function(n,e,r){var i=n4(this.that).datum();a.call(n,this.that,new rq(n,{sourceEvent:e,target:c,selection:t.output(this.state.selection),mode:r,dispatch:a}),i)}},c.extent=function(t){return arguments.length?(e="function"==typeof t?t:rF(r0(t)),c):e},c.filter=function(t){return arguments.length?(r="function"==typeof t?t:rF(!!t),c):r},c.touchable=function(t){return arguments.length?(i="function"==typeof t?t:rF(!!t),c):i},c.handleSize=function(t){return arguments.length?(u=+t,c):u},c.keyModifiers=function(t){return arguments.length?(o=!!t,c):o},c.on=function(){var t=a.on.apply(a,arguments);return t===a?c:t},c}var is=Math.abs,ih=Math.cos,id=Math.sin,ip=Math.PI,iv=ip/2,ig=2*ip,iy=Math.max;function i_(t,n){return Array.from({length:n-t},(n,e)=>t+e)}function ib(){return iw(!1,!1)}function im(){return iw(!1,!0)}function ix(){return iw(!0,!1)}function iw(t,n){var e=0,r=null,i=null,o=null;function a(a){var u,c=a.length,l=Array(c),f=i_(0,c),s=Array(c*c),h=Array(c),d=0;a=Float64Array.from({length:c*c},n?(t,n)=>a[n%c][n/c|0]:(t,n)=>a[n/c|0][n%c]);for(let n=0;n<c;++n){let e=0;for(let r=0;r<c;++r)e+=a[n*c+r]+t*a[r*c+n];d+=l[n]=e}u=(d=iy(0,ig-e*c)/d)?e:ig/c;{let n=0;for(let e of(r&&f.sort((t,n)=>r(l[t],l[n])),f)){let r=n;if(t){let t=i_(~c+1,c).filter(t=>t<0?a[~t*c+e]:a[e*c+t]);for(let r of(i&&t.sort((t,n)=>i(t<0?-a[~t*c+e]:a[e*c+t],n<0?-a[~n*c+e]:a[e*c+n])),t))r<0?(s[~r*c+e]||(s[~r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=a[~r*c+e]*d,value:a[~r*c+e]}:(s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=a[e*c+r]*d,value:a[e*c+r]};h[e]={index:e,startAngle:r,endAngle:n,value:l[e]}}else{let t=i_(0,c).filter(t=>a[e*c+t]||a[t*c+e]);for(let r of(i&&t.sort((t,n)=>i(a[e*c+t],a[e*c+n])),t)){let t;if(e<r?(t=s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=a[e*c+r]*d,value:a[e*c+r]}:((t=s[r*c+e]||(s[r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=a[e*c+r]*d,value:a[e*c+r]},e===r&&(t.source=t.target)),t.source&&t.target&&t.source.value<t.target.value){let n=t.source;t.source=t.target,t.target=n}}h[e]={index:e,startAngle:r,endAngle:n,value:l[e]}}n+=u}}return(s=Object.values(s)).groups=h,o?s.sort(o):s}return a.padAngle=function(t){return arguments.length?(e=iy(0,t),a):e},a.sortGroups=function(t){return arguments.length?(r=t,a):r},a.sortSubgroups=function(t){return arguments.length?(i=t,a):i},a.sortChords=function(t){return arguments.length?(null==t?o=null:(o=function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)})._=t,a):o&&o._},a}let iM=Math.PI,iT=2*iM,iA=iT-1e-6;function ik(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}class iS{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?ik:function(t){let n=Math.floor(t);if(!(n>=0))throw Error(`invalid digits: ${t}`);if(n>15)return ik;let e=10**n;return function(t){this._+=t[0];for(let n=1,r=t.length;n<r;++n)this._+=Math.round(arguments[n]*e)/e+t[n]}}(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,e,r){this._append`Q${+t},${+n},${this._x1=+e},${this._y1=+r}`}bezierCurveTo(t,n,e,r,i,o){this._append`C${+t},${+n},${+e},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(t,n,e,r,i){if(t=+t,n=+n,e=+e,r=+r,(i=+i)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,u=e-t,c=r-n,l=o-t,f=a-n,s=l*l+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=n}`;else if(s>1e-6){if(Math.abs(f*u-c*l)>1e-6&&i){let h=e-o,d=r-a,p=u*u+c*c,v=Math.sqrt(p),g=Math.sqrt(s),y=i*Math.tan((iM-Math.acos((p+s-(h*h+d*d))/(2*v*g)))/2),_=y/g,b=y/v;Math.abs(_-1)>1e-6&&this._append`L${t+_*l},${n+_*f}`,this._append`A${i},${i},0,0,${+(f*h>l*d)},${this._x1=t+b*u},${this._y1=n+b*c}`}else this._append`L${this._x1=t},${this._y1=n}`}}arc(t,n,e,r,i,o){if(t=+t,n=+n,o=!!o,(e=+e)<0)throw Error(`negative radius: ${e}`);let a=e*Math.cos(r),u=e*Math.sin(r),c=t+a,l=n+u,f=1^o,s=o?r-i:i-r;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${c},${l}`,e&&(s<0&&(s=s%iT+iT),s>iA?this._append`A${e},${e},0,1,${f},${t-a},${n-u}A${e},${e},0,1,${f},${this._x1=c},${this._y1=l}`:s>1e-6&&this._append`A${e},${e},0,${+(s>=iM)},${f},${this._x1=t+e*Math.cos(i)},${this._y1=n+e*Math.sin(i)}`)}rect(t,n,e,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${e=+e}v${+r}h${-e}Z`}toString(){return this._}}function iE(){return new iS}function iN(t=3){return new iS(+t)}iE.prototype=iS.prototype;var iC=Array.prototype.slice;function iP(t){return function(){return t}}function iR(t){return t.source}function iO(t){return t.target}function ij(t){return t.radius}function iz(t){return t.startAngle}function iD(t){return t.endAngle}function iL(){return 0}function iI(){return 10}function i$(t){var n=iR,e=iO,r=ij,i=ij,o=iz,a=iD,u=iL,c=null;function l(){var l,f=n.apply(this,arguments),s=e.apply(this,arguments),h=u.apply(this,arguments)/2,d=iC.call(arguments),p=+r.apply(this,(d[0]=f,d)),v=o.apply(this,d)-iv,g=a.apply(this,d)-iv,y=+i.apply(this,(d[0]=s,d)),_=o.apply(this,d)-iv,b=a.apply(this,d)-iv;if(c||(c=l=iE()),h>1e-12&&(is(g-v)>2*h+1e-12?g>v?(v+=h,g-=h):(v-=h,g+=h):v=g=(v+g)/2,is(b-_)>2*h+1e-12?b>_?(_+=h,b-=h):(_-=h,b+=h):_=b=(_+b)/2),c.moveTo(p*ih(v),p*id(v)),c.arc(0,0,p,v,g),v!==_||g!==b){if(t){var m=+t.apply(this,arguments),x=y-m,w=(_+b)/2;c.quadraticCurveTo(0,0,x*ih(_),x*id(_)),c.lineTo(y*ih(w),y*id(w)),c.lineTo(x*ih(b),x*id(b))}else c.quadraticCurveTo(0,0,y*ih(_),y*id(_)),c.arc(0,0,y,_,b)}if(c.quadraticCurveTo(0,0,p*ih(v),p*id(v)),c.closePath(),l)return c=null,l+""||null}return t&&(l.headRadius=function(n){return arguments.length?(t="function"==typeof n?n:iP(+n),l):t}),l.radius=function(t){return arguments.length?(r=i="function"==typeof t?t:iP(+t),l):r},l.sourceRadius=function(t){return arguments.length?(r="function"==typeof t?t:iP(+t),l):r},l.targetRadius=function(t){return arguments.length?(i="function"==typeof t?t:iP(+t),l):i},l.startAngle=function(t){return arguments.length?(o="function"==typeof t?t:iP(+t),l):o},l.endAngle=function(t){return arguments.length?(a="function"==typeof t?t:iP(+t),l):a},l.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:iP(+t),l):u},l.source=function(t){return arguments.length?(n=t,l):n},l.target=function(t){return arguments.length?(e=t,l):e},l.context=function(t){return arguments.length?(c=null==t?null:t,l):c},l}function iB(){return i$()}function iV(){return i$(iI)}let iU=Math.PI/180,iF=180/Math.PI,iq=4/29,iG=6/29,iY=6/29*3*(6/29),iH=6/29*(6/29)*(6/29);function iW(t){if(t instanceof iK)return new iK(t.l,t.a,t.b,t.opacity);if(t instanceof i5)return i4(t);t instanceof eT||(t=ew(t));var n,e,r=i1(t.r),i=i1(t.g),o=i1(t.b),a=iJ((.2225045*r+.7168786*i+.0606169*o)/1);return r===i&&i===o?n=e=a:(n=iJ((.4360747*r+.3850649*i+.1430804*o)/.96422),e=iJ((.0139322*r+.0971045*i+.7141733*o)/.82521)),new iK(116*a-16,500*(n-a),200*(a-e),t.opacity)}function iZ(t,n){return new iK(t,0,0,null==n?1:n)}function iX(t,n,e,r){return 1==arguments.length?iW(t):new iK(t,n,e,null==r?1:r)}function iK(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function iJ(t){return t>iH?Math.pow(t,1/3):t/iY+iq}function iQ(t){return t>iG?t*t*t:iY*(t-iq)}function i0(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function i1(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function i2(t){if(t instanceof i5)return new i5(t.h,t.c,t.l,t.opacity);if(t instanceof iK||(t=iW(t)),0===t.a&&0===t.b)return new i5(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*iF;return new i5(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function i6(t,n,e,r){return 1==arguments.length?i2(t):new i5(e,n,t,null==r?1:r)}function i3(t,n,e,r){return 1==arguments.length?i2(t):new i5(t,n,e,null==r?1:r)}function i5(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function i4(t){if(isNaN(t.h))return new iK(t.l,0,0,t.opacity);var n=t.h*iU;return new iK(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}er(iK,iX,ei(eo,{brighter(t){return new iK(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new iK(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new eT(i0(3.1338561*(n=.96422*iQ(n))-1.6168667*(t=1*iQ(t))-.4906146*(e=.82521*iQ(e))),i0(-.9787684*n+1.9161415*t+.033454*e),i0(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),er(i5,i3,ei(eo,{brighter(t){return new i5(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new i5(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return i4(this).rgb()}}));var i8=-1.78277*.29227-.1347134789;function i7(t,n,e,r){return 1==arguments.length?function(t){if(t instanceof i9)return new i9(t.h,t.s,t.l,t.opacity);t instanceof eT||(t=ew(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(i8*r+-1.7884503806*n-3.5172982438*e)/(i8+-1.7884503806-3.5172982438),o=r-i,a=-((1.97294*(e-i)- -.29227*o)/.90649),u=Math.sqrt(a*a+o*o)/(1.97294*i*(1-i)),c=u?Math.atan2(a,o)*iF-120:NaN;return new i9(c<0?c+360:c,u,i,t.opacity)}(t):new i9(t,n,e,null==r?1:r)}function i9(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}er(i9,i7,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new i9(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new i9(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*iU,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new eT(255*(n+e*(-.14861*r+1.78277*i)),255*(n+e*(-.29227*r+-.90649*i)),255*(n+1.97294*r*e),this.opacity)}}));var ot=Array.prototype.slice;function on(t,n){return t-n}let oe=t=>()=>t;function or(){}var oi=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function oo(){var t=1,n=1,e=tm,r=u;function i(t){var n=e(t);if(Array.isArray(n))n=n.slice().sort(on);else{let e=R(t,oa);for(n=tg(...tb(e[0],e[1],n),n);n[n.length-1]>=e[1];)n.pop();for(;n[1]<e[0];)n.shift()}return n.map(n=>o(t,n))}function o(e,i){let o=null==i?NaN:+i;if(isNaN(o))throw Error(`invalid value: ${i}`);var u=[],c=[];return function(e,r,i){var o,u,c,l,f,s,h=[],d=[];for(o=u=-1,oi[(l=ou(e[0],r))<<1].forEach(p);++o<t-1;)oi[(c=l)|(l=ou(e[o+1],r))<<1].forEach(p);for(oi[l<<0].forEach(p);++u<n-1;){for(o=-1,oi[(l=ou(e[u*t+t],r))<<1|(f=ou(e[u*t],r))<<2].forEach(p);++o<t-1;)c=l,l=ou(e[u*t+t+o+1],r),s=f,oi[c|l<<1|(f=ou(e[u*t+o+1],r))<<2|s<<3].forEach(p);oi[l|f<<3].forEach(p)}for(o=-1,oi[(f=e[u*t]>=r)<<2].forEach(p);++o<t-1;)s=f,oi[(f=ou(e[u*t+o+1],r))<<2|s<<3].forEach(p);function p(t){var n,e,r=[t[0][0]+o,t[0][1]+u],c=[t[1][0]+o,t[1][1]+u],l=a(r),f=a(c);(n=d[l])?(e=h[f])?(delete d[n.end],delete h[e.start],n===e?(n.ring.push(c),i(n.ring)):h[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete d[n.end],n.ring.push(c),d[n.end=f]=n):(n=h[f])?(e=d[l])?(delete h[n.start],delete d[e.end],n===e?(n.ring.push(c),i(n.ring)):h[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete h[n.start],n.ring.unshift(r),h[n.start=l]=n):h[l]=d[f]={start:l,end:f,ring:[r,c]}}oi[f<<3].forEach(p)}(e,o,function(t){r(t,e,o),function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}(t)>0?u.push([t]):c.push(t)}),c.forEach(function(t){for(var n,e=0,r=u.length;e<r;++e)if(-1!==function(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=function(t,n){for(var e=n[0],r=n[1],i=-1,o=0,a=t.length,u=a-1;o<a;u=o++){var c=t[o],l=c[0],f=c[1],s=t[u],h=s[0],d=s[1];if(function(t,n,e){var r,i,o,a;return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])&&(i=t[r=+(t[0]===n[0])],o=e[r],a=n[r],i<=o&&o<=a||a<=o&&o<=i)}(c,s,n))return 0;f>r!=d>r&&e<(h-l)*(r-f)/(d-f)+l&&(i=-i)}return i}(t,n[r]))return e;return 0}((n=u[e])[0],t)){n.push(t);return}}),{type:"MultiPolygon",value:i,coordinates:u}}function a(n){return 2*n[0]+n[1]*(t+1)*4}function u(e,r,i){e.forEach(function(e){var o=e[0],a=e[1],u=0|o,c=0|a,l=oc(r[c*t+u]);o>0&&o<t&&u===o&&(e[0]=ol(o,oc(r[c*t+u-1]),l,i)),a>0&&a<n&&c===a&&(e[1]=ol(a,oc(r[(c-1)*t+u]),l,i))})}return i.contour=o,i.size=function(e){if(!arguments.length)return[t,n];var r=Math.floor(e[0]),o=Math.floor(e[1]);if(!(r>=0&&o>=0))throw Error("invalid size");return t=r,n=o,i},i.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?oe(ot.call(t)):oe(t),i):e},i.smooth=function(t){return arguments.length?(r=t?u:or,i):r===u},i}function oa(t){return isFinite(t)?t:NaN}function ou(t,n){return null!=t&&+t>=n}function oc(t){return null==t||isNaN(t=+t)?-1/0:t}function ol(t,n,e,r){let i=r-n,o=e-n,a=isFinite(i)||isFinite(o)?i/o:Math.sign(i)/Math.sign(o);return isNaN(a)?t:t+a-.5}function of(t){return t[0]}function os(t){return t[1]}function oh(){return 1}function od(){var t=of,n=os,e=oh,r=960,i=500,o=20,a=2,u=60,c=270,l=155,f=oe(20);function s(r){var i=new Float32Array(c*l),f=Math.pow(2,-a),s=-1;for(let o of r){var h=(t(o,++s,r)+u)*f,d=(n(o,s,r)+u)*f,p=+e(o,s,r);if(p&&h>=0&&h<c&&d>=0&&d<l){var v=Math.floor(h),g=Math.floor(d),y=h-v-.5,b=d-g-.5;i[v+g*c]+=(1-y)*(1-b)*p,i[v+1+g*c]+=y*(1-b)*p,i[v+1+(g+1)*c]+=y*b*p,i[v+(g+1)*c]+=(1-y)*b*p}}return _({data:i,width:c,height:l},o*f),i}function h(t){var n=s(t),e=f(n),r=Math.pow(2,2*a);return Array.isArray(e)||(e=tg(Number.MIN_VALUE,tw(n)/r,e)),oo().size([c,l]).thresholds(e.map(t=>t*r))(n).map((t,n)=>(t.value=+e[n],d(t)))}function d(t){return t.coordinates.forEach(p),t}function p(t){t.forEach(v)}function v(t){t.forEach(g)}function g(t){t[0]=t[0]*Math.pow(2,a)-u,t[1]=t[1]*Math.pow(2,a)-u}function y(){return c=r+2*(u=3*o)>>a,l=i+2*u>>a,h}return h.contours=function(t){var n=s(t),e=oo().size([c,l]),r=Math.pow(2,2*a),i=t=>{t=+t;var i=d(e.contour(n,t*r));return i.value=t,i};return Object.defineProperty(i,"max",{get:()=>tw(n)/r}),i},h.x=function(n){return arguments.length?(t="function"==typeof n?n:oe(+n),h):t},h.y=function(t){return arguments.length?(n="function"==typeof t?t:oe(+t),h):n},h.weight=function(t){return arguments.length?(e="function"==typeof t?t:oe(+t),h):e},h.size=function(t){if(!arguments.length)return[r,i];var n=+t[0],e=+t[1];if(!(n>=0&&e>=0))throw Error("invalid size");return r=n,i=e,y()},h.cellSize=function(t){if(!arguments.length)return 1<<a;if(!((t=+t)>=1))throw Error("invalid cell size");return a=Math.floor(Math.log(t)/Math.LN2),y()},h.thresholds=function(t){return arguments.length?(f="function"==typeof t?t:Array.isArray(t)?oe(ot.call(t)):oe(t),h):f},h.bandwidth=function(t){if(!arguments.length)return Math.sqrt(o*(o+1));if(!((t=+t)>=0))throw Error("invalid bandwidth");return o=(Math.sqrt(4*t*t+1)-1)/2,y()},h}function op(t,n,e,r,i){let o,a,u,c;let l=n[0],f=r[0],s=0,h=0;f>l==f>-l?(o=l,l=n[++s]):(o=f,f=r[++h]);let d=0;if(s<t&&h<e)for(f>l==f>-l?(a=l+o,u=o-(a-l),l=n[++s]):(a=f+o,u=o-(a-f),f=r[++h]),o=a,0!==u&&(i[d++]=u);s<t&&h<e;)f>l==f>-l?(c=(a=o+l)-o,u=o-(a-c)+(l-c),l=n[++s]):(c=(a=o+f)-o,u=o-(a-c)+(f-c),f=r[++h]),o=a,0!==u&&(i[d++]=u);for(;s<t;)c=(a=o+l)-o,u=o-(a-c)+(l-c),l=n[++s],o=a,0!==u&&(i[d++]=u);for(;h<e;)c=(a=o+f)-o,u=o-(a-c)+(f-c),f=r[++h],o=a,0!==u&&(i[d++]=u);return(0!==o||0===d)&&(i[d++]=o),d}function ov(t){return new Float64Array(t)}let og=ov(4),oy=ov(8),o_=ov(12),ob=ov(16),om=ov(4);function ox(t,n,e,r,i,o){let a=(n-o)*(e-i),u=(t-i)*(r-o),c=a-u,l=Math.abs(a+u);return Math.abs(c)>=33306690738754716e-32*l?c:-function(t,n,e,r,i,o,a){let u,c,l,f,s,h,d,p,v,g,y,_,b,m,x,w,M,T;let A=t-i,k=e-i,S=n-o,E=r-o;m=A*E,d=(h=134217729*A)-(h-A),p=A-d,v=(h=134217729*E)-(h-E),x=p*(g=E-v)-(m-d*v-p*v-d*g),w=S*k,d=(h=134217729*S)-(h-S),p=S-d,v=(h=134217729*k)-(h-k),y=x-(M=p*(g=k-v)-(w-d*v-p*v-d*g)),s=x-y,og[0]=x-(y+s)+(s-M),s=(_=m+y)-m,y=(b=m-(_-s)+(y-s))-w,s=b-y,og[1]=b-(y+s)+(s-w),s=(T=_+y)-_,og[2]=_-(T-s)+(y-s),og[3]=T;let N=function(t,n){let e=n[0];for(let t=1;t<4;t++)e+=n[t];return e}(0,og),C=22204460492503146e-32*a;if(N>=C||-N>=C||(s=t-A,u=t-(A+s)+(s-i),s=e-k,l=e-(k+s)+(s-i),s=n-S,c=n-(S+s)+(s-o),s=r-E,f=r-(E+s)+(s-o),0===u&&0===c&&0===l&&0===f)||(C=11093356479670487e-47*a+33306690738754706e-32*Math.abs(N),(N+=A*f+E*u-(S*l+k*c))>=C||-N>=C))return N;m=u*E,d=(h=134217729*u)-(h-u),p=u-d,v=(h=134217729*E)-(h-E),x=p*(g=E-v)-(m-d*v-p*v-d*g),w=c*k,d=(h=134217729*c)-(h-c),p=c-d,v=(h=134217729*k)-(h-k),y=x-(M=p*(g=k-v)-(w-d*v-p*v-d*g)),s=x-y,om[0]=x-(y+s)+(s-M),s=(_=m+y)-m,y=(b=m-(_-s)+(y-s))-w,s=b-y,om[1]=b-(y+s)+(s-w),s=(T=_+y)-_,om[2]=_-(T-s)+(y-s),om[3]=T;let P=op(4,og,4,om,oy);m=A*f,d=(h=134217729*A)-(h-A),p=A-d,v=(h=134217729*f)-(h-f),x=p*(g=f-v)-(m-d*v-p*v-d*g),w=S*l,d=(h=134217729*S)-(h-S),p=S-d,v=(h=134217729*l)-(h-l),y=x-(M=p*(g=l-v)-(w-d*v-p*v-d*g)),s=x-y,om[0]=x-(y+s)+(s-M),s=(_=m+y)-m,y=(b=m-(_-s)+(y-s))-w,s=b-y,om[1]=b-(y+s)+(s-w),s=(T=_+y)-_,om[2]=_-(T-s)+(y-s),om[3]=T;let R=op(P,oy,4,om,o_);m=u*f,d=(h=134217729*u)-(h-u),p=u-d,v=(h=134217729*f)-(h-f),x=p*(g=f-v)-(m-d*v-p*v-d*g),w=c*l,d=(h=134217729*c)-(h-c),p=c-d,v=(h=134217729*l)-(h-l),y=x-(M=p*(g=l-v)-(w-d*v-p*v-d*g)),s=x-y,om[0]=x-(y+s)+(s-M),s=(_=m+y)-m,y=(b=m-(_-s)+(y-s))-w,s=b-y,om[1]=b-(y+s)+(s-w),s=(T=_+y)-_,om[2]=_-(T-s)+(y-s),om[3]=T;let O=op(R,o_,4,om,ob);return ob[O-1]}(t,n,e,r,i,o,l)}ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(8),ov(8),ov(8),ov(4),ov(8),ov(8),ov(8),ov(12);let ow=ov(192),oM=ov(192);ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(8),ov(8),ov(8),ov(8),ov(8),ov(8),ov(8),ov(8),ov(8),ov(4),ov(4),ov(4),ov(8),ov(16),ov(16),ov(16),ov(32),ov(32),ov(48),ov(64),ov(1152),ov(1152),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(4),ov(24),ov(24),ov(24),ov(24),ov(24),ov(24),ov(24),ov(24),ov(24),ov(24),ov(1152),ov(1152),ov(1152),ov(1152),ov(1152),ov(2304),ov(2304),ov(3456),ov(5760),ov(8),ov(8),ov(8),ov(16),ov(24),ov(48),ov(48),ov(96),ov(192),ov(384),ov(384),ov(384),ov(768),ov(96),ov(96),ov(96),ov(1152);let oT=new Uint32Array(512);class oA{static from(t,n=oN,e=oC){let r=t.length,i=new Float64Array(2*r);for(let o=0;o<r;o++){let r=t[o];i[2*o]=n(r),i[2*o+1]=e(r)}return new oA(i)}constructor(t){let n=t.length>>1;if(n>0&&"number"!=typeof t[0])throw Error("Expected coords to contain numbers.");this.coords=t;let e=Math.max(2*n-5,0);this._triangles=new Uint32Array(3*e),this._halfedges=new Int32Array(3*e),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){let t,n,e;let{coords:r,_hullPrev:i,_hullNext:o,_hullTri:a,_hullHash:u}=this,c=r.length>>1,l=1/0,f=1/0,s=-1/0,h=-1/0;for(let t=0;t<c;t++){let n=r[2*t],e=r[2*t+1];n<l&&(l=n),e<f&&(f=e),n>s&&(s=n),e>h&&(h=e),this._ids[t]=t}let d=(l+s)/2,p=(f+h)/2;for(let n=0,e=1/0;n<c;n++){let i=ok(d,p,r[2*n],r[2*n+1]);i<e&&(t=n,e=i)}let v=r[2*t],g=r[2*t+1];for(let e=0,i=1/0;e<c;e++){if(e===t)continue;let o=ok(v,g,r[2*e],r[2*e+1]);o<i&&o>0&&(n=e,i=o)}let y=r[2*n],_=r[2*n+1],b=1/0;for(let i=0;i<c;i++){if(i===t||i===n)continue;let o=function(t,n,e,r,i,o){let a=e-t,u=r-n,c=i-t,l=o-n,f=a*a+u*u,s=c*c+l*l,h=.5/(a*l-u*c),d=(l*f-u*s)*h,p=(a*s-c*f)*h;return d*d+p*p}(v,g,y,_,r[2*i],r[2*i+1]);o<b&&(e=i,b=o)}let m=r[2*e],x=r[2*e+1];if(b===1/0){for(let t=0;t<c;t++)this._dists[t]=r[2*t]-r[0]||r[2*t+1]-r[1];oS(this._ids,this._dists,0,c-1);let t=new Uint32Array(c),n=0;for(let e=0,r=-1/0;e<c;e++){let i=this._ids[e],o=this._dists[i];o>r&&(t[n++]=i,r=o)}this.hull=t.subarray(0,n),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(0>ox(v,g,y,_,m,x)){let t=n,r=y,i=_;n=e,y=m,_=x,e=t,m=r,x=i}let w=function(t,n,e,r,i,o){let a=e-t,u=r-n,c=i-t,l=o-n,f=a*a+u*u,s=c*c+l*l,h=.5/(a*l-u*c);return{x:t+(l*f-u*s)*h,y:n+(a*s-c*f)*h}}(v,g,y,_,m,x);this._cx=w.x,this._cy=w.y;for(let t=0;t<c;t++)this._dists[t]=ok(r[2*t],r[2*t+1],w.x,w.y);oS(this._ids,this._dists,0,c-1),this._hullStart=t;let M=3;o[t]=i[e]=n,o[n]=i[t]=e,o[e]=i[n]=t,a[t]=0,a[n]=1,a[e]=2,u.fill(-1),u[this._hashKey(v,g)]=t,u[this._hashKey(y,_)]=n,u[this._hashKey(m,x)]=e,this.trianglesLen=0,this._addTriangle(t,n,e,-1,-1,-1);for(let c=0,l,f;c<this._ids.length;c++){let s=this._ids[c],h=r[2*s],d=r[2*s+1];if(c>0&&2220446049250313e-31>=Math.abs(h-l)&&2220446049250313e-31>=Math.abs(d-f)||(l=h,f=d,s===t||s===n||s===e))continue;let p=0;for(let t=0,n=this._hashKey(h,d);t<this._hashSize&&(-1===(p=u[(n+t)%this._hashSize])||p===o[p]);t++);let v=p=i[p],g;for(;g=o[v],ox(h,d,r[2*v],r[2*v+1],r[2*g],r[2*g+1])>=0;)if((v=g)===p){v=-1;break}if(-1===v)continue;let y=this._addTriangle(v,s,o[v],-1,-1,a[v]);a[s]=this._legalize(y+2),a[v]=y,M++;let _=o[v];for(;g=o[_],0>ox(h,d,r[2*_],r[2*_+1],r[2*g],r[2*g+1]);)y=this._addTriangle(_,s,g,a[s],-1,a[_]),a[s]=this._legalize(y+2),o[_]=_,M--,_=g;if(v===p)for(;0>ox(h,d,r[2*(g=i[v])],r[2*g+1],r[2*v],r[2*v+1]);)y=this._addTriangle(g,s,v,-1,a[v],a[g]),this._legalize(y+2),a[g]=y,o[v]=v,M--,v=g;this._hullStart=i[s]=v,o[v]=i[_]=s,o[s]=_,u[this._hashKey(h,d)]=s,u[this._hashKey(r[2*v],r[2*v+1])]=v}this.hull=new Uint32Array(M);for(let t=0,n=this._hullStart;t<M;t++)this.hull[t]=n,n=o[n];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,n){return Math.floor(function(t,n){let e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}(t-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(t){let{_triangles:n,_halfedges:e,coords:r}=this,i=0,o=0;for(;;){let a=e[t],u=t-t%3;if(o=u+(t+2)%3,-1===a){if(0===i)break;t=oT[--i];continue}let c=a-a%3,l=u+(t+1)%3,f=c+(a+2)%3,s=n[o],h=n[t],d=n[l],p=n[f];if(function(t,n,e,r,i,o,a,u){let c=t-a,l=n-u,f=e-a,s=r-u,h=i-a,d=o-u,p=f*f+s*s,v=h*h+d*d;return c*(s*v-p*d)-l*(f*v-p*h)+(c*c+l*l)*(f*d-s*h)<0}(r[2*s],r[2*s+1],r[2*h],r[2*h+1],r[2*d],r[2*d+1],r[2*p],r[2*p+1])){n[t]=p,n[a]=s;let r=e[f];if(-1===r){let n=this._hullStart;do{if(this._hullTri[n]===f){this._hullTri[n]=t;break}n=this._hullPrev[n]}while(n!==this._hullStart)}this._link(t,r),this._link(a,e[o]),this._link(o,f);let u=c+(a+1)%3;i<oT.length&&(oT[i++]=u)}else{if(0===i)break;t=oT[--i]}}return o}_link(t,n){this._halfedges[t]=n,-1!==n&&(this._halfedges[n]=t)}_addTriangle(t,n,e,r,i,o){let a=this.trianglesLen;return this._triangles[a]=t,this._triangles[a+1]=n,this._triangles[a+2]=e,this._link(a,r),this._link(a+1,i),this._link(a+2,o),this.trianglesLen+=3,a}}function ok(t,n,e,r){let i=t-e,o=n-r;return i*i+o*o}function oS(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){let r=t[i],o=n[r],a=i-1;for(;a>=e&&n[t[a]]>o;)t[a+1]=t[a--];t[a+1]=r}else{let i=e+r>>1,o=e+1,a=r;oE(t,i,o),n[t[e]]>n[t[r]]&&oE(t,e,r),n[t[o]]>n[t[r]]&&oE(t,o,r),n[t[e]]>n[t[o]]&&oE(t,e,o);let u=t[o],c=n[u];for(;;){do o++;while(n[t[o]]<c);do a--;while(n[t[a]]>c);if(a<o)break;oE(t,o,a)}t[e+1]=t[a],t[a]=u,r-o+1>=a-e?(oS(t,n,o,r),oS(t,n,e,a-1)):(oS(t,n,e,a-1),oS(t,n,o,r))}}function oE(t,n,e){let r=t[n];t[n]=t[e],t[e]=r}function oN(t){return t[0]}function oC(t){return t[1]}class oP{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,n){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+=`L${this._x1=+t},${this._y1=+n}`}arc(t,n,e){t=+t,n=+n;let r=t+(e=+e),i=n;if(e<0)throw Error("negative radius");null===this._x1?this._+=`M${r},${i}`:(Math.abs(this._x1-r)>1e-6||Math.abs(this._y1-i)>1e-6)&&(this._+="L"+r+","+i),e&&(this._+=`A${e},${e},0,1,1,${t-e},${n}A${e},${e},0,1,1,${this._x1=r},${this._y1=i}`)}rect(t,n,e,r){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${+e}v${+r}h${-e}Z`}value(){return this._||null}}class oR{constructor(){this._=[]}moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}}class oO{constructor(t,[n,e,r,i]=[0,0,960,500]){if(!((r=+r)>=(n=+n))||!((i=+i)>=(e=+e)))throw Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=n,this.ymax=i,this.ymin=e,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){let t,n;let{delaunay:{points:e,hull:r,triangles:i},vectors:o}=this,a=this.circumcenters=this._circumcenters.subarray(0,i.length/3*2);for(let o=0,u=0,c=i.length,l,f;o<c;o+=3,u+=2){let c=2*i[o],s=2*i[o+1],h=2*i[o+2],d=e[c],p=e[c+1],v=e[s],g=e[s+1],y=e[h],_=e[h+1],b=v-d,m=g-p,x=y-d,w=_-p,M=(b*w-m*x)*2;if(1e-9>Math.abs(M)){if(void 0===t){for(let i of(t=n=0,r))t+=e[2*i],n+=e[2*i+1];t/=r.length,n/=r.length}let i=1e9*Math.sign((t-d)*w-(n-p)*x);l=(d+y)/2-i*w,f=(p+_)/2+i*x}else{let t=1/M,n=b*b+m*m,e=x*x+w*w;l=d+(w*n-m*e)*t,f=p+(b*e-x*n)*t}a[u]=l,a[u+1]=f}let u=r[r.length-1],c,l=4*u,f,s=e[2*u],h,d=e[2*u+1];o.fill(0);for(let t=0;t<r.length;++t)u=r[t],c=l,f=s,h=d,l=4*u,s=e[2*u],d=e[2*u+1],o[c+2]=o[l]=h-d,o[c+3]=o[l+1]=s-f}render(t){let n=null==t?t=new oP:void 0,{delaunay:{halfedges:e,inedges:r,hull:i},circumcenters:o,vectors:a}=this;if(i.length<=1)return null;for(let n=0,r=e.length;n<r;++n){let r=e[n];if(r<n)continue;let i=2*Math.floor(n/3),a=2*Math.floor(r/3),u=o[i],c=o[i+1],l=o[a],f=o[a+1];this._renderSegment(u,c,l,f,t)}let u,c=i[i.length-1];for(let n=0;n<i.length;++n){u=c;let e=2*Math.floor(r[c=i[n]]/3),l=o[e],f=o[e+1],s=4*u,h=this._project(l,f,a[s+2],a[s+3]);h&&this._renderSegment(l,f,h[0],h[1],t)}return n&&n.value()}renderBounds(t){let n=null==t?t=new oP:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,n){let e=null==n?n=new oP:void 0,r=this._clip(t);if(null===r||!r.length)return;n.moveTo(r[0],r[1]);let i=r.length;for(;r[0]===r[i-2]&&r[1]===r[i-1]&&i>1;)i-=2;for(let t=2;t<i;t+=2)(r[t]!==r[t-2]||r[t+1]!==r[t-1])&&n.lineTo(r[t],r[t+1]);return n.closePath(),e&&e.value()}*cellPolygons(){let{delaunay:{points:t}}=this;for(let n=0,e=t.length/2;n<e;++n){let t=this.cellPolygon(n);t&&(t.index=n,yield t)}}cellPolygon(t){let n=new oR;return this.renderCell(t,n),n.value()}_renderSegment(t,n,e,r,i){let o;let a=this._regioncode(t,n),u=this._regioncode(e,r);0===a&&0===u?(i.moveTo(t,n),i.lineTo(e,r)):(o=this._clipSegment(t,n,e,r,a,u))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,n,e){return(n=+n)==n&&(e=+e)==e&&this.delaunay._step(t,n,e)===t}*neighbors(t){let n=this._clip(t);if(n)for(let e of this.delaunay.neighbors(t)){let t=this._clip(e);if(t){e:for(let r=0,i=n.length;r<i;r+=2)for(let o=0,a=t.length;o<a;o+=2)if(n[r]===t[o]&&n[r+1]===t[o+1]&&n[(r+2)%i]===t[(o+a-2)%a]&&n[(r+3)%i]===t[(o+a-1)%a]){yield e;break e}}}}_cell(t){let{circumcenters:n,delaunay:{inedges:e,halfedges:r,triangles:i}}=this,o=e[t];if(-1===o)return null;let a=[],u=o;do{let e=Math.floor(u/3);if(a.push(n[2*e],n[2*e+1]),i[u=u%3==2?u-2:u+1]!==t)break;u=r[u]}while(u!==o&&-1!==u);return a}_clip(t){if(0===t&&1===this.delaunay.hull.length)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];let n=this._cell(t);if(null===n)return null;let{vectors:e}=this,r=4*t;return this._simplify(e[r]||e[r+1]?this._clipInfinite(t,n,e[r],e[r+1],e[r+2],e[r+3]):this._clipFinite(t,n))}_clipFinite(t,n){let e=n.length,r=null,i,o,a=n[e-2],u=n[e-1],c,l=this._regioncode(a,u),f,s=0;for(let h=0;h<e;h+=2)if(i=a,o=u,a=n[h],u=n[h+1],c=l,l=this._regioncode(a,u),0===c&&0===l)f=s,s=0,r?r.push(a,u):r=[a,u];else{let n,e,h,d,p;if(0===c){if(null===(n=this._clipSegment(i,o,a,u,c,l)))continue;[e,h,d,p]=n}else{if(null===(n=this._clipSegment(a,u,i,o,l,c)))continue;[d,p,e,h]=n,f=s,s=this._edgecode(e,h),f&&s&&this._edge(t,f,s,r,r.length),r?r.push(e,h):r=[e,h]}f=s,s=this._edgecode(d,p),f&&s&&this._edge(t,f,s,r,r.length),r?r.push(d,p):r=[d,p]}if(r)f=s,s=this._edgecode(r[0],r[1]),f&&s&&this._edge(t,f,s,r,r.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return r}_clipSegment(t,n,e,r,i,o){let a=i<o;for(a&&([t,n,e,r,i,o]=[e,r,t,n,o,i]);;){if(0===i&&0===o)return a?[e,r,t,n]:[t,n,e,r];if(i&o)return null;let u,c,l=i||o;8&l?(u=t+(e-t)*(this.ymax-n)/(r-n),c=this.ymax):4&l?(u=t+(e-t)*(this.ymin-n)/(r-n),c=this.ymin):2&l?(c=n+(r-n)*(this.xmax-t)/(e-t),u=this.xmax):(c=n+(r-n)*(this.xmin-t)/(e-t),u=this.xmin),i?(t=u,n=c,i=this._regioncode(t,n)):(e=u,r=c,o=this._regioncode(e,r))}}_clipInfinite(t,n,e,r,i,o){let a=Array.from(n),u;if((u=this._project(a[0],a[1],e,r))&&a.unshift(u[0],u[1]),(u=this._project(a[a.length-2],a[a.length-1],i,o))&&a.push(u[0],u[1]),a=this._clipFinite(t,a))for(let n=0,e=a.length,r,i=this._edgecode(a[e-2],a[e-1]);n<e;n+=2)r=i,i=this._edgecode(a[n],a[n+1]),r&&i&&(n=this._edge(t,r,i,a,n),e=a.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(a=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return a}_edge(t,n,e,r,i){for(;n!==e;){let e,o;switch(n){case 5:n=4;continue;case 4:n=6,e=this.xmax,o=this.ymin;break;case 6:n=2;continue;case 2:n=10,e=this.xmax,o=this.ymax;break;case 10:n=8;continue;case 8:n=9,e=this.xmin,o=this.ymax;break;case 9:n=1;continue;case 1:n=5,e=this.xmin,o=this.ymin}(r[i]!==e||r[i+1]!==o)&&this.contains(t,e,o)&&(r.splice(i,0,e,o),i+=2)}return i}_project(t,n,e,r){let i=1/0,o,a,u;if(r<0){if(n<=this.ymin)return null;(o=(this.ymin-n)/r)<i&&(u=this.ymin,a=t+(i=o)*e)}else if(r>0){if(n>=this.ymax)return null;(o=(this.ymax-n)/r)<i&&(u=this.ymax,a=t+(i=o)*e)}if(e>0){if(t>=this.xmax)return null;(o=(this.xmax-t)/e)<i&&(a=this.xmax,u=n+(i=o)*r)}else if(e<0){if(t<=this.xmin)return null;(o=(this.xmin-t)/e)<i&&(a=this.xmin,u=n+(i=o)*r)}return[a,u]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let n=0;n<t.length;n+=2){let e=(n+2)%t.length,r=(n+4)%t.length;(t[n]===t[e]&&t[e]===t[r]||t[n+1]===t[e+1]&&t[e+1]===t[r+1])&&(t.splice(e,2),n-=2)}t.length||(t=null)}return t}}let oj=2*Math.PI,oz=Math.pow;function oD(t){return t[0]}function oL(t){return t[1]}class oI{static from(t,n=oD,e=oL,r){return new oI("length"in t?function(t,n,e,r){let i=t.length,o=new Float64Array(2*i);for(let a=0;a<i;++a){let i=t[a];o[2*a]=n.call(r,i,a,t),o[2*a+1]=e.call(r,i,a,t)}return o}(t,n,e,r):Float64Array.from(function*(t,n,e,r){let i=0;for(let o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}(t,n,e,r)))}constructor(t){this._delaunator=new oA(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){let t=this._delaunator,n=this.points;if(t.hull&&t.hull.length>2&&function(t){let{triangles:n,coords:e}=t;for(let t=0;t<n.length;t+=3){let r=2*n[t],i=2*n[t+1],o=2*n[t+2];if((e[o]-e[r])*(e[i+1]-e[r+1])-(e[i]-e[r])*(e[o+1]-e[r+1])>1e-10)return!1}return!0}(t)){this.collinear=Int32Array.from({length:n.length/2},(t,n)=>n).sort((t,e)=>n[2*t]-n[2*e]||n[2*t+1]-n[2*e+1]);let t=this.collinear[0],i=this.collinear[this.collinear.length-1],o=[n[2*t],n[2*t+1],n[2*i],n[2*i+1]],a=1e-8*Math.hypot(o[3]-o[1],o[2]-o[0]);for(let t=0,i=n.length/2;t<i;++t){var e,r;let i=[(e=n[2*t])+Math.sin(e+(r=n[2*t+1]))*a,r+Math.cos(e-r)*a];n[2*t]=i[0],n[2*t+1]=i[1]}this._delaunator=new oA(n)}else delete this.collinear;let i=this.halfedges=this._delaunator.halfedges,o=this.hull=this._delaunator.hull,a=this.triangles=this._delaunator.triangles,u=this.inedges.fill(-1),c=this._hullIndex.fill(-1);for(let t=0,n=i.length;t<n;++t){let n=a[t%3==2?t-2:t+1];(-1===i[t]||-1===u[n])&&(u[n]=t)}for(let t=0,n=o.length;t<n;++t)c[o[t]]=t;o.length<=2&&o.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=o[0],u[o[0]]=1,2===o.length&&(u[o[1]]=0,this.triangles[1]=o[1],this.triangles[2]=o[1]))}voronoi(t){return new oO(this,t)}*neighbors(t){let{inedges:n,hull:e,_hullIndex:r,halfedges:i,triangles:o,collinear:a}=this;if(a){let n=a.indexOf(t);n>0&&(yield a[n-1]),n<a.length-1&&(yield a[n+1]);return}let u=n[t];if(-1===u)return;let c=u,l=-1;do{if(yield l=o[c],o[c=c%3==2?c-2:c+1]!==t)return;if(-1===(c=i[c])){let n=e[(r[t]+1)%e.length];n!==l&&(yield n);return}}while(c!==u)}find(t,n,e=0){let r;if((t=+t)!=t||(n=+n)!=n)return -1;let i=e;for(;(r=this._step(e,t,n))>=0&&r!==e&&r!==i;)e=r;return r}_step(t,n,e){let{inedges:r,hull:i,_hullIndex:o,halfedges:a,triangles:u,points:c}=this;if(-1===r[t]||!c.length)return(t+1)%(c.length>>1);let l=t,f=oz(n-c[2*t],2)+oz(e-c[2*t+1],2),s=r[t],h=s;do{let r=u[h],s=oz(n-c[2*r],2)+oz(e-c[2*r+1],2);if(s<f&&(f=s,l=r),u[h=h%3==2?h-2:h+1]!==t)break;if(-1===(h=a[h])){if((h=i[(o[t]+1)%i.length])!==r&&oz(n-c[2*h],2)+oz(e-c[2*h+1],2)<f)return h;break}}while(h!==s);return l}render(t){let n=null==t?t=new oP:void 0,{points:e,halfedges:r,triangles:i}=this;for(let n=0,o=r.length;n<o;++n){let o=r[n];if(o<n)continue;let a=2*i[n],u=2*i[o];t.moveTo(e[a],e[a+1]),t.lineTo(e[u],e[u+1])}return this.renderHull(t),n&&n.value()}renderPoints(t,n){void 0!==n||t&&"function"==typeof t.moveTo||(n=t,t=null),n=void 0==n?2:+n;let e=null==t?t=new oP:void 0,{points:r}=this;for(let e=0,i=r.length;e<i;e+=2){let i=r[e],o=r[e+1];t.moveTo(i+n,o),t.arc(i,o,n,0,oj)}return e&&e.value()}renderHull(t){let n=null==t?t=new oP:void 0,{hull:e,points:r}=this,i=2*e[0],o=e.length;t.moveTo(r[i],r[i+1]);for(let n=1;n<o;++n){let i=2*e[n];t.lineTo(r[i],r[i+1])}return t.closePath(),n&&n.value()}hullPolygon(){let t=new oR;return this.renderHull(t),t.value()}renderTriangle(t,n){let e=null==n?n=new oP:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],a=2*i[t+1],u=2*i[t+2];return n.moveTo(r[o],r[o+1]),n.lineTo(r[a],r[a+1]),n.lineTo(r[u],r[u+1]),n.closePath(),e&&e.value()}*trianglePolygons(){let{triangles:t}=this;for(let n=0,e=t.length/3;n<e;++n)yield this.trianglePolygon(n)}trianglePolygon(t){let n=new oR;return this.renderTriangle(t,n),n.value()}}let o$=t=>()=>t;function oB(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:a,y:u,dx:c,dy:l,dispatch:f}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:f}})}function oV(t){return!t.ctrlKey&&!t.button}function oU(){return this.parentNode}function oF(t,n){return null==n?{x:t.x,y:t.y}:n}function oq(){return navigator.maxTouchPoints||"ontouchstart"in this}function oG(){var t,n,e,r,i=oV,o=oU,a=oF,u=oq,c={},l=ng("start","drag","end"),f=0,s=0;function h(t){t.on("mousedown.drag",d).filter(u).on("touchstart.drag",g).on("touchmove.drag",y,n8).on("touchend.drag touchcancel.drag",_).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(a,u){if(!r&&i.call(this,a,u)){var c=b(this,o.call(this,a,u),a,u,"mouse");c&&(n4(a.view).on("mousemove.drag",p,n7).on("mouseup.drag",v,n7),en(a.view),n9(a),e=!1,t=a.clientX,n=a.clientY,c("start",a))}}function p(r){if(et(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>s}c.mouse("drag",r)}function v(t){n4(t.view).on("mousemove.drag mouseup.drag",null),ee(t.view,e),et(t),c.mouse("end",t)}function g(t,n){if(i.call(this,t,n)){var e,r,a=t.changedTouches,u=o.call(this,t,n),c=a.length;for(e=0;e<c;++e)(r=b(this,u,t,n,a[e].identifier,a[e]))&&(n9(t),r("start",t,a[e]))}}function y(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=c[r[n].identifier])&&(et(t),e("drag",t,r[n]))}function _(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=c[i[n].identifier])&&(n9(t),e("end",t,i[n]))}function b(t,n,e,r,i,o){var u,s,d,p=l.copy(),v=e4(o||e,n);if(null!=(d=a.call(t,new oB("beforestart",{sourceEvent:e,target:h,identifier:i,active:f,x:v[0],y:v[1],dx:0,dy:0,dispatch:p}),r)))return u=d.x-v[0]||0,s=d.y-v[1]||0,function e(o,a,l){var g,y=v;switch(o){case"start":c[i]=e,g=f++;break;case"end":delete c[i],--f;case"drag":v=e4(l||a,n),g=f}p.call(o,t,new oB(o,{sourceEvent:a,subject:d,target:h,identifier:i,active:g,x:v[0]+u,y:v[1]+s,dx:v[0]-y[0],dy:v[1]-y[1],dispatch:p}),r)}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:o$(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:o$(t),h):o},h.subject=function(t){return arguments.length?(a="function"==typeof t?t:o$(t),h):a},h.touchable=function(t){return arguments.length?(u="function"==typeof t?t:o$(!!t),h):u},h.on=function(){var t=l.on.apply(l,arguments);return t===l?h:t},h.clickDistance=function(t){return arguments.length?(s=(t=+t)*t,h):Math.sqrt(s)},h}oB.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var oY={},oH={};function oW(t){return Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+'] || ""'}).join(",")+"}")}function oZ(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}function oX(t,n){var e=t+"",r=e.length;return r<n?Array(n-r+1).join(0)+e:e}function oK(t){var n=RegExp('["'+t+"\n\r]"),e=t.charCodeAt(0);function r(t,n){var r,i=[],o=t.length,a=0,u=0,c=o<=0,l=!1;function f(){if(c)return oH;if(l)return l=!1,oY;var n,r,i=a;if(34===t.charCodeAt(i)){for(;a++<o&&34!==t.charCodeAt(a)||34===t.charCodeAt(++a););return(n=a)>=o?c=!0:10===(r=t.charCodeAt(a++))?l=!0:13===r&&(l=!0,10===t.charCodeAt(a)&&++a),t.slice(i+1,n-1).replace(/""/g,'"')}for(;a<o;){if(10===(r=t.charCodeAt(n=a++)))l=!0;else if(13===r)l=!0,10===t.charCodeAt(a)&&++a;else if(r!==e)continue;return t.slice(i,n)}return c=!0,t.slice(i,o)}for(10===t.charCodeAt(o-1)&&--o,13===t.charCodeAt(o-1)&&--o;(r=f())!==oH;){for(var s=[];r!==oY&&r!==oH;)s.push(r),r=f();n&&null==(s=n(s,u++))||i.push(s)}return i}function i(n,e){return n.map(function(n){return e.map(function(t){return a(n[t])}).join(t)})}function o(n){return n.map(a).join(t)}function a(t){var e,r,i,o,a,u;return null==t?"":t instanceof Date?(r=(e=t).getUTCHours(),i=e.getUTCMinutes(),o=e.getUTCSeconds(),a=e.getUTCMilliseconds(),isNaN(e)?"Invalid Date":((u=e.getUTCFullYear())<0?"-"+oX(-u,6):u>9999?"+"+oX(u,6):oX(u,4))+"-"+oX(e.getUTCMonth()+1,2)+"-"+oX(e.getUTCDate(),2)+(a?"T"+oX(r,2)+":"+oX(i,2)+":"+oX(o,2)+"."+oX(a,3)+"Z":o?"T"+oX(r,2)+":"+oX(i,2)+":"+oX(o,2)+"Z":i||r?"T"+oX(r,2)+":"+oX(i,2)+"Z":"")):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var e,i,o=r(t,function(t,r){var o;if(e)return e(t,r-1);i=t,e=n?(o=oW(t),function(e,r){return n(o(e),r,t)}):oW(t)});return o.columns=i||[],o},parseRows:r,format:function(n,e){return null==e&&(e=oZ(n)),[e.map(a).join(t)].concat(i(n,e)).join("\n")},formatBody:function(t,n){return null==n&&(n=oZ(t)),i(t,n).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:a}}var oJ=oK(","),oQ=oJ.parse,o0=oJ.parseRows,o1=oJ.format,o2=oJ.formatBody,o6=oJ.formatRows,o3=oJ.formatRow,o5=oJ.formatValue,o4=oK("	"),o8=o4.parse,o7=o4.parseRows,o9=o4.format,at=o4.formatBody,an=o4.formatRows,ae=o4.formatRow,ar=o4.formatValue;function ai(t){for(var n in t){var e,r,i=t[n].trim();if(i){if("true"===i)i=!0;else if("false"===i)i=!1;else if("NaN"===i)i=NaN;else if(isNaN(e=+i)){if(!(r=i.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/)))continue;ao&&r[4]&&!r[7]&&(i=i.replace(/-/g,"/").replace(/T/," ")),i=new Date(i)}else i=e}else i=null;t[n]=i}return t}let ao=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours(),aa=t=>+t;function au(t){return t*t}function ac(t){return t*(2-t)}function al(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}var af=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),as=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),ah=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),ad=Math.PI,ap=ad/2;function av(t){return 1==+t?1:1-Math.cos(t*ap)}function ag(t){return Math.sin(t*ap)}function ay(t){return(1-Math.cos(ad*t))/2}function a_(t){return(Math.pow(2,-10*t)-9765625e-10)*1.0009775171065494}function ab(t){return a_(1-+t)}function am(t){return 1-a_(t)}function ax(t){return((t*=2)<=1?a_(1-t):2-a_(t-1))/2}function aw(t){return 1-Math.sqrt(1-t*t)}function aM(t){return Math.sqrt(1- --t*t)}function aT(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var aA=4/11,ak=6/11,aS=8/11,aE=3/4,aN=9/11,aC=10/11,aP=15/16,aR=21/22,aO=63/64,aj=1/(4/11)/(4/11);function az(t){return 1-aD(1-t)}function aD(t){return(t=+t)<aA?aj*t*t:t<aS?aj*(t-=ak)*t+aE:t<aC?aj*(t-=aN)*t+aP:aj*(t-=aR)*t+aO}function aL(t){return((t*=2)<=1?1-aD(1-t):aD(t-1)+1)/2}var aI=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(1.70158),a$=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(1.70158),aB=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),aV=2*Math.PI,aU=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=aV);function i(t){return n*a_(- --t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*aV)},i.period=function(e){return t(n,e)},i}(1,.3),aF=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=aV);function i(t){return 1-n*a_(t=+t)*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*aV)},i.period=function(e){return t(n,e)},i}(1,.3),aq=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=aV);function i(t){return((t=2*t-1)<0?n*a_(-t)*Math.sin((r-t)/e):2-n*a_(t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*aV)},i.period=function(e){return t(n,e)},i}(1,.3);function aG(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.blob()}function aY(t,n){return fetch(t,n).then(aG)}function aH(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.arrayBuffer()}function aW(t,n){return fetch(t,n).then(aH)}function aZ(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.text()}function aX(t,n){return fetch(t,n).then(aZ)}function aK(t){return function(n,e,r){return 2==arguments.length&&"function"==typeof e&&(r=e,e=void 0),aX(n,e).then(function(n){return t(n,r)})}}function aJ(t,n,e,r){3==arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=oK(t);return aX(n,e).then(function(t){return i.parse(t,r)})}var aQ=aK(oQ),a0=aK(o8);function a1(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})}function a2(t){if(!t.ok)throw Error(t.status+" "+t.statusText);if(204!==t.status&&205!==t.status)return t.json()}function a6(t,n){return fetch(t,n).then(a2)}function a3(t){return(n,e)=>aX(n,e).then(n=>(new DOMParser).parseFromString(n,t))}let a5=a3("application/xml");var a4=a3("text/html"),a8=a3("image/svg+xml");function a7(t,n){var e,r=1;function i(){var i,o,a=e.length,u=0,c=0;for(i=0;i<a;++i)u+=(o=e[i]).x,c+=o.y;for(u=(u/a-t)*r,c=(c/a-n)*r,i=0;i<a;++i)o=e[i],o.x-=u,o.y-=c}return null==t&&(t=0),null==n&&(n=0),i.initialize=function(t){e=t},i.x=function(n){return arguments.length?(t=+n,i):t},i.y=function(t){return arguments.length?(n=+t,i):n},i.strength=function(t){return arguments.length?(r=+t,i):r},i}function a9(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,a,u,c,l,f,s,h,d=t._root,p={data:r},v=t._x0,g=t._y0,y=t._x1,_=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((l=n>=(o=(v+y)/2))?v=o:y=o,(f=e>=(a=(g+_)/2))?g=a:_=a,i=d,!(d=d[s=f<<1|l]))return i[s]=p,t;if(u=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===u&&e===c)return p.next=d,i?i[s]=p:t._root=p,t;do i=i?i[s]=[,,,,]:t._root=[,,,,],(l=n>=(o=(v+y)/2))?v=o:y=o,(f=e>=(a=(g+_)/2))?g=a:_=a;while((s=f<<1|l)==(h=(c>=a)<<1|u>=o));return i[h]=d,i[s]=p,t}function ut(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function un(t){return t[0]}function ue(t){return t[1]}function ur(t,n,e){var r=new ui(null==n?un:n,null==e?ue:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function ui(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function uo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var ua=ur.prototype=ui.prototype;function uu(t){return function(){return t}}function uc(t){return(t()-.5)*1e-6}function ul(t){return t.x+t.vx}function uf(t){return t.y+t.vy}function us(t){var n,e,r,i=1,o=1;function a(){for(var t,a,c,l,f,s,h,d=n.length,p=0;p<o;++p)for(t=0,a=ur(n,ul,uf).visitAfter(u);t<d;++t)h=(s=e[(c=n[t]).index])*s,l=c.x+c.vx,f=c.y+c.vy,a.visit(v);function v(t,n,e,o,a){var u=t.data,d=t.r,p=s+d;if(u){if(u.index>c.index){var v=l-u.x-u.vx,g=f-u.y-u.vy,y=v*v+g*g;y<p*p&&(0===v&&(y+=(v=uc(r))*v),0===g&&(y+=(g=uc(r))*g),y=(p-(y=Math.sqrt(y)))/y*i,c.vx+=(v*=y)*(p=(d*=d)/(h+d)),c.vy+=(g*=y)*p,u.vx-=v*(p=1-p),u.vy-=g*p)}return}return n>l+p||o<l-p||e>f+p||a<f-p}}function u(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function c(){if(n){var r,i,o=n.length;for(r=0,e=Array(o);r<o;++r)e[(i=n[r]).index]=+t(i,r,n)}}return"function"!=typeof t&&(t=uu(null==t?1:+t)),a.initialize=function(t,e){n=t,r=e,c()},a.iterations=function(t){return arguments.length?(o=+t,a):o},a.strength=function(t){return arguments.length?(i=+t,a):i},a.radius=function(n){return arguments.length?(t="function"==typeof n?n:uu(+n),c(),a):t},a}function uh(t){return t.index}function ud(t,n){var e=t.get(n);if(!e)throw Error("node not found: "+n);return e}function up(t){var n,e,r,i,o,a,u=uh,c=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},l=uu(30),f=1;function s(r){for(var i=0,u=t.length;i<f;++i)for(var c,l,s,h,d,p,v,g=0;g<u;++g)l=(c=t[g]).source,p=((p=Math.sqrt((h=(s=c.target).x+s.vx-l.x-l.vx||uc(a))*h+(d=s.y+s.vy-l.y-l.vy||uc(a))*d))-e[g])/p*r*n[g],h*=p,d*=p,s.vx-=h*(v=o[g]),s.vy-=d*v,l.vx+=h*(v=1-v),l.vy+=d*v}function h(){if(r){var a,c,l=r.length,f=t.length,s=new Map(r.map((t,n)=>[u(t,n,r),t]));for(a=0,i=Array(l);a<f;++a)(c=t[a]).index=a,"object"!=typeof c.source&&(c.source=ud(s,c.source)),"object"!=typeof c.target&&(c.target=ud(s,c.target)),i[c.source.index]=(i[c.source.index]||0)+1,i[c.target.index]=(i[c.target.index]||0)+1;for(a=0,o=Array(f);a<f;++a)c=t[a],o[a]=i[c.source.index]/(i[c.source.index]+i[c.target.index]);n=Array(f),d(),e=Array(f),p()}}function d(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+c(t[e],e,t)}function p(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+l(t[n],n,t)}return null==t&&(t=[]),s.initialize=function(t,n){r=t,a=n,h()},s.links=function(n){return arguments.length?(t=n,h(),s):t},s.id=function(t){return arguments.length?(u=t,s):u},s.iterations=function(t){return arguments.length?(f=+t,s):f},s.strength=function(t){return arguments.length?(c="function"==typeof t?t:uu(+t),d(),s):c},s.distance=function(t){return arguments.length?(l="function"==typeof t?t:uu(+t),p(),s):l},s}function uv(t){return t.x}function ug(t){return t.y}ua.copy=function(){var t,n,e=new ui(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=uo(r),e;for(t=[{source:r,target:e._root=[,,,,]}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=[,,,,]}):r.target[i]=uo(n));return e},ua.add=function(t){let n=+this._x.call(null,t),e=+this._y.call(null,t);return a9(this.cover(n,e),n,e,t)},ua.addAll=function(t){var n,e,r,i,o=t.length,a=Array(o),u=Array(o),c=1/0,l=1/0,f=-1/0,s=-1/0;for(e=0;e<o;++e)!(isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n)))&&(a[e]=r,u[e]=i,r<c&&(c=r),r>f&&(f=r),i<l&&(l=i),i>s&&(s=i));if(c>f||l>s)return this;for(this.cover(c,l).cover(f,s),e=0;e<o;++e)a9(this,a[e],u[e],t[e]);return this},ua.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,c=i-e||1,l=this._root;e>t||t>=i||r>n||n>=o;)switch(u=(n<r)<<1|t<e,(a=[,,,,])[u]=l,l=a,c*=2,u){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=l)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},ua.data=function(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t},ua.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},ua.find=function(t,n,e){var r,i,o,a,u,c,l,f=this._x0,s=this._y0,h=this._x1,d=this._y1,p=[],v=this._root;for(v&&p.push(new ut(v,f,s,h,d)),null==e?e=1/0:(f=t-e,s=n-e,h=t+e,d=n+e,e*=e);c=p.pop();)if((v=c.node)&&!((i=c.x0)>h)&&!((o=c.y0)>d)&&!((a=c.x1)<f)&&!((u=c.y1)<s)){if(v.length){var g=(i+a)/2,y=(o+u)/2;p.push(new ut(v[3],g,y,a,u),new ut(v[2],i,y,g,u),new ut(v[1],g,o,a,y),new ut(v[0],i,o,g,y)),(l=(n>=y)<<1|t>=g)&&(c=p[p.length-1],p[p.length-1]=p[p.length-1-l],p[p.length-1-l]=c)}else{var _=t-+this._x.call(null,v.data),b=n-+this._y.call(null,v.data),m=_*_+b*b;if(m<e){var x=Math.sqrt(e=m);f=t-x,s=n-x,h=t+x,d=n+x,r=v.data}}}return r},ua.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var n,e,r,i,o,a,u,c,l,f,s,h,d=this._root,p=this._x0,v=this._y0,g=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((l=o>=(u=(p+g)/2))?p=u:g=u,(f=a>=(c=(v+y)/2))?v=c:y=c,n=d,!(d=d[s=f<<1|l]))return this;if(!d.length)break;(n[s+1&3]||n[s+2&3]||n[s+3&3])&&(e=n,h=s)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return((i=d.next)&&delete d.next,r)?i?r.next=i:delete r.next:n?(i?n[s]=i:delete n[s],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d)):this._root=i,this},ua.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},ua.root=function(){return this._root},ua.size=function(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t},ua.visit=function(t){var n,e,r,i,o,a,u=[],c=this._root;for(c&&u.push(new ut(c,this._x0,this._y0,this._x1,this._y1));n=u.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.x1,a=n.y1)&&c.length){var l=(r+o)/2,f=(i+a)/2;(e=c[3])&&u.push(new ut(e,l,f,o,a)),(e=c[2])&&u.push(new ut(e,r,f,l,a)),(e=c[1])&&u.push(new ut(e,l,i,o,f)),(e=c[0])&&u.push(new ut(e,r,i,l,f))}return this},ua.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new ut(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,c=n.x1,l=n.y1,f=(a+c)/2,s=(u+l)/2;(o=i[0])&&e.push(new ut(o,a,u,f,s)),(o=i[1])&&e.push(new ut(o,f,u,c,s)),(o=i[2])&&e.push(new ut(o,a,s,f,l)),(o=i[3])&&e.push(new ut(o,f,s,c,l))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},ua.x=function(t){return arguments.length?(this._x=t,this):this._x},ua.y=function(t){return arguments.length?(this._y=t,this):this._y};var uy=Math.PI*(3-Math.sqrt(5));function u_(t){let n;var e,r=1,i=.001,o=1-Math.pow(.001,1/300),a=0,u=.6,c=new Map,l=rf(h),f=ng("tick","end"),s=(n=1,()=>(n=(1664525*n+1013904223)%4294967296)/4294967296);function h(){d(),f.call("tick",e),r<i&&(l.stop(),f.call("end",e))}function d(n){var i,l,f=t.length;void 0===n&&(n=1);for(var s=0;s<n;++s)for(r+=(a-r)*o,c.forEach(function(t){t(r)}),i=0;i<f;++i)null==(l=t[i]).fx?l.x+=l.vx*=u:(l.x=l.fx,l.vx=0),null==l.fy?l.y+=l.vy*=u:(l.y=l.fy,l.vy=0);return e}function p(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=10*Math.sqrt(.5+e),o=e*uy;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function v(n){return n.initialize&&n.initialize(t,s),n}return null==t&&(t=[]),p(),e={tick:d,restart:function(){return l.restart(h),e},stop:function(){return l.stop(),e},nodes:function(n){return arguments.length?(t=n,p(),c.forEach(v),e):t},alpha:function(t){return arguments.length?(r=+t,e):r},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(o=+t,e):+o},alphaTarget:function(t){return arguments.length?(a=+t,e):a},velocityDecay:function(t){return arguments.length?(u=1-t,e):1-u},randomSource:function(t){return arguments.length?(s=t,c.forEach(v),e):s},force:function(t,n){return arguments.length>1?(null==n?c.delete(t):c.set(t,v(n)),e):c.get(t)},find:function(n,e,r){var i,o,a,u,c,l=0,f=t.length;for(null==r?r=1/0:r*=r,l=0;l<f;++l)(a=(i=n-(u=t[l]).x)*i+(o=e-u.y)*o)<r&&(c=u,r=a);return c},on:function(t,n){return arguments.length>1?(f.on(t,n),e):f.on(t)}}}function ub(){var t,n,e,r,i,o=uu(-30),a=1,u=1/0,c=.81;function l(e){var i,o=t.length,a=ur(t,uv,ug).visitAfter(s);for(r=e,i=0;i<o;++i)n=t[i],a.visit(h)}function f(){if(t){var n,e,r=t.length;for(n=0,i=Array(r);n<r;++n)i[(e=t[n]).index]=+o(e,n,t)}}function s(t){var n,e,r,o,a,u=0,c=0;if(t.length){for(r=o=a=0;a<4;++a)(n=t[a])&&(e=Math.abs(n.value))&&(u+=n.value,c+=e,r+=e*n.x,o+=e*n.y);t.x=r/c,t.y=o/c}else{(n=t).x=n.data.x,n.y=n.data.y;do u+=i[n.data.index];while(n=n.next)}t.value=u}function h(t,o,l,f){if(!t.value)return!0;var s=t.x-n.x,h=t.y-n.y,d=f-o,p=s*s+h*h;if(d*d/c<p)return p<u&&(0===s&&(p+=(s=uc(e))*s),0===h&&(p+=(h=uc(e))*h),p<a&&(p=Math.sqrt(a*p)),n.vx+=s*t.value*r/p,n.vy+=h*t.value*r/p),!0;if(!t.length&&!(p>=u)){(t.data!==n||t.next)&&(0===s&&(p+=(s=uc(e))*s),0===h&&(p+=(h=uc(e))*h),p<a&&(p=Math.sqrt(a*p)));do t.data!==n&&(d=i[t.data.index]*r/p,n.vx+=s*d,n.vy+=h*d);while(t=t.next)}}return l.initialize=function(n,r){t=n,e=r,f()},l.strength=function(t){return arguments.length?(o="function"==typeof t?t:uu(+t),f(),l):o},l.distanceMin=function(t){return arguments.length?(a=t*t,l):Math.sqrt(a)},l.distanceMax=function(t){return arguments.length?(u=t*t,l):Math.sqrt(u)},l.theta=function(t){return arguments.length?(c=t*t,l):Math.sqrt(c)},l}function um(t,n,e){var r,i,o,a=uu(.1);function u(t){for(var a=0,u=r.length;a<u;++a){var c=r[a],l=c.x-n||1e-6,f=c.y-e||1e-6,s=Math.sqrt(l*l+f*f),h=(o[a]-s)*i[a]*t/s;c.vx+=l*h,c.vy+=f*h}}function c(){if(r){var n,e=r.length;for(n=0,i=Array(e),o=Array(e);n<e;++n)o[n]=+t(r[n],n,r),i[n]=isNaN(o[n])?0:+a(r[n],n,r)}}return"function"!=typeof t&&(t=uu(+t)),null==n&&(n=0),null==e&&(e=0),u.initialize=function(t){r=t,c()},u.strength=function(t){return arguments.length?(a="function"==typeof t?t:uu(+t),c(),u):a},u.radius=function(n){return arguments.length?(t="function"==typeof n?n:uu(+n),c(),u):t},u.x=function(t){return arguments.length?(n=+t,u):n},u.y=function(t){return arguments.length?(e=+t,u):e},u}function ux(t){var n,e,r,i=uu(.1);function o(t){for(var i,o=0,a=n.length;o<a;++o)i=n[o],i.vx+=(r[o]-i.x)*e[o]*t}function a(){if(n){var o,a=n.length;for(o=0,e=Array(a),r=Array(a);o<a;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=uu(null==t?0:+t)),o.initialize=function(t){n=t,a()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:uu(+t),a(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:uu(+n),a(),o):t},o}function uw(t){var n,e,r,i=uu(.1);function o(t){for(var i,o=0,a=n.length;o<a;++o)i=n[o],i.vy+=(r[o]-i.y)*e[o]*t}function a(){if(n){var o,a=n.length;for(o=0,e=Array(a),r=Array(a);o<a;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=uu(null==t?0:+t)),o.initialize=function(t){n=t,a()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:uu(+t),a(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:uu(+n),a(),o):t},o}function uM(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function uT(t){return(t=uM(Math.abs(t)))?t[1]:NaN}var uA=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function uk(t){var n;if(!(n=uA.exec(t)))throw Error("invalid format: "+t);return new uS({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function uS(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function uE(t,n){var e=uM(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+Array(i-r.length+2).join("0")}uk.prototype=uS.prototype,uS.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let uN={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>uE(100*t,n),r:uE,s:function(t,n){var e=uM(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(cr=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=r.length;return o===a?r:o>a?r+Array(o-a+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+Array(1-o).join("0")+uM(t,Math.max(0,n+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function uC(t){return t}var uP=Array.prototype.map,uR=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function uO(t){var n,e,r,i=void 0===t.grouping||void 0===t.thousands?uC:(n=uP.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,o=[],a=0,u=n[0],c=0;i>0&&u>0&&(c+u+1>r&&(u=Math.max(1,r-c)),o.push(t.substring(i-=u,i+u)),!((c+=u+1)>r));)u=n[a=(a+1)%n.length];return o.reverse().join(e)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?uC:(r=uP.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return r[+t]})}),l=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function h(t){var n=(t=uk(t)).fill,e=t.align,r=t.sign,h=t.symbol,d=t.zero,p=t.width,v=t.comma,g=t.precision,y=t.trim,_=t.type;"n"===_?(v=!0,_="g"):uN[_]||(void 0===g&&(g=12),y=!0,_="g"),(d||"0"===n&&"="===e)&&(d=!0,n="0",e="=");var b="$"===h?o:"#"===h&&/[boxX]/.test(_)?"0"+_.toLowerCase():"",m="$"===h?a:/[%p]/.test(_)?l:"",x=uN[_],w=/[defgprs%]/.test(_);function M(t){var o,a,l,h=b,M=m;if("c"===_)M=x(t)+M,t="";else{var T=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:x(Math.abs(t),g),y&&(t=function(t){n:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break n;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),T&&0==+t&&"+"!==r&&(T=!1),h=(T?"("===r?r:f:"-"===r||"("===r?"":r)+h,M=("s"===_?uR[8+cr/3]:"")+M+(T&&"("===r?")":""),w){for(o=-1,a=t.length;++o<a;)if(48>(l=t.charCodeAt(o))||l>57){M=(46===l?u+t.slice(o+1):t.slice(o))+M,t=t.slice(0,o);break}}}v&&!d&&(t=i(t,1/0));var A=h.length+t.length+M.length,k=A<p?Array(p-A+1).join(n):"";switch(v&&d&&(t=i(k+t,k.length?p-M.length:1/0),k=""),e){case"<":t=h+t+M+k;break;case"=":t=h+k+t+M;break;case"^":t=k.slice(0,A=k.length>>1)+h+t+M+k.slice(A);break;default:t=k+h+t+M}return c(t)}return g=void 0===g?6:/[gprs]/.test(_)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),M.toString=function(){return t+""},M}return{format:h,formatPrefix:function(t,n){var e=h(((t=uk(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(uT(n)/3))),i=Math.pow(10,-r),o=uR[8+r/3];return function(t){return e(i*t)+o}}}}function uj(t){return co=(ci=uO(t)).format,ca=ci.formatPrefix,ci}function uz(t){return Math.max(0,-uT(Math.abs(t)))}function uD(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(uT(n)/3)))-uT(Math.abs(t)))}function uL(t,n){return Math.max(0,uT(n=Math.abs(n)-(t=Math.abs(t)))-uT(t))+1}uj({thousands:",",grouping:[3],currency:["$",""]});var uI=Math.PI,u$=uI/2,uB=uI/4,uV=2*uI,uU=180/uI,uF=uI/180,uq=Math.abs,uG=Math.atan,uY=Math.atan2,uH=Math.cos,uW=Math.ceil,uZ=Math.exp,uX=Math.hypot,uK=Math.log,uJ=Math.pow,uQ=Math.sin,u0=Math.sign||function(t){return t>0?1:t<0?-1:0},u1=Math.sqrt,u2=Math.tan;function u6(t){return t>1?0:t<-1?uI:Math.acos(t)}function u3(t){return t>1?u$:t<-1?-u$:Math.asin(t)}function u5(){}function u4(t,n){t&&u7.hasOwnProperty(t.type)&&u7[t.type](t,n)}var u8={Feature:function(t,n){u4(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)u4(e[r].geometry,n)}},u7={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){u9(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)u9(e[r],n,0)},Polygon:function(t,n){ct(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)ct(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)u4(e[r],n)}};function u9(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function ct(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)u9(t[e],n,1);n.polygonEnd()}function cn(t,n){t&&u8.hasOwnProperty(t.type)?u8[t.type](t,n):u4(t,n)}var ce,cr,ci,co,ca,cu,cc,cl,cf,cs,ch=new O,cd=new O,cp={point:u5,lineStart:u5,lineEnd:u5,polygonStart:function(){ch=new O,cp.lineStart=cv,cp.lineEnd=cg},polygonEnd:function(){var t=+ch;cd.add(t<0?uV+t:t),this.lineStart=this.lineEnd=this.point=u5},sphere:function(){cd.add(uV)}};function cv(){cp.point=cy}function cg(){c_(cu,cc)}function cy(t,n){cp.point=c_,cu=t,cc=n,t*=uF,n*=uF,cl=t,cf=uH(n=n/2+uB),cs=uQ(n)}function c_(t,n){t*=uF,n*=uF;var e=t-cl,r=e>=0?1:-1,i=r*e,o=uH(n=n/2+uB),a=uQ(n),u=cs*a,c=cf*o+u*uH(i),l=u*r*uQ(i);ch.add(uY(l,c)),cl=t,cf=o,cs=a}function cb(t){return cd=new O,cn(t,cp),2*cd}function cm(t){return[uY(t[1],t[0]),u3(t[2])]}function cx(t){var n=t[0],e=t[1],r=uH(e);return[r*uH(n),r*uQ(n),uQ(e)]}function cw(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function cM(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function cT(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function cA(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function ck(t){var n=u1(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var cS={point:cE,lineStart:cC,lineEnd:cP,polygonStart:function(){cS.point=cR,cS.lineStart=cO,cS.lineEnd=cj,lU=new O,cp.polygonStart()},polygonEnd:function(){cp.polygonEnd(),cS.point=cE,cS.lineStart=cC,cS.lineEnd=cP,ch<0?(lj=-(lD=180),lz=-(lL=90)):lU>1e-6?lL=90:lU<-.000001&&(lz=-90),lq[0]=lj,lq[1]=lD},sphere:function(){lj=-(lD=180),lz=-(lL=90)}};function cE(t,n){lF.push(lq=[lj=t,lD=t]),n<lz&&(lz=n),n>lL&&(lL=n)}function cN(t,n){var e=cx([t*uF,n*uF]);if(lV){var r=cM(lV,e),i=cM([r[1],-r[0],0],r);ck(i),i=cm(i);var o,a=t-lI,u=a>0?1:-1,c=i[0]*uU*u,l=uq(a)>180;l^(u*lI<c&&c<u*t)?(o=i[1]*uU)>lL&&(lL=o):l^(u*lI<(c=(c+360)%360-180)&&c<u*t)?(o=-i[1]*uU)<lz&&(lz=o):(n<lz&&(lz=n),n>lL&&(lL=n)),l?t<lI?cz(lj,t)>cz(lj,lD)&&(lD=t):cz(t,lD)>cz(lj,lD)&&(lj=t):lD>=lj?(t<lj&&(lj=t),t>lD&&(lD=t)):t>lI?cz(lj,t)>cz(lj,lD)&&(lD=t):cz(t,lD)>cz(lj,lD)&&(lj=t)}else lF.push(lq=[lj=t,lD=t]);n<lz&&(lz=n),n>lL&&(lL=n),lV=e,lI=t}function cC(){cS.point=cN}function cP(){lq[0]=lj,lq[1]=lD,cS.point=cE,lV=null}function cR(t,n){if(lV){var e=t-lI;lU.add(uq(e)>180?e+(e>0?360:-360):e)}else l$=t,lB=n;cp.point(t,n),cN(t,n)}function cO(){cp.lineStart()}function cj(){cR(l$,lB),cp.lineEnd(),uq(lU)>1e-6&&(lj=-(lD=180)),lq[0]=lj,lq[1]=lD,lV=null}function cz(t,n){return(n-=t)<0?n+360:n}function cD(t,n){return t[0]-n[0]}function cL(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function cI(t){var n,e,r,i,o,a,u;if(lL=lD=-(lj=lz=1/0),lF=[],cn(t,cS),e=lF.length){for(lF.sort(cD),n=1,o=[r=lF[0]];n<e;++n)cL(r,(i=lF[n])[0])||cL(r,i[1])?(cz(r[0],i[1])>cz(r[0],r[1])&&(r[1]=i[1]),cz(i[0],r[1])>cz(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(a=-1/0,e=o.length-1,n=0,r=o[e];n<=e;r=i,++n)i=o[n],(u=cz(r[1],i[0]))>a&&(a=u,lj=i[0],lD=r[1])}return lF=lq=null,lj===1/0||lz===1/0?[[NaN,NaN],[NaN,NaN]]:[[lj,lz],[lD,lL]]}var c$={sphere:u5,point:cB,lineStart:cU,lineEnd:cG,polygonStart:function(){c$.lineStart=cY,c$.lineEnd=cH},polygonEnd:function(){c$.lineStart=cU,c$.lineEnd=cG}};function cB(t,n){t*=uF;var e=uH(n*=uF);cV(e*uH(t),e*uQ(t),uQ(n))}function cV(t,n,e){++lG,lH+=(t-lH)/lG,lW+=(n-lW)/lG,lZ+=(e-lZ)/lG}function cU(){c$.point=cF}function cF(t,n){t*=uF;var e=uH(n*=uF);l3=e*uH(t),l5=e*uQ(t),l4=uQ(n),c$.point=cq,cV(l3,l5,l4)}function cq(t,n){t*=uF;var e=uH(n*=uF),r=e*uH(t),i=e*uQ(t),o=uQ(n),a=uY(u1((a=l5*o-l4*i)*a+(a=l4*r-l3*o)*a+(a=l3*i-l5*r)*a),l3*r+l5*i+l4*o);lY+=a,lX+=a*(l3+(l3=r)),lK+=a*(l5+(l5=i)),lJ+=a*(l4+(l4=o)),cV(l3,l5,l4)}function cG(){c$.point=cB}function cY(){c$.point=cW}function cH(){cZ(l2,l6),c$.point=cB}function cW(t,n){l2=t,l6=n,t*=uF,n*=uF,c$.point=cZ;var e=uH(n);l3=e*uH(t),l5=e*uQ(t),l4=uQ(n),cV(l3,l5,l4)}function cZ(t,n){t*=uF;var e=uH(n*=uF),r=e*uH(t),i=e*uQ(t),o=uQ(n),a=l5*o-l4*i,u=l4*r-l3*o,c=l3*i-l5*r,l=uX(a,u,c),f=u3(l),s=l&&-f/l;lQ.add(s*a),l0.add(s*u),l1.add(s*c),lY+=f,lX+=f*(l3+(l3=r)),lK+=f*(l5+(l5=i)),lJ+=f*(l4+(l4=o)),cV(l3,l5,l4)}function cX(t){lG=lY=lH=lW=lZ=lX=lK=lJ=0,lQ=new O,l0=new O,l1=new O,cn(t,c$);var n=+lQ,e=+l0,r=+l1,i=uX(n,e,r);return i<1e-12&&(n=lX,e=lK,r=lJ,lY<1e-6&&(n=lH,e=lW,r=lZ),(i=uX(n,e,r))<1e-12)?[NaN,NaN]:[uY(e,n)*uU,u3(r/i)*uU]}function cK(t){return function(){return t}}function cJ(t,n){function e(e,r){return n((e=t(e,r))[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function cQ(t,n){return uq(t)>uI&&(t-=Math.round(t/uV)*uV),[t,n]}function c0(t,n,e){return(t%=uV)?n||e?cJ(c2(t),c6(n,e)):c2(t):n||e?c6(n,e):cQ}function c1(t){return function(n,e){return uq(n+=t)>uI&&(n-=Math.round(n/uV)*uV),[n,e]}}function c2(t){var n=c1(t);return n.invert=c1(-t),n}function c6(t,n){var e=uH(t),r=uQ(t),i=uH(n),o=uQ(n);function a(t,n){var a=uH(n),u=uH(t)*a,c=uQ(t)*a,l=uQ(n),f=l*e+u*r;return[uY(c*i-f*o,u*e-l*r),u3(f*i+c*o)]}return a.invert=function(t,n){var a=uH(n),u=uH(t)*a,c=uQ(t)*a,l=uQ(n),f=l*i-c*o;return[uY(c*i+l*o,u*e+f*r),u3(f*e-u*r)]},a}function c3(t){function n(n){return n=t(n[0]*uF,n[1]*uF),n[0]*=uU,n[1]*=uU,n}return t=c0(t[0]*uF,t[1]*uF,t.length>2?t[2]*uF:0),n.invert=function(n){return n=t.invert(n[0]*uF,n[1]*uF),n[0]*=uU,n[1]*=uU,n},n}function c5(t,n,e,r,i,o){if(e){var a=uH(n),u=uQ(n),c=r*e;null==i?(i=n+r*uV,o=n-c/2):(i=c4(a,i),o=c4(a,o),(r>0?i<o:i>o)&&(i+=r*uV));for(var l,f=i;r>0?f>o:f<o;f-=c)l=cm([a,-u*uH(f),-u*uQ(f)]),t.point(l[0],l[1])}}function c4(t,n){n=cx(n),n[0]-=t,ck(n);var e=u6(-n[1]);return((0>-n[2]?-e:e)+uV-1e-6)%uV}function c8(){var t,n,e=cK([0,0]),r=cK(90),i=cK(2),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=uU,e[1]*=uU}};function a(){var a=e.apply(this,arguments),u=r.apply(this,arguments)*uF,c=i.apply(this,arguments)*uF;return t=[],n=c0(-a[0]*uF,-a[1]*uF,0).invert,c5(o,u,c,1),a={type:"Polygon",coordinates:[t]},t=n=null,a}return a.center=function(t){return arguments.length?(e="function"==typeof t?t:cK([+t[0],+t[1]]),a):e},a.radius=function(t){return arguments.length?(r="function"==typeof t?t:cK(+t),a):r},a.precision=function(t){return arguments.length?(i="function"==typeof t?t:cK(+t),a):i},a}function c7(){var t,n=[];return{point:function(n,e,r){t.push([n,e,r])},lineStart:function(){n.push(t=[])},lineEnd:u5,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function c9(t,n){return 1e-6>uq(t[0]-n[0])&&1e-6>uq(t[1]-n[1])}function lt(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function ln(t,n,e,r,i){var o,a,u=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],a=t[n];if(c9(r,a)){if(!r[2]&&!a[2]){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd();return}a[0]+=2e-6}u.push(e=new lt(r,t,null,!0)),c.push(e.o=new lt(r,null,e,!1)),u.push(e=new lt(a,t,null,!1)),c.push(e.o=new lt(a,null,e,!0))}}),u.length){for(c.sort(n),le(u),le(c),o=0,a=c.length;o<a;++o)c[o].e=e=!e;for(var l,f,s=u[0];;){for(var h=s,d=!0;h.v;)if((h=h.n)===s)return;l=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,a=l.length;o<a;++o)i.point((f=l[o])[0],f[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(o=(l=h.p.z).length-1;o>=0;--o)i.point((f=l[o])[0],f[1]);else r(h.x,h.p.x,-1,i);h=h.p}l=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}}function le(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}function lr(t){return uq(t[0])<=uI?t[0]:u0(t[0])*((uq(t[0])+uI)%uV-uI)}function li(t,n){var e=lr(n),r=n[1],i=uQ(r),o=[uQ(e),-uH(e),0],a=0,u=0,c=new O;1===i?r=u$+1e-6:-1===i&&(r=-u$-1e-6);for(var l=0,f=t.length;l<f;++l)if(h=(s=t[l]).length)for(var s,h,d=s[h-1],p=lr(d),v=d[1]/2+uB,g=uQ(v),y=uH(v),_=0;_<h;++_,p=m,g=w,y=M,d=b){var b=s[_],m=lr(b),x=b[1]/2+uB,w=uQ(x),M=uH(x),T=m-p,A=T>=0?1:-1,k=A*T,S=k>uI,E=g*w;if(c.add(uY(E*A*uQ(k),y*M+E*uH(k))),a+=S?T+A*uV:T,S^p>=e^m>=e){var N=cM(cx(d),cx(b));ck(N);var C=cM(o,N);ck(C);var P=(S^T>=0?-1:1)*u3(C[2]);(r>P||r===P&&(N[0]||N[1]))&&(u+=S^T>=0?1:-1)}}return(a<-.000001||a<1e-6&&c<-.000000000001)^1&u}function lo(t,n,e,r){return function(i){var o,a,u,c=n(i),l=c7(),f=n(l),s=!1,h={point:d,lineStart:v,lineEnd:g,polygonStart:function(){h.point=y,h.lineStart=_,h.lineEnd=b,a=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=v,h.lineEnd=g,a=tL(a);var t=li(o,r);a.length?(s||(i.polygonStart(),s=!0),ln(a,lu,t,e,i)):t&&(s||(i.polygonStart(),s=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),s&&(i.polygonEnd(),s=!1),a=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(n,e){t(n,e)&&i.point(n,e)}function p(t,n){c.point(t,n)}function v(){h.point=p,c.lineStart()}function g(){h.point=d,c.lineEnd()}function y(t,n){u.push([t,n]),f.point(t,n)}function _(){f.lineStart(),u=[]}function b(){y(u[0][0],u[0][1]),f.lineEnd();var t,n,e,r,c=f.clean(),h=l.result(),d=h.length;if(u.pop(),o.push(u),u=null,d){if(1&c){if((n=(e=h[0]).length-1)>0){for(s||(i.polygonStart(),s=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}return}d>1&&2&c&&h.push(h.pop().concat(h.shift())),a.push(h.filter(la))}}return h}}function la(t){return t.length>1}function lu(t,n){return((t=t.x)[0]<0?t[1]-u$-1e-6:u$-t[1])-((n=n.x)[0]<0?n[1]-u$-1e-6:u$-n[1])}cQ.invert=cQ;let lc=lo(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,a){var u,c,l,f,s,h,d=o>0?uI:-uI,p=uq(o-e);1e-6>uq(p-uI)?(t.point(e,r=(r+a)/2>0?u$:-u$),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),t.point(o,r),n=0):i!==d&&p>=uI&&(1e-6>uq(e-i)&&(e-=1e-6*i),1e-6>uq(o-d)&&(o-=1e-6*d),u=e,c=r,r=uq(h=uQ(u-(l=o)))>1e-6?uG((uQ(c)*(s=uH(a))*uQ(l)-uQ(a)*(f=uH(c))*uQ(u))/(f*s*h)):(c+a)/2,t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),n=0),t.point(e=o,r=a),i=d},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*u$,r.point(-uI,i),r.point(0,i),r.point(uI,i),r.point(uI,0),r.point(uI,-i),r.point(0,-i),r.point(-uI,-i),r.point(-uI,0),r.point(-uI,i);else if(uq(t[0]-n[0])>1e-6){var o=t[0]<n[0]?uI:-uI;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-uI,-u$]);function ll(t){var n=uH(t),e=2*uF,r=n>0,i=uq(n)>1e-6;function o(t,e){return uH(t)*uH(e)>n}function a(t,e,r){var i=cx(t),o=cx(e),a=[1,0,0],u=cM(i,o),c=cw(u,u),l=u[0],f=c-l*l;if(!f)return!r&&t;var s=cM(a,u),h=cA(a,n*c/f);cT(h,cA(u,-n*l/f));var d=cw(h,s),p=cw(s,s),v=d*d-p*(cw(h,h)-1);if(!(v<0)){var g=u1(v),y=cA(s,(-d-g)/p);if(cT(y,h),y=cm(y),!r)return y;var _,b=t[0],m=e[0],x=t[1],w=e[1];m<b&&(_=b,b=m,m=_);var M=m-b,T=1e-6>uq(M-uI);if(!T&&w<x&&(_=x,x=w,w=_),T||M<1e-6?T?x+w>0^y[1]<(1e-6>uq(y[0]-b)?x:w):x<=y[1]&&y[1]<=w:M>uI^(b<=y[0]&&y[0]<=m)){var A=cA(s,(-d+g)/p);return cT(A,h),[y,cm(A)]}}}function u(n,e){var i=r?t:uI-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return lo(o,function(t){var n,e,c,l,f;return{lineStart:function(){l=c=!1,f=1},point:function(s,h){var d,p,v=[s,h],g=o(s,h),y=r?g?0:u(s,h):g?u(s+(s<0?uI:-uI),h):0;!n&&(l=c=g)&&t.lineStart(),g!==c&&(!(p=a(n,v))||c9(n,p)||c9(v,p))&&(v[2]=1),g!==c?(f=0,g?(t.lineStart(),p=a(v,n),t.point(p[0],p[1])):(p=a(n,v),t.point(p[0],p[1],2),t.lineEnd()),n=p):i&&n&&r^g&&!(y&e)&&(d=a(v,n,!0))&&(f=0,r?(t.lineStart(),t.point(d[0][0],d[0][1]),t.point(d[1][0],d[1][1]),t.lineEnd()):(t.point(d[1][0],d[1][1]),t.lineEnd(),t.lineStart(),t.point(d[0][0],d[0][1],3))),!g||n&&c9(n,v)||t.point(v[0],v[1]),n=v,c=g,e=y},lineEnd:function(){c&&t.lineEnd(),n=null},clean:function(){return f|(l&&c)<<1}}},function(n,r,i,o){c5(o,t,e,i,n,r)},r?[0,-t]:[-uI,t-uI])}function lf(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,u,l){var f=0,s=0;if(null==i||(f=a(i,u))!==(s=a(o,u))||0>c(i,o)^u>0)do l.point(0===f||3===f?t:e,f>1?r:n);while((f=(f+u+4)%4)!==s);else l.point(o[0],o[1])}function a(r,i){return 1e-6>uq(r[0]-t)?i>0?0:3:1e-6>uq(r[0]-e)?i>0?2:1:1e-6>uq(r[1]-n)?i>0?1:0:i>0?3:2}function u(t,n){return c(t.x,n.x)}function c(t,n){var e=a(t,1),r=a(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(a){var c,l,f,s,h,d,p,v,g,y,_,b=a,m=c7(),x={point:w,lineStart:function(){x.point=M,l&&l.push(f=[]),y=!0,g=!1,p=v=NaN},lineEnd:function(){c&&(M(s,h),d&&g&&m.rejoin(),c.push(m.result())),x.point=w,g&&b.lineEnd()},polygonStart:function(){b=m,c=[],l=[],_=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=l.length;e<i;++e)for(var o,a,u=l[e],c=1,f=u.length,s=u[0],h=s[0],d=s[1];c<f;++c)o=h,a=d,h=(s=u[c])[0],d=s[1],a<=r?d>r&&(h-o)*(r-a)>(d-a)*(t-o)&&++n:d<=r&&(h-o)*(r-a)<(d-a)*(t-o)&&--n;return n}(),e=_&&n,i=(c=tL(c)).length;(e||i)&&(a.polygonStart(),e&&(a.lineStart(),o(null,null,1,a),a.lineEnd()),i&&ln(c,u,n,o,a),a.polygonEnd()),b=a,c=l=f=null}};function w(t,n){i(t,n)&&b.point(t,n)}function M(o,a){var u=i(o,a);if(l&&f.push([o,a]),y)s=o,h=a,d=u,y=!1,u&&(b.lineStart(),b.point(o,a));else if(u&&g)b.point(o,a);else{var c=[p=Math.max(-1e9,Math.min(1e9,p)),v=Math.max(-1e9,Math.min(1e9,v))],m=[o=Math.max(-1e9,Math.min(1e9,o)),a=Math.max(-1e9,Math.min(1e9,a))];(function(t,n,e,r,i,o){var a,u=t[0],c=t[1],l=n[0],f=n[1],s=0,h=1,d=l-u,p=f-c;if(a=e-u,d||!(a>0)){if(a/=d,d<0){if(a<s)return;a<h&&(h=a)}else if(d>0){if(a>h)return;a>s&&(s=a)}if(a=i-u,d||!(a<0)){if(a/=d,d<0){if(a>h)return;a>s&&(s=a)}else if(d>0){if(a<s)return;a<h&&(h=a)}if(a=r-c,p||!(a>0)){if(a/=p,p<0){if(a<s)return;a<h&&(h=a)}else if(p>0){if(a>h)return;a>s&&(s=a)}if(a=o-c,p||!(a<0)){if(a/=p,p<0){if(a>h)return;a>s&&(s=a)}else if(p>0){if(a<s)return;a<h&&(h=a)}return s>0&&(t[0]=u+s*d,t[1]=c+s*p),h<1&&(n[0]=u+h*d,n[1]=c+h*p),!0}}}}})(c,m,t,n,e,r)?(g||(b.lineStart(),b.point(c[0],c[1])),b.point(m[0],m[1]),u||b.lineEnd(),_=!1):u&&(b.lineStart(),b.point(o,a),_=!1)}p=o,v=a,g=u}return x}}function ls(){var t,n,e,r=0,i=0,o=960,a=500;return e={stream:function(e){return t&&n===e?t:t=lf(r,i,o,a)(n=e)},extent:function(u){return arguments.length?(r=+u[0][0],i=+u[0][1],o=+u[1][0],a=+u[1][1],t=n=null,e):[[r,i],[o,a]]}}}var lh={sphere:u5,point:u5,lineStart:function(){lh.point=lp,lh.lineEnd=ld},lineEnd:u5,polygonStart:u5,polygonEnd:u5};function ld(){lh.point=lh.lineEnd=u5}function lp(t,n){t*=uF,n*=uF,l7=t,l9=uQ(n),ft=uH(n),lh.point=lv}function lv(t,n){t*=uF;var e=uQ(n*=uF),r=uH(n),i=uq(t-l7),o=uH(i),a=r*uQ(i),u=ft*e-l9*r*o,c=l9*e+ft*r*o;l8.add(uY(u1(a*a+u*u),c)),l7=t,l9=e,ft=r}function lg(t){return l8=new O,cn(t,lh),+l8}var ly=[null,null],l_={type:"LineString",coordinates:ly};function lb(t,n){return ly[0]=t,ly[1]=n,lg(l_)}var lm={Feature:function(t,n){return lw(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(lw(e[r].geometry,n))return!0;return!1}},lx={Sphere:function(){return!0},Point:function(t,n){return 0===lb(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(0===lb(e[r],n))return!0;return!1},LineString:function(t,n){return lM(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(lM(e[r],n))return!0;return!1},Polygon:function(t,n){return lT(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(lT(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(lw(e[r],n))return!0;return!1}};function lw(t,n){return!!(t&&lx.hasOwnProperty(t.type))&&lx[t.type](t,n)}function lM(t,n){for(var e,r,i,o=0,a=t.length;o<a;o++){if(0===(r=lb(t[o],n))||o>0&&(i=lb(t[o],t[o-1]))>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<1e-12*i)return!0;e=r}return!1}function lT(t,n){return!!li(t.map(lA),lk(n))}function lA(t){return(t=t.map(lk)).pop(),t}function lk(t){return[t[0]*uF,t[1]*uF]}function lS(t,n){return(t&&lm.hasOwnProperty(t.type)?lm[t.type]:lw)(t,n)}function lE(t,n,e){var r=tV(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function lN(t,n,e){var r=tV(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function lC(){var t,n,e,r,i,o,a,u,c,l,f,s,h=10,d=10,p=90,v=360,g=2.5;function y(){return{type:"MultiLineString",coordinates:_()}}function _(){return tV(uW(r/p)*p,e,p).map(f).concat(tV(uW(u/v)*v,a,v).map(s)).concat(tV(uW(n/h)*h,t,h).filter(function(t){return uq(t%p)>1e-6}).map(c)).concat(tV(uW(o/d)*d,i,d).filter(function(t){return uq(t%v)>1e-6}).map(l))}return y.lines=function(){return _().map(function(t){return{type:"LineString",coordinates:t}})},y.outline=function(){return{type:"Polygon",coordinates:[f(r).concat(s(a).slice(1),f(e).reverse().slice(1),s(u).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],u=+t[0][1],a=+t[1][1],r>e&&(t=r,r=e,e=t),u>a&&(t=u,u=a,a=t),y.precision(g)):[[r,u],[e,a]]},y.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),y.precision(g)):[[n,o],[t,i]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(p=+t[0],v=+t[1],y):[p,v]},y.stepMinor=function(t){return arguments.length?(h=+t[0],d=+t[1],y):[h,d]},y.precision=function(h){return arguments.length?(g=+h,c=lE(o,i,90),l=lN(n,t,g),f=lE(u,a,90),s=lN(r,e,g),y):g},y.extentMajor([[-180,-89.999999],[180,89.999999]]).extentMinor([[-180,-80.000001],[180,80.000001]])}function lP(){return lC()()}function lR(t,n){var e,r,i=t[0]*uF,o=t[1]*uF,a=n[0]*uF,u=n[1]*uF,c=uH(o),l=uQ(o),f=uH(u),s=uQ(u),h=c*uH(i),d=c*uQ(i),p=f*uH(a),v=f*uQ(a),g=2*u3(u1((e=uQ((e=u-o)/2))*e+c*f*((r=uQ((r=a-i)/2))*r))),y=uQ(g),_=g?function(t){var n=uQ(t*=g)/y,e=uQ(g-t)/y,r=e*h+n*p,i=e*d+n*v;return[uY(i,r)*uU,uY(e*l+n*s,u1(r*r+i*i))*uU]}:function(){return[i*uU,o*uU]};return _.distance=g,_}let lO=t=>t;var lj,lz,lD,lL,lI,l$,lB,lV,lU,lF,lq,lG,lY,lH,lW,lZ,lX,lK,lJ,lQ,l0,l1,l2,l6,l3,l5,l4,l8,l7,l9,ft,fn,fe,fr,fi,fo=new O,fa=new O,fu={point:u5,lineStart:u5,lineEnd:u5,polygonStart:function(){fu.lineStart=fc,fu.lineEnd=fs},polygonEnd:function(){fu.lineStart=fu.lineEnd=fu.point=u5,fo.add(uq(fa)),fa=new O},result:function(){var t=fo/2;return fo=new O,t}};function fc(){fu.point=fl}function fl(t,n){fu.point=ff,fn=fr=t,fe=fi=n}function ff(t,n){fa.add(fi*t-fr*n),fr=t,fi=n}function fs(){ff(fn,fe)}var fh=1/0,fd=1/0,fp=-1/0,fv=fp;let fg={point:function(t,n){t<fh&&(fh=t),t>fp&&(fp=t),n<fd&&(fd=n),n>fv&&(fv=n)},lineStart:u5,lineEnd:u5,polygonStart:u5,polygonEnd:u5,result:function(){var t=[[fh,fd],[fp,fv]];return fp=fv=-(fd=fh=1/0),t}};var fy,f_,fb,fm,fx=0,fw=0,fM=0,fT=0,fA=0,fk=0,fS=0,fE=0,fN=0,fC={point:fP,lineStart:fR,lineEnd:fz,polygonStart:function(){fC.lineStart=fD,fC.lineEnd=fL},polygonEnd:function(){fC.point=fP,fC.lineStart=fR,fC.lineEnd=fz},result:function(){var t=fN?[fS/fN,fE/fN]:fk?[fT/fk,fA/fk]:fM?[fx/fM,fw/fM]:[NaN,NaN];return fx=fw=fM=fT=fA=fk=fS=fE=fN=0,t}};function fP(t,n){fx+=t,fw+=n,++fM}function fR(){fC.point=fO}function fO(t,n){fC.point=fj,fP(fb=t,fm=n)}function fj(t,n){var e=t-fb,r=n-fm,i=u1(e*e+r*r);fT+=i*(fb+t)/2,fA+=i*(fm+n)/2,fk+=i,fP(fb=t,fm=n)}function fz(){fC.point=fP}function fD(){fC.point=fI}function fL(){f$(fy,f_)}function fI(t,n){fC.point=f$,fP(fy=fb=t,f_=fm=n)}function f$(t,n){var e=t-fb,r=n-fm,i=u1(e*e+r*r);fT+=i*(fb+t)/2,fA+=i*(fm+n)/2,fk+=i,fS+=(i=fm*t-fb*n)*(fb+t),fE+=i*(fm+n),fN+=3*i,fP(fb=t,fm=n)}function fB(t){this._context=t}fB.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,uV)}},result:u5};var fV,fU,fF,fq,fG,fY=new O,fH={point:u5,lineStart:function(){fH.point=fW},lineEnd:function(){fV&&fZ(fU,fF),fH.point=u5},polygonStart:function(){fV=!0},polygonEnd:function(){fV=null},result:function(){var t=+fY;return fY=new O,t}};function fW(t,n){fH.point=fZ,fU=fq=t,fF=fG=n}function fZ(t,n){fq-=t,fG-=n,fY.add(u1(fq*fq+fG*fG)),fq=t,fG=n}class fX{constructor(t){this._append=null==t?fK:function(t){let n=Math.floor(t);if(!(n>=0))throw RangeError(`invalid digits: ${t}`);if(n>15)return fK;if(n!==r){let t=10**n;r=n,i=function(n){let e=1;this._+=n[0];for(let r=n.length;e<r;++e)this._+=Math.round(arguments[e]*t)/t+n[e]}}return i}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:this._append`M${t},${n}`,this._point=1;break;case 1:this._append`L${t},${n}`;break;default:if(this._append`M${t},${n}`,this._radius!==o||this._append!==i){let t=this._radius,n=this._;this._="",this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`,o=t,i=this._append,a=this._,this._=n}this._+=a}}result(){let t=this._;return this._="",t.length?t:null}}function fK(t){let n=1;this._+=t[0];for(let e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function fJ(t,n){let e=3,r=4.5,i,o;function a(t){return t&&("function"==typeof r&&o.pointRadius(+r.apply(this,arguments)),cn(t,i(o))),o.result()}return a.area=function(t){return cn(t,i(fu)),fu.result()},a.measure=function(t){return cn(t,i(fH)),fH.result()},a.bounds=function(t){return cn(t,i(fg)),fg.result()},a.centroid=function(t){return cn(t,i(fC)),fC.result()},a.projection=function(n){return arguments.length?(i=null==n?(t=null,lO):(t=n).stream,a):t},a.context=function(t){return arguments.length?(o=null==t?(n=null,new fX(e)):new fB(n=t),"function"!=typeof r&&o.pointRadius(r),a):n},a.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(o.pointRadius(+t),+t),a):r},a.digits=function(t){if(!arguments.length)return e;if(null==t)e=null;else{let n=Math.floor(t);if(!(n>=0))throw RangeError(`invalid digits: ${t}`);e=n}return null===n&&(o=new fX(e)),a},a.projection(t).digits(e).context(n)}function fQ(t){return{stream:f0(t)}}function f0(t){return function(n){var e=new f1;for(var r in t)e[r]=t[r];return e.stream=n,e}}function f1(){}function f2(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),cn(e,t.stream(fg)),n(fg.result()),null!=r&&t.clipExtent(r),t}function f6(t,n,e){return f2(t,function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),a=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,u=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([a,u])},e)}function f3(t,n,e){return f6(t,[[0,0],n],e)}function f5(t,n,e){return f2(t,function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,a=-i*e[0][1];t.scale(150*i).translate([o,a])},e)}function f4(t,n,e){return f2(t,function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],a=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,a])},e)}f1.prototype={constructor:f1,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var f8=uH(30*uF);function f7(t,n){return+n?function(t,n){function e(r,i,o,a,u,c,l,f,s,h,d,p,v,g){var y=l-r,_=f-i,b=y*y+_*_;if(b>4*n&&v--){var m=a+h,x=u+d,w=c+p,M=u1(m*m+x*x+w*w),T=u3(w/=M),A=1e-6>uq(uq(w)-1)||1e-6>uq(o-s)?(o+s)/2:uY(x,m),k=t(A,T),S=k[0],E=k[1],N=S-r,C=E-i,P=_*N-y*C;(P*P/b>n||uq((y*N+_*C)/b-.5)>.3||a*h+u*d+c*p<f8)&&(e(r,i,o,a,u,c,S,E,A,m/=M,x/=M,w,v,g),g.point(S,E),e(S,E,A,m,x,w,l,f,s,h,d,p,v,g))}}return function(n){var r,i,o,a,u,c,l,f,s,h,d,p,v={point:g,lineStart:y,lineEnd:b,polygonStart:function(){n.polygonStart(),v.lineStart=m},polygonEnd:function(){n.polygonEnd(),v.lineStart=y}};function g(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){f=NaN,v.point=_,n.lineStart()}function _(r,i){var o=cx([r,i]),a=t(r,i);e(f,s,l,h,d,p,f=a[0],s=a[1],l=r,h=o[0],d=o[1],p=o[2],16,n),n.point(f,s)}function b(){v.point=g,n.lineEnd()}function m(){y(),v.point=x,v.lineEnd=w}function x(t,n){_(r=t,n),i=f,o=s,a=h,u=d,c=p,v.point=_}function w(){e(f,s,l,h,d,p,i,o,r,a,u,c,16,n),v.lineEnd=b,b()}return v}}(t,n):f0({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}var f9=f0({point:function(t,n){this.stream.point(t*uF,n*uF)}});function st(t,n,e,r,i,o){if(!o)return function(t,n,e,r,i){function o(o,a){return[n+t*(o*=r),e-t*(a*=i)]}return o.invert=function(o,a){return[(o-n)/t*r,(e-a)/t*i]},o}(t,n,e,r,i);var a=uH(o),u=uQ(o),c=a*t,l=u*t,f=a/t,s=u/t,h=(u*e-a*n)/t,d=(u*n+a*e)/t;function p(t,o){return[c*(t*=r)-l*(o*=i)+n,e-l*t-c*o]}return p.invert=function(t,n){return[r*(f*t-s*n+h),i*(d-s*t-f*n)]},p}function sn(t){return se(function(){return t})()}function se(t){var n,e,r,i,o,a,u,c,l,f,s=150,h=480,d=250,p=0,v=0,g=0,y=0,_=0,b=0,m=1,x=1,w=null,M=lc,T=null,A=lO,k=.5;function S(t){return c(t[0]*uF,t[1]*uF)}function E(t){return(t=c.invert(t[0],t[1]))&&[t[0]*uU,t[1]*uU]}function N(){var t=st(s,0,0,m,x,b).apply(null,n(p,v)),r=st(s,h-t[0],d-t[1],m,x,b);return e=c0(g,y,_),u=cJ(n,r),c=cJ(e,u),a=f7(u,k),C()}function C(){return l=f=null,S}return S.stream=function(t){var n;return l&&f===t?l:l=f9((n=e,f0({point:function(t,e){var r=n(t,e);return this.stream.point(r[0],r[1])}}))(M(a(A(f=t)))))},S.preclip=function(t){return arguments.length?(M=t,w=void 0,C()):M},S.postclip=function(t){return arguments.length?(A=t,T=r=i=o=null,C()):A},S.clipAngle=function(t){return arguments.length?(M=+t?ll(w=t*uF):(w=null,lc),C()):w*uU},S.clipExtent=function(t){return arguments.length?(A=null==t?(T=r=i=o=null,lO):lf(T=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),C()):null==T?null:[[T,r],[i,o]]},S.scale=function(t){return arguments.length?(s=+t,N()):s},S.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],N()):[h,d]},S.center=function(t){return arguments.length?(p=t[0]%360*uF,v=t[1]%360*uF,N()):[p*uU,v*uU]},S.rotate=function(t){return arguments.length?(g=t[0]%360*uF,y=t[1]%360*uF,_=t.length>2?t[2]%360*uF:0,N()):[g*uU,y*uU,_*uU]},S.angle=function(t){return arguments.length?(b=t%360*uF,N()):b*uU},S.reflectX=function(t){return arguments.length?(m=t?-1:1,N()):m<0},S.reflectY=function(t){return arguments.length?(x=t?-1:1,N()):x<0},S.precision=function(t){return arguments.length?(a=f7(u,k=t*t),C()):u1(k)},S.fitExtent=function(t,n){return f6(S,t,n)},S.fitSize=function(t,n){return f3(S,t,n)},S.fitWidth=function(t,n){return f5(S,t,n)},S.fitHeight=function(t,n){return f4(S,t,n)},function(){return n=t.apply(this,arguments),S.invert=n.invert&&E,N()}}function sr(t){var n=0,e=uI/3,r=se(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*uF,e=t[1]*uF):[n*uU,e*uU]},i}function si(t,n){var e=uQ(t),r=(e+uQ(n))/2;if(1e-6>uq(r))return function(t){var n=uH(t);function e(t,e){return[t*n,uQ(e)/n]}return e.invert=function(t,e){return[t/n,u3(e*n)]},e}(t);var i=1+e*(2*r-e),o=u1(i)/r;function a(t,n){var e=u1(i-2*r*uQ(n))/r;return[e*uQ(t*=r),o-e*uH(t)]}return a.invert=function(t,n){var e=o-n,a=uY(t,uq(e))*u0(e);return e*r<0&&(a-=uI*u0(t)*u0(e)),[a/r,u3((i-(t*t+e*e)*r*r)/(2*r))]},a}function so(){return sr(si).scale(155.424).center([0,33.6442])}function sa(){return so().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function su(){var t,n,e,r,i,o,a=sa(),u=so().rotate([154,0]).center([-2,58.5]).parallels([55,65]),c=so().rotate([157,0]).center([-3,19.9]).parallels([8,18]),l={point:function(t,n){o=[t,n]}};function f(t){var n=t[0],a=t[1];return o=null,e.point(n,a),o||(r.point(n,a),o)||(i.point(n,a),o)}function s(){return t=n=null,f}return f.invert=function(t){var n=a.scale(),e=a.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?u:i>=.166&&i<.234&&r>=-.214&&r<-.115?c:a).invert(t)},f.stream=function(e){var r,i;return t&&n===e?t:(i=(r=[a.stream(n=e),u.stream(e),c.stream(e)]).length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}})},f.precision=function(t){return arguments.length?(a.precision(t),u.precision(t),c.precision(t),s()):a.precision()},f.scale=function(t){return arguments.length?(a.scale(t),u.scale(.35*t),c.scale(t),f.translate(a.translate())):a.scale()},f.translate=function(t){if(!arguments.length)return a.translate();var n=a.scale(),o=+t[0],f=+t[1];return e=a.translate(t).clipExtent([[o-.455*n,f-.238*n],[o+.455*n,f+.238*n]]).stream(l),r=u.translate([o-.307*n,f+.201*n]).clipExtent([[o-.425*n+1e-6,f+.12*n+1e-6],[o-.214*n-1e-6,f+.234*n-1e-6]]).stream(l),i=c.translate([o-.205*n,f+.212*n]).clipExtent([[o-.214*n+1e-6,f+.166*n+1e-6],[o-.115*n-1e-6,f+.234*n-1e-6]]).stream(l),s()},f.fitExtent=function(t,n){return f6(f,t,n)},f.fitSize=function(t,n){return f3(f,t,n)},f.fitWidth=function(t,n){return f5(f,t,n)},f.fitHeight=function(t,n){return f4(f,t,n)},f.scale(1070)}function sc(t){return function(n,e){var r=uH(n),i=uH(e),o=t(r*i);return o===1/0?[2,0]:[o*i*uQ(n),o*uQ(e)]}}function sl(t){return function(n,e){var r=u1(n*n+e*e),i=t(r),o=uQ(i);return[uY(n*o,r*uH(i)),u3(r&&e*o/r)]}}var sf=sc(function(t){return u1(2/(1+t))});function ss(){return sn(sf).scale(124.75).clipAngle(179.999)}sf.invert=sl(function(t){return 2*u3(t/2)});var sh=sc(function(t){return(t=u6(t))&&t/uQ(t)});function sd(){return sn(sh).scale(79.4188).clipAngle(179.999)}function sp(t,n){return[t,uK(u2((u$+n)/2))]}function sv(){return sg(sp).scale(961/uV)}function sg(t){var n,e,r,i=sn(t),o=i.center,a=i.scale,u=i.translate,c=i.clipExtent,l=null;function f(){var o=uI*a(),u=i(c3(i.rotate()).invert([0,0]));return c(null==l?[[u[0]-o,u[1]-o],[u[0]+o,u[1]+o]]:t===sp?[[Math.max(u[0]-o,l),n],[Math.min(u[0]+o,e),r]]:[[l,Math.max(u[1]-o,n)],[e,Math.min(u[1]+o,r)]])}return i.scale=function(t){return arguments.length?(a(t),f()):a()},i.translate=function(t){return arguments.length?(u(t),f()):u()},i.center=function(t){return arguments.length?(o(t),f()):o()},i.clipExtent=function(t){return arguments.length?(null==t?l=n=e=r=null:(l=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),f()):null==l?null:[[l,n],[e,r]]},f()}function sy(t){return u2((u$+t)/2)}function s_(t,n){var e=uH(t),r=t===n?uQ(t):uK(e/uH(n))/uK(sy(n)/sy(t)),i=e*uJ(sy(t),r)/r;if(!r)return sp;function o(t,n){i>0?n<-u$+1e-6&&(n=-u$+1e-6):n>u$-1e-6&&(n=u$-1e-6);var e=i/uJ(sy(n),r);return[e*uQ(r*t),i-e*uH(r*t)]}return o.invert=function(t,n){var e=i-n,o=u0(r)*u1(t*t+e*e),a=uY(t,uq(e))*u0(e);return e*r<0&&(a-=uI*u0(t)*u0(e)),[a/r,2*uG(uJ(i/o,1/r))-u$]},o}function sb(){return sr(s_).scale(109.5).parallels([30,30])}function sm(t,n){return[t,n]}function sx(){return sn(sm).scale(152.63)}function sw(t,n){var e=uH(t),r=t===n?uQ(t):(e-uH(n))/(n-t),i=e/r+t;if(1e-6>uq(r))return sm;function o(t,n){var e=i-n,o=r*t;return[e*uQ(o),i-e*uH(o)]}return o.invert=function(t,n){var e=i-n,o=uY(t,uq(e))*u0(e);return e*r<0&&(o-=uI*u0(t)*u0(e)),[o/r,i-u0(r)*u1(t*t+e*e)]},o}function sM(){return sr(sw).scale(131.154).center([0,13.9389])}sh.invert=sl(function(t){return t}),sp.invert=function(t,n){return[t,2*uG(uZ(n))-u$]},sm.invert=sm;var sT=u1(3)/2;function sA(t,n){var e=u3(sT*uQ(n)),r=e*e,i=r*r*r;return[t*uH(e)/(sT*(1.340264+-.24331799999999998*r+i*(.0062510000000000005+.034164*r))),e*(1.340264+-.081106*r+i*(893e-6+.003796*r))]}function sk(){return sn(sA).scale(177.158)}function sS(t,n){var e=uH(n),r=uH(t)*e;return[e*uQ(t)/r,uQ(n)/r]}function sE(){return sn(sS).scale(144.049).clipAngle(60)}function sN(){var t,n,e,r,i,o,a,u=1,c=0,l=0,f=1,s=1,h=0,d=null,p=1,v=1,g=f0({point:function(t,n){var e=b([t,n]);this.stream.point(e[0],e[1])}}),y=lO;function _(){return p=u*f,v=u*s,o=a=null,b}function b(e){var r=e[0]*p,i=e[1]*v;if(h){var o=i*t-r*n;r=r*t+i*n,i=o}return[r+c,i+l]}return b.invert=function(e){var r=e[0]-c,i=e[1]-l;if(h){var o=i*t+r*n;r=r*t-i*n,i=o}return[r/p,i/v]},b.stream=function(t){return o&&a===t?o:o=g(y(a=t))},b.postclip=function(t){return arguments.length?(y=t,d=e=r=i=null,_()):y},b.clipExtent=function(t){return arguments.length?(y=null==t?(d=e=r=i=null,lO):lf(d=+t[0][0],e=+t[0][1],r=+t[1][0],i=+t[1][1]),_()):null==d?null:[[d,e],[r,i]]},b.scale=function(t){return arguments.length?(u=+t,_()):u},b.translate=function(t){return arguments.length?(c=+t[0],l=+t[1],_()):[c,l]},b.angle=function(e){return arguments.length?(n=uQ(h=e%360*uF),t=uH(h),_()):h*uU},b.reflectX=function(t){return arguments.length?(f=t?-1:1,_()):f<0},b.reflectY=function(t){return arguments.length?(s=t?-1:1,_()):s<0},b.fitExtent=function(t,n){return f6(b,t,n)},b.fitSize=function(t,n){return f3(b,t,n)},b.fitWidth=function(t,n){return f5(b,t,n)},b.fitHeight=function(t,n){return f4(b,t,n)},b}function sC(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(-.013791+r*(.003971*e-.001529*r))),n*(1.007226+e*(.015085+r*(-.044475+.028874*e-.005916*r)))]}function sP(){return sn(sC).scale(175.295)}function sR(t,n){return[uH(n)*uQ(t),uQ(n)]}function sO(){return sn(sR).scale(249.5).clipAngle(90.000001)}function sj(t,n){var e=uH(n),r=1+uH(t)*e;return[e*uQ(t)/r,uQ(n)/r]}function sz(){return sn(sj).scale(250).clipAngle(142)}function sD(t,n){return[uK(u2((u$+n)/2)),-t]}function sL(){var t=sg(sD),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)}function sI(t,n){return t.parent===n.parent?1:2}function s$(t,n){return t+n.x}function sB(t,n){return Math.max(t,n.y)}function sV(){var t=sI,n=1,e=1,r=!1;function i(i){var o,a=0;i.eachAfter(function(n){var e=n.children;e?(n.x=e.reduce(s$,0)/e.length,n.y=1+e.reduce(sB,0)):(n.x=o?a+=t(n,o):0,n.y=0,o=n)});var u=function(t){for(var n;n=t.children;)t=n[0];return t}(i),c=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(i),l=u.x-t(u,c)/2,f=c.x+t(c,u)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*n,t.y=(i.y-t.y)*e}:function(t){t.x=(t.x-l)/(f-l)*n,t.y=(1-(i.y?t.y/i.y:1))*e})}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function sU(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function sF(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=sG)):void 0===n&&(n=sq);for(var e,r,i,o,a,u=new sW(t),c=[u];e=c.pop();)if((i=n(e.data))&&(a=(i=Array.from(i)).length))for(e.children=i,o=a-1;o>=0;--o)c.push(r=i[o]=new sW(i[o])),r.parent=e,r.depth=e.depth+1;return u.eachBefore(sH)}function sq(t){return t.children}function sG(t){return Array.isArray(t)?t[1]:null}function sY(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function sH(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function sW(t){this.data=t,this.depth=this.height=0,this.parent=null}function sZ(t){return null==t?null:sX(t)}function sX(t){if("function"!=typeof t)throw Error();return t}function sK(){return 0}function sJ(t){return function(){return t}}function sQ(){let t=1;return()=>(t=(1664525*t+1013904223)%4294967296)/4294967296}function s0(t){return s1(t,sQ())}function s1(t,n){for(var e,r,i=0,o=(t=function(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}(Array.from(t),n)).length,a=[];i<o;)e=t[i],r&&s6(r,e)?++i:(r=function(t){switch(t.length){case 1:var n;return{x:(n=t[0]).x,y:n.y,r:n.r};case 2:return s5(t[0],t[1]);case 3:return s4(t[0],t[1],t[2])}}(a=function(t,n){var e,r;if(s3(n,t))return[n];for(e=0;e<t.length;++e)if(s2(n,t[e])&&s3(s5(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(s2(s5(t[e],t[r]),n)&&s2(s5(t[e],n),t[r])&&s2(s5(t[r],n),t[e])&&s3(s4(t[e],t[r],n),t))return[t[e],t[r],n];throw Error()}(a,e)),i=0);return r}function s2(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function s6(t,n){var e=t.r-n.r+1e-9*Math.max(t.r,n.r,1),r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function s3(t,n){for(var e=0;e<n.length;++e)if(!s6(t,n[e]))return!1;return!0}function s5(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,a=n.y,u=n.r,c=o-e,l=a-r,f=u-i,s=Math.sqrt(c*c+l*l);return{x:(e+o+c/s*f)/2,y:(r+a+l/s*f)/2,r:(s+i+u)/2}}function s4(t,n,e){var r=t.x,i=t.y,o=t.r,a=n.x,u=n.y,c=n.r,l=e.x,f=e.y,s=e.r,h=r-a,d=r-l,p=i-u,v=i-f,g=c-o,y=s-o,_=r*r+i*i-o*o,b=_-a*a-u*u+c*c,m=_-l*l-f*f+s*s,x=d*p-h*v,w=(p*m-v*b)/(2*x)-r,M=(v*g-p*y)/x,T=(d*b-h*m)/(2*x)-i,A=(h*y-d*g)/x,k=M*M+A*A-1,S=2*(o+w*M+T*A),E=w*w+T*T-o*o,N=-(Math.abs(k)>1e-6?(S+Math.sqrt(S*S-4*k*E))/(2*k):E/S);return{x:r+w+M*N,y:i+T+A*N,r:N}}function s8(t,n,e){var r,i,o,a,u=t.x-n.x,c=t.y-n.y,l=u*u+c*c;l?(i=n.r+e.r,i*=i,a=t.r+e.r,i>(a*=a)?(r=(l+a-i)/(2*l),o=Math.sqrt(Math.max(0,a/l-r*r)),e.x=t.x-r*u-o*c,e.y=t.y-r*c+o*u):(r=(l+i-a)/(2*l),o=Math.sqrt(Math.max(0,i/l-r*r)),e.x=n.x+r*u-o*c,e.y=n.y+r*c+o*u)):(e.x=n.x+e.r,e.y=n.y)}function s7(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function s9(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function ht(t){this._=t,this.next=null,this.previous=null}function hn(t,n){var e,r,i,o,a,u,c,l,f,s,h,d;if(!(o=(t="object"==typeof(d=t)&&"length"in d?d:Array.from(d)).length))return 0;if((e=t[0]).x=0,e.y=0,!(o>1))return e.r;if(r=t[1],e.x=-r.r,r.x=e.r,r.y=0,!(o>2))return e.r+r.r;s8(r,e,i=t[2]),e=new ht(e),r=new ht(r),i=new ht(i),e.next=i.previous=r,r.next=e.previous=i,i.next=r.previous=e;r:for(c=3;c<o;++c){s8(e._,r._,i=t[c]),i=new ht(i),l=r.next,f=e.previous,s=r._.r,h=e._.r;do if(s<=h){if(s7(l._,i._)){r=l,e.next=r,r.previous=e,--c;continue r}s+=l._.r,l=l.next}else{if(s7(f._,i._)){(e=f).next=r,r.previous=e,--c;continue r}h+=f._.r,f=f.previous}while(l!==f.next);for(i.previous=e,i.next=r,e.next=r.previous=r=i,a=s9(e);(i=i.next)!==r;)(u=s9(i))<a&&(e=i,a=u);r=e.next}for(e=[r._],i=r;(i=i.next)!==r;)e.push(i._);for(c=0,i=s1(e,n);c<o;++c)e=t[c],e.x-=i.x,e.y-=i.y;return i.r}function he(t){return hn(t,sQ()),t}function hr(t){return Math.sqrt(t.value)}function hi(){var t=null,n=1,e=1,r=sK;function i(i){let o=sQ();return i.x=n/2,i.y=e/2,t?i.eachBefore(ho(t)).eachAfter(ha(r,.5,o)).eachBefore(hu(1)):i.eachBefore(ho(hr)).eachAfter(ha(sK,1,o)).eachAfter(ha(r,i.r/Math.min(n,e),o)).eachBefore(hu(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=sZ(n),i):t},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:sJ(+t),i):r},i}function ho(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function ha(t,n,e){return function(r){if(i=r.children){var i,o,a,u=i.length,c=t(r)*n||0;if(c)for(o=0;o<u;++o)i[o].r+=c;if(a=hn(i,e),c)for(o=0;o<u;++o)i[o].r-=c;r.r=a+c}}}function hu(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function hc(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function hl(t,n,e,r,i){for(var o,a=t.children,u=-1,c=a.length,l=t.value&&(r-n)/t.value;++u<c;)(o=a[u]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*l}function hf(){var t=1,n=1,e=0,r=!1;function i(i){var o,a=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/a,i.eachBefore((o=n,function(t){t.children&&hl(t,t.x0,o*(t.depth+1)/a,t.x1,o*(t.depth+2)/a);var n=t.x0,r=t.y0,i=t.x1-e,u=t.y1-e;i<n&&(n=i=(n+i)/2),u<r&&(r=u=(r+u)/2),t.x0=n,t.y0=r,t.x1=i,t.y1=u})),r&&i.eachBefore(hc),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i}sA.invert=function(t,n){for(var e,r,i=n,o=i*i,a=o*o*o,u=0;u<12&&(r=i*(1.340264+-.081106*o+a*(893e-6+.003796*o))-n,i-=e=r/(1.340264+-.24331799999999998*o+a*(.0062510000000000005+.034164*o)),a=(o=i*i)*o*o,!(1e-12>uq(e)));++u);return[sT*t*(1.340264+-.24331799999999998*o+a*(.0062510000000000005+.034164*o))/uH(i),u3(uQ(i)/sT)]},sS.invert=sl(uG),sC.invert=function(t,n){var e,r=n,i=25;do{var o=r*r,a=o*o;r-=e=(r*(1.007226+o*(.015085+a*(-.044475+.028874*o-.005916*a)))-n)/(1.007226+o*(.045255+a*(-.311325+.259866*o-.005916*11*a)))}while(uq(e)>1e-6&&--i>0);return[t/(.8707+(o=r*r)*(-.131979+o*(-.013791+o*o*o*(.003971-.001529*o)))),r]},sR.invert=sl(u3),sj.invert=sl(function(t){return 2*uG(t)}),sD.invert=function(t,n){return[-n,2*uG(uZ(t))-u$]},sW.prototype=sF.prototype={constructor:sW,count:function(){return this.eachAfter(sU)},each:function(t,n){let e=-1;for(let r of this)t.call(n,r,++e,this);return this},eachAfter:function(t,n){for(var e,r,i,o=this,a=[o],u=[],c=-1;o=a.pop();)if(u.push(o),e=o.children)for(r=0,i=e.length;r<i;++r)a.push(e[r]);for(;o=u.pop();)t.call(n,o,++c,this);return this},eachBefore:function(t,n){for(var e,r,i=this,o=[i],a=-1;i=o.pop();)if(t.call(n,i,++a,this),e=i.children)for(r=e.length-1;r>=0;--r)o.push(e[r]);return this},find:function(t,n){let e=-1;for(let r of this)if(t.call(n,r,++e,this))return r},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)r.push(n=n.parent);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return sF(this).eachBefore(sY)},[Symbol.iterator]:function*(){var t,n,e,r,i=this,o=[i];do for(t=o.reverse(),o=[];i=t.pop();)if(yield i,n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);while(o.length)}};var hs={depth:-1},hh={},hd={};function hp(t){return t.id}function hv(t){return t.parentId}function hg(){var t,n=hp,e=hv;function r(r){var i,o,a,u,c,l,f,s,h=Array.from(r),d=n,p=e,v=new Map;if(null!=t){let n=h.map((n,e)=>{var i;let o;return i=t(n,e,r),o=(i=`${i}`).length,h_(i,o-1)&&!h_(i,o-2)&&(i=i.slice(0,-1)),"/"===i[0]?i:`/${i}`}),e=n.map(hy),i=new Set(n).add("");for(let t of e)i.has(t)||(i.add(t),n.push(t),e.push(hy(t)),h.push(hd));d=(t,e)=>n[e],p=(t,n)=>e[n]}for(a=0,i=h.length;a<i;++a)o=h[a],l=h[a]=new sW(o),null!=(f=d(o,a,r))&&(f+="")&&(s=l.id=f,v.set(s,v.has(s)?hh:l)),null!=(f=p(o,a,r))&&(f+="")&&(l.parent=f);for(a=0;a<i;++a)if(f=(l=h[a]).parent){if(!(c=v.get(f)))throw Error("missing: "+f);if(c===hh)throw Error("ambiguous: "+f);c.children?c.children.push(l):c.children=[l],l.parent=c}else{if(u)throw Error("multiple roots");u=l}if(!u)throw Error("no root");if(null!=t){for(;u.data===hd&&1===u.children.length;)u=u.children[0],--i;for(let t=h.length-1;t>=0&&(l=h[t]).data===hd;--t)l.data=null}if(u.parent=hs,u.eachBefore(function(t){t.depth=t.parent.depth+1,--i}).eachBefore(sH),u.parent=null,i>0)throw Error("cycle");return u}return r.id=function(t){return arguments.length?(n=sZ(t),r):n},r.parentId=function(t){return arguments.length?(e=sZ(t),r):e},r.path=function(n){return arguments.length?(t=sZ(n),r):t},r}function hy(t){let n=t.length;if(n<2)return"";for(;--n>1&&!h_(t,n););return t.slice(0,n)}function h_(t,n){if("/"===t[n]){let e=0;for(;n>0&&"\\"===t[--n];)++e;if((1&e)==0)return!0}return!1}function hb(t,n){return t.parent===n.parent?1:2}function hm(t){var n=t.children;return n?n[0]:t.t}function hx(t){var n=t.children;return n?n[n.length-1]:t.t}function hw(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function hM(){var t=hb,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,a=new hw(t,0),u=[a];n=u.pop();)if(r=n._.children)for(n.children=Array(o=r.length),i=o-1;i>=0;--i)u.push(e=n.children[i]=new hw(r[i],i)),e.parent=n;return(a.parent=new hw(null,0)).children=[a],a}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(a),r)i.eachBefore(u);else{var l=i,f=i,s=i;i.eachBefore(function(t){t.x<l.x&&(l=t),t.x>f.x&&(f=t),t.depth>s.depth&&(s=t)});var h=l===f?1:t(l,f)/2,d=h-l.x,p=n/(f.x+h+d),v=e/(s.depth||1);i.eachBefore(function(t){t.x=(t.x+d)*p,t.y=t.depth*v})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)n=i[o],n.z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o,a,u=n,c=n,l=e,f=u.parent.children[0],s=u.m,h=c.m,d=l.m,p=f.m;l=hx(l),u=hm(u),l&&u;)f=hm(f),(c=hx(c)).a=n,(a=l.z+d-u.z-s+t(l._,u._))>0&&(function(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}((i=l,o=r,i.a.parent===n.parent?i.a:o),n,a),s+=a,h+=a),d+=l.m,s+=u.m,p+=f.m,h+=c.m;l&&!hx(c)&&(c.t=l,c.m+=d-h),u&&!hm(f)&&(f.t=u,f.m+=s-p,r=n)}return r}(n,i,n.parent.A||r[0])}function a(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function u(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function hT(t,n,e,r,i){for(var o,a=t.children,u=-1,c=a.length,l=t.value&&(i-e)/t.value;++u<c;)(o=a[u]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*l}hw.prototype=Object.create(sW.prototype);var hA=(1+Math.sqrt(5))/2;function hk(t,n,e,r,i,o){for(var a,u,c,l,f,s,h,d,p,v,g,y=[],_=n.children,b=0,m=0,x=_.length,w=n.value;b<x;){c=i-e,l=o-r;do f=_[m++].value;while(!f&&m<x);for(s=h=f,p=Math.max(h/(g=f*f*(v=Math.max(l/c,c/l)/(w*t))),g/s);m<x;++m){if(f+=u=_[m].value,u<s&&(s=u),u>h&&(h=u),(d=Math.max(h/(g=f*f*v),g/s))>p){f-=u;break}p=d}y.push(a={value:f,dice:c<l,children:_.slice(b,m)}),a.dice?hl(a,e,r,i,w?r+=l*f/w:o):hT(a,e,r,w?e+=c*f/w:i,o),w-=f,b=m}return y}let hS=function t(n){function e(t,e,r,i,o){hk(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(hA);function hE(){var t=hS,n=!1,e=1,r=1,i=[0],o=sK,a=sK,u=sK,c=sK,l=sK;function f(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(s),i=[0],n&&t.eachBefore(hc),t}function s(n){var e=i[n.depth],r=n.x0+e,f=n.y0+e,s=n.x1-e,h=n.y1-e;s<r&&(r=s=(r+s)/2),h<f&&(f=h=(f+h)/2),n.x0=r,n.y0=f,n.x1=s,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=l(n)-e,f+=a(n)-e,s-=u(n)-e,h-=c(n)-e,s<r&&(r=s=(r+s)/2),h<f&&(f=h=(f+h)/2),t(n,r,f,s,h))}return f.round=function(t){return arguments.length?(n=!!t,f):n},f.size=function(t){return arguments.length?(e=+t[0],r=+t[1],f):[e,r]},f.tile=function(n){return arguments.length?(t=sX(n),f):t},f.padding=function(t){return arguments.length?f.paddingInner(t).paddingOuter(t):f.paddingInner()},f.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:sJ(+t),f):o},f.paddingOuter=function(t){return arguments.length?f.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):f.paddingTop()},f.paddingTop=function(t){return arguments.length?(a="function"==typeof t?t:sJ(+t),f):a},f.paddingRight=function(t){return arguments.length?(u="function"==typeof t?t:sJ(+t),f):u},f.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:sJ(+t),f):c},f.paddingLeft=function(t){return arguments.length?(l="function"==typeof t?t:sJ(+t),f):l},f}function hN(t,n,e,r,i){var o,a,u=t.children,c=u.length,l=Array(c+1);for(l[0]=a=o=0;o<c;++o)l[o+1]=a+=u[o].value;(function t(n,e,r,i,o,a,c){if(n>=e-1){var f=u[n];f.x0=i,f.y0=o,f.x1=a,f.y1=c;return}for(var s=l[n],h=r/2+s,d=n+1,p=e-1;d<p;){var v=d+p>>>1;l[v]<h?d=v+1:p=v}h-l[d-1]<l[d]-h&&n+1<d&&--d;var g=l[d]-s,y=r-g;if(a-i>c-o){var _=r?(i*y+a*g)/r:a;t(n,d,g,i,o,_,c),t(d,e,y,_,o,a,c)}else{var b=r?(o*y+c*g)/r:c;t(n,d,g,i,o,a,b),t(d,e,y,i,b,a,c)}})(0,c,t.value,n,e,r,i)}function hC(t,n,e,r,i){(1&t.depth?hT:hl)(t,n,e,r,i)}let hP=function t(n){function e(t,e,r,i,o){if((a=t._squarify)&&a.ratio===n)for(var a,u,c,l,f,s=-1,h=a.length,d=t.value;++s<h;){for(c=(u=a[s]).children,l=u.value=0,f=c.length;l<f;++l)u.value+=c[l].value;u.dice?hl(u,e,r,i,d?r+=(o-r)*u.value/d:o):hT(u,e,r,d?e+=(i-e)*u.value/d:i,o),d-=u.value}else t._squarify=a=hk(n,t,e,r,i,o),a.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(hA);function hR(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}function hO(t,n){var e=eU(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}}function hj(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function hz(t){return((t=Math.exp(t))+1/t)/2}let hD=function t(n,e,r){function i(t,i){var o,a,u=t[0],c=t[1],l=t[2],f=i[0],s=i[1],h=i[2],d=f-u,p=s-c,v=d*d+p*p;if(v<1e-12)a=Math.log(h/l)/n,o=function(t){return[u+t*d,c+t*p,l*Math.exp(n*t*a)]};else{var g=Math.sqrt(v),y=(h*h-l*l+r*v)/(2*l*e*g),_=(h*h-l*l-r*v)/(2*h*e*g),b=Math.log(Math.sqrt(y*y+1)-y);a=(Math.log(Math.sqrt(_*_+1)-_)-b)/n,o=function(t){var r,i,o=t*a,f=hz(b),s=l/(e*g)*(((r=Math.exp(2*(r=n*o+b)))-1)/(r+1)*f-((i=Math.exp(i=b))-1/i)/2);return[u+s*d,c+s*p,l*f/hz(n*o+b)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4);function hL(t){return function(n,e){var r=t((n=eR(n)).h,(e=eR(e)).h),i=eF(n.s,e.s),o=eF(n.l,e.l),a=eF(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=o(t),n.opacity=a(t),n+""}}}let hI=hL(eU);var h$=hL(eF);function hB(t,n){var e=eF((t=iX(t)).l,(n=iX(n)).l),r=eF(t.a,n.a),i=eF(t.b,n.b),o=eF(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=o(n),t+""}}function hV(t){return function(n,e){var r=t((n=i3(n)).h,(e=i3(e)).h),i=eF(n.c,e.c),o=eF(n.l,e.l),a=eF(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=o(t),n.opacity=a(t),n+""}}}let hU=hV(eU);var hF=hV(eF);function hq(t){return function n(e){function r(n,r){var i=t((n=i7(n)).h,(r=i7(r)).h),o=eF(n.s,r.s),a=eF(n.l,r.l),u=eF(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=a(Math.pow(t,e)),n.opacity=u(t),n+""}}return e=+e,r.gamma=n,r}(1)}let hG=hq(eU);var hY=hq(eF);function hH(t,n){void 0===n&&(n=t,t=e3);for(var e=0,r=n.length-1,i=n[0],o=Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}function hW(t,n){for(var e=Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e}function hZ(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2}function hX(t){for(var n,e,r=-1,i=t.length,o=0,a=0,u=t[i-1],c=0;++r<i;)n=u,u=t[r],c+=e=n[0]*u[1]-u[0]*n[1],o+=(n[0]+u[0])*e,a+=(n[1]+u[1])*e;return[o/(c*=3),a/c]}function hK(t,n){return t[0]-n[0]||t[1]-n[1]}function hJ(t){let n=t.length,e=[0,1],r=2,i;for(i=2;i<n;++i){for(var o,a,u;r>1&&0>=(o=t[e[r-2]],a=t[e[r-1]],u=t[i],(a[0]-o[0])*(u[1]-o[1])-(a[1]-o[1])*(u[0]-o[0]));)--r;e[r++]=i}return e.slice(0,r)}function hQ(t){if((e=t.length)<3)return null;var n,e,r=Array(e),i=Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(hK),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=hJ(r),a=hJ(i),u=a[0]===o[0],c=a[a.length-1]===o[o.length-1],l=[];for(n=o.length-1;n>=0;--n)l.push(t[r[o[n]][2]]);for(n=+u;n<a.length-c;++n)l.push(t[r[a[n]][2]]);return l}function h0(t,n){for(var e,r,i=t.length,o=t[i-1],a=n[0],u=n[1],c=o[0],l=o[1],f=!1,s=0;s<i;++s)e=(o=t[s])[0],(r=o[1])>u!=l>u&&a<(c-e)*(u-r)/(l-r)+e&&(f=!f),c=e,l=r;return f}function h1(t){for(var n,e,r=-1,i=t.length,o=t[i-1],a=o[0],u=o[1],c=0;++r<i;)n=a,e=u,a=(o=t[r])[0],u=o[1],n-=a,e-=u,c+=Math.hypot(n,e);return c}let h2=Math.random,h6=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,1==arguments.length?(e=t,t=0):e-=t,function(){return n()*e+t}}return e.source=t,e}(h2),h3=function t(n){function e(t,e){return arguments.length<2&&(e=t,t=0),e=Math.floor(e)-(t=Math.floor(t)),function(){return Math.floor(n()*e+t)}}return e.source=t,e}(h2),h5=function t(n){function e(t,e){var r,i;return t=null==t?0:+t,e=null==e?1:+e,function(){var o;if(null!=r)o=r,r=null;else do r=2*n()-1,o=2*n()-1,i=r*r+o*o;while(!i||i>1);return t+e*o*Math.sqrt(-2*Math.log(i)/i)}}return e.source=t,e}(h2),h4=function t(n){var e=h5.source(n);function r(){var t=e.apply(this,arguments);return function(){return Math.exp(t())}}return r.source=t,r}(h2),h8=function t(n){function e(t){return(t=+t)<=0?()=>0:function(){for(var e=0,r=t;r>1;--r)e+=n();return e+r*n()}}return e.source=t,e}(h2),h7=function t(n){var e=h8.source(n);function r(t){if(0==(t=+t))return n;var r=e(t);return function(){return r()/t}}return r.source=t,r}(h2),h9=function t(n){function e(t){return function(){return-Math.log1p(-n())/t}}return e.source=t,e}(h2),dt=function t(n){function e(t){if((t=+t)<0)throw RangeError("invalid alpha");return t=-(1/t),function(){return Math.pow(1-n(),t)}}return e.source=t,e}(h2),dn=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return function(){return Math.floor(n()+t)}}return e.source=t,e}(h2),de=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return 0===t?()=>1/0:1===t?()=>1:(t=Math.log1p(-t),function(){return 1+Math.floor(Math.log1p(-n())/t)})}return e.source=t,e}(h2),dr=function t(n){var e=h5.source(n)();function r(t,r){if((t=+t)<0)throw RangeError("invalid k");if(0===t)return()=>0;if(r=null==r?1:+r,1===t)return()=>-Math.log1p(-n())*r;var i=(t<1?t+1:t)-1/3,o=1/(3*Math.sqrt(i)),a=t<1?()=>Math.pow(n(),1/t):()=>1;return function(){do{do var t=e(),u=1+o*t;while(u<=0);u*=u*u;var c=1-n()}while(c>=1-.0331*t*t*t*t&&Math.log(c)>=.5*t*t+i*(1-u+Math.log(u)));return i*u*a()*r}}return r.source=t,r}(h2),di=function t(n){var e=dr.source(n);function r(t,n){var r=e(t),i=e(n);return function(){var t=r();return 0===t?0:t/(t+i())}}return r.source=t,r}(h2),da=function t(n){var e=de.source(n),r=di.source(n);function i(t,n){return(t=+t,(n=+n)>=1)?()=>t:n<=0?()=>0:function(){for(var i=0,o=t,a=n;o*a>16&&o*(1-a)>16;){var u=Math.floor((o+1)*a),c=r(u,o-u+1)();c<=a?(i+=u,o-=u,a=(a-c)/(1-c)):(o=u-1,a/=c)}for(var l=a<.5,f=e(l?a:1-a),s=f(),h=0;s<=o;++h)s+=f();return i+(l?h:o-h)}}return i.source=t,i}(h2),du=function t(n){function e(t,e,r){var i;return 0==(t=+t)?i=t=>-Math.log(t):(t=1/t,i=n=>Math.pow(n,t)),e=null==e?0:+e,r=null==r?1:+r,function(){return e+r*i(-Math.log1p(-n()))}}return e.source=t,e}(h2),dc=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){return t+e*Math.tan(Math.PI*n())}}return e.source=t,e}(h2),dl=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){var r=n();return t+e*Math.log(r/(1-r))}}return e.source=t,e}(h2),df=function t(n){var e=dr.source(n),r=da.source(n);function i(t){return function(){for(var i=0,o=t;o>16;){var a=Math.floor(.875*o),u=e(a)();if(u>o)return i+r(a-1,o/u)();i+=a,o-=u}for(var c=-Math.log1p(-n()),l=0;c<=o;++l)c-=Math.log1p(-n());return i+l}}return i.source=t,i}(h2),ds=1/4294967296;function dh(t=Math.random()){let n=(0<=t&&t<1?t/ds:Math.abs(t))|0;return()=>ds*((n=1664525*n+1013904223|0)>>>0)}function dd(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function dp(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}let dv=Symbol("implicit");function dg(){var t=new D,n=[],e=[],r=dv;function i(i){let o=t.get(i);if(void 0===o){if(r!==dv)return r;t.set(i,o=n.push(i)-1)}return e[o%e.length]}return i.domain=function(e){if(!arguments.length)return n.slice();for(let r of(n=[],t=new D,e))t.has(r)||t.set(r,n.push(r)-1);return i},i.range=function(t){return arguments.length?(e=Array.from(t),i):e.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return dg(n,e).unknown(r)},dd.apply(i,arguments),i}function dy(){var t,n,e=dg().unknown(void 0),r=e.domain,i=e.range,o=0,a=1,u=!1,c=0,l=0,f=.5;function s(){var e=r().length,s=a<o,h=s?a:o,d=s?o:a;t=(d-h)/Math.max(1,e-c+2*l),u&&(t=Math.floor(t)),h+=(d-h-t*(e-c))*f,n=t*(1-c),u&&(h=Math.round(h),n=Math.round(n));var p=tV(e).map(function(n){return h+t*n});return i(s?p.reverse():p)}return delete e.unknown,e.domain=function(t){return arguments.length?(r(t),s()):r()},e.range=function(t){return arguments.length?([o,a]=t,o=+o,a=+a,s()):[o,a]},e.rangeRound=function(t){return[o,a]=t,o=+o,a=+a,u=!0,s()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(u=!!t,s()):u},e.padding=function(t){return arguments.length?(c=Math.min(1,l=+t),s()):c},e.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),s()):c},e.paddingOuter=function(t){return arguments.length?(l=+t,s()):l},e.align=function(t){return arguments.length?(f=Math.max(0,Math.min(1,t)),s()):f},e.copy=function(){return dy(r(),[o,a]).round(u).paddingInner(c).paddingOuter(l).align(f)},dd.apply(s(),arguments)}function d_(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(dy.apply(null,arguments).paddingInner(1))}function db(t){return+t}var dm=[0,1];function dx(t){return t}function dw(t,n){var e;return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e})}function dM(t,n,e){var r=t[0],i=t[1],o=n[0],a=n[1];return i<r?(r=dw(i,r),o=e(a,o)):(r=dw(r,i),o=e(o,a)),function(t){return o(r(t))}}function dT(t,n,e){var r=Math.min(t.length,n.length)-1,i=Array(r),o=Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<r;)i[a]=dw(t[a],t[a+1]),o[a]=e(n[a],n[a+1]);return function(n){var e=g(t,n,1,r)-1;return o[e](i[e](n))}}function dA(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function dk(){var t,n,e,r,i,o,a=dm,u=dm,c=e3,l=dx;function f(){var t,n,e,c=Math.min(a.length,u.length);return l!==dx&&(t=a[0],n=a[c-1],t>n&&(e=t,t=n,n=e),l=function(e){return Math.max(t,Math.min(n,e))}),r=c>2?dT:dM,i=o=null,s}function s(n){return null==n||isNaN(n=+n)?e:(i||(i=r(a.map(t),u,c)))(t(l(n)))}return s.invert=function(e){return l(n((o||(o=r(u,a.map(t),eQ)))(e)))},s.domain=function(t){return arguments.length?(a=Array.from(t,db),f()):a.slice()},s.range=function(t){return arguments.length?(u=Array.from(t),f()):u.slice()},s.rangeRound=function(t){return u=Array.from(t),c=hj,f()},s.clamp=function(t){return arguments.length?(l=!!t||dx,f()):l!==dx},s.interpolate=function(t){return arguments.length?(c=t,f()):c},s.unknown=function(t){return arguments.length?(e=t,s):e},function(e,r){return t=e,n=r,f()}}function dS(){return dk()(dx,dx)}function dE(t,n,e,r){var i,o=t_(t,n,e);switch((r=uk(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=uD(o,a))||(r.precision=i),ca(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=uL(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=uz(o))||(r.precision=i-("%"===r.type)*2)}return co(r)}function dN(t){var n=t.domain;return t.ticks=function(t){var e=n();return tg(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return dE(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,i,o=n(),a=0,u=o.length-1,c=o[a],l=o[u],f=10;for(l<c&&(i=c,c=l,l=i,i=a,a=u,u=i);f-- >0;){if((i=ty(c,l,e))===r)return o[a]=c,o[u]=l,n(o);if(i>0)c=Math.floor(c/i)*i,l=Math.ceil(l/i)*i;else if(i<0)c=Math.ceil(c*i)/i,l=Math.floor(l*i)/i;else break;r=i}return t},t}function dC(t,n){t=t.slice();var e,r=0,i=t.length-1,o=t[r],a=t[i];return a<o&&(e=r,r=i,i=e,e=o,o=a,a=e),t[r]=n.floor(o),t[i]=n.ceil(a),t}function dP(t){return Math.log(t)}function dR(t){return Math.exp(t)}function dO(t){return-Math.log(-t)}function dj(t){return-Math.exp(-t)}function dz(t){return isFinite(t)?+("1e"+t):t<0?0:t}function dD(t){return(n,e)=>-t(-n,e)}function dL(t){let n,e;let r=t(dP,dR),i=r.domain,o=10;function a(){var a,u;return n=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),e=10===(u=o)?dz:u===Math.E?Math.exp:t=>Math.pow(u,t),i()[0]<0?(n=dD(n),e=dD(e),t(dO,dj)):t(dP,dR),r}return r.base=function(t){return arguments.length?(o=+t,a()):o},r.domain=function(t){return arguments.length?(i(t),a()):i()},r.ticks=t=>{let r,a;let u=i(),c=u[0],l=u[u.length-1],f=l<c;f&&([c,l]=[l,c]);let s=n(c),h=n(l),d=null==t?10:+t,p=[];if(!(o%1)&&h-s<d){if(s=Math.floor(s),h=Math.ceil(h),c>0){for(;s<=h;++s)for(r=1;r<o;++r)if(!((a=s<0?r/e(-s):r*e(s))<c)){if(a>l)break;p.push(a)}}else for(;s<=h;++s)for(r=o-1;r>=1;--r)if(!((a=s>0?r/e(-s):r*e(s))<c)){if(a>l)break;p.push(a)}2*p.length<d&&(p=tg(c,l,d))}else p=tg(s,h,Math.min(h-s,d)).map(e);return f?p.reverse():p},r.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=uk(i)).precision||(i.trim=!0),i=co(i)),t===1/0)return i;let a=Math.max(1,o*t/r.ticks().length);return t=>{let r=t/e(Math.round(n(t)));return r*o<o-.5&&(r*=o),r<=a?i(t):""}},r.nice=()=>i(dC(i(),{floor:t=>e(Math.floor(n(t))),ceil:t=>e(Math.ceil(n(t)))})),r}function dI(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function d$(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function dB(t){var n=1,e=t(dI(1),d$(n));return e.constant=function(e){return arguments.length?t(dI(n=+e),d$(n)):n},dN(e)}function dV(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function dU(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function dF(t){return t<0?-t*t:t*t}function dq(t){var n=t(dx,dx),e=1;return n.exponent=function(n){return arguments.length?1==(e=+n)?t(dx,dx):.5===e?t(dU,dF):t(dV(e),dV(1/e)):e},dN(n)}function dG(){var t=dq(dk());return t.copy=function(){return dA(t,dG()).exponent(t.exponent())},dd.apply(t,arguments),t}function dY(){return dG.apply(null,arguments).exponent(.5)}function dH(t){return Math.sign(t)*t*t}let dW=new Date,dZ=new Date;function dX(t,n,e,r){function i(n){return t(n=0==arguments.length?new Date:new Date(+n)),n}return i.floor=n=>(t(n=new Date(+n)),n),i.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e),i.round=t=>{let n=i(t),e=i.ceil(t);return t-n<e-t?n:e},i.offset=(t,e)=>(n(t=new Date(+t),null==e?1:Math.floor(e)),t),i.range=(e,r,o)=>{let a;let u=[];if(e=i.ceil(e),o=null==o?1:Math.floor(o),!(e<r)||!(o>0))return u;do u.push(a=new Date(+e)),n(e,o),t(e);while(a<e&&e<r);return u},i.filter=e=>dX(n=>{if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)},(t,r)=>{if(t>=t){if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););}}),e&&(i.count=(n,r)=>(dW.setTime(+n),dZ.setTime(+r),t(dW),t(dZ),Math.floor(e(dW,dZ))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(r?n=>r(n)%t==0:n=>i.count(0,n)%t==0):i:null),i}let dK=dX(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);dK.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?dX(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):dK:null;let dJ=dK.range,dQ=dX(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+1e3*n)},(t,n)=>(n-t)/1e3,t=>t.getUTCSeconds()),d0=dQ.range,d1=dX(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getMinutes()),d2=d1.range,d6=dX(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getUTCMinutes()),d3=d6.range,d5=dX(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getHours()),d4=d5.range,d8=dX(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getUTCHours()),d7=d8.range,d9=dX(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1),pt=d9.range,pn=dX(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>t.getUTCDate()-1),pe=pn.range,pr=dX(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>Math.floor(t/864e5)),pi=pr.range;function po(t){return dX(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}let pa=po(0),pu=po(1),pc=po(2),pl=po(3),pf=po(4),ps=po(5),ph=po(6),pd=pa.range,pp=pu.range,pv=pc.range,pg=pl.range,py=pf.range,p_=ps.range,pb=ph.range;function pm(t){return dX(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/6048e5)}let px=pm(0),pw=pm(1),pM=pm(2),pT=pm(3),pA=pm(4),pk=pm(5),pS=pm(6),pE=px.range,pN=pw.range,pC=pM.range,pP=pT.range,pR=pA.range,pO=pk.range,pj=pS.range,pz=dX(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12,t=>t.getMonth()),pD=pz.range,pL=dX(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth()),pI=pL.range,p$=dX(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());p$.every=t=>isFinite(t=Math.floor(t))&&t>0?dX(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)}):null;let pB=p$.range,pV=dX(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());pV.every=t=>isFinite(t=Math.floor(t))&&t>0?dX(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null;let pU=pV.range;function pF(t,n,e,r,i,o){let a=[[dQ,1,1e3],[dQ,5,5e3],[dQ,15,15e3],[dQ,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[r,1,864e5],[r,2,1728e5],[e,1,6048e5],[n,1,2592e6],[n,3,7776e6],[t,1,31536e6]];function u(n,e,r){let i=Math.abs(e-n)/r,o=l(([,,t])=>t).right(a,i);if(o===a.length)return t.every(t_(n/31536e6,e/31536e6,r));if(0===o)return dK.every(Math.max(t_(n,e,r),1));let[u,c]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return u.every(c)}return[function(t,n,e){let r=n<t;r&&([t,n]=[n,t]);let i=e&&"function"==typeof e.range?e:u(t,n,e),o=i?i.range(t,+n+1):[];return r?o.reverse():o},u]}let[pq,pG]=pF(pV,pL,px,pr,d8,d6),[pY,pH]=pF(p$,pz,pa,d9,d5,d1);function pW(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function pZ(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function pX(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function pK(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=p3(i),f=p5(i),s=p3(o),h=p5(o),d=p3(a),p=p5(a),v=p3(u),g=p5(u),y=p3(c),_=p5(c),b={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:vg,e:vg,f:vx,g:vR,G:vj,H:vy,I:v_,j:vb,L:vm,m:vw,M:vM,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:v6,s:v3,S:vT,u:vA,U:vk,V:vE,w:vN,W:vC,x:null,X:null,y:vP,Y:vO,Z:vz,"%":v2},m={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:vD,e:vD,f:vV,g:vJ,G:v0,H:vL,I:vI,j:v$,L:vB,m:vU,M:vF,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:v6,s:v3,S:vq,u:vG,U:vY,V:vW,w:vZ,W:vX,x:null,X:null,y:vK,Y:vQ,Z:v1,"%":v2},x={a:function(t,n,e){var r=d.exec(n.slice(e));return r?(t.w=p.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(t,n,e){var r=s.exec(n.slice(e));return r?(t.w=h.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(t,n,e){var r=y.exec(n.slice(e));return r?(t.m=_.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(t,n,e){var r=v.exec(n.slice(e));return r?(t.m=g.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(t,e,r){return T(t,n,e,r)},d:va,e:va,f:vh,g:ve,G:vn,H:vc,I:vc,j:vu,L:vs,m:vo,M:vl,p:function(t,n,e){var r=l.exec(n.slice(e));return r?(t.p=f.get(r[0].toLowerCase()),e+r[0].length):-1},q:vi,Q:vp,s:vv,S:vf,u:p8,U:p7,V:p9,w:p4,W:vt,x:function(t,n,r){return T(t,e,n,r)},X:function(t,n,e){return T(t,r,n,e)},y:ve,Y:vn,Z:vr,"%":vd};function w(t,n){return function(e){var r,i,o,a=[],u=-1,c=0,l=t.length;for(e instanceof Date||(e=new Date(+e));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(i=pJ[r=t.charAt(++u)])?r=t.charAt(++u):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),a.push(r),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function M(t,n){return function(e){var r,i,o=pX(1900,void 0,1);if(T(o,t,e+="",0)!=e.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!n||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(r=(i=(r=pZ(pX(o.y,0,1))).getUTCDay())>4||0===i?pw.ceil(r):pw(r),r=pn.offset(r,(o.V-1)*7),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(r=(i=(r=pW(pX(o.y,0,1))).getDay())>4||0===i?pu.ceil(r):pu(r),r=d9.offset(r,(o.V-1)*7),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?pZ(pX(o.y,0,1)).getUTCDay():pW(pX(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,pZ(o)):pW(o)}}function T(t,n,e,r){for(var i,o,a=0,u=n.length,c=e.length;a<u;){if(r>=c)return -1;if(37===(i=n.charCodeAt(a++))){if(!(o=x[(i=n.charAt(a++))in pJ?n.charAt(a++):i])||(r=o(t,e,r))<0)return -1}else if(i!=e.charCodeAt(r++))return -1}return r}return b.x=w(e,b),b.X=w(r,b),b.c=w(n,b),m.x=w(e,m),m.X=w(r,m),m.c=w(n,m),{format:function(t){var n=w(t+="",b);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",m);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t+="",!0);return n.toString=function(){return t},n}}}var pJ={"-":"",_:" ",0:"0"},pQ=/^\s*\d+/,p0=/^%/,p1=/[\\^$*+?|[\]().{}]/g;function p2(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?Array(e-o+1).join(n)+i:i)}function p6(t){return t.replace(p1,"\\$&")}function p3(t){return RegExp("^(?:"+t.map(p6).join("|")+")","i")}function p5(t){return new Map(t.map((t,n)=>[t.toLowerCase(),n]))}function p4(t,n,e){var r=pQ.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function p8(t,n,e){var r=pQ.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function p7(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function p9(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function vt(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function vn(t,n,e){var r=pQ.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function ve(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function vr(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function vi(t,n,e){var r=pQ.exec(n.slice(e,e+1));return r?(t.q=3*r[0]-3,e+r[0].length):-1}function vo(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function va(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function vu(t,n,e){var r=pQ.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function vc(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function vl(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function vf(t,n,e){var r=pQ.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function vs(t,n,e){var r=pQ.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function vh(t,n,e){var r=pQ.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function vd(t,n,e){var r=p0.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function vp(t,n,e){var r=pQ.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function vv(t,n,e){var r=pQ.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function vg(t,n){return p2(t.getDate(),n,2)}function vy(t,n){return p2(t.getHours(),n,2)}function v_(t,n){return p2(t.getHours()%12||12,n,2)}function vb(t,n){return p2(1+d9.count(p$(t),t),n,3)}function vm(t,n){return p2(t.getMilliseconds(),n,3)}function vx(t,n){return vm(t,n)+"000"}function vw(t,n){return p2(t.getMonth()+1,n,2)}function vM(t,n){return p2(t.getMinutes(),n,2)}function vT(t,n){return p2(t.getSeconds(),n,2)}function vA(t){var n=t.getDay();return 0===n?7:n}function vk(t,n){return p2(pa.count(p$(t)-1,t),n,2)}function vS(t){var n=t.getDay();return n>=4||0===n?pf(t):pf.ceil(t)}function vE(t,n){return t=vS(t),p2(pf.count(p$(t),t)+(4===p$(t).getDay()),n,2)}function vN(t){return t.getDay()}function vC(t,n){return p2(pu.count(p$(t)-1,t),n,2)}function vP(t,n){return p2(t.getFullYear()%100,n,2)}function vR(t,n){return p2((t=vS(t)).getFullYear()%100,n,2)}function vO(t,n){return p2(t.getFullYear()%1e4,n,4)}function vj(t,n){var e=t.getDay();return p2((t=e>=4||0===e?pf(t):pf.ceil(t)).getFullYear()%1e4,n,4)}function vz(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+p2(n/60|0,"0",2)+p2(n%60,"0",2)}function vD(t,n){return p2(t.getUTCDate(),n,2)}function vL(t,n){return p2(t.getUTCHours(),n,2)}function vI(t,n){return p2(t.getUTCHours()%12||12,n,2)}function v$(t,n){return p2(1+pn.count(pV(t),t),n,3)}function vB(t,n){return p2(t.getUTCMilliseconds(),n,3)}function vV(t,n){return vB(t,n)+"000"}function vU(t,n){return p2(t.getUTCMonth()+1,n,2)}function vF(t,n){return p2(t.getUTCMinutes(),n,2)}function vq(t,n){return p2(t.getUTCSeconds(),n,2)}function vG(t){var n=t.getUTCDay();return 0===n?7:n}function vY(t,n){return p2(px.count(pV(t)-1,t),n,2)}function vH(t){var n=t.getUTCDay();return n>=4||0===n?pA(t):pA.ceil(t)}function vW(t,n){return t=vH(t),p2(pA.count(pV(t),t)+(4===pV(t).getUTCDay()),n,2)}function vZ(t){return t.getUTCDay()}function vX(t,n){return p2(pw.count(pV(t)-1,t),n,2)}function vK(t,n){return p2(t.getUTCFullYear()%100,n,2)}function vJ(t,n){return p2((t=vH(t)).getUTCFullYear()%100,n,2)}function vQ(t,n){return p2(t.getUTCFullYear()%1e4,n,4)}function v0(t,n){var e=t.getUTCDay();return p2((t=e>=4||0===e?pA(t):pA.ceil(t)).getUTCFullYear()%1e4,n,4)}function v1(){return"+0000"}function v2(){return"%"}function v6(t){return+t}function v3(t){return Math.floor(+t/1e3)}function v5(t){return ta=(to=pK(t)).format,tu=to.parse,tc=to.utcFormat,tl=to.utcParse,to}function v4(t){return new Date(t)}function v8(t){return t instanceof Date?+t:+new Date(+t)}function v7(t,n,e,r,i,o,a,u,c,l){var f=dS(),s=f.invert,h=f.domain,d=l(".%L"),p=l(":%S"),v=l("%I:%M"),g=l("%I %p"),y=l("%a %d"),_=l("%b %d"),b=l("%B"),m=l("%Y");function x(t){return(c(t)<t?d:u(t)<t?p:a(t)<t?v:o(t)<t?g:r(t)<t?i(t)<t?y:_:e(t)<t?b:m)(t)}return f.invert=function(t){return new Date(s(t))},f.domain=function(t){return arguments.length?h(Array.from(t,v8)):h().map(v4)},f.ticks=function(n){var e=h();return t(e[0],e[e.length-1],null==n?10:n)},f.tickFormat=function(t,n){return null==n?x:l(n)},f.nice=function(t){var e=h();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?h(dC(e,t)):f},f.copy=function(){return dA(f,v7(t,n,e,r,i,o,a,u,c,l))},f}function v9(){return dd.apply(v7(pY,pH,p$,pz,pa,d9,d5,d1,dQ,ta).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gt(){return dd.apply(v7(pq,pG,pV,pL,px,pn,d8,d6,dQ,tc).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gn(){var t,n,e,r,i,o=0,a=1,u=dx,c=!1;function l(n){return null==n||isNaN(n=+n)?i:u(0===e?.5:(n=(r(n)-t)*e,c?Math.max(0,Math.min(1,n)):n))}function f(t){return function(n){var e,r;return arguments.length?([e,r]=n,u=t(e,r),l):[u(0),u(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=r(o=+o),n=r(a=+a),e=t===n?0:1/(n-t),l):[o,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=f(e3),l.rangeRound=f(hj),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return r=i,t=i(o),n=i(a),e=t===n?0:1/(n-t),l}}function ge(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function gr(){var t=dq(gn());return t.copy=function(){return ge(t,gr()).exponent(t.exponent())},dp.apply(t,arguments)}function gi(){return gr.apply(null,arguments).exponent(.5)}function go(){var t,n,e,r,i,o,a,u=0,c=.5,l=1,f=1,s=dx,h=!1;function d(t){return isNaN(t=+t)?a:(t=.5+((t=+o(t))-n)*(f*t<f*n?r:i),s(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(n){var e,r,i;return arguments.length?([e,r,i]=n,s=hH(t,[e,r,i]),d):[s(0),s(.5),s(1)]}}return d.domain=function(a){return arguments.length?([u,c,l]=a,t=o(u=+u),n=o(c=+c),e=o(l=+l),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),f=n<t?-1:1,d):[u,c,l]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(s=t,d):s},d.range=p(e3),d.rangeRound=p(hj),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return o=a,t=a(u),n=a(c),e=a(l),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),f=n<t?-1:1,d}}function ga(){var t=dq(go());return t.copy=function(){return ge(t,ga()).exponent(t.exponent())},dp.apply(t,arguments)}function gu(){return ga.apply(null,arguments).exponent(.5)}function gc(t){for(var n=t.length/6|0,e=Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}v5({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});let gl=gc("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),gf=gc("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),gs=gc("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),gh=gc("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),gd=gc("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),gp=gc("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),gv=gc("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),gg=gc("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),gy=gc("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),g_=gc("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),gb=gc("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),gm=t=>eY(t[t.length-1]);var gx=[,,,].concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(gc);let gw=gm(gx);var gM=[,,,].concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(gc);let gT=gm(gM);var gA=[,,,].concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(gc);let gk=gm(gA);var gS=[,,,].concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(gc);let gE=gm(gS);var gN=[,,,].concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(gc);let gC=gm(gN);var gP=[,,,].concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(gc);let gR=gm(gP);var gO=[,,,].concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(gc);let gj=gm(gO);var gz=[,,,].concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(gc);let gD=gm(gz);var gL=[,,,].concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(gc);let gI=gm(gL);var g$=[,,,].concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(gc);let gB=gm(g$);var gV=[,,,].concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(gc);let gU=gm(gV);var gF=[,,,].concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(gc);let gq=gm(gF);var gG=[,,,].concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(gc);let gY=gm(gG);var gH=[,,,].concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(gc);let gW=gm(gH);var gZ=[,,,].concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(gc);let gX=gm(gZ);var gK=[,,,].concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(gc);let gJ=gm(gK);var gQ=[,,,].concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(gc);let g0=gm(gQ);var g1=[,,,].concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(gc);let g2=gm(g1);var g6=[,,,].concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(gc);let g3=gm(g6);var g5=[,,,].concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(gc);let g4=gm(g5);var g8=[,,,].concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(gc);let g7=gm(g8);var g9=[,,,].concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(gc);let yt=gm(g9);var yn=[,,,].concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(gc);let ye=gm(yn);var yr=[,,,].concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(gc);let yi=gm(yr);var yo=[,,,].concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(gc);let ya=gm(yo);var yu=[,,,].concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(gc);let yc=gm(yu);var yl=[,,,].concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(gc);let yf=gm(yl);function ys(t){return"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-(t=Math.max(0,Math.min(1,t)))*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-2710.57*t)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-67.37*t)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-2475.67*t)))))))+")"}let yh=hY(i7(300,.5,0),i7(-240,.5,1));var yd=hY(i7(-100,.75,.35),i7(80,1.5,.8)),yp=hY(i7(260,.75,.35),i7(80,1.5,.8)),yv=i7();function yg(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return yv.h=360*t-100,yv.s=1.5-1.5*n,yv.l=.8-.9*n,yv+""}var yy=eM(),y_=Math.PI/3,yb=2*Math.PI/3;function ym(t){var n;return t=(.5-t)*Math.PI,yy.r=255*(n=Math.sin(t))*n,yy.g=255*(n=Math.sin(t+y_))*n,yy.b=255*(n=Math.sin(t+yb))*n,yy+""}function yx(t){return"rgb("+Math.max(0,Math.min(255,Math.round(34.61+(t=Math.max(0,Math.min(1,t)))*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-14825.05*t)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+707.56*t)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-6838.66*t)))))))+")"}function yw(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}let yM=yw(gc("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));var yT=yw(gc("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),yA=yw(gc("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),yk=yw(gc("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));function yS(t){return n4(nW(t).call(document.documentElement))}var yE=0;function yN(){return new yC}function yC(){this._="@"+(++yE).toString(36)}function yP(t,n){return t.target&&(t=e5(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,t=>e4(t,n))}function yR(t){return"string"==typeof t?new n6([document.querySelectorAll(t)],[document.documentElement]):new n6([nb(t)],n2)}function yO(t){return function(){return t}}yC.prototype=yN.prototype={constructor:yC,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};let yj=Math.abs,yz=Math.atan2,yD=Math.cos,yL=Math.max,yI=Math.min,y$=Math.sin,yB=Math.sqrt,yV=Math.PI,yU=yV/2,yF=2*yV;function yq(t){return t>=1?yU:t<=-1?-yU:Math.asin(t)}function yG(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{let t=Math.floor(e);if(!(t>=0))throw RangeError(`invalid digits: ${e}`);n=t}return t},()=>new iS(n)}function yY(t){return t.innerRadius}function yH(t){return t.outerRadius}function yW(t){return t.startAngle}function yZ(t){return t.endAngle}function yX(t){return t&&t.padAngle}function yK(t,n,e,r,i,o,a){var u=t-e,c=n-r,l=(a?o:-o)/yB(u*u+c*c),f=l*c,s=-l*u,h=t+f,d=n+s,p=e+f,v=r+s,g=(h+p)/2,y=(d+v)/2,_=p-h,b=v-d,m=_*_+b*b,x=i-o,w=h*v-p*d,M=(b<0?-1:1)*yB(yL(0,x*x*m-w*w)),T=(w*b-_*M)/m,A=(-w*_-b*M)/m,k=(w*b+_*M)/m,S=(-w*_+b*M)/m,E=T-g,N=A-y,C=k-g,P=S-y;return E*E+N*N>C*C+P*P&&(T=k,A=S),{cx:T,cy:A,x01:-f,y01:-s,x11:T*(i/x-1),y11:A*(i/x-1)}}function yJ(){var t=yY,n=yH,e=yO(0),r=null,i=yW,o=yZ,a=yX,u=null,c=yG(l);function l(){var l,f,s=+t.apply(this,arguments),h=+n.apply(this,arguments),d=i.apply(this,arguments)-yU,p=o.apply(this,arguments)-yU,v=yj(p-d),g=p>d;if(u||(u=l=c()),h<s&&(f=h,h=s,s=f),h>1e-12){if(v>yF-1e-12)u.moveTo(h*yD(d),h*y$(d)),u.arc(0,0,h,d,p,!g),s>1e-12&&(u.moveTo(s*yD(p),s*y$(p)),u.arc(0,0,s,p,d,g));else{var y,_,b=d,m=p,x=d,w=p,M=v,T=v,A=a.apply(this,arguments)/2,k=A>1e-12&&(r?+r.apply(this,arguments):yB(s*s+h*h)),S=yI(yj(h-s)/2,+e.apply(this,arguments)),E=S,N=S;if(k>1e-12){var C=yq(k/s*y$(A)),P=yq(k/h*y$(A));(M-=2*C)>1e-12?(C*=g?1:-1,x+=C,w-=C):(M=0,x=w=(d+p)/2),(T-=2*P)>1e-12?(P*=g?1:-1,b+=P,m-=P):(T=0,b=m=(d+p)/2)}var R=h*yD(b),O=h*y$(b),j=s*yD(w),z=s*y$(w);if(S>1e-12){var D,L=h*yD(m),I=h*y$(m),$=s*yD(x),B=s*y$(x);if(v<yV){if(D=function(t,n,e,r,i,o,a,u){var c=e-t,l=r-n,f=a-i,s=u-o,h=s*c-f*l;if(!(h*h<1e-12))return h=(f*(n-o)-s*(t-i))/h,[t+h*c,n+h*l]}(R,O,$,B,L,I,j,z)){var V,U=R-D[0],F=O-D[1],q=L-D[0],G=I-D[1],Y=1/y$(((V=(U*q+F*G)/(yB(U*U+F*F)*yB(q*q+G*G)))>1?0:V<-1?yV:Math.acos(V))/2),H=yB(D[0]*D[0]+D[1]*D[1]);E=yI(S,(s-H)/(Y-1)),N=yI(S,(h-H)/(Y+1))}else E=N=0}}T>1e-12?N>1e-12?(y=yK($,B,R,O,h,N,g),_=yK(L,I,j,z,h,N,g),u.moveTo(y.cx+y.x01,y.cy+y.y01),N<S?u.arc(y.cx,y.cy,N,yz(y.y01,y.x01),yz(_.y01,_.x01),!g):(u.arc(y.cx,y.cy,N,yz(y.y01,y.x01),yz(y.y11,y.x11),!g),u.arc(0,0,h,yz(y.cy+y.y11,y.cx+y.x11),yz(_.cy+_.y11,_.cx+_.x11),!g),u.arc(_.cx,_.cy,N,yz(_.y11,_.x11),yz(_.y01,_.x01),!g))):(u.moveTo(R,O),u.arc(0,0,h,b,m,!g)):u.moveTo(R,O),s>1e-12&&M>1e-12?E>1e-12?(y=yK(j,z,L,I,s,-E,g),_=yK(R,O,$,B,s,-E,g),u.lineTo(y.cx+y.x01,y.cy+y.y01),E<S?u.arc(y.cx,y.cy,E,yz(y.y01,y.x01),yz(_.y01,_.x01),!g):(u.arc(y.cx,y.cy,E,yz(y.y01,y.x01),yz(y.y11,y.x11),!g),u.arc(0,0,s,yz(y.cy+y.y11,y.cx+y.x11),yz(_.cy+_.y11,_.cx+_.x11),g),u.arc(_.cx,_.cy,E,yz(_.y11,_.x11),yz(_.y01,_.x01),!g))):u.arc(0,0,s,w,x,g):u.lineTo(j,z)}}else u.moveTo(0,0);if(u.closePath(),l)return u=null,l+""||null}return l.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-yV/2;return[yD(r)*e,y$(r)*e]},l.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:yO(+n),l):t},l.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:yO(+t),l):n},l.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:yO(+t),l):e},l.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:yO(+t),l):r},l.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:yO(+t),l):i},l.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:yO(+t),l):o},l.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:yO(+t),l):a},l.context=function(t){return arguments.length?(u=null==t?null:t,l):u},l}var yQ=Array.prototype.slice;function y0(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function y1(t){this._context=t}function y2(t){return new y1(t)}function y6(t){return t[0]}function y3(t){return t[1]}function y5(t,n){var e=yO(!0),r=null,i=y2,o=null,a=yG(u);function u(u){var c,l,f,s=(u=y0(u)).length,h=!1;for(null==r&&(o=i(f=a())),c=0;c<=s;++c)!(c<s&&e(l=u[c],c,u))===h&&((h=!h)?o.lineStart():o.lineEnd()),h&&o.point(+t(l,c,u),+n(l,c,u));if(f)return o=null,f+""||null}return t="function"==typeof t?t:void 0===t?y6:yO(t),n="function"==typeof n?n:void 0===n?y3:yO(n),u.x=function(n){return arguments.length?(t="function"==typeof n?n:yO(+n),u):t},u.y=function(t){return arguments.length?(n="function"==typeof t?t:yO(+t),u):n},u.defined=function(t){return arguments.length?(e="function"==typeof t?t:yO(!!t),u):e},u.curve=function(t){return arguments.length?(i=t,null!=r&&(o=i(r)),u):i},u.context=function(t){return arguments.length?(null==t?r=o=null:o=i(r=t),u):r},u}function y4(t,n,e){var r=null,i=yO(!0),o=null,a=y2,u=null,c=yG(l);function l(l){var f,s,h,d,p,v=(l=y0(l)).length,g=!1,y=Array(v),_=Array(v);for(null==o&&(u=a(p=c())),f=0;f<=v;++f){if(!(f<v&&i(d=l[f],f,l))===g){if(g=!g)s=f,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),h=f-1;h>=s;--h)u.point(y[h],_[h]);u.lineEnd(),u.areaEnd()}}g&&(y[f]=+t(d,f,l),_[f]=+n(d,f,l),u.point(r?+r(d,f,l):y[f],e?+e(d,f,l):_[f]))}if(p)return u=null,p+""||null}function f(){return y5().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?y6:yO(+t),n="function"==typeof n?n:void 0===n?yO(0):yO(+n),e="function"==typeof e?e:void 0===e?y3:yO(+e),l.x=function(n){return arguments.length?(t="function"==typeof n?n:yO(+n),r=null,l):t},l.x0=function(n){return arguments.length?(t="function"==typeof n?n:yO(+n),l):t},l.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:yO(+t),l):r},l.y=function(t){return arguments.length?(n="function"==typeof t?t:yO(+t),e=null,l):n},l.y0=function(t){return arguments.length?(n="function"==typeof t?t:yO(+t),l):n},l.y1=function(t){return arguments.length?(e=null==t?null:"function"==typeof t?t:yO(+t),l):e},l.lineX0=l.lineY0=function(){return f().x(t).y(n)},l.lineY1=function(){return f().x(t).y(e)},l.lineX1=function(){return f().x(r).y(n)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:yO(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(u=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=u=null:u=a(o=t),l):o},l}function y8(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function y7(t){return t}function y9(){var t=y7,n=y8,e=null,r=yO(0),i=yO(yF),o=yO(0);function a(a){var u,c,l,f,s,h=(a=y0(a)).length,d=0,p=Array(h),v=Array(h),g=+r.apply(this,arguments),y=Math.min(yF,Math.max(-yF,i.apply(this,arguments)-g)),_=Math.min(Math.abs(y)/h,o.apply(this,arguments)),b=_*(y<0?-1:1);for(u=0;u<h;++u)(s=v[p[u]=u]=+t(a[u],u,a))>0&&(d+=s);for(null!=n?p.sort(function(t,e){return n(v[t],v[e])}):null!=e&&p.sort(function(t,n){return e(a[t],a[n])}),u=0,l=d?(y-h*b)/d:0;u<h;++u,g=f)f=g+((s=v[c=p[u]])>0?s*l:0)+b,v[c]={data:a[c],index:u,value:s,startAngle:g,endAngle:f,padAngle:_};return v}return a.value=function(n){return arguments.length?(t="function"==typeof n?n:yO(+n),a):t},a.sortValues=function(t){return arguments.length?(n=t,e=null,a):n},a.sort=function(t){return arguments.length?(e=t,n=null,a):e},a.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:yO(+t),a):r},a.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:yO(+t),a):i},a.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:yO(+t),a):o},a}y1.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var _t=_e(y2);function _n(t){this._curve=t}function _e(t){function n(n){return new _n(t(n))}return n._curve=t,n}function _r(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(_e(t)):n()._curve},t}function _i(){return _r(y5().curve(_t))}function _o(){var t=y4().curve(_t),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return _r(e())},delete t.lineX0,t.lineEndAngle=function(){return _r(r())},delete t.lineX1,t.lineInnerRadius=function(){return _r(i())},delete t.lineY0,t.lineOuterRadius=function(){return _r(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(_e(t)):n()._curve},t}function _a(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]}_n.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),-(n*Math.cos(t)))}};class _u{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}}class _c{constructor(t){this._context=t}lineStart(){this._point=0}lineEnd(){}point(t,n){if(t=+t,n=+n,0===this._point)this._point=1;else{let e=_a(this._x0,this._y0),r=_a(this._x0,this._y0=(this._y0+n)/2),i=_a(t,this._y0),o=_a(t,n);this._context.moveTo(...e),this._context.bezierCurveTo(...r,...i,...o)}this._x0=t,this._y0=n}}function _l(t){return new _u(t,!0)}function _f(t){return new _u(t,!1)}function _s(t){return new _c(t)}function _h(t){return t.source}function _d(t){return t.target}function _p(t){let n=_h,e=_d,r=y6,i=y3,o=null,a=null,u=yG(c);function c(){let c;let l=yQ.call(arguments),f=n.apply(this,l),s=e.apply(this,l);if(null==o&&(a=t(c=u())),a.lineStart(),l[0]=f,a.point(+r.apply(this,l),+i.apply(this,l)),l[0]=s,a.point(+r.apply(this,l),+i.apply(this,l)),a.lineEnd(),c)return a=null,c+""||null}return c.source=function(t){return arguments.length?(n=t,c):n},c.target=function(t){return arguments.length?(e=t,c):e},c.x=function(t){return arguments.length?(r="function"==typeof t?t:yO(+t),c):r},c.y=function(t){return arguments.length?(i="function"==typeof t?t:yO(+t),c):i},c.context=function(n){return arguments.length?(null==n?o=a=null:a=t(o=n),c):o},c}function _v(){return _p(_l)}function _g(){return _p(_f)}function _y(){let t=_p(_s);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}let __=yB(3),_b={draw(t,n){let e=.59436*yB(n+yI(n/28,.75)),r=e/2,i=r*__;t.moveTo(0,e),t.lineTo(0,-e),t.moveTo(-i,-r),t.lineTo(i,r),t.moveTo(-i,r),t.lineTo(i,-r)}},_m={draw(t,n){let e=yB(n/yV);t.moveTo(e,0),t.arc(0,0,e,0,yF)}},_x={draw(t,n){let e=yB(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},_w=yB(1/3),_M=2*_w,_T={draw(t,n){let e=yB(n/_M),r=e*_w;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},_A={draw(t,n){let e=.62625*yB(n);t.moveTo(0,-e),t.lineTo(e,0),t.lineTo(0,e),t.lineTo(-e,0),t.closePath()}},_k={draw(t,n){let e=.87559*yB(n-yI(n/7,2));t.moveTo(-e,0),t.lineTo(e,0),t.moveTo(0,e),t.lineTo(0,-e)}},_S={draw(t,n){let e=yB(n),r=-e/2;t.rect(r,r,e,e)}},_E={draw(t,n){let e=.4431*yB(n);t.moveTo(e,e),t.lineTo(e,-e),t.lineTo(-e,-e),t.lineTo(-e,e),t.closePath()}},_N=y$(yV/10)/y$(7*yV/10),_C=y$(yF/10)*_N,_P=-yD(yF/10)*_N,_R={draw(t,n){let e=yB(.8908130915292852*n),r=_C*e,i=_P*e;t.moveTo(0,-e),t.lineTo(r,i);for(let n=1;n<5;++n){let o=yF*n/5,a=yD(o),u=y$(o);t.lineTo(u*e,-a*e),t.lineTo(a*r-u*i,u*r+a*i)}t.closePath()}},_O=yB(3),_j={draw(t,n){let e=-yB(n/(3*_O));t.moveTo(0,2*e),t.lineTo(-_O*e,-e),t.lineTo(_O*e,-e),t.closePath()}},_z=yB(3),_D={draw(t,n){let e=.6824*yB(n),r=e/2,i=e*_z/2;t.moveTo(0,-e),t.lineTo(i,r),t.lineTo(-i,r),t.closePath()}},_L=yB(3)/2,_I=1/yB(12),_$=(_I/2+1)*3,_B={draw(t,n){let e=yB(n/_$),r=e/2,i=e*_I,o=e*_I+e,a=-r;t.moveTo(r,i),t.lineTo(r,o),t.lineTo(a,o),t.lineTo(-.5*r-_L*i,_L*r+-.5*i),t.lineTo(-.5*r-_L*o,_L*r+-.5*o),t.lineTo(-.5*a-_L*o,_L*a+-.5*o),t.lineTo(-.5*r+_L*i,-.5*i-_L*r),t.lineTo(-.5*r+_L*o,-.5*o-_L*r),t.lineTo(-.5*a+_L*o,-.5*o-_L*a),t.closePath()}},_V={draw(t,n){let e=.6189*yB(n-yI(n/6,1.7));t.moveTo(-e,-e),t.lineTo(e,e),t.moveTo(-e,e),t.lineTo(e,-e)}},_U=[_m,_x,_T,_S,_R,_j,_B],_F=[_m,_k,_V,_D,_b,_E,_A];function _q(t,n){let e=null,r=yG(i);function i(){let i;if(e||(e=i=r()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),i)return e=null,i+""||null}return t="function"==typeof t?t:yO(t||_m),n="function"==typeof n?n:yO(void 0===n?64:+n),i.type=function(n){return arguments.length?(t="function"==typeof n?n:yO(n),i):t},i.size=function(t){return arguments.length?(n="function"==typeof t?t:yO(+t),i):n},i.context=function(t){return arguments.length?(e=null==t?null:t,i):e},i}function _G(){}function _Y(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function _H(t){this._context=t}function _W(t){return new _H(t)}function _Z(t){this._context=t}function _X(t){return new _Z(t)}function _K(t){this._context=t}function _J(t){return new _K(t)}function _Q(t,n){this._basis=new _H(t),this._beta=n}_H.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:_Y(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:_Y(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},_Z.prototype={areaStart:_G,areaEnd:_G,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:_Y(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},_K.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:_Y(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},_Q.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],a=t[e]-i,u=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*a),this._beta*n[c]+(1-this._beta)*(o+r*u));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};let _0=function t(n){function e(t){return 1===n?new _H(t):new _Q(t,n)}return e.beta=function(n){return t(+n)},e}(.85);function _1(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function _2(t,n){this._context=t,this._k=(1-n)/6}_2.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:_1(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:_1(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let _6=function t(n){function e(t){return new _2(t,n)}return e.tension=function(n){return t(+n)},e}(0);function _3(t,n){this._context=t,this._k=(1-n)/6}_3.prototype={areaStart:_G,areaEnd:_G,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:_1(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let _5=function t(n){function e(t){return new _3(t,n)}return e.tension=function(n){return t(+n)},e}(0);function _4(t,n){this._context=t,this._k=(1-n)/6}_4.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:_1(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let _8=function t(n){function e(t){return new _4(t,n)}return e.tension=function(n){return t(+n)},e}(0);function _7(t,n,e){var r=t._x1,i=t._y1,o=t._x2,a=t._y2;if(t._l01_a>1e-12){var u=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*u-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*u-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>1e-12){var l=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,f=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*l+t._x1*t._l23_2a-n*t._l12_2a)/f,a=(a*l+t._y1*t._l23_2a-e*t._l12_2a)/f}t._context.bezierCurveTo(r,i,o,a,t._x2,t._y2)}function _9(t,n){this._context=t,this._alpha=n}_9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:_7(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let bt=function t(n){function e(t){return n?new _9(t,n):new _2(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function bn(t,n){this._context=t,this._alpha=n}bn.prototype={areaStart:_G,areaEnd:_G,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:_7(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let be=function t(n){function e(t){return n?new bn(t,n):new _3(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function br(t,n){this._context=t,this._alpha=n}br.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:_7(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};let bi=function t(n){function e(t){return n?new br(t,n):new _4(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function bo(t){this._context=t}function ba(t){return new bo(t)}function bu(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),a=(e-t._y1)/(i||r<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*r)/(r+i)))||0}function bc(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function bl(t,n,e){var r=t._x0,i=t._y0,o=t._x1,a=t._y1,u=(o-r)/3;t._context.bezierCurveTo(r+u,i+u*n,o-u,a-u*e,o,a)}function bf(t){this._context=t}function bs(t){this._context=new bh(t)}function bh(t){this._context=t}function bd(t){return new bf(t)}function bp(t){return new bs(t)}function bv(t){this._context=t}function bg(t){var n,e,r=t.length-1,i=Array(r),o=Array(r),a=Array(r);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,a[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,a[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,a[n]-=e*a[n-1];for(i[r-1]=a[r-1]/o[r-1],n=r-2;n>=0;--n)i[n]=(a[n]-i[n+1])/o[n];for(n=0,o[r-1]=(t[r]+i[r-1])/2;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}function by(t){return new bv(t)}function b_(t,n){this._context=t,this._t=n}function bb(t){return new b_(t,.5)}function bm(t){return new b_(t,0)}function bx(t){return new b_(t,1)}function bw(t,n){if((i=t.length)>1)for(var e,r,i,o=1,a=t[n[0]],u=a.length;o<i;++o)for(r=a,a=t[n[o]],e=0;e<u;++e)a[e][1]+=a[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]}function bM(t){for(var n=t.length,e=Array(n);--n>=0;)e[n]=n;return e}function bT(t,n){return t[n]}function bA(t){let n=[];return n.key=t,n}function bk(){var t=yO([]),n=bM,e=bw,r=bT;function i(i){var o,a,u=Array.from(t.apply(this,arguments),bA),c=u.length,l=-1;for(let t of i)for(o=0,++l;o<c;++o)(u[o][l]=[0,+r(t,u[o].key,l,i)]).data=t;for(o=0,a=y0(n(u));o<c;++o)u[a[o]].index=o;return e(u,a),u}return i.keys=function(n){return arguments.length?(t="function"==typeof n?n:yO(Array.from(n)),i):t},i.value=function(t){return arguments.length?(r="function"==typeof t?t:yO(+t),i):r},i.order=function(t){return arguments.length?(n=null==t?bM:"function"==typeof t?t:yO(Array.from(t)),i):n},i.offset=function(t){return arguments.length?(e=null==t?bw:t,i):e},i}function bS(t,n){if((r=t.length)>0){for(var e,r,i,o=0,a=t[0].length;o<a;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}bw(t,n)}}function bE(t,n){if((u=t.length)>0)for(var e,r,i,o,a,u,c=0,l=t[n[0]].length;c<l;++c)for(o=a=0,e=0;e<u;++e)(i=(r=t[n[e]][c])[1]-r[0])>0?(r[0]=o,r[1]=o+=i):i<0?(r[1]=a,r[0]=a+=i):(r[0]=0,r[1]=i)}function bN(t,n){if((e=t.length)>0){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var a=0,u=0;a<e;++a)u+=t[a][r][1]||0;i[r][1]+=i[r][0]=-u/2}bw(t,n)}}function bC(t,n){if((i=t.length)>0&&(r=(e=t[n[0]]).length)>0){for(var e,r,i,o=0,a=1;a<r;++a){for(var u=0,c=0,l=0;u<i;++u){for(var f=t[n[u]],s=f[a][1]||0,h=(s-(f[a-1][1]||0))/2,d=0;d<u;++d){var p=t[n[d]];h+=(p[a][1]||0)-(p[a-1][1]||0)}c+=s,l+=h*s}e[a-1][1]+=e[a-1][0]=o,c&&(o-=l/c)}e[a-1][1]+=e[a-1][0]=o,bw(t,n)}}function bP(t){var n=t.map(bR);return bM(t).sort(function(t,e){return n[t]-n[e]})}function bR(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}function bO(t){var n=t.map(bj);return bM(t).sort(function(t,e){return n[t]-n[e]})}function bj(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}function bz(t){return bO(t).reverse()}function bD(t){var n,e,r=t.length,i=t.map(bj),o=bP(t),a=0,u=0,c=[],l=[];for(n=0;n<r;++n)e=o[n],a<u?(a+=i[e],c.push(e)):(u+=i[e],l.push(e));return l.reverse().concat(c)}function bL(t){return bM(t).reverse()}bo.prototype={areaStart:_G,areaEnd:_G,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},bf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:bl(this,this._t0,bc(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,bl(this,bc(this,e=bu(this,t,n)),e);break;default:bl(this,this._t0,e=bu(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(bs.prototype=Object.create(bf.prototype)).point=function(t,n){bf.prototype.point.call(this,n,t)},bh.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},bv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e){if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=bg(t),i=bg(n),o=0,a=1;a<e;++o,++a)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[a],n[a])}(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},b_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}};var bI="%Y-%m-%dT%H:%M:%S.%LZ";let b$=Date.prototype.toISOString?function(t){return t.toISOString()}:tc(bI),bB=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:tl(bI);function bV(t,n,e){var r=new rl,i=n;return null==n||(r._restart=r.restart,r.restart=function(t,n,e){n=+n,e=null==e?ru():+e,r._restart(function o(a){a+=i,r._restart(o,i+=n,e),t(a)},n,e)}),r.restart(t,n,e),r}let bU=t=>()=>t;function bF(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function bq(t,n,e){this.k=t,this.x=n,this.y=e}bq.prototype={constructor:bq,scale:function(t){return 1===t?this:new bq(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new bq(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var bG=new bq(1,0,0);function bY(t){for(;!t.__zoom;)if(!(t=t.parentNode))return bG;return t.__zoom}function bH(t){t.stopImmediatePropagation()}function bW(t){t.preventDefault(),t.stopImmediatePropagation()}function bZ(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function bX(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function bK(){return this.__zoom||bG}function bJ(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function bQ(){return navigator.maxTouchPoints||"ontouchstart"in this}function b0(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function b1(){var t,n,e,r=bZ,i=bX,o=b0,a=bJ,u=bQ,c=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],f=250,s=hD,h=ng("start","zoom","end"),d=0,p=10;function v(t){t.property("__zoom",bK).on("wheel.zoom",w,{passive:!1}).on("mousedown.zoom",M).on("dblclick.zoom",T).filter(u).on("touchstart.zoom",A).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",S).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(t,n){return(n=Math.max(c[0],Math.min(c[1],n)))===t.k?t:new bq(n,t.x,t.y)}function y(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new bq(t.k,r,i)}function _(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function b(t,n,e,r){t.on("start.zoom",function(){m(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){m(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=m(this,t).event(r),a=i.apply(this,t),u=null==e?_(a):"function"==typeof e?e.apply(this,t):e,c=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),l=this.__zoom,f="function"==typeof n?n.apply(this,t):n,h=s(l.invert(u).concat(c/l.k),f.invert(u).concat(c/f.k));return function(t){if(1===t)t=f;else{var n=h(t),e=c/n[2];t=new bq(e,u[0]-n[0]*e,u[1]-n[1]*e)}o.zoom(null,t)}})}function m(t,n,e){return!e&&t.__zooming||new x(t,n)}function x(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function w(t,...n){if(r.apply(this,arguments)){var e=m(this,n).event(t),i=this.__zoom,u=Math.max(c[0],Math.min(c[1],i.k*Math.pow(2,a.apply(this,arguments)))),f=e4(t);if(e.wheel)(e.mouse[0][0]!==f[0]||e.mouse[0][1]!==f[1])&&(e.mouse[1]=i.invert(e.mouse[0]=f)),clearTimeout(e.wheel);else{if(i.k===u)return;e.mouse=[f,i.invert(f)],rw(this),e.start()}bW(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",o(y(g(i,u),e.mouse[0],e.mouse[1]),e.extent,l))}}function M(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,a=m(this,n,!0).event(t),u=n4(t.view).on("mousemove.zoom",function(t){if(bW(t),!a.moved){var n=t.clientX-f,e=t.clientY-s;a.moved=n*n+e*e>d}a.event(t).zoom("mouse",o(y(a.that.__zoom,a.mouse[0]=e4(t,i),a.mouse[1]),a.extent,l))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),ee(t.view,a.moved),bW(t),a.event(t).end()},!0),c=e4(t,i),f=t.clientX,s=t.clientY;en(t.view),bH(t),a.mouse=[c,this.__zoom.invert(c)],rw(this),a.start()}}function T(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,a=e4(t.changedTouches?t.changedTouches[0]:t,this),u=e.invert(a),c=e.k*(t.shiftKey?.5:2),s=o(y(g(e,c),a,u),i.apply(this,n),l);bW(t),f>0?n4(this).transition().duration(f).call(b,s,a,t):n4(this).call(v.transform,s,a,t)}}function A(e,...i){if(r.apply(this,arguments)){var o,a,u,c,l=e.touches,f=l.length,s=m(this,i,e.changedTouches.length===f).event(e);for(bH(e),a=0;a<f;++a)c=[c=e4(u=l[a],this),this.__zoom.invert(c),u.identifier],s.touch0?s.touch1||s.touch0[2]===c[2]||(s.touch1=c,s.taps=0):(s.touch0=c,o=!0,s.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(s.taps<2&&(n=c[0],t=setTimeout(function(){t=null},500)),rw(this),s.start())}}function k(t,...n){if(this.__zooming){var e,r,i,a,u=m(this,n).event(t),c=t.changedTouches,f=c.length;for(bW(t),e=0;e<f;++e)i=e4(r=c[e],this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var s=u.touch0[0],h=u.touch0[1],d=u.touch1[0],p=u.touch1[1],v=(v=d[0]-s[0])*v+(v=d[1]-s[1])*v,_=(_=p[0]-h[0])*_+(_=p[1]-h[1])*_;r=g(r,Math.sqrt(v/_)),i=[(s[0]+d[0])/2,(s[1]+d[1])/2],a=[(h[0]+p[0])/2,(h[1]+p[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],a=u.touch0[1]}u.zoom("touch",o(y(r,i,a),u.extent,l))}}function S(t,...r){if(this.__zooming){var i,o,a=m(this,r).event(t),u=t.changedTouches,c=u.length;for(bH(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<c;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=e4(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<p)){var l=n4(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return v.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",bK),t!==i?b(t,n,e,r):i.interrupt().each(function(){m(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},v.scaleBy=function(t,n,e,r){v.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},v.scaleTo=function(t,n,e,r){v.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,a=null==e?_(t):"function"==typeof e?e.apply(this,arguments):e,u=r.invert(a),c="function"==typeof n?n.apply(this,arguments):n;return o(y(g(r,c),a,u),t,l)},e,r)},v.translateBy=function(t,n,e,r){v.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),l)},null,r)},v.translateTo=function(t,n,e,r,a){v.transform(t,function(){var t=i.apply(this,arguments),a=this.__zoom,u=null==r?_(t):"function"==typeof r?r.apply(this,arguments):r;return o(bG.translate(u[0],u[1]).scale(a.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,l)},r,a)},x.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=n4(this.that).datum();h.call(t,this.that,new bF(t,{sourceEvent:this.sourceEvent,target:v,type:t,transform:this.that.__zoom,dispatch:h}),n)}},v.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:bU(+t),v):a},v.filter=function(t){return arguments.length?(r="function"==typeof t?t:bU(!!t),v):r},v.touchable=function(t){return arguments.length?(u="function"==typeof t?t:bU(!!t),v):u},v.extent=function(t){return arguments.length?(i="function"==typeof t?t:bU([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),v):i},v.scaleExtent=function(t){return arguments.length?(c[0]=+t[0],c[1]=+t[1],v):[c[0],c[1]]},v.translateExtent=function(t){return arguments.length?(l[0][0]=+t[0][0],l[1][0]=+t[1][0],l[0][1]=+t[0][1],l[1][1]=+t[1][1],v):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},v.constrain=function(t){return arguments.length?(o=t,v):o},v.duration=function(t){return arguments.length?(f=+t,v):f},v.interpolate=function(t){return arguments.length?(s=t,v):s},v.on=function(){var t=h.on.apply(h,arguments);return t===h?v:t},v.clickDistance=function(t){return arguments.length?(d=(t=+t)*t,v):Math.sqrt(d)},v.tapDistance=function(t){return arguments.length?(p=+t,v):p},v}bY.prototype=bq.prototype}};