"use strict";(()=>{var e={};e.id=3800,e.ids=[3800],e.modules={47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},66579:(e,t,a)=>{a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>x,tree:()=>p});var r=a(50482),s=a(69108),o=a(62563),n=a.n(o),i=a(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let p=["",{children:["cookie-policy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,37856)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\cookie-policy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,24692)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,48206)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\not-found.tsx"]}],u=["C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\cookie-policy\\page.tsx"],c="/cookie-policy/page",d={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/cookie-policy/page",pathname:"/cookie-policy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},37856:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u,generateMetadata:()=>p});var r=a(25036),s=a(23691),o=a(49256),n=a(35992),i=a(75509);async function l(){return await (0,i.Z)("cookie-policy","populate=rich_text,seo.schema")}async function p({}){let e=await (0,i.Z)("cookie-policy","populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema"),t=e?.data?.attributes?.seo;return(0,n.Z)(t)}async function u(){let e=await l();return(0,r.jsxs)(r.Fragment,{children:[e?.data?.attributes?.seo&&r.jsx(o.Z,{data:e?.data?.attributes?.seo}),e?.data?.attributes?.rich_text&&r.jsx(s.Z,{richTextData:e?.data?.attributes})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,42,3676,3401],()=>a(66579));module.exports=r})();