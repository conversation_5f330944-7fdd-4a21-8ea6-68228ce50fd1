"use strict";exports.id=367,exports.ids=[367],exports.modules={90009:(e,a,i)=>{e.exports={parallel:i(10683),serial:i(80035),serialOrdered:i(88003)}},76687:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},23101:(e,a,i)=>{var n=i(35971);e.exports=function(e){var a=!1;return n(function(){a=!0}),function(i,o){a?e(i,o):n(function(){e(i,o)})}}},35971:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},42953:(e,a,i)=>{var n=i(23101),o=i(76687);e.exports=function(e,a,i,s){var t,r,c=i.keyedList?i.keyedList[i.index]:i.index;i.jobs[c]=(t=e[c],r=function(e,a){c in i.jobs&&(delete i.jobs[c],e?o(i):i.results[c]=a,s(e,i.results))},2==a.length?a(t,n(r)):a(t,c,n(r)))}},18703:e=>{e.exports=function(e,a){var i=!Array.isArray(e),n={index:0,keyedList:i||a?Object.keys(e):null,jobs:{},results:i?{}:[],size:i?Object.keys(e).length:e.length};return a&&n.keyedList.sort(i?a:function(i,n){return a(e[i],e[n])}),n}},38530:(e,a,i)=>{var n=i(76687),o=i(23101);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),o(e)(null,this.results))}},10683:(e,a,i)=>{var n=i(42953),o=i(18703),s=i(38530);e.exports=function(e,a,i){for(var t=o(e);t.index<(t.keyedList||e).length;)n(e,a,t,function(e,a){if(e){i(e,a);return}if(0===Object.keys(t.jobs).length){i(null,t.results);return}}),t.index++;return s.bind(t,i)}},80035:(e,a,i)=>{var n=i(88003);e.exports=function(e,a,i){return n(e,a,null,i)}},88003:(e,a,i)=>{var n=i(42953),o=i(18703),s=i(38530);function t(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,i,t){var r=o(e,i);return n(e,a,r,function i(o,s){if(o){t(o,s);return}if(r.index++,r.index<(r.keyedList||e).length){n(e,a,r,i);return}t(null,r.results)}),s.bind(r,t)},e.exports.ascending=t,e.exports.descending=function(e,a){return -1*t(e,a)}},48030:(e,a,i)=>{var n=i(73837),o=i(12781).Stream,s=i(5554);function t(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=t,n.inherits(t,o),t.create=function(e){var a=new this;for(var i in e=e||{})a[i]=e[i];return a},t.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},t.prototype.append=function(e){if(t.isStreamLike(e)){if(!(e instanceof s)){var a=s.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},t.prototype.pipe=function(e,a){return o.prototype.pipe.call(this,e,a),this.resume(),e},t.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},t.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){t.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},t.prototype._pipeNext=function(e){if(this._currentStream=e,t.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},t.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},t.prototype.write=function(e){this.emit("data",e)},t.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},t.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},t.prototype.end=function(){this._reset(),this.emit("end")},t.prototype.destroy=function(){this._reset(),this.emit("close")},t.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},t.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},t.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},t.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},71798:(e,a,i)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let i="color: "+this.color;a.splice(1,0,i,"color: inherit");let n=0,o=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))}),a.splice(o,0,i)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=i(93287)(a);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},93287:(e,a,i)=>{e.exports=function(e){function a(e){let i,o,s;let t=null;function r(...e){if(!r.enabled)return;let n=Number(new Date),o=n-(i||n);r.diff=o,r.prev=i,r.curr=n,i=n,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(i,n)=>{if("%%"===i)return"%";s++;let o=a.formatters[n];if("function"==typeof o){let a=e[s];i=o.call(r,a),e.splice(s,1),s--}return i}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=n,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==t?t:(o!==a.namespaces&&(o=a.namespaces,s=a.enabled(e)),s),set:e=>{t=e}}),"function"==typeof a.init&&a.init(r),r}function n(e,i){let n=a(this.namespace+(void 0===i?":":i)+e);return n.log=this.log,n}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names.map(o),...a.skips.map(o).map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){let i;a.save(e),a.namespaces=e,a.names=[],a.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),o=n.length;for(i=0;i<o;i++)n[i]&&("-"===(e=n[i].replace(/\*/g,".*?"))[0]?a.skips.push(RegExp("^"+e.slice(1)+"$")):a.names.push(RegExp("^"+e+"$")))},a.enabled=function(e){let i,n;if("*"===e[e.length-1])return!0;for(i=0,n=a.skips.length;i<n;i++)if(a.skips[i].test(e))return!1;for(i=0,n=a.names.length;i<n;i++)if(a.names[i].test(e))return!0;return!1},a.humanize=i(87553),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(i=>{a[i]=e[i]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let i=0;for(let a=0;a<e.length;a++)i=(i<<5)-i+e.charCodeAt(a)|0;return a.colors[Math.abs(i)%a.colors.length]},a.enable(a.load()),a}},7901:(e,a,i)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=i(71798):e.exports=i(61730)},61730:(e,a,i)=>{let n=i(76224),o=i(73837);a.init=function(e){e.inspectOpts={};let i=Object.keys(a.inspectOpts);for(let n=0;n<i.length;n++)e.inspectOpts[i[n]]=a.inspectOpts[i[n]]},a.log=function(...e){return process.stderr.write(o.format(...e)+"\n")},a.formatArgs=function(i){let{namespace:n,useColors:o}=this;if(o){let a=this.color,o="\x1b[3"+(a<8?a:"8;5;"+a),s=`  ${o};1m${n} \u001B[0m`;i[0]=s+i[0].split("\n").join("\n"+s),i.push(o+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else i[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+i[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:n.isatty(process.stderr.fd)},a.destroy=o.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=i(46052);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let i=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),n=process.env[a];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[i]=n,e},{}),e.exports=i(93287)(a);let{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,o.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,o.inspect(e,this.inspectOpts)}},5554:(e,a,i)=>{var n=i(12781).Stream,o=i(73837);function s(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=s,o.inherits(s,n),s.create=function(e,a){var i=new this;for(var n in a=a||{})i[n]=a[n];i.source=e;var o=e.emit;return e.emit=function(){return i._handleEmit(arguments),o.apply(e,arguments)},e.on("error",function(){}),i.pauseStream&&e.pause(),i},Object.defineProperty(s.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),s.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},s.prototype.resume=function(){this._released||this.release(),this.source.resume()},s.prototype.pause=function(){this.source.pause()},s.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},s.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},s.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},s.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},1649:(e,a,i)=>{var n;e.exports=function(){if(!n){try{n=i(7901)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},19017:(e,a,i)=>{var n=i(57310),o=n.URL,s=i(13685),t=i(95687),r=i(12781).Writable,c=i(39491),p=i(1649);!function(){var e="undefined"!=typeof process,a=T(Error.captureStackTrace);e||a||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new o(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,i,n){this._redirectable.emit(e,a,i,n)}});var x=S("ERR_INVALID_URL","Invalid URL",TypeError),f=S("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),h=S("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",f),v=S("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=S("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||k;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var i=this;this._onNativeResponse=function(e){try{i._processResponse(e)}catch(e){i.emit("error",e instanceof f?e:new f({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},i={};return Object.keys(e).forEach(function(n){var s=n+":",t=i[s]=e[n],r=a[n]=Object.create(t);Object.defineProperties(r,{request:{value:function(e,n,t){var r;return(r=e,o&&r instanceof o)?e=R(e):O(e)?e=R(j(e)):(t=n,n=_(e),e={protocol:s}),T(n)&&(t=n,n=null),(n=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,n)).nativeProtocols=i,O(n.host)||O(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,s,"protocol mismatch"),p("options",n),new y(n,t)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,i){var n=r.request(e,a,i);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),a}function k(){}function j(e){var a;if(l)a=new o(e);else if(!O((a=_(n.parse(e))).protocol))throw new x({input:e});return a}function _(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new x({input:e.href||e});return e}function R(e,a){var i=a||{};for(var n of u)i[n]=e[n];return i.hostname.startsWith("[")&&(i.hostname=i.hostname.slice(1,-1)),""!==i.port&&(i.port=Number(i.port)),i.path=i.search?i.pathname+i.search:i.pathname,i}function E(e,a){var i;for(var n in a)e.test(n)&&(i=a[n],delete a[n]);return null==i?void 0:String(i).trim()}function S(e,a,i){function n(i){T(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,i||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return n.prototype=new(i||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function C(e,a){for(var i of d)e.removeListener(i,m[i]);e.on("error",k),e.destroy(a)}function O(e){return"string"==typeof e||e instanceof String}function T(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){C(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return C(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,i){if(this._ending)throw new b;if(!O(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(T(a)&&(i=a,a=null),0===e.length){i&&i();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,i)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,i){if(T(e)?(i=e,e=a=null):T(a)&&(i=a,a=null),e){var n=this,o=this._currentRequest;this.write(e,a,function(){n._ended=!0,o.end(null,null,i)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,i)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var i=this;function n(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function o(a){i._timeout&&clearTimeout(i._timeout),i._timeout=setTimeout(function(){i.emit("timeout"),s()},e),n(a)}function s(){i._timeout&&(clearTimeout(i._timeout),i._timeout=null),i.removeListener("abort",s),i.removeListener("error",s),i.removeListener("response",s),i.removeListener("close",s),a&&i.removeListener("timeout",a),i.socket||i._currentRequest.removeListener("socket",o)}return a&&this.on("timeout",a),this.socket?o(this.socket):this._currentRequest.once("socket",o),this.on("socket",n),this.on("abort",s),this.on("error",s),this.on("response",s),this.on("close",s),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,i){return this._currentRequest[e](a,i)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var i=e.slice(0,-1);this._options.agent=this._options.agents[i]}var o=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var s of(o._redirectable=this,d))o.on(s,m[s]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var t=0,r=this,c=this._requestBodyBuffers;!function e(a){if(o===r._currentRequest){if(a)r.emit("error",a);else if(t<c.length){var i=c[t++];o.finished||o.write(i.data,i.encoding,e)}else r._ended&&o.end()}}()}},y.prototype._processResponse=function(e){var a,i,s,t=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:t});var r=e.headers.location;if(!r||!1===this._options.followRedirects||t<300||t>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(C(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new h;var u=this._options.beforeRedirect;u&&(s=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==t&&302!==t||"POST"!==this._options.method)&&(303!==t||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],E(/^content-/i,this._options.headers));var m=E(/^host$/i,this._options.headers),x=j(this._currentUrl),f=m||x.host,v=/^\w+:/.test(r)?this._currentUrl:n.format(Object.assign(x,{host:f})),b=l?new o(r,v):j(n.resolve(v,r));if(p("redirecting to",b.href),this._isRedirect=!0,R(b,this._options),(b.protocol===x.protocol||"https:"===b.protocol)&&(b.host===f||(c(O(a=b.host)&&O(f)),(i=a.length-f.length-1)>0&&"."===a[i]&&a.endsWith(f)))||E(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),T(u)){var g={headers:e.headers,statusCode:t},y={url:v,method:d,headers:s};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:s,https:t}),e.exports.wrap=w},8951:(e,a,i)=>{var n=i(48030),o=i(73837),s=i(71017),t=i(13685),r=i(95687),c=i(57310).parse,p=i(57147),l=i(12781).Stream,u=i(78813),d=i(90009),m=i(70896);function x(e){if(!(this instanceof x))return new x(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[a]=e[a]}e.exports=x,o.inherits(x,n),x.LINE_BREAK="\r\n",x.DEFAULT_CONTENT_TYPE="application/octet-stream",x.prototype.append=function(e,a,i){"string"==typeof(i=i||{})&&(i={filename:i});var s=n.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),o.isArray(a)){this._error(Error("Arrays are not supported."));return}var t=this._multiPartHeader(e,a,i),r=this._multiPartFooter();s(t),s(a),s(r),this._trackLength(t,a,i)},x.prototype._trackLength=function(e,a,i){var n=0;null!=i.knownLength?n+=+i.knownLength:Buffer.isBuffer(a)?n=a.length:"string"==typeof a&&(n=Buffer.byteLength(a)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+x.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion")||a instanceof l)&&(i.knownLength||this._valuesToMeasure.push(a))},x.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(i,n){if(i){a(i);return}a(null,n.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(i){e.pause(),a(null,+i.headers["content-length"])}),e.resume()):a("Unknown stream")},x.prototype._multiPartHeader=function(e,a,i){if("string"==typeof i.header)return i.header;var n,o=this._getContentDisposition(a,i),s=this._getContentType(a,i),t="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(o||[]),"Content-Type":[].concat(s||[])};for(var c in"object"==typeof i.header&&m(r,i.header),r)if(r.hasOwnProperty(c)){if(null==(n=r[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(t+=c+": "+n.join("; ")+x.LINE_BREAK)}return"--"+this.getBoundary()+x.LINE_BREAK+t+x.LINE_BREAK},x.prototype._getContentDisposition=function(e,a){var i,n;return"string"==typeof a.filepath?i=s.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?i=s.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(i=s.basename(e.client._httpMessage.path||"")),i&&(n='filename="'+i+'"'),n},x.prototype._getContentType=function(e,a){var i=a.contentType;return!i&&e.name&&(i=u.lookup(e.name)),!i&&e.path&&(i=u.lookup(e.path)),!i&&e.readable&&e.hasOwnProperty("httpVersion")&&(i=e.headers["content-type"]),!i&&(a.filepath||a.filename)&&(i=u.lookup(a.filepath||a.filename)),i||"object"!=typeof e||(i=x.DEFAULT_CONTENT_TYPE),i},x.prototype._multiPartFooter=function(){return(function(e){var a=x.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},x.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+x.LINE_BREAK},x.prototype.getHeaders=function(e){var a,i={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(i[a.toLowerCase()]=e[a]);return i},x.prototype.setBoundary=function(e){this._boundary=e},x.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},x.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),i=0,n=this._streams.length;i<n;i++)"function"!=typeof this._streams[i]&&(e=Buffer.isBuffer(this._streams[i])?Buffer.concat([e,this._streams[i]]):Buffer.concat([e,Buffer.from(this._streams[i])]),("string"!=typeof this._streams[i]||this._streams[i].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(x.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},x.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},x.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},x.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},x.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(i,n){if(i){e(i);return}n.forEach(function(e){a+=e}),e(null,a)})},x.prototype.submit=function(e,a){var i,n,o={method:"post"};return"string"==typeof e?n=m({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},o):(n=m(e,o)).port||(n.port="https:"==n.protocol?443:80),n.headers=this.getHeaders(e.headers),i="https:"==n.protocol?r.request(n):t.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e){this._error(e);return}if(n&&i.setHeader("Content-Length",n),this.pipe(i),a){var o,s=function(e,n){return i.removeListener("error",s),i.removeListener("response",o),a.call(this,e,n)};o=s.bind(this,null),i.on("error",s),i.on("response",o)}}).bind(this)),i},x.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},x.prototype.toString=function(){return"[object FormData]"}},70896:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(i){e[i]=e[i]||a[i]}),e}},10753:e=>{e.exports=(e,a)=>{a=a||process.argv;let i=e.startsWith("-")?"":1===e.length?"-":"--",n=a.indexOf(i+e),o=a.indexOf("--");return -1!==n&&(-1===o||n<o)}},90279:(e,a,i)=>{/*!
 * mime-db
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015-2022 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=i(2753)},78813:(e,a,i)=>{/*!
 * mime-types
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var n=i(90279),o=i(71017).extname,s=/^\s*([^;\s]*)(?:;|\s|$)/,t=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=s.exec(e),i=a&&n[a[1].toLowerCase()];return i&&i.charset?i.charset:!!(a&&t.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var i=-1===e.indexOf("/")?a.lookup(e):e;if(!i)return!1;if(-1===i.indexOf("charset")){var n=a.charset(i);n&&(i+="; charset="+n.toLowerCase())}return i},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var i=s.exec(e),n=i&&a.extensions[i[1].toLowerCase()];return!!n&&!!n.length&&n[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var i=o("x."+e).toLowerCase().substr(1);return!!i&&(a.types[i]||!1)},a.types=Object.create(null),function(e,a){var i=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(o){var s=n[o],t=s.extensions;if(t&&t.length){e[o]=t;for(var r=0;r<t.length;r++){var c=t[r];if(a[c]){var p=i.indexOf(n[a[c]].source),l=i.indexOf(s.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=o}}})}(a.extensions,a.types)},87553:e=>{function a(e,a,i,n){return Math.round(e/i)+" "+n+(a>=1.5*i?"s":"")}e.exports=function(e,i){i=i||{};var n,o,s=typeof e;if("string"===s&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var i=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return 6048e5*i;case"days":case"day":case"d":return 864e5*i;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*i;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*i;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*i;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}(e);if("number"===s&&isFinite(e))return i.long?(n=Math.abs(e))>=864e5?a(e,n,864e5,"day"):n>=36e5?a(e,n,36e5,"hour"):n>=6e4?a(e,n,6e4,"minute"):n>=1e3?a(e,n,1e3,"second"):e+" ms":(o=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":o>=36e5?Math.round(e/36e5)+"h":o>=6e4?Math.round(e/6e4)+"m":o>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},98984:(e,a,i)=>{var n=i(39729);i.o(n,"NextResponse")&&i.d(a,{NextResponse:function(){return n.NextResponse}})},97347:e=>{var a=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};function t(e){var a;let i=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(a=e.value)?a:"")}; ${i.join("; ")}`}function r(e){let a=new Map;for(let i of e.split(/; */)){if(!i)continue;let e=i.indexOf("=");if(-1===e){a.set(i,"true");continue}let[n,o]=[i.slice(0,e),i.slice(e+1)];try{a.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return a}function c(e){var a,i;if(!e)return;let[[n,o],...s]=r(e),{domain:t,expires:c,httponly:u,maxage:d,path:m,samesite:x,secure:f,priority:h}=Object.fromEntries(s.map(([e,a])=>[e.toLowerCase(),a]));return function(e){let a={};for(let i in e)e[i]&&(a[i]=e[i]);return a}({name:n,value:decodeURIComponent(o),domain:t,...c&&{expires:new Date(c)},...u&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:m,...x&&{sameSite:p.includes(a=(a=x).toLowerCase())?a:void 0},...f&&{secure:!0},...h&&{priority:l.includes(i=(i=h).toLowerCase())?i:void 0}})}((e,i)=>{for(var n in i)a(e,n,{get:i[n],enumerable:!0})})(s,{RequestCookies:()=>u,ResponseCookies:()=>d,parseCookie:()=>r,parseSetCookie:()=>c,stringifyCookie:()=>t}),e.exports=((e,s,t,r)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let t of n(s))o.call(e,t)||void 0===t||a(e,t,{get:()=>s[t],enumerable:!(r=i(s,t))||r.enumerable});return e})(a({},"__esModule",{value:!0}),s);var p=["strict","lax","none"],l=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let a=e.get("cookie");if(a)for(let[e,i]of r(a))this._parsed.set(e,{name:e,value:i})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let a="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(a)}getAll(...e){var a;let i=Array.from(this._parsed);if(!e.length)return i.map(([e,a])=>a);let n="string"==typeof e[0]?e[0]:null==(a=e[0])?void 0:a.name;return i.filter(([e])=>e===n).map(([e,a])=>a)}has(e){return this._parsed.has(e)}set(...e){let[a,i]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(a,{name:a,value:i}),this._headers.set("cookie",Array.from(n).map(([e,a])=>t(a)).join("; ")),this}delete(e){let a=this._parsed,i=Array.isArray(e)?e.map(e=>a.delete(e)):a.delete(e);return this._headers.set("cookie",Array.from(a).map(([e,a])=>t(a)).join("; ")),i}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var a,i,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(i=null==(a=e.getSetCookie)?void 0:a.call(e))?i:e.get("set-cookie"))?n:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var a,i,n,o,s,t=[],r=0;function c(){for(;r<e.length&&/\s/.test(e.charAt(r));)r+=1;return r<e.length}for(;r<e.length;){for(a=r,s=!1;c();)if(","===(i=e.charAt(r))){for(n=r,r+=1,c(),o=r;r<e.length&&"="!==(i=e.charAt(r))&&";"!==i&&","!==i;)r+=1;r<e.length&&"="===e.charAt(r)?(s=!0,r=o,t.push(e.substring(a,n)),a=r):r=n+1}else r+=1;(!s||r>=e.length)&&t.push(e.substring(a,e.length))}return t}(o)){let a=c(e);a&&this._parsed.set(a.name,a)}}get(...e){let a="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(a)}getAll(...e){var a;let i=Array.from(this._parsed.values());if(!e.length)return i;let n="string"==typeof e[0]?e[0]:null==(a=e[0])?void 0:a.name;return i.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[a,i,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(a,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:a,value:i,...n})),function(e,a){for(let[,i]of(a.delete("set-cookie"),e)){let e=t(i);a.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[a,i,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:a,path:i,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(t).join("; ")}}},42881:(e,a,i)=>{var n;(()=>{var o={226:function(o,s){!function(t,r){var c="function",p="undefined",l="object",u="string",d="major",m="model",x="name",f="type",h="vendor",v="version",b="architecture",g="console",y="mobile",w="tablet",k="smarttv",j="wearable",_="embedded",R="Amazon",E="Apple",S="ASUS",C="BlackBerry",O="Browser",T="Chrome",L="Firefox",z="Google",A="Huawei",P="Microsoft",q="Motorola",F="Opera",N="Samsung",U="Sharp",B="Sony",D="Xiaomi",I="Zebra",M="Facebook",H="Chromium OS",$="Mac OS",W=function(e,a){var i={};for(var n in e)a[n]&&a[n].length%2==0?i[n]=a[n].concat(e[n]):i[n]=e[n];return i},G=function(e){for(var a={},i=0;i<e.length;i++)a[e[i].toUpperCase()]=e[i];return a},V=function(e,a){return typeof e===u&&-1!==J(a).indexOf(J(e))},J=function(e){return e.toLowerCase()},K=function(e,a){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof a===p?e:e.substring(0,350)},Y=function(e,a){for(var i,n,o,s,t,p,u=0;u<a.length&&!t;){var d=a[u],m=a[u+1];for(i=n=0;i<d.length&&!t&&d[i];)if(t=d[i++].exec(e))for(o=0;o<m.length;o++)p=t[++n],typeof(s=m[o])===l&&s.length>0?2===s.length?typeof s[1]==c?this[s[0]]=s[1].call(this,p):this[s[0]]=s[1]:3===s.length?typeof s[1]!==c||s[1].exec&&s[1].test?this[s[0]]=p?p.replace(s[1],s[2]):r:this[s[0]]=p?s[1].call(this,p,s[2]):r:4===s.length&&(this[s[0]]=p?s[3].call(this,p.replace(s[1],s[2])):r):this[s]=p||r;u+=2}},X=function(e,a){for(var i in a)if(typeof a[i]===l&&a[i].length>0){for(var n=0;n<a[i].length;n++)if(V(a[i][n],e))return"?"===i?r:i}else if(V(a[i],e))return"?"===i?r:i;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[v,[x,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[v,[x,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[x,v],[/opios[\/ ]+([\w\.]+)/i],[v,[x,F+" Mini"]],[/\bopr\/([\w\.]+)/i],[v,[x,F]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[x,v],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[v,[x,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[v,[x,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[v,[x,"WeChat"]],[/konqueror\/([\w\.]+)/i],[v,[x,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[v,[x,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[v,[x,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[x,/(.+)/,"$1 Secure "+O],v],[/\bfocus\/([\w\.]+)/i],[v,[x,L+" Focus"]],[/\bopt\/([\w\.]+)/i],[v,[x,F+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[v,[x,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[v,[x,"Dolphin"]],[/coast\/([\w\.]+)/i],[v,[x,F+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[v,[x,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[v,[x,L]],[/\bqihu|(qi?ho?o?|360)browser/i],[[x,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[x,/(.+)/,"$1 "+O],v],[/(comodo_dragon)\/([\w\.]+)/i],[[x,/_/g," "],v],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[x,v],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[x],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[x,M],v],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[x,v],[/\bgsa\/([\w\.]+) .*safari\//i],[v,[x,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[v,[x,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[v,[x,T+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[x,T+" WebView"],v],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[v,[x,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[x,v],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[v,[x,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[v,x],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[x,[v,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[x,v],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[x,"Netscape"],v],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[v,[x,L+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[x,v],[/(cobalt)\/([\w\.]+)/i],[x,[v,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,J]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[m,[h,N],[f,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[m,[h,N],[f,y]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[m,[h,E],[f,y]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[m,[h,E],[f,w]],[/(macintosh);/i],[m,[h,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[m,[h,U],[f,y]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[m,[h,A],[f,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[m,[h,A],[f,y]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[m,/_/g," "],[h,D],[f,y]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[m,/_/g," "],[h,D],[f,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[m,[h,"OPPO"],[f,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[m,[h,"Vivo"],[f,y]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[m,[h,"Realme"],[f,y]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[m,[h,q],[f,y]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[m,[h,q],[f,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[m,[h,"LG"],[f,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[m,[h,"LG"],[f,y]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[m,[h,"Lenovo"],[f,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[m,/_/g," "],[h,"Nokia"],[f,y]],[/(pixel c)\b/i],[m,[h,z],[f,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[m,[h,z],[f,y]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[m,[h,B],[f,y]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[m,"Xperia Tablet"],[h,B],[f,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[m,[h,"OnePlus"],[f,y]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[m,[h,R],[f,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[m,/(.+)/g,"Fire Phone $1"],[h,R],[f,y]],[/(playbook);[-\w\),; ]+(rim)/i],[m,h,[f,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[m,[h,C],[f,y]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[m,[h,S],[f,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[m,[h,S],[f,y]],[/(nexus 9)/i],[m,[h,"HTC"],[f,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[m,/_/g," "],[f,y]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[m,[h,"Acer"],[f,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[m,[h,"Meizu"],[f,y]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,m,[f,y]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,m,[f,w]],[/(surface duo)/i],[m,[h,P],[f,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[m,[h,"Fairphone"],[f,y]],[/(u304aa)/i],[m,[h,"AT&T"],[f,y]],[/\bsie-(\w*)/i],[m,[h,"Siemens"],[f,y]],[/\b(rct\w+) b/i],[m,[h,"RCA"],[f,w]],[/\b(venue[\d ]{2,7}) b/i],[m,[h,"Dell"],[f,w]],[/\b(q(?:mv|ta)\w+) b/i],[m,[h,"Verizon"],[f,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[m,[h,"Barnes & Noble"],[f,w]],[/\b(tm\d{3}\w+) b/i],[m,[h,"NuVision"],[f,w]],[/\b(k88) b/i],[m,[h,"ZTE"],[f,w]],[/\b(nx\d{3}j) b/i],[m,[h,"ZTE"],[f,y]],[/\b(gen\d{3}) b.+49h/i],[m,[h,"Swiss"],[f,y]],[/\b(zur\d{3}) b/i],[m,[h,"Swiss"],[f,w]],[/\b((zeki)?tb.*\b) b/i],[m,[h,"Zeki"],[f,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],m,[f,w]],[/\b(ns-?\w{0,9}) b/i],[m,[h,"Insignia"],[f,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[m,[h,"NextBook"],[f,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],m,[f,y]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],m,[f,y]],[/\b(ph-1) /i],[m,[h,"Essential"],[f,y]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[m,[h,"Envizen"],[f,w]],[/\b(trio[-\w\. ]+) b/i],[m,[h,"MachSpeed"],[f,w]],[/\btu_(1491) b/i],[m,[h,"Rotor"],[f,w]],[/(shield[\w ]+) b/i],[m,[h,"Nvidia"],[f,w]],[/(sprint) (\w+)/i],[h,m,[f,y]],[/(kin\.[onetw]{3})/i],[[m,/\./g," "],[h,P],[f,y]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[m,[h,I],[f,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[m,[h,I],[f,y]],[/smart-tv.+(samsung)/i],[h,[f,k]],[/hbbtv.+maple;(\d+)/i],[[m,/^/,"SmartTV"],[h,N],[f,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,"LG"],[f,k]],[/(apple) ?tv/i],[h,[m,E+" TV"],[f,k]],[/crkey/i],[[m,T+"cast"],[h,z],[f,k]],[/droid.+aft(\w)( bui|\))/i],[m,[h,R],[f,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[m,[h,U],[f,k]],[/(bravia[\w ]+)( bui|\))/i],[m,[h,B],[f,k]],[/(mitv-\w{5}) bui/i],[m,[h,D],[f,k]],[/Hbbtv.*(technisat) (.*);/i],[h,m,[f,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,K],[m,K],[f,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,m,[f,g]],[/droid.+; (shield) bui/i],[m,[h,"Nvidia"],[f,g]],[/(playstation [345portablevi]+)/i],[m,[h,B],[f,g]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[m,[h,P],[f,g]],[/((pebble))app/i],[h,m,[f,j]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[m,[h,E],[f,j]],[/droid.+; (glass) \d/i],[m,[h,z],[f,j]],[/droid.+; (wt63?0{2,3})\)/i],[m,[h,I],[f,j]],[/(quest( 2| pro)?)/i],[m,[h,M],[f,j]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[f,_]],[/(aeobc)\b/i],[m,[h,R],[f,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[m,[f,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[m,[f,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,y]],[/(android[-\w\. ]{0,9});.+buil/i],[m,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[v,[x,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[v,[x,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[x,v],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[v,x]],os:[[/microsoft (windows) (vista|xp)/i],[x,v],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[x,[v,X,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[x,"Windows"],[v,X,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[v,/_/g,"."],[x,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[x,$],[v,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[v,x],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[x,v],[/\(bb(10);/i],[v,[x,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[v,[x,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[v,[x,L+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[v,[x,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[v,[x,"watchOS"]],[/crkey\/([\d\.]+)/i],[v,[x,T+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[x,H],v],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[x,v],[/(sunos) ?([\w\.\d]*)/i],[[x,"Solaris"],v],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[x,v]]},ee=function(e,a){if(typeof e===l&&(a=e,e=r),!(this instanceof ee))return new ee(e,a).getResult();var i=typeof t!==p&&t.navigator?t.navigator:r,n=e||(i&&i.userAgent?i.userAgent:""),o=i&&i.userAgentData?i.userAgentData:r,s=a?W(Q,a):Q,g=i&&i.userAgent==n;return this.getBrowser=function(){var e,a={};return a[x]=r,a[v]=r,Y.call(a,n,s.browser),a[d]=typeof(e=a[v])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:r,g&&i&&i.brave&&typeof i.brave.isBrave==c&&(a[x]="Brave"),a},this.getCPU=function(){var e={};return e[b]=r,Y.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[h]=r,e[m]=r,e[f]=r,Y.call(e,n,s.device),g&&!e[f]&&o&&o.mobile&&(e[f]=y),g&&"Macintosh"==e[m]&&i&&typeof i.standalone!==p&&i.maxTouchPoints&&i.maxTouchPoints>2&&(e[m]="iPad",e[f]=w),e},this.getEngine=function(){var e={};return e[x]=r,e[v]=r,Y.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[x]=r,e[v]=r,Y.call(e,n,s.os),g&&!e[x]&&o&&"Unknown"!=o.platform&&(e[x]=o.platform.replace(/chrome os/i,H).replace(/macos/i,$)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===u&&e.length>350?K(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=G([x,v,d]),ee.CPU=G([b]),ee.DEVICE=G([m,h,f,g,y,k,w,j,_]),ee.ENGINE=ee.OS=G([x,v]),typeof s!==p?(o.exports&&(s=o.exports=ee),s.UAParser=ee):i.amdO?void 0!==(n=(function(){return ee}).call(a,i,a,e))&&(e.exports=n):typeof t!==p&&(t.UAParser=ee);var ea=typeof t!==p&&(t.jQuery||t.Zepto);if(ea&&!ea.ua){var ei=new ee;ea.ua=ei.getResult(),ea.ua.get=function(){return ei.getUA()},ea.ua.set=function(e){ei.setUA(e);var a=ei.getResult();for(var i in a)ea.ua[i]=a[i]}}}(this)}},s={};function t(e){var a=s[e];if(void 0!==a)return a.exports;var i=s[e]={exports:{}},n=!0;try{o[e].call(i.exports,i,i.exports,t),n=!1}finally{n&&delete s[e]}return i.exports}t.ab=__dirname+"/";var r=t(226);e.exports=r})()},95419:(e,a,i)=>{e.exports=i(30517)},8397:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{PageSignatureError:function(){return i},RemovedPageError:function(){return n},RemovedUAError:function(){return o}});class i extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class o extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},39729:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return o.NextRequest},NextResponse:function(){return s.NextResponse},userAgent:function(){return t.userAgent},userAgentFromString:function(){return t.userAgentFromString},URLPattern:function(){return r.URLPattern}});let n=i(78496),o=i(71868),s=i(70457),t=i(44214),r=i(27449)},10514:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"NextURL",{enumerable:!0,get:function(){return l}});let n=i(737),o=i(65418),s=i(40283),t=i(23588),r=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,a){return new URL(String(e).replace(r,"localhost"),a&&String(a).replace(r,"localhost"))}let p=Symbol("NextURLInternal");class l{constructor(e,a,i){let n,o;"object"==typeof a&&"pathname"in a||"string"==typeof a?(n=a,o=i||{}):o=i||a||{},this[p]={url:c(e,n??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,a,i,o,r;let c=(0,t.getNextPathnameInfo)(this[p].url.pathname,{nextConfig:this[p].options.nextConfig,parseData:!0,i18nProvider:this[p].options.i18nProvider}),l=(0,s.getHostname)(this[p].url,this[p].options.headers);this[p].domainLocale=this[p].options.i18nProvider?this[p].options.i18nProvider.detectDomainLocale(l):(0,n.detectDomainLocale)(null==(a=this[p].options.nextConfig)?void 0:null==(e=a.i18n)?void 0:e.domains,l);let u=(null==(i=this[p].domainLocale)?void 0:i.defaultLocale)||(null==(r=this[p].options.nextConfig)?void 0:null==(o=r.i18n)?void 0:o.defaultLocale);this[p].url.pathname=c.pathname,this[p].defaultLocale=u,this[p].basePath=c.basePath??"",this[p].buildId=c.buildId,this[p].locale=c.locale??u,this[p].trailingSlash=c.trailingSlash}formatPathname(){return(0,o.formatNextPathnameInfo)({basePath:this[p].basePath,buildId:this[p].buildId,defaultLocale:this[p].options.forceLocale?void 0:this[p].defaultLocale,locale:this[p].locale,pathname:this[p].url.pathname,trailingSlash:this[p].trailingSlash})}formatSearch(){return this[p].url.search}get buildId(){return this[p].buildId}set buildId(e){this[p].buildId=e}get locale(){return this[p].locale??""}set locale(e){var a,i;if(!this[p].locale||!(null==(i=this[p].options.nextConfig)?void 0:null==(a=i.i18n)?void 0:a.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[p].locale=e}get defaultLocale(){return this[p].defaultLocale}get domainLocale(){return this[p].domainLocale}get searchParams(){return this[p].url.searchParams}get host(){return this[p].url.host}set host(e){this[p].url.host=e}get hostname(){return this[p].url.hostname}set hostname(e){this[p].url.hostname=e}get port(){return this[p].url.port}set port(e){this[p].url.port=e}get protocol(){return this[p].url.protocol}set protocol(e){this[p].url.protocol=e}get href(){let e=this.formatPathname(),a=this.formatSearch();return`${this.protocol}//${this.host}${e}${a}${this.hash}`}set href(e){this[p].url=c(e),this.analyze()}get origin(){return this[p].url.origin}get pathname(){return this[p].url.pathname}set pathname(e){this[p].url.pathname=e}get hash(){return this[p].url.hash}set hash(e){this[p].url.hash=e}get search(){return this[p].url.search}set search(e){this[p].url.search=e}get password(){return this[p].url.password}set password(e){this[p].url.password=e}get username(){return this[p].url.username}set username(e){this[p].url.username=e}get basePath(){return this[p].basePath}set basePath(e){this[p].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new l(String(this),this[p].options)}}},63608:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=i(97347)},78496:(e,a)=>{function i(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"ImageResponse",{enumerable:!0,get:function(){return i}})},71868:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{INTERNALS:function(){return r},NextRequest:function(){return c}});let n=i(10514),o=i(68670),s=i(8397),t=i(63608),r=Symbol("internal request");class c extends Request{constructor(e,a={}){let i="string"!=typeof e&&"url"in e?e.url:String(e);(0,o.validateURL)(i),e instanceof Request?super(e,a):super(i,a);let s=new n.NextURL(i,{headers:(0,o.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:a.nextConfig});this[r]={cookies:new t.RequestCookies(this.headers),geo:a.geo||{},ip:a.ip,nextUrl:s,url:s.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[r].cookies}get geo(){return this[r].geo}get ip(){return this[r].ip}get nextUrl(){return this[r].nextUrl}get page(){throw new s.RemovedPageError}get ua(){throw new s.RemovedUAError}get url(){return this[r].url}}},70457:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"NextResponse",{enumerable:!0,get:function(){return p}});let n=i(10514),o=i(68670),s=i(63608),t=Symbol("internal response"),r=new Set([301,302,303,307,308]);function c(e,a){var i;if(null==e?void 0:null==(i=e.request)?void 0:i.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let i=[];for(let[n,o]of e.request.headers)a.set("x-middleware-request-"+n,o),i.push(n);a.set("x-middleware-override-headers",i.join(","))}}class p extends Response{constructor(e,a={}){super(e,a),this[t]={cookies:new s.ResponseCookies(this.headers),url:a.url?new n.NextURL(a.url,{headers:(0,o.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:a.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[t].cookies}static json(e,a){let i=Response.json(e,a);return new p(i.body,i)}static redirect(e,a){let i="number"==typeof a?a:(null==a?void 0:a.status)??307;if(!r.has(i))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let n="object"==typeof a?a:{},s=new Headers(null==n?void 0:n.headers);return s.set("Location",(0,o.validateURL)(e)),new p(null,{...n,headers:s,status:i})}static rewrite(e,a){let i=new Headers(null==a?void 0:a.headers);return i.set("x-middleware-rewrite",(0,o.validateURL)(e)),c(a,i),new p(null,{...a,headers:i})}static next(e){let a=new Headers(null==e?void 0:e.headers);return a.set("x-middleware-next","1"),c(e,a),new p(null,{...e,headers:a})}}},27449:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"URLPattern",{enumerable:!0,get:function(){return i}});let i="undefined"==typeof URLPattern?void 0:URLPattern},44214:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{isBot:function(){return o},userAgentFromString:function(){return s},userAgent:function(){return t}});let n=function(e){return e&&e.__esModule?e:{default:e}}(i(42881));function o(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function s(e){return{...(0,n.default)(e),isBot:void 0!==e&&o(e)}}function t({headers:e}){return s(e.get("user-agent")||void 0)}},68670:(e,a)=>{function i(e){let a=new Headers;for(let[i,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),a.append(i,e));return a}function n(e){var a,i,n,o,s,t=[],r=0;function c(){for(;r<e.length&&/\s/.test(e.charAt(r));)r+=1;return r<e.length}for(;r<e.length;){for(a=r,s=!1;c();)if(","===(i=e.charAt(r))){for(n=r,r+=1,c(),o=r;r<e.length&&"="!==(i=e.charAt(r))&&";"!==i&&","!==i;)r+=1;r<e.length&&"="===e.charAt(r)?(s=!0,r=o,t.push(e.substring(a,n)),a=r):r=n+1}else r+=1;(!s||r>=e.length)&&t.push(e.substring(a,e.length))}return t}function o(e){let a={},i=[];if(e)for(let[o,s]of e.entries())"set-cookie"===o.toLowerCase()?(i.push(...n(s)),a[o]=1===i.length?i[0]:i):a[o]=s;return a}function s(e){try{return String(new URL(String(e)))}catch(a){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:a})}}Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var i in a)Object.defineProperty(e,i,{enumerable:!0,get:a[i]})}(a,{fromNodeOutgoingHttpHeaders:function(){return i},splitCookiesString:function(){return n},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return s}})},40283:(e,a)=>{function i(e,a){let i;if((null==a?void 0:a.host)&&!Array.isArray(a.host))i=a.host.toString().split(":",1)[0];else{if(!e.hostname)return;i=e.hostname}return i.toLowerCase()}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"getHostname",{enumerable:!0,get:function(){return i}})},737:(e,a)=>{function i(e,a,i){if(e)for(let s of(i&&(i=i.toLowerCase()),e)){var n,o;if(a===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||i===s.defaultLocale.toLowerCase()||(null==(o=s.locales)?void 0:o.some(e=>e.toLowerCase()===i)))return s}}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"detectDomainLocale",{enumerable:!0,get:function(){return i}})},73935:(e,a)=>{function i(e,a){let i;let n=e.split("/");return(a||[]).some(a=>!!n[1]&&n[1].toLowerCase()===a.toLowerCase()&&(i=a,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:i}}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"normalizeLocalePath",{enumerable:!0,get:function(){return i}})},28030:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"addLocale",{enumerable:!0,get:function(){return s}});let n=i(23495),o=i(67211);function s(e,a,i,s){if(!a||a===i)return e;let t=e.toLowerCase();return!s&&((0,o.pathHasPrefix)(t,"/api")||(0,o.pathHasPrefix)(t,"/"+a.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+a)}},23495:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=i(81955);function o(e,a){if(!e.startsWith("/")||!a)return e;let{pathname:i,query:o,hash:s}=(0,n.parsePath)(e);return""+a+i+o+s}},2348:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"addPathSuffix",{enumerable:!0,get:function(){return o}});let n=i(81955);function o(e,a){if(!e.startsWith("/")||!a)return e;let{pathname:i,query:o,hash:s}=(0,n.parsePath)(e);return""+i+a+o+s}},65418:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"formatNextPathnameInfo",{enumerable:!0,get:function(){return r}});let n=i(5545),o=i(23495),s=i(2348),t=i(28030);function r(e){let a=(0,t.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(a=(0,n.removeTrailingSlash)(a)),e.buildId&&(a=(0,s.addPathSuffix)((0,o.addPathPrefix)(a,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),a=(0,o.addPathPrefix)(a,e.basePath),!e.buildId&&e.trailingSlash?a.endsWith("/")?a:(0,s.addPathSuffix)(a,"/"):(0,n.removeTrailingSlash)(a)}},23588:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"getNextPathnameInfo",{enumerable:!0,get:function(){return t}});let n=i(73935),o=i(37188),s=i(67211);function t(e,a){var i,t;let{basePath:r,i18n:c,trailingSlash:p}=null!=(i=a.nextConfig)?i:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):p};r&&(0,s.pathHasPrefix)(l.pathname,r)&&(l.pathname=(0,o.removePathPrefix)(l.pathname,r),l.basePath=r);let u=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),i=e[0];l.buildId=i,u="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===a.parseData&&(l.pathname=u)}if(c){let e=a.i18nProvider?a.i18nProvider.analyze(l.pathname):(0,n.normalizeLocalePath)(l.pathname,c.locales);l.locale=e.detectedLocale,l.pathname=null!=(t=e.pathname)?t:l.pathname,!e.detectedLocale&&l.buildId&&(e=a.i18nProvider?a.i18nProvider.analyze(u):(0,n.normalizeLocalePath)(u,c.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}},81955:(e,a)=>{function i(e){let a=e.indexOf("#"),i=e.indexOf("?"),n=i>-1&&(a<0||i<a);return n||a>-1?{pathname:e.substring(0,n?i:a),query:n?e.substring(i,a>-1?a:void 0):"",hash:a>-1?e.slice(a):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"parsePath",{enumerable:!0,get:function(){return i}})},67211:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=i(81955);function o(e,a){if("string"!=typeof e)return!1;let{pathname:i}=(0,n.parsePath)(e);return i===a||i.startsWith(a+"/")}},37188:(e,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"removePathPrefix",{enumerable:!0,get:function(){return o}});let n=i(67211);function o(e,a){if(!(0,n.pathHasPrefix)(e,a))return e;let i=e.slice(a.length);return i.startsWith("/")?i:"/"+i}},5545:(e,a)=>{function i(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"removeTrailingSlash",{enumerable:!0,get:function(){return i}})},33027:(e,a,i)=>{var n=i(57310).parse,o={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},s=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function t(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,i,r,c="string"==typeof e?n(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),i=u=parseInt(u)||o[p]||0,!(!(r=(t("npm_config_no_proxy")||t("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),o=n?n[1]:e,t=n?parseInt(n[2]):0;return!!t&&t!==i||(/^[.*]/.test(o)?("*"===o.charAt(0)&&(o=o.slice(1)),!s.call(a,o)):a!==o)}))))return"";var d=t("npm_config_"+p+"_proxy")||t(p+"_proxy")||t("npm_config_proxy")||t("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},46052:(e,a,i)=>{let n;let o=i(22037),s=i(10753),t=process.env;function r(e){var a;return 0!==(a=function(e){if(!1===n)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!e.isTTY&&!0!==n)return 0;let a=n?1:0;if("win32"===process.platform){let e=o.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in t)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(e=>e in t)||"codeship"===t.CI_NAME?1:a;if("TEAMCITY_VERSION"in t)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(t.TEAMCITY_VERSION)?1:0;if("truecolor"===t.COLORTERM)return 3;if("TERM_PROGRAM"in t){let e=parseInt((t.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(t.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(t.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(t.TERM)||"COLORTERM"in t?1:(t.TERM,a)}(e))&&{level:a,hasBasic:!0,has256:a>=2,has16m:a>=3}}s("no-color")||s("no-colors")||s("color=false")?n=!1:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(n=!0),"FORCE_COLOR"in t&&(n=0===t.FORCE_COLOR.length||0!==parseInt(t.FORCE_COLOR,10)),e.exports={supportsColor:r,stdout:r(process.stdout),stderr:r(process.stderr)}},12089:(e,a,i)=>{var n,o,s,t,r,c,p,l;let u=i(8951),d=i(57310),m=i(33027),x=i(13685),f=i(95687),h=i(73837),v=i(19017),b=i(59796),g=i(12781),y=i(82361);function w(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let k=w(u),j=w(d),_=w(x),R=w(f),E=w(h),S=w(v),C=w(b),O=w(g);function T(e,a){return function(){return e.apply(a,arguments)}}let{toString:L}=Object.prototype,{getPrototypeOf:z}=Object,A=(n=Object.create(null),e=>{let a=L.call(e);return n[a]||(n[a]=a.slice(8,-1).toLowerCase())}),P=e=>(e=e.toLowerCase(),a=>A(a)===e),q=e=>a=>typeof a===e,{isArray:F}=Array,N=q("undefined"),U=P("ArrayBuffer"),B=q("string"),D=q("function"),I=q("number"),M=e=>null!==e&&"object"==typeof e,H=e=>{if("object"!==A(e))return!1;let a=z(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},$=P("Date"),W=P("File"),G=P("Blob"),V=P("FileList"),J=P("URLSearchParams"),[K,Y,X,Z]=["ReadableStream","Request","Response","Headers"].map(P);function Q(e,a,{allOwnKeys:i=!1}={}){let n,o;if(null!=e){if("object"!=typeof e&&(e=[e]),F(e))for(n=0,o=e.length;n<o;n++)a.call(null,e[n],n,e);else{let o;let s=i?Object.getOwnPropertyNames(e):Object.keys(e),t=s.length;for(n=0;n<t;n++)o=s[n],a.call(null,e[o],o,e)}}}function ee(e,a){let i;a=a.toLowerCase();let n=Object.keys(e),o=n.length;for(;o-- >0;)if(a===(i=n[o]).toLowerCase())return i;return null}let ea="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:global,ei=e=>!N(e)&&e!==ea,en=(o="undefined"!=typeof Uint8Array&&z(Uint8Array),e=>o&&e instanceof o),eo=P("HTMLFormElement"),es=(({hasOwnProperty:e})=>(a,i)=>e.call(a,i))(Object.prototype),et=P("RegExp"),er=(e,a)=>{let i=Object.getOwnPropertyDescriptors(e),n={};Q(i,(i,o)=>{let s;!1!==(s=a(i,o,e))&&(n[o]=s||i)}),Object.defineProperties(e,n)},ec="abcdefghijklmnopqrstuvwxyz",ep="0123456789",el={DIGIT:ep,ALPHA:ec,ALPHA_DIGIT:ec+ec.toUpperCase()+ep},eu=P("AsyncFunction"),ed=(s="function"==typeof setImmediate,t=D(ea.postMessage),s?setImmediate:t?(p=`axios@${Math.random()}`,l=[],ea.addEventListener("message",({source:e,data:a})=>{e===ea&&a===p&&l.length&&l.shift()()},!1),e=>{l.push(e),ea.postMessage(p,"*")}):e=>setTimeout(e)),em="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ea):"undefined"!=typeof process&&process.nextTick||ed,ex={isArray:F,isArrayBuffer:U,isBuffer:function(e){return null!==e&&!N(e)&&null!==e.constructor&&!N(e.constructor)&&D(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||D(e.append)&&("formdata"===(a=A(e))||"object"===a&&D(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&U(e.buffer)},isString:B,isNumber:I,isBoolean:e=>!0===e||!1===e,isObject:M,isPlainObject:H,isReadableStream:K,isRequest:Y,isResponse:X,isHeaders:Z,isUndefined:N,isDate:$,isFile:W,isBlob:G,isRegExp:et,isFunction:D,isStream:e=>M(e)&&D(e.pipe),isURLSearchParams:J,isTypedArray:en,isFileList:V,forEach:Q,merge:function e(){let{caseless:a}=ei(this)&&this||{},i={},n=(n,o)=>{let s=a&&ee(i,o)||o;H(i[s])&&H(n)?i[s]=e(i[s],n):H(n)?i[s]=e({},n):F(n)?i[s]=n.slice():i[s]=n};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&Q(arguments[e],n);return i},extend:(e,a,i,{allOwnKeys:n}={})=>(Q(a,(a,n)=>{i&&D(a)?e[n]=T(a,i):e[n]=a},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,i,n)=>{e.prototype=Object.create(a.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),i&&Object.assign(e.prototype,i)},toFlatObject:(e,a,i,n)=>{let o,s,t;let r={};if(a=a||{},null==e)return a;do{for(s=(o=Object.getOwnPropertyNames(e)).length;s-- >0;)t=o[s],(!n||n(t,e,a))&&!r[t]&&(a[t]=e[t],r[t]=!0);e=!1!==i&&z(e)}while(e&&(!i||i(e,a))&&e!==Object.prototype);return a},kindOf:A,kindOfTest:P,endsWith:(e,a,i)=>{e=String(e),(void 0===i||i>e.length)&&(i=e.length),i-=a.length;let n=e.indexOf(a,i);return -1!==n&&n===i},toArray:e=>{if(!e)return null;if(F(e))return e;let a=e.length;if(!I(a))return null;let i=Array(a);for(;a-- >0;)i[a]=e[a];return i},forEachEntry:(e,a)=>{let i;let n=(e&&e[Symbol.iterator]).call(e);for(;(i=n.next())&&!i.done;){let n=i.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let i;let n=[];for(;null!==(i=e.exec(a));)n.push(i);return n},isHTMLForm:eo,hasOwnProperty:es,hasOwnProp:es,reduceDescriptors:er,freezeMethods:e=>{er(e,(a,i)=>{if(D(e)&&-1!==["arguments","caller","callee"].indexOf(i))return!1;if(D(e[i])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+i+"'")})}})},toObjectSet:(e,a)=>{let i={};return(e=>{e.forEach(e=>{i[e]=!0})})(F(e)?e:String(e).split(a)),i},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,i){return a.toUpperCase()+i}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:ee,global:ea,isContextDefined:ei,ALPHABET:el,generateString:(e=16,a=el.ALPHA_DIGIT)=>{let i="",{length:n}=a;for(;e--;)i+=a[Math.random()*n|0];return i},isSpecCompliantForm:function(e){return!!(e&&D(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let a=Array(10),i=(e,n)=>{if(M(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[n]=e;let o=F(e)?[]:{};return Q(e,(e,a)=>{let s=i(e,n+1);N(s)||(o[a]=s)}),a[n]=void 0,o}}return e};return i(e,0)},isAsyncFn:eu,isThenable:e=>e&&(M(e)||D(e))&&D(e.then)&&D(e.catch),setImmediate:ed,asap:em};function ef(e,a,i,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),i&&(this.config=i),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}ex.inherits(ef,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ex.toJSONObject(this.config),code:this.code,status:this.status}}});let eh=ef.prototype,ev={};function eb(e){return ex.isPlainObject(e)||ex.isArray(e)}function eg(e){return ex.endsWith(e,"[]")?e.slice(0,-2):e}function ey(e,a,i){return e?e.concat(a).map(function(e,a){return e=eg(e),!i&&a?"["+e+"]":e}).join(i?".":""):a}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ev[e]={value:e}}),Object.defineProperties(ef,ev),Object.defineProperty(eh,"isAxiosError",{value:!0}),ef.from=(e,a,i,n,o,s)=>{let t=Object.create(eh);return ex.toFlatObject(e,t,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),ef.call(t,e.message,a,i,n,o),t.cause=e,t.name=e.name,s&&Object.assign(t,s),t};let ew=ex.toFlatObject(ex,{},null,function(e){return/^is[A-Z]/.test(e)});function ek(e,a,i){if(!ex.isObject(e))throw TypeError("target must be an object");a=a||new(k.default||FormData);let n=(i=ex.toFlatObject(i,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!ex.isUndefined(a[e])})).metaTokens,o=i.visitor||p,s=i.dots,t=i.indexes,r=(i.Blob||"undefined"!=typeof Blob&&Blob)&&ex.isSpecCompliantForm(a);if(!ex.isFunction(o))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ex.isDate(e))return e.toISOString();if(!r&&ex.isBlob(e))throw new ef("Blob is not supported. Use a Buffer instead.");return ex.isArrayBuffer(e)||ex.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,i,o){let r=e;if(e&&!o&&"object"==typeof e){if(ex.endsWith(i,"{}"))i=n?i:i.slice(0,-2),e=JSON.stringify(e);else{var p;if(ex.isArray(e)&&(p=e,ex.isArray(p)&&!p.some(eb))||(ex.isFileList(e)||ex.endsWith(i,"[]"))&&(r=ex.toArray(e)))return i=eg(i),r.forEach(function(e,n){ex.isUndefined(e)||null===e||a.append(!0===t?ey([i],n,s):null===t?i:i+"[]",c(e))}),!1}}return!!eb(e)||(a.append(ey(o,i,s),c(e)),!1)}let l=[],u=Object.assign(ew,{defaultVisitor:p,convertValue:c,isVisitable:eb});if(!ex.isObject(e))throw TypeError("data must be an object");return function e(i,n){if(!ex.isUndefined(i)){if(-1!==l.indexOf(i))throw Error("Circular reference detected in "+n.join("."));l.push(i),ex.forEach(i,function(i,s){!0===(!(ex.isUndefined(i)||null===i)&&o.call(a,i,ex.isString(s)?s.trim():s,n,u))&&e(i,n?n.concat(s):[s])}),l.pop()}}(e),a}function ej(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\x00"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function e_(e,a){this._pairs=[],e&&ek(e,this,a)}let eR=e_.prototype;function eE(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eS(e,a,i){let n;if(!a)return e;let o=i&&i.encode||eE,s=i&&i.serialize;if(n=s?s(a,i):ex.isURLSearchParams(a)?a.toString():new e_(a,i).toString(o)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}eR.append=function(e,a){this._pairs.push([e,a])},eR.toString=function(e){let a=e?function(a){return e.call(this,a,ej)}:ej;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class eC{constructor(){this.handlers=[]}use(e,a,i){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!i&&i.synchronous,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ex.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eO={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eT={isNode:!0,classes:{URLSearchParams:j.default.URLSearchParams,FormData:k.default,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]},eL="object"==typeof navigator&&navigator||void 0,ez={...Object.freeze({__proto__:null,hasBrowserEnv:!1,hasStandardBrowserWebWorkerEnv:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,hasStandardBrowserEnv:!1,navigator:eL,origin:"http://localhost"}),...eT};function eA(e){if(ex.isFormData(e)&&ex.isFunction(e.entries)){let a={};return ex.forEachEntry(e,(e,i)=>{!function e(a,i,n,o){let s=a[o++];if("__proto__"===s)return!0;let t=Number.isFinite(+s),r=o>=a.length;return(s=!s&&ex.isArray(n)?n.length:s,r)?ex.hasOwnProp(n,s)?n[s]=[n[s],i]:n[s]=i:(n[s]&&ex.isObject(n[s])||(n[s]=[]),e(a,i,n[s],o)&&ex.isArray(n[s])&&(n[s]=function(e){let a,i;let n={},o=Object.keys(e),s=o.length;for(a=0;a<s;a++)n[i=o[a]]=e[i];return n}(n[s]))),!t}(ex.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),i,a,0)}),a}return null}let eP={transitional:eO,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let i;let n=a.getContentType()||"",o=n.indexOf("application/json")>-1,s=ex.isObject(e);if(s&&ex.isHTMLForm(e)&&(e=new FormData(e)),ex.isFormData(e))return o?JSON.stringify(eA(e)):e;if(ex.isArrayBuffer(e)||ex.isBuffer(e)||ex.isStream(e)||ex.isFile(e)||ex.isBlob(e)||ex.isReadableStream(e))return e;if(ex.isArrayBufferView(e))return e.buffer;if(ex.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1){var t,r;return(t=e,r=this.formSerializer,ek(t,new ez.classes.URLSearchParams,Object.assign({visitor:function(e,a,i,n){return ez.isNode&&ex.isBuffer(e)?(this.append(a,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},r))).toString()}if((i=ex.isFileList(e))||n.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return ek(i?{"files[]":e}:e,a&&new a,this.formSerializer)}}return s||o?(a.setContentType("application/json",!1),function(e,a,i){if(ex.isString(e))try{return(0,JSON.parse)(e),ex.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eP.transitional,i=a&&a.forcedJSONParsing,n="json"===this.responseType;if(ex.isResponse(e)||ex.isReadableStream(e))return e;if(e&&ex.isString(e)&&(i&&!this.responseType||n)){let i=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!i&&n){if("SyntaxError"===e.name)throw ef.from(e,ef.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ez.classes.FormData,Blob:ez.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ex.forEach(["delete","get","head","post","put","patch"],e=>{eP.headers[e]={}});let eq=ex.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eF=e=>{let a,i,n;let o={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),a=e.substring(0,n).trim().toLowerCase(),i=e.substring(n+1).trim(),!a||o[a]&&eq[a]||("set-cookie"===a?o[a]?o[a].push(i):o[a]=[i]:o[a]=o[a]?o[a]+", "+i:i)}),o},eN=Symbol("internals");function eU(e){return e&&String(e).trim().toLowerCase()}function eB(e){return!1===e||null==e?e:ex.isArray(e)?e.map(eB):String(e)}let eD=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eI(e,a,i,n,o){if(ex.isFunction(n))return n.call(this,a,i);if(o&&(a=i),ex.isString(a)){if(ex.isString(n))return -1!==a.indexOf(n);if(ex.isRegExp(n))return n.test(a)}}class eM{constructor(e){e&&this.set(e)}set(e,a,i){let n=this;function o(e,a,i){let o=eU(a);if(!o)throw Error("header name must be a non-empty string");let s=ex.findKey(n,o);s&&void 0!==n[s]&&!0!==i&&(void 0!==i||!1===n[s])||(n[s||a]=eB(e))}let s=(e,a)=>ex.forEach(e,(e,i)=>o(e,i,a));if(ex.isPlainObject(e)||e instanceof this.constructor)s(e,a);else if(ex.isString(e)&&(e=e.trim())&&!eD(e))s(eF(e),a);else if(ex.isHeaders(e))for(let[a,n]of e.entries())o(n,a,i);else null!=e&&o(a,e,i);return this}get(e,a){if(e=eU(e)){let i=ex.findKey(this,e);if(i){let e=this[i];if(!a)return e;if(!0===a)return function(e){let a;let i=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=n.exec(e);)i[a[1]]=a[2];return i}(e);if(ex.isFunction(a))return a.call(this,e,i);if(ex.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eU(e)){let i=ex.findKey(this,e);return!!(i&&void 0!==this[i]&&(!a||eI(this,this[i],i,a)))}return!1}delete(e,a){let i=this,n=!1;function o(e){if(e=eU(e)){let o=ex.findKey(i,e);o&&(!a||eI(i,i[o],o,a))&&(delete i[o],n=!0)}}return ex.isArray(e)?e.forEach(o):o(e),n}clear(e){let a=Object.keys(this),i=a.length,n=!1;for(;i--;){let o=a[i];(!e||eI(this,this[o],o,e,!0))&&(delete this[o],n=!0)}return n}normalize(e){let a=this,i={};return ex.forEach(this,(n,o)=>{let s=ex.findKey(i,o);if(s){a[s]=eB(n),delete a[o];return}let t=e?o.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,i)=>a.toUpperCase()+i):String(o).trim();t!==o&&delete a[o],a[t]=eB(n),i[t]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return ex.forEach(this,(i,n)=>{null!=i&&!1!==i&&(a[n]=e&&ex.isArray(i)?i.join(", "):i)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let i=new this(e);return a.forEach(e=>i.set(e)),i}static accessor(e){let a=(this[eN]=this[eN]={accessors:{}}).accessors,i=this.prototype;function n(e){let n=eU(e);a[n]||(function(e,a){let i=ex.toCamelCase(" "+a);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+i,{value:function(e,i,o){return this[n].call(this,a,e,i,o)},configurable:!0})})}(i,e),a[n]=!0)}return ex.isArray(e)?e.forEach(n):n(e),this}}function eH(e,a){let i=this||eP,n=a||i,o=eM.from(n.headers),s=n.data;return ex.forEach(e,function(e){s=e.call(i,s,o.normalize(),a?a.status:void 0)}),o.normalize(),s}function e$(e){return!!(e&&e.__CANCEL__)}function eW(e,a,i){ef.call(this,null==e?"canceled":e,ef.ERR_CANCELED,a,i),this.name="CanceledError"}function eG(e,a,i){let n=i.config.validateStatus;!i.status||!n||n(i.status)?e(i):a(new ef("Request failed with status code "+i.status,[ef.ERR_BAD_REQUEST,ef.ERR_BAD_RESPONSE][Math.floor(i.status/100)-4],i.config,i.request,i))}function eV(e,a){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}eM.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ex.reduceDescriptors(eM.prototype,({value:e},a)=>{let i=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[i]=e}}}),ex.freezeMethods(eM),ex.inherits(eW,ef,{__CANCEL__:!0});let eJ="1.7.7";function eK(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eY=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/,eX=Symbol("internals");class eZ extends O.default.Transform{constructor(e){super({readableHighWaterMark:(e=ex.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!ex.isUndefined(a[e]))).chunkSize});let a=this[eX]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[eX];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,i){let n=this[eX],o=n.maxRate,s=this.readableHighWaterMark,t=n.timeWindow,r=o/(1e3/t),c=!1!==n.minChunkSize?Math.max(n.minChunkSize,.01*r):0,p=(e,a)=>{let i=Buffer.byteLength(e);n.bytesSeen+=i,n.bytes+=i,n.isCaptured&&this.emit("progress",n.bytesSeen),this.push(e)?process.nextTick(a):n.onReadCallback=()=>{n.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let i;let l=Buffer.byteLength(e),u=null,d=s,m=0;if(o){let e=Date.now();(!n.ts||(m=e-n.ts)>=t)&&(n.ts=e,i=r-n.bytes,n.bytes=i<0?-i:0,m=0),i=r-n.bytes}if(o){if(i<=0)return setTimeout(()=>{a(null,e)},t-m);i<d&&(d=i)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,n){if(a)return i(a);n?l(n,e):i(null)})}}let{asyncIterator:eQ}=Symbol,e0=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eQ]?yield*e[eQ]():yield e},e1=ex.ALPHABET.ALPHA_DIGIT+"-_",e3=new h.TextEncoder,e2=e3.encode("\r\n");class e6{constructor(e,a){let{escapeName:i}=this.constructor,n=ex.isString(a),o=`Content-Disposition: form-data; name="${i(e)}"${!n&&a.name?`; filename="${i(a.name)}"`:""}\r
`;n?a=e3.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):o+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=e3.encode(o+"\r\n"),this.contentLength=n?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;ex.isTypedArray(e)?yield e:yield*e0(e),yield e2}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e4=(e,a,i)=>{let{tag:n="form-data-boundary",size:o=25,boundary:s=n+"-"+ex.generateString(o,e1)}=i||{};if(!ex.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");let t=e3.encode("--"+s+"\r\n"),r=e3.encode("--"+s+"--\r\n\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let i=new e6(e,a);return c+=i.size,i});c+=t.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(c=ex.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),g.Readable.from(async function*(){for(let e of p)yield t,yield*e.encode();yield r}())};class e8 extends O.default.Transform{__transform(e,a,i){this.push(e),i()}_transform(e,a,i){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,i)}}let e7=(e,a)=>ex.isAsyncFn(e)?function(...i){let n=i.pop();e.apply(this,i).then(e=>{try{a?n(null,...a(e)):n(null,e)}catch(e){n(e)}},n)}:e,e5=(e,a,i=3)=>{let n=0,o=function(e,a){let i;let n=Array(e=e||10),o=Array(e),s=0,t=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=o[t];i||(i=c),n[s]=r,o[s]=c;let l=t,u=0;for(;l!==s;)u+=n[l++],l%=e;if((s=(s+1)%e)===t&&(t=(t+1)%e),c-i<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}}(50,250);return function(e,a){let i,n,o=0,s=1e3/a,t=(a,s=Date.now())=>{o=s,i=null,n&&(clearTimeout(n),n=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-o;r>=s?t(e,a):(i=e,n||(n=setTimeout(()=>{n=null,t(i)},s-r)))},()=>i&&t(i)]}(i=>{let s=i.loaded,t=i.lengthComputable?i.total:void 0,r=s-n,c=o(r);n=s,e({loaded:s,total:t,progress:t?s/t:void 0,bytes:r,rate:c||void 0,estimated:c&&t&&s<=t?(t-s)/c:void 0,event:i,lengthComputable:null!=t,[a?"download":"upload"]:!0})},i)},e9=(e,a)=>{let i=null!=e;return[n=>a[0]({lengthComputable:i,total:e,loaded:n}),a[1]]},ae=e=>(...a)=>ex.asap(()=>e(...a)),aa={flush:C.default.constants.Z_SYNC_FLUSH,finishFlush:C.default.constants.Z_SYNC_FLUSH},ai={flush:C.default.constants.BROTLI_OPERATION_FLUSH,finishFlush:C.default.constants.BROTLI_OPERATION_FLUSH},an=ex.isFunction(C.default.createBrotliDecompress),{http:ao,https:as}=S.default,at=/https:?/,ar=ez.protocols.map(e=>e+":"),ac=(e,[a,i])=>(e.on("end",i).on("error",i),a);function ap(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let al="undefined"!=typeof process&&"process"===ex.kindOf(process),au=e=>new Promise((a,i)=>{let n,o;let s=(e,a)=>{!o&&(o=!0,n&&n(e,a))},t=e=>{s(e,!0),i(e)};e(e=>{s(e),a(e)},t,e=>n=e).catch(t)}),ad=({address:e,family:a})=>{if(!ex.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},am=(e,a)=>ad(ex.isObject(e)?e:{address:e,family:a}),ax=al&&function(e){return au(async function(a,i,n){let o,s,t,r,c,p,l,{data:u,lookup:d,family:x}=e,{responseType:f,responseEncoding:h}=e,v=e.method.toUpperCase(),b=!1;if(d){let e=e7(d,e=>ex.isArray(e)?e:[e]);d=(a,i,n)=>{e(a,i,(e,a,o)=>{if(e)return n(e);let s=ex.isArray(a)?a.map(e=>am(e)):[am(a,o)];i.all?n(e,s):n(e,s[0].address,s[0].family)})}}let g=new y.EventEmitter,w=()=>{e.cancelToken&&e.cancelToken.unsubscribe(k),e.signal&&e.signal.removeEventListener("abort",k),g.removeAllListeners()};function k(a){g.emit("abort",!a||a.type?new eW(null,e,c):a)}n((e,a)=>{r=!0,a&&(b=!0,w())}),g.once("abort",i),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(k),e.signal&&(e.signal.aborted?k():e.signal.addEventListener("abort",k)));let j=eV(e.baseURL,e.url),S=new URL(j,ez.hasBrowserEnv?ez.origin:void 0),T=S.protocol||ar[0];if("data:"===T){let n;if("GET"!==v)return eG(a,i,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,a,i){let n=i&&i.Blob||ez.classes.Blob,o=eK(e);if(void 0===a&&n&&(a=!0),"data"===o){e=o.length?e.slice(o.length+1):e;let i=eY.exec(e);if(!i)throw new ef("Invalid URL",ef.ERR_INVALID_URL);let s=i[1],t=i[2],r=i[3],c=Buffer.from(decodeURIComponent(r),t?"base64":"utf8");if(a){if(!n)throw new ef("Blob is not supported",ef.ERR_NOT_SUPPORT);return new n([c],{type:s})}return c}throw new ef("Unsupported protocol "+o,ef.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw ef.from(a,ef.ERR_BAD_REQUEST,e)}return"text"===f?(n=n.toString(h),h&&"utf8"!==h||(n=ex.stripBOM(n))):"stream"===f&&(n=O.default.Readable.from(n)),eG(a,i,{data:n,status:200,statusText:"OK",headers:new eM,config:e})}if(-1===ar.indexOf(T))return i(new ef("Unsupported protocol "+T,ef.ERR_BAD_REQUEST,e));let L=eM.from(e.headers).normalize();L.set("User-Agent","axios/"+eJ,!1);let{onUploadProgress:z,onDownloadProgress:A}=e,P=e.maxRate;if(ex.isSpecCompliantForm(u)){let e=L.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e4(u,e=>{L.set(e)},{tag:`axios-${eJ}-boundary`,boundary:e&&e[1]||void 0})}else if(ex.isFormData(u)&&ex.isFunction(u.getHeaders)){if(L.set(u.getHeaders()),!L.hasContentLength())try{let e=await E.default.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&L.setContentLength(e)}catch(e){}}else if(ex.isBlob(u))u.size&&L.setContentType(u.type||"application/octet-stream"),L.setContentLength(u.size||0),u=O.default.Readable.from(e0(u));else if(u&&!ex.isStream(u)){if(Buffer.isBuffer(u));else if(ex.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!ex.isString(u))return i(new ef("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ef.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(L.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return i(new ef("Request body larger than maxBodyLength limit",ef.ERR_BAD_REQUEST,e))}let q=ex.toFiniteNumber(L.getContentLength());ex.isArray(P)?(o=P[0],s=P[1]):o=s=P,u&&(z||o)&&(ex.isStream(u)||(u=O.default.Readable.from(u,{objectMode:!1})),u=O.default.pipeline([u,new eZ({maxRate:ex.toFiniteNumber(o)})],ex.noop),z&&u.on("progress",ac(u,e9(q,e5(ae(z),!1,3))))),e.auth&&(t=(e.auth.username||"")+":"+(e.auth.password||"")),!t&&S.username&&(t=S.username+":"+S.password),t&&L.delete("authorization");try{p=eS(S.pathname+S.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let a=Error(n.message);return a.config=e,a.url=e.url,a.exists=!0,i(a)}L.set("Accept-Encoding","gzip, compress, deflate"+(an?", br":""),!1);let F={path:p,method:v,headers:L.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:t,protocol:T,family:x,beforeRedirect:ap,beforeRedirects:{}};ex.isUndefined(d)||(F.lookup=d),e.socketPath?F.socketPath=e.socketPath:(F.hostname=S.hostname.startsWith("[")?S.hostname.slice(1,-1):S.hostname,F.port=S.port,function e(a,i,n){let o=i;if(!o&&!1!==o){let e=m.getProxyForUrl(n);e&&(o=new URL(e))}if(o){if(o.username&&(o.auth=(o.username||"")+":"+(o.password||"")),o.auth){(o.auth.username||o.auth.password)&&(o.auth=(o.auth.username||"")+":"+(o.auth.password||""));let e=Buffer.from(o.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=o.hostname||o.host;a.hostname=e,a.host=e,a.port=o.port,a.path=n,o.protocol&&(a.protocol=o.protocol.includes(":")?o.protocol:`${o.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,i,a.href)}}(F,e.proxy,T+"//"+S.hostname+(S.port?":"+S.port:"")+F.path));let N=at.test(F.protocol);if(F.agent=N?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=N?R.default:_.default:(e.maxRedirects&&(F.maxRedirects=e.maxRedirects),e.beforeRedirect&&(F.beforeRedirects.config=e.beforeRedirect),l=N?as:ao),e.maxBodyLength>-1?F.maxBodyLength=e.maxBodyLength:F.maxBodyLength=1/0,e.insecureHTTPParser&&(F.insecureHTTPParser=e.insecureHTTPParser),c=l.request(F,function(n){if(c.destroyed)return;let o=[n],t=+n.headers["content-length"];if(A||s){let e=new eZ({maxRate:ex.toFiniteNumber(s)});A&&e.on("progress",ac(e,e9(t,e5(ae(A),!0,3)))),o.push(e)}let r=n,p=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===v||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":o.push(C.default.createUnzip(aa)),delete n.headers["content-encoding"];break;case"deflate":o.push(new e8),o.push(C.default.createUnzip(aa)),delete n.headers["content-encoding"];break;case"br":an&&(o.push(C.default.createBrotliDecompress(ai)),delete n.headers["content-encoding"])}r=o.length>1?O.default.pipeline(o,ex.noop):o[0];let l=O.default.finished(r,()=>{l(),w()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new eM(n.headers),config:e,request:p};if("stream"===f)u.data=r,eG(a,i,u);else{let n=[],o=0;r.on("data",function(a){n.push(a),o+=a.length,e.maxContentLength>-1&&o>e.maxContentLength&&(b=!0,r.destroy(),i(new ef("maxContentLength size of "+e.maxContentLength+" exceeded",ef.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(b)return;let a=new ef("maxContentLength size of "+e.maxContentLength+" exceeded",ef.ERR_BAD_RESPONSE,e,p);r.destroy(a),i(a)}),r.on("error",function(a){c.destroyed||i(ef.from(a,null,e,p))}),r.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"===f||(e=e.toString(h),h&&"utf8"!==h||(e=ex.stripBOM(e))),u.data=e}catch(a){return i(ef.from(a,null,e,u.request,u))}eG(a,i,u)})}g.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),g.once("abort",e=>{i(e),c.destroy(e)}),c.on("error",function(a){i(ef.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){i(new ef("error trying to parse `config.timeout` to int",ef.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||eO;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),i(new ef(a,n.clarifyTimeoutError?ef.ETIMEDOUT:ef.ECONNABORTED,e,c)),k()})}if(ex.isStream(u)){let a=!1,i=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{i=!0,c.destroy(e)}),u.on("close",()=>{a||i||k(new eW("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},af=ez.hasStandardBrowserEnv?function(){let e;let a=ez.navigator&&/(msie|trident)/i.test(ez.navigator.userAgent),i=document.createElement("a");function n(e){let n=e;return a&&(i.setAttribute("href",n),n=i.href),i.setAttribute("href",n),{href:i.href,protocol:i.protocol?i.protocol.replace(/:$/,""):"",host:i.host,search:i.search?i.search.replace(/^\?/,""):"",hash:i.hash?i.hash.replace(/^#/,""):"",hostname:i.hostname,port:i.port,pathname:"/"===i.pathname.charAt(0)?i.pathname:"/"+i.pathname}}return e=n(window.location.href),function(a){let i=ex.isString(a)?n(a):a;return i.protocol===e.protocol&&i.host===e.host}}():function(){return!0},ah=ez.hasStandardBrowserEnv?{write(e,a,i,n,o,s){let t=[e+"="+encodeURIComponent(a)];ex.isNumber(i)&&t.push("expires="+new Date(i).toGMTString()),ex.isString(n)&&t.push("path="+n),ex.isString(o)&&t.push("domain="+o),!0===s&&t.push("secure"),document.cookie=t.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},av=e=>e instanceof eM?{...e}:e;function ab(e,a){a=a||{};let i={};function n(e,a,i){return ex.isPlainObject(e)&&ex.isPlainObject(a)?ex.merge.call({caseless:i},e,a):ex.isPlainObject(a)?ex.merge({},a):ex.isArray(a)?a.slice():a}function o(e,a,i){return ex.isUndefined(a)?ex.isUndefined(e)?void 0:n(void 0,e,i):n(e,a,i)}function s(e,a){if(!ex.isUndefined(a))return n(void 0,a)}function t(e,a){return ex.isUndefined(a)?ex.isUndefined(e)?void 0:n(void 0,e):n(void 0,a)}function r(i,o,s){return s in a?n(i,o):s in e?n(void 0,i):void 0}let c={url:s,method:s,data:s,baseURL:t,transformRequest:t,transformResponse:t,paramsSerializer:t,timeout:t,timeoutMessage:t,withCredentials:t,withXSRFToken:t,adapter:t,responseType:t,xsrfCookieName:t,xsrfHeaderName:t,onUploadProgress:t,onDownloadProgress:t,decompress:t,maxContentLength:t,maxBodyLength:t,beforeRedirect:t,transport:t,httpAgent:t,httpsAgent:t,cancelToken:t,socketPath:t,responseEncoding:t,validateStatus:r,headers:(e,a)=>o(av(e),av(a),!0)};return ex.forEach(Object.keys(Object.assign({},e,a)),function(n){let s=c[n]||o,t=s(e[n],a[n],n);ex.isUndefined(t)&&s!==r||(i[n]=t)}),i}let ag=e=>{let a;let i=ab({},e),{data:n,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:t,headers:r,auth:c}=i;if(i.headers=r=eM.from(r),i.url=eS(eV(i.baseURL,i.url),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ex.isFormData(n)){if(ez.hasStandardBrowserEnv||ez.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...i]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...i].join("; "))}}if(ez.hasStandardBrowserEnv&&(o&&ex.isFunction(o)&&(o=o(i)),o||!1!==o&&af(i.url))){let e=s&&t&&ah.read(t);e&&r.set(s,e)}return i},ay="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,i){let n,o,s,t,r;let c=ag(e),p=c.data,l=eM.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function x(){t&&t(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(n),c.signal&&c.signal.removeEventListener("abort",n)}let f=new XMLHttpRequest;function h(){if(!f)return;let n=eM.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders());eG(function(e){a(e),x()},function(e){i(e),x()},{data:u&&"text"!==u&&"json"!==u?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:n,config:e,request:f}),f=null}f.open(c.method.toUpperCase(),c.url,!0),f.timeout=c.timeout,"onloadend"in f?f.onloadend=h:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(h)},f.onabort=function(){f&&(i(new ef("Request aborted",ef.ECONNABORTED,e,f)),f=null)},f.onerror=function(){i(new ef("Network Error",ef.ERR_NETWORK,e,f)),f=null},f.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",n=c.transitional||eO;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),i(new ef(a,n.clarifyTimeoutError?ef.ETIMEDOUT:ef.ECONNABORTED,e,f)),f=null},void 0===p&&l.setContentType(null),"setRequestHeader"in f&&ex.forEach(l.toJSON(),function(e,a){f.setRequestHeader(a,e)}),ex.isUndefined(c.withCredentials)||(f.withCredentials=!!c.withCredentials),u&&"json"!==u&&(f.responseType=c.responseType),m&&([s,r]=e5(m,!0),f.addEventListener("progress",s)),d&&f.upload&&([o,t]=e5(d),f.upload.addEventListener("progress",o),f.upload.addEventListener("loadend",t)),(c.cancelToken||c.signal)&&(n=a=>{f&&(i(!a||a.type?new eW(null,e,f):a),f.abort(),f=null)},c.cancelToken&&c.cancelToken.subscribe(n),c.signal&&(c.signal.aborted?n():c.signal.addEventListener("abort",n)));let v=eK(c.url);if(v&&-1===ez.protocols.indexOf(v)){i(new ef("Unsupported protocol "+v+":",ef.ERR_BAD_REQUEST,e));return}f.send(p||null)})},aw=(e,a)=>{let{length:i}=e=e?e.filter(Boolean):[];if(a||i){let i,n=new AbortController,o=function(e){if(!i){i=!0,t();let a=e instanceof Error?e:this.reason;n.abort(a instanceof ef?a:new eW(a instanceof Error?a.message:a))}},s=a&&setTimeout(()=>{s=null,o(new ef(`timeout ${a} of ms exceeded`,ef.ETIMEDOUT))},a),t=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));let{signal:r}=n;return r.unsubscribe=()=>ex.asap(t),r}},ak=function*(e,a){let i,n=e.byteLength;if(!a||n<a){yield e;return}let o=0;for(;o<n;)i=o+a,yield e.slice(o,i),o=i},aj=async function*(e,a){for await(let i of a_(e))yield*ak(i,a)},a_=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:i}=await a.read();if(e)break;yield i}}finally{await a.cancel()}},aR=(e,a,i,n)=>{let o;let s=aj(e,a),t=0,r=e=>{!o&&(o=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:a,value:n}=await s.next();if(a){r(),e.close();return}let o=n.byteLength;if(i){let e=t+=o;i(e)}e.enqueue(new Uint8Array(n))}catch(e){throw r(e),e}},cancel:e=>(r(e),s.return())},{highWaterMark:2})},aE="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aS=aE&&"function"==typeof ReadableStream,aC=aE&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aO=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},aT=aS&&aO(()=>{let e=!1,a=new Request(ez.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aL=aS&&aO(()=>ex.isReadableStream(new Response("").body)),az={stream:aL&&(e=>e.body)};aE&&(c=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{az[e]||(az[e]=ex.isFunction(c[e])?a=>a[e]():(a,i)=>{throw new ef(`Response type '${e}' is not supported`,ef.ERR_NOT_SUPPORT,i)})}));let aA=async e=>{if(null==e)return 0;if(ex.isBlob(e))return e.size;if(ex.isSpecCompliantForm(e)){let a=new Request(ez.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return ex.isArrayBufferView(e)||ex.isArrayBuffer(e)?e.byteLength:(ex.isURLSearchParams(e)&&(e+=""),ex.isString(e))?(await aC(e)).byteLength:void 0},aP=async(e,a)=>{let i=ex.toFiniteNumber(e.getContentLength());return null==i?aA(a):i},aq={http:ax,xhr:ay,fetch:aE&&(async e=>{let a,i,{url:n,method:o,data:s,signal:t,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:x}=ag(e);u=u?(u+"").toLowerCase():"text";let f=aw([t,r&&r.toAbortSignal()],c),h=f&&f.unsubscribe&&(()=>{f.unsubscribe()});try{if(l&&aT&&"get"!==o&&"head"!==o&&0!==(i=await aP(d,s))){let e,a=new Request(n,{method:"POST",body:s,duplex:"half"});if(ex.isFormData(s)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,n]=e9(i,e5(ae(l)));s=aR(a.body,65536,e,n)}}ex.isString(m)||(m=m?"include":"omit");let t="credentials"in Request.prototype;a=new Request(n,{...x,signal:f,method:o.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:t?m:void 0});let r=await fetch(a),c=aL&&("stream"===u||"response"===u);if(aL&&(p||c&&h)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=ex.toFiniteNumber(r.headers.get("content-length")),[i,n]=p&&e9(a,e5(ae(p),!0))||[];r=new Response(aR(r.body,65536,i,()=>{n&&n(),h&&h()}),e)}u=u||"text";let v=await az[ex.findKey(az,u)||"text"](r,e);return!c&&h&&h(),await new Promise((i,n)=>{eG(i,n,{data:v,headers:eM.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(i){if(h&&h(),i&&"TypeError"===i.name&&/fetch/i.test(i.message))throw Object.assign(new ef("Network Error",ef.ERR_NETWORK,e,a),{cause:i.cause||i});throw ef.from(i,i&&i.code,e,a)}})};ex.forEach(aq,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aF=e=>`- ${e}`,aN=e=>ex.isFunction(e)||null===e||!1===e,aU={getAdapter:e=>{let a,i;let{length:n}=e=ex.isArray(e)?e:[e],o={};for(let s=0;s<n;s++){let n;if(i=a=e[s],!aN(a)&&void 0===(i=aq[(n=String(a)).toLowerCase()]))throw new ef(`Unknown adapter '${n}'`);if(i)break;o[n||"#"+s]=i}if(!i){let e=Object.entries(o).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new ef("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(aF).join("\n"):" "+aF(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i},adapters:aq};function aB(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eW(null,e)}function aD(e){return aB(e),e.headers=eM.from(e.headers),e.data=eH.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aU.getAdapter(e.adapter||eP.adapter)(e).then(function(a){return aB(e),a.data=eH.call(e,e.transformResponse,a),a.headers=eM.from(a.headers),a},function(a){return!e$(a)&&(aB(e),a&&a.response&&(a.response.data=eH.call(e,e.transformResponse,a.response),a.response.headers=eM.from(a.response.headers))),Promise.reject(a)})}let aI={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aI[e]=function(i){return typeof i===e||"a"+(a<1?"n ":" ")+e}});let aM={};aI.transitional=function(e,a,i){function n(e,a){return"[Axios v"+eJ+"] Transitional option '"+e+"'"+a+(i?". "+i:"")}return(i,o,s)=>{if(!1===e)throw new ef(n(o," has been removed"+(a?" in "+a:"")),ef.ERR_DEPRECATED);return a&&!aM[o]&&(aM[o]=!0,console.warn(n(o," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(i,o,s)}};let aH={assertOptions:function(e,a,i){if("object"!=typeof e)throw new ef("options must be an object",ef.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),o=n.length;for(;o-- >0;){let s=n[o],t=a[s];if(t){let a=e[s],i=void 0===a||t(a,s,e);if(!0!==i)throw new ef("option "+s+" must be "+i,ef.ERR_BAD_OPTION_VALUE);continue}if(!0!==i)throw new ef("Unknown option "+s,ef.ERR_BAD_OPTION)}},validators:aI},a$=aH.validators;class aW{constructor(e){this.defaults=e,this.interceptors={request:new eC,response:new eC}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a;Error.captureStackTrace?Error.captureStackTrace(a={}):a=Error();let i=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?i&&!String(e.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+i):e.stack=i}catch(e){}}throw e}}_request(e,a){let i,n;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:o,paramsSerializer:s,headers:t}=a=ab(this.defaults,a);void 0!==o&&aH.assertOptions(o,{silentJSONParsing:a$.transitional(a$.boolean),forcedJSONParsing:a$.transitional(a$.boolean),clarifyTimeoutError:a$.transitional(a$.boolean)},!1),null!=s&&(ex.isFunction(s)?a.paramsSerializer={serialize:s}:aH.assertOptions(s,{encode:a$.function,serialize:a$.function},!0)),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=t&&ex.merge(t.common,t[a.method]);t&&ex.forEach(["delete","get","head","post","put","patch","common"],e=>{delete t[e]}),a.headers=eM.concat(r,t);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[aD.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),n=e.length,i=Promise.resolve(a);u<n;)i=i.then(e[u++],e[u++]);return i}n=c.length;let d=a;for(u=0;u<n;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{i=aD.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=l.length;u<n;)i=i.then(l[u++],l[u++]);return i}getUri(e){return eS(eV((e=ab(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}ex.forEach(["delete","get","head","options"],function(e){aW.prototype[e]=function(a,i){return this.request(ab(i||{},{method:e,url:a,data:(i||{}).data}))}}),ex.forEach(["post","put","patch"],function(e){function a(a){return function(i,n,o){return this.request(ab(o||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:i,data:n}))}}aW.prototype[e]=a(),aW.prototype[e+"Form"]=a(!0)});class aG{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let i=this;this.promise.then(e=>{if(!i._listeners)return;let a=i._listeners.length;for(;a-- >0;)i._listeners[a](e);i._listeners=null}),this.promise.then=e=>{let a;let n=new Promise(e=>{i.subscribe(e),a=e}).then(e);return n.cancel=function(){i.unsubscribe(a)},n},e(function(e,n,o){i.reason||(i.reason=new eW(e,n,o),a(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aG(function(a){e=a}),cancel:e}}}let aV={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aV).forEach(([e,a])=>{aV[a]=e});let aJ=function e(a){let i=new aW(a),n=T(aW.prototype.request,i);return ex.extend(n,aW.prototype,i,{allOwnKeys:!0}),ex.extend(n,i,null,{allOwnKeys:!0}),n.create=function(i){return e(ab(a,i))},n}(eP);aJ.Axios=aW,aJ.CanceledError=eW,aJ.CancelToken=aG,aJ.isCancel=e$,aJ.VERSION=eJ,aJ.toFormData=ek,aJ.AxiosError=ef,aJ.Cancel=aJ.CanceledError,aJ.all=function(e){return Promise.all(e)},aJ.spread=function(e){return function(a){return e.apply(null,a)}},aJ.isAxiosError=function(e){return ex.isObject(e)&&!0===e.isAxiosError},aJ.mergeConfig=ab,aJ.AxiosHeaders=eM,aJ.formToJSON=e=>eA(ex.isHTMLForm(e)?new FormData(e):e),aJ.getAdapter=aU.getAdapter,aJ.HttpStatusCode=aV,aJ.default=aJ,e.exports=aJ},2753:e=>{e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};