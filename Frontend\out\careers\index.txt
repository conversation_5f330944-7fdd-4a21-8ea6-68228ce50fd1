3:I[5613,[],""]
4:I[31778,[],""]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["careers",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["careers",{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","careers","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/628cc127aecc0b5b.css","precedence":"next","crossOrigin":""}]]}]]},[null,"$L5",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L6"]]]]
7:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
8:"$Sreact.suspense"
9:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
f:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
5:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L7",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$8",null,{"fallback":null,"children":["$","$L9",null,{"reason":"next/dynamic","children":"$La"}]}],["$","$Lb",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Lc",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$8",null,{"fallback":null,"children":"$Ld"}],["$","div",null,{"id":"scroll_to_top","children":["$","$8",null,{"fallback":null,"children":"$Le"}]}],["$","$Lf",null,{}]]}]]}]
10:T564,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/careers/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/careers/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/careers/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/careers/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/careers/#webpage","url":"https://marutitech.com/careers/","inLanguage":"en-US","name":"Careers & Job Openings | Maruti Techlabs","isPartOf":{"@id":"https://marutitech.com/careers/#website"},"about":{"@id":"https://marutitech.com/careers/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/careers/#primaryimage","url":"https://cdn.marutitech.com//Product_Strategy_1_4047beff34.png","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/careers/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Automate your processes and accelerate your business growth with Maruti Techlabs’ scalable, secure, custom software product development solutions."}]}6:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Careers & Job Openings | Maruti Techlabs"}],["$","meta","3",{"name":"description","content":"Automate your processes and accelerate your business growth with Maruti Techlabs’ scalable, secure, custom software product development solutions."}],["$","meta","4",{"name":"keywords","content":"Job Openings"}],["$","meta","5",{"name":"application/ld+json","content":"$10"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/careers/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"Careers & Job Openings | Maruti Techlabs"}],["$","meta","9",{"property":"og:description","content":"Automate your processes and accelerate your business growth with Maruti Techlabs’ scalable, secure, custom software product development solutions."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/careers/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//Product_Strategy_1_4047beff34.png"}],["$","meta","14",{"property":"og:image:alt","content":"Careers & Job Openings | Maruti Techlabs"}],["$","meta","15",{"property":"og:type","content":"website"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"Careers & Job Openings | Maruti Techlabs"}],["$","meta","19",{"name":"twitter:description","content":"Automate your processes and accelerate your business growth with Maruti Techlabs’ scalable, secure, custom software product development solutions."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//Product_Strategy_1_4047beff34.png"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
11:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
12:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
13:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
a:["$","$L11",null,{}]
d:["$","$L12",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]
e:["$","$L13",null,{"variant":"scroll_to_top","scroll_to":true}]
14:I[70914,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
15:I[48184,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
16:I[41157,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
17:I[36957,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
18:I[18498,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
19:I[63989,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
1a:I[52231,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6995","static/chunks/app/careers/page-f571f3bdb8a31f6c.js"],""]
2:["$","$L14",null,{"children":[null,["$","div",null,{"id":"current-opportunities","children":["$","$L15",null,{"CareersHeroSectionData":{"id":4,"rich_text":"<h1 style=\"text-align:center;\"><strong>Work with experts who value your contributions.</strong></h1><p style=\"text-align:center;\"><br>Having a global clientele, our projects are across myriad industries, offering exciting opportunities for growth and development.<br>Our team of skilled professionals is committed to creating innovative tech solutions. If you share our enthusiasm for creativity and innovation, we invite you to explore our opportunities and join our mission to drive meaningful change.</p>","vimeoVideoLink":"https://www.youtube.com/embed/c0U96phDX_U","button":{"id":38,"title":"Explore Open Roles","link":"https://marutitech.keka.com/careers"}}}]}],["$","$L16",null,{"GPTWData":{"id":47,"title":"Acclaimed Accreditations -  ‘Great Place to Work’ Certified  ","description":"<p>Maruti Techlabs, a leading technology solutions provider, has been recognized as a 'Great Place to Work' for its exceptional workplace culture. We foster an employee-first approach, prioritizing collaboration and personal growth above all. With exciting projects, we offer the perfect environment for newcomers and experienced professionals to learn and grow.</p>","image":{"data":{"id":691,"attributes":{"name":"image 105.png","alternativeText":null,"caption":null,"width":230,"height":392,"formats":{"thumbnail":{"name":"thumbnail_image 105.png","hash":"thumbnail_image_105_d688840529","ext":".png","mime":"image/png","path":null,"width":92,"height":156,"size":16.06,"sizeInBytes":16061,"url":"https://cdn.marutitech.com//thumbnail_image_105_d688840529.png"}},"hash":"image_105_d688840529","ext":".png","mime":"image/png","size":15.31,"url":"https://cdn.marutitech.com//image_105_d688840529.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-01-02T09:42:34.562Z","updatedAt":"2025-01-02T09:42:34.562Z"}}}}}],["$","div",null,{"id":"core-values","children":["$","$L17",null,{"coreValuesData":{"id":1,"title":"Core Values","description":"<p>Our values accurately represent who we are. We aim to embody these values in everything we do, ensuring a brighter future for our company, customers, and community.</p>","box":[{"id":52,"title":"Customer First","description":"<p>We prioritize our customers’ time and needs and strive for excellence in serving them. We firmly believe that their success is our success.</p>","image":{"data":{"id":3088,"attributes":{"name":"Customer First.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Customer First.png","hash":"thumbnail_Customer_First_ff00b678e6","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":55.84,"sizeInBytes":55837,"url":"https://cdn.marutitech.com//thumbnail_Customer_First_ff00b678e6.png"}},"hash":"Customer_First_ff00b678e6","ext":".png","mime":"image/png","size":42.55,"url":"https://cdn.marutitech.com//Customer_First_ff00b678e6.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:52:26.648Z","updatedAt":"2025-02-18T05:52:26.648Z"}}}},{"id":53,"title":"Growth and Development","description":"<p>Our commitment to continuous development, staying competitive, and fostering a learning curve helps us meet customers' evolving needs.</p>","image":{"data":{"id":3089,"attributes":{"name":"Growth and Development.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Growth and Development.png","hash":"thumbnail_Growth_and_Development_4bfcb6f85d","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":58.54,"sizeInBytes":58535,"url":"https://cdn.marutitech.com//thumbnail_Growth_and_Development_4bfcb6f85d.png"}},"hash":"Growth_and_Development_4bfcb6f85d","ext":".png","mime":"image/png","size":41.4,"url":"https://cdn.marutitech.com//Growth_and_Development_4bfcb6f85d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:53:09.010Z","updatedAt":"2025-02-18T05:53:09.010Z"}}}},{"id":54,"title":"Service Excellence","description":"<p>We provide exceptional services by consistently delivering innovative solutions that create positive and memorable customer experiences.</p>","image":{"data":{"id":3090,"attributes":{"name":"Service Excellence.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Service Excellence.png","hash":"thumbnail_Service_Excellence_ceafd62bc7","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":55.11,"sizeInBytes":55110,"url":"https://cdn.marutitech.com//thumbnail_Service_Excellence_ceafd62bc7.png"}},"hash":"Service_Excellence_ceafd62bc7","ext":".png","mime":"image/png","size":42.36,"url":"https://cdn.marutitech.com//Service_Excellence_ceafd62bc7.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:53:31.402Z","updatedAt":"2025-02-18T05:53:31.402Z"}}}},{"id":55,"title":"Accountability & Ownership","description":"<p>We take ownership of our actions, take proactive steps in problem-solving, are open to feedback, and are willing to learn from mistakes.</p>","image":{"data":{"id":3091,"attributes":{"name":"Accountability & Ownership.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Accountability & Ownership.png","hash":"thumbnail_Accountability_and_Ownership_99420810fc","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":75.53,"sizeInBytes":75527,"url":"https://cdn.marutitech.com//thumbnail_Accountability_and_Ownership_99420810fc.png"}},"hash":"Accountability_and_Ownership_99420810fc","ext":".png","mime":"image/png","size":62.4,"url":"https://cdn.marutitech.com//Accountability_and_Ownership_99420810fc.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:53:53.623Z","updatedAt":"2025-02-18T05:53:53.623Z"}}}},{"id":56,"title":"Respect","description":"<p>We strive to create an environment of respect and treat all individuals with dignity, valuing their abilities and qualities regardless of their position or status.</p>","image":{"data":{"id":3092,"attributes":{"name":"Respect.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Respect.png","hash":"thumbnail_Respect_e451921db4","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":59.56,"sizeInBytes":59564,"url":"https://cdn.marutitech.com//thumbnail_Respect_e451921db4.png"}},"hash":"Respect_e451921db4","ext":".png","mime":"image/png","size":47.08,"url":"https://cdn.marutitech.com//Respect_e451921db4.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:54:18.586Z","updatedAt":"2025-02-18T05:54:18.586Z"}}}},{"id":94,"title":"Team Work","description":"<p>We collaborate &amp; cooperate to achieve a common goal, support &amp; help one another, and take responsibility for the team's success and mishaps.</p>","image":{"data":{"id":3093,"attributes":{"name":"Team Work.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Team Work.png","hash":"thumbnail_Team_Work_478dcfa047","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":69.54,"sizeInBytes":69543,"url":"https://cdn.marutitech.com//thumbnail_Team_Work_478dcfa047.png"}},"hash":"Team_Work_478dcfa047","ext":".png","mime":"image/png","size":63.32,"url":"https://cdn.marutitech.com//Team_Work_478dcfa047.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:54:38.759Z","updatedAt":"2025-02-18T05:54:38.759Z"}}}},{"id":95,"title":"Fun@Work","description":"<p>We offer the perfect work-life balance, where employees enjoy coming to work, feel a sense of camaraderie, celebrate successes, and create memorable moments.</p>","image":{"data":{"id":3094,"attributes":{"name":"Fun @ Work.png","alternativeText":null,"caption":null,"width":490,"height":300,"formats":{"thumbnail":{"name":"thumbnail_Fun @ Work.png","hash":"thumbnail_Fun_Work_8f01b2ebf7","ext":".png","mime":"image/png","path":null,"width":245,"height":150,"size":92.75,"sizeInBytes":92754,"url":"https://cdn.marutitech.com//thumbnail_Fun_Work_8f01b2ebf7.png"}},"hash":"Fun_Work_8f01b2ebf7","ext":".png","mime":"image/png","size":70.65,"url":"https://cdn.marutitech.com//Fun_Work_8f01b2ebf7.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T05:55:11.262Z","updatedAt":"2025-02-18T05:55:11.262Z"}}}}]}}]}],["$","div",null,{"id":"life-at-mtl","children":["$","$L18",null,{"LifeAtMtlData":{"id":1,"title":"Life @ MTL","description":"<p>At Maruti Techlabs, we blend work with fun, learning, and collaboration. From team events and workshops to everyday moments in our vibrant workspace, our culture is all about growing together and enjoying the journey.</p>","images":{"data":[{"id":3043,"attributes":{"name":"3.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_3.png","hash":"thumbnail_3_967c3d5c78","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":79.95,"sizeInBytes":79953,"url":"https://cdn.marutitech.com//thumbnail_3_967c3d5c78.png"}},"hash":"3_967c3d5c78","ext":".png","mime":"image/png","size":60.19,"url":"https://cdn.marutitech.com//3_967c3d5c78.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:31:59.128Z","updatedAt":"2025-05-05T08:51:27.599Z"}},{"id":3044,"attributes":{"name":"2.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_8fe474b57d","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":82.77,"sizeInBytes":82766,"url":"https://cdn.marutitech.com//thumbnail_2_8fe474b57d.png"}},"hash":"2_8fe474b57d","ext":".png","mime":"image/png","size":64.55,"url":"https://cdn.marutitech.com//2_8fe474b57d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:31:59.176Z","updatedAt":"2025-05-05T09:11:48.014Z"}},{"id":3045,"attributes":{"name":"4.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_4.png","hash":"thumbnail_4_2ed40ac5f1","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":95.17,"sizeInBytes":95174,"url":"https://cdn.marutitech.com//thumbnail_4_2ed40ac5f1.png"}},"hash":"4_2ed40ac5f1","ext":".png","mime":"image/png","size":74,"url":"https://cdn.marutitech.com//4_2ed40ac5f1.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:31:59.222Z","updatedAt":"2025-05-05T08:58:01.868Z"}},{"id":3047,"attributes":{"name":"5.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_5.png","hash":"thumbnail_5_c6f71899fc","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":96.31,"sizeInBytes":96313,"url":"https://cdn.marutitech.com//thumbnail_5_c6f71899fc.png"}},"hash":"5_c6f71899fc","ext":".png","mime":"image/png","size":67.42,"url":"https://cdn.marutitech.com//5_c6f71899fc.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:31:59.269Z","updatedAt":"2025-05-05T08:50:56.870Z"}},{"id":3046,"attributes":{"name":"1.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_1ef17d4b6f","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":93.08,"sizeInBytes":93080,"url":"https://cdn.marutitech.com//thumbnail_1_1ef17d4b6f.png"}},"hash":"1_1ef17d4b6f","ext":".png","mime":"image/png","size":65.22,"url":"https://cdn.marutitech.com//1_1ef17d4b6f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:31:59.250Z","updatedAt":"2025-05-05T09:10:49.583Z"}},{"id":3048,"attributes":{"name":"6.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_6.png","hash":"thumbnail_6_ba709e3788","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":92.93,"sizeInBytes":92927,"url":"https://cdn.marutitech.com//thumbnail_6_ba709e3788.png"}},"hash":"6_ba709e3788","ext":".png","mime":"image/png","size":70,"url":"https://cdn.marutitech.com//6_ba709e3788.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:00.198Z","updatedAt":"2025-05-05T08:52:18.167Z"}},{"id":3049,"attributes":{"name":"8.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_8.png","hash":"thumbnail_8_572ffee661","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":83.91,"sizeInBytes":83906,"url":"https://cdn.marutitech.com//thumbnail_8_572ffee661.png"}},"hash":"8_572ffee661","ext":".png","mime":"image/png","size":70.26,"url":"https://cdn.marutitech.com//8_572ffee661.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.058Z","updatedAt":"2025-05-05T08:47:30.368Z"}},{"id":3050,"attributes":{"name":"7.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_7.png","hash":"thumbnail_7_13edef8c51","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":91.33,"sizeInBytes":91333,"url":"https://cdn.marutitech.com//thumbnail_7_13edef8c51.png"}},"hash":"7_13edef8c51","ext":".png","mime":"image/png","size":57.25,"url":"https://cdn.marutitech.com//7_13edef8c51.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.229Z","updatedAt":"2025-05-05T08:51:08.265Z"}},{"id":3051,"attributes":{"name":"10.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_10.png","hash":"thumbnail_10_504185a788","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":82.34,"sizeInBytes":82335,"url":"https://cdn.marutitech.com//thumbnail_10_504185a788.png"}},"hash":"10_504185a788","ext":".png","mime":"image/png","size":56.52,"url":"https://cdn.marutitech.com//10_504185a788.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.332Z","updatedAt":"2025-05-05T08:48:57.907Z"}},{"id":3052,"attributes":{"name":"11.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_11.png","hash":"thumbnail_11_a01bd3dcd3","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":90.01,"sizeInBytes":90013,"url":"https://cdn.marutitech.com//thumbnail_11_a01bd3dcd3.png"}},"hash":"11_a01bd3dcd3","ext":".png","mime":"image/png","size":65.81,"url":"https://cdn.marutitech.com//11_a01bd3dcd3.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.748Z","updatedAt":"2025-05-05T08:59:09.057Z"}},{"id":3053,"attributes":{"name":"12.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_12.png","hash":"thumbnail_12_f392628366","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":90.65,"sizeInBytes":90650,"url":"https://cdn.marutitech.com//thumbnail_12_f392628366.png"}},"hash":"12_f392628366","ext":".png","mime":"image/png","size":63.03,"url":"https://cdn.marutitech.com//12_f392628366.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.781Z","updatedAt":"2025-05-05T08:51:48.415Z"}},{"id":3054,"attributes":{"name":"9.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_9.png","hash":"thumbnail_9_9ea9e2cb5d","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":95.65,"sizeInBytes":95647,"url":"https://cdn.marutitech.com//thumbnail_9_9ea9e2cb5d.png"}},"hash":"9_9ea9e2cb5d","ext":".png","mime":"image/png","size":77.61,"url":"https://cdn.marutitech.com//9_9ea9e2cb5d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:03.992Z","updatedAt":"2025-05-05T08:46:32.355Z"}},{"id":3055,"attributes":{"name":"13.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_13.png","hash":"thumbnail_13_5d16293249","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":95.3,"sizeInBytes":95302,"url":"https://cdn.marutitech.com//thumbnail_13_5d16293249.png"}},"hash":"13_5d16293249","ext":".png","mime":"image/png","size":73.6,"url":"https://cdn.marutitech.com//13_5d16293249.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:06.378Z","updatedAt":"2025-05-05T09:10:33.021Z"}},{"id":3056,"attributes":{"name":"15.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_15.png","hash":"thumbnail_15_3bfd5611b5","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":97.01,"sizeInBytes":97005,"url":"https://cdn.marutitech.com//thumbnail_15_3bfd5611b5.png"}},"hash":"15_3bfd5611b5","ext":".png","mime":"image/png","size":62.27,"url":"https://cdn.marutitech.com//15_3bfd5611b5.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:06.794Z","updatedAt":"2025-05-05T08:50:06.721Z"}},{"id":3057,"attributes":{"name":"14.png","alternativeText":"Life @ MTL","caption":null,"width":409,"height":273,"formats":{"thumbnail":{"name":"thumbnail_14.png","hash":"thumbnail_14_1e157034fe","ext":".png","mime":"image/png","path":null,"width":234,"height":156,"size":83.63,"sizeInBytes":83628,"url":"https://cdn.marutitech.com//thumbnail_14_1e157034fe.png"}},"hash":"14_1e157034fe","ext":".png","mime":"image/png","size":59.29,"url":"https://cdn.marutitech.com//14_1e157034fe.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:32:07.270Z","updatedAt":"2025-05-05T08:47:47.663Z"}}]}}}]}],["$","div",null,{"id":"benefits","children":["$","$L19",null,{"BenefitsData":{"id":1,"title":"Benefits","description":"<p>We offer a wide array of employee benefits &amp; development programs that reward and equip our team to grow as well as acquire new skills to deliver success.</p>","second_slider":[{"id":66,"title":"Employee Engagement","description":"<p>We prioritize enhancing employee relations, morale, and satisfaction through engagement and tokens of appreciation.</p>","image":{"data":{"id":3072,"attributes":{"name":"Employee Engagement.png","alternativeText":"Employee Engagement","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Employee Engagement.png","hash":"thumbnail_Employee_Engagement_a5ba09ab46","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":67.11,"sizeInBytes":67109,"url":"https://cdn.marutitech.com//thumbnail_Employee_Engagement_a5ba09ab46.png"}},"hash":"Employee_Engagement_a5ba09ab46","ext":".png","mime":"image/png","size":39.31,"url":"https://cdn.marutitech.com//Employee_Engagement_a5ba09ab46.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:07.767Z","updatedAt":"2025-05-05T08:56:45.073Z"}}}},{"id":64,"title":"Rewards & Recognition","description":"<p>Maruti Techlabs has introduced many rewards and recognition programs to appreciate employees' hard work and dedication.&nbsp;</p>","image":{"data":{"id":3073,"attributes":{"name":"Rewards and Recognition.png","alternativeText":"reward & recognition","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Rewards and Recognition.png","hash":"thumbnail_Rewards_and_Recognition_1db70ab0be","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":63.97,"sizeInBytes":63972,"url":"https://cdn.marutitech.com//thumbnail_Rewards_and_Recognition_1db70ab0be.png"}},"hash":"Rewards_and_Recognition_1db70ab0be","ext":".png","mime":"image/png","size":38.19,"url":"https://cdn.marutitech.com//Rewards_and_Recognition_1db70ab0be.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:26.059Z","updatedAt":"2025-04-16T11:47:40.573Z"}}}},{"id":65,"title":"Accidental Insurance","description":"<p>We offer employees accidental insurance coverage to focus on their work without worrying about the unexpected.</p>","image":{"data":{"id":3074,"attributes":{"name":"Accidental Insurance.png","alternativeText":"accidental insurance","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Accidental Insurance.png","hash":"thumbnail_Accidental_Insurance_6050f324aa","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":44.62,"sizeInBytes":44620,"url":"https://cdn.marutitech.com//thumbnail_Accidental_Insurance_6050f324aa.png"}},"hash":"Accidental_Insurance_6050f324aa","ext":".png","mime":"image/png","size":22,"url":"https://cdn.marutitech.com//Accidental_Insurance_6050f324aa.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:45.426Z","updatedAt":"2025-04-16T11:50:56.378Z"}}}},{"id":67,"title":"Long Service Reward","description":"<p>We celebrate loyalty and dedication to recognize and appreciate our employees’ commitment to the company over time.</p>","image":{"data":{"id":3075,"attributes":{"name":"Long Service Reward.png","alternativeText":"Long Service Reward","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Long Service Reward.png","hash":"thumbnail_Long_Service_Reward_ae22add1f8","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":53.55,"sizeInBytes":53550,"url":"https://cdn.marutitech.com//thumbnail_Long_Service_Reward_ae22add1f8.png"}},"hash":"Long_Service_Reward_ae22add1f8","ext":".png","mime":"image/png","size":30.91,"url":"https://cdn.marutitech.com//Long_Service_Reward_ae22add1f8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:03.385Z","updatedAt":"2025-05-05T08:53:29.979Z"}}}},{"id":68,"title":"Paternity Benefit","description":"<p>We understand your family's importance and offer paternity leave to support new fathers and their families.</p>","image":{"data":{"id":3076,"attributes":{"name":"Paternity Benefit.png","alternativeText":"Paternity Benefit","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Paternity Benefit.png","hash":"thumbnail_Paternity_Benefit_90b494c0ef","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":51.74,"sizeInBytes":51741,"url":"https://cdn.marutitech.com//thumbnail_Paternity_Benefit_90b494c0ef.png"}},"hash":"Paternity_Benefit_90b494c0ef","ext":".png","mime":"image/png","size":37.44,"url":"https://cdn.marutitech.com//Paternity_Benefit_90b494c0ef.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:26.833Z","updatedAt":"2025-05-05T08:57:11.049Z"}}}},{"id":69,"title":"Team Dinner/ Outing","description":"<p>To create a sense of camaraderie among our employees and promote a positive work culture with our annual team dinners.</p>","image":{"data":{"id":3077,"attributes":{"name":"Team Dinner_ Outing.png","alternativeText":"Team Dinner/ Outing","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Team Dinner_ Outing.png","hash":"thumbnail_Team_Dinner_Outing_9d62e392ca","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":65.23,"sizeInBytes":65229,"url":"https://cdn.marutitech.com//thumbnail_Team_Dinner_Outing_9d62e392ca.png"}},"hash":"Team_Dinner_Outing_9d62e392ca","ext":".png","mime":"image/png","size":45.84,"url":"https://cdn.marutitech.com//Team_Dinner_Outing_9d62e392ca.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:45.088Z","updatedAt":"2025-05-05T09:10:08.851Z"}}}},{"id":70,"title":"Dinner Sponsor","description":"<p>We provide dinner sponsorships for employees working late to show our appreciation for their dedication and commitment.</p>","image":{"data":{"id":3078,"attributes":{"name":"Dinner Sponsor.png","alternativeText":"Dinner Sponsor","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Dinner Sponsor.png","hash":"thumbnail_Dinner_Sponsor_1f70c2ff9a","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":50.56,"sizeInBytes":50561,"url":"https://cdn.marutitech.com//thumbnail_Dinner_Sponsor_1f70c2ff9a.png"}},"hash":"Dinner_Sponsor_1f70c2ff9a","ext":".png","mime":"image/png","size":30.31,"url":"https://cdn.marutitech.com//Dinner_Sponsor_1f70c2ff9a.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:39:06.978Z","updatedAt":"2025-05-05T08:52:50.355Z"}}}},{"id":96,"title":"Leave Encashment","description":"<p>Maruti Techabs offers greater flexibility and financial stability by converting unused paid time off into additional compensation.</p>","image":{"data":{"id":3079,"attributes":{"name":"Leave Encashment.png","alternativeText":"Leave Encashment","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Leave Encashment.png","hash":"thumbnail_Leave_Encashment_bc969a23d6","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":45.11,"sizeInBytes":45114,"url":"https://cdn.marutitech.com//thumbnail_Leave_Encashment_bc969a23d6.png"}},"hash":"Leave_Encashment_bc969a23d6","ext":".png","mime":"image/png","size":28.35,"url":"https://cdn.marutitech.com//Leave_Encashment_bc969a23d6.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:39:19.739Z","updatedAt":"2025-05-05T08:57:26.298Z"}}}},{"id":120,"title":"Employee Engagement","description":"<p>We prioritize enhancing employee relations, morale, and satisfaction through engagement and tokens of appreciation.</p>","image":{"data":{"id":3072,"attributes":{"name":"Employee Engagement.png","alternativeText":"Employee Engagement","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Employee Engagement.png","hash":"thumbnail_Employee_Engagement_a5ba09ab46","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":67.11,"sizeInBytes":67109,"url":"https://cdn.marutitech.com//thumbnail_Employee_Engagement_a5ba09ab46.png"}},"hash":"Employee_Engagement_a5ba09ab46","ext":".png","mime":"image/png","size":39.31,"url":"https://cdn.marutitech.com//Employee_Engagement_a5ba09ab46.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:07.767Z","updatedAt":"2025-05-05T08:56:45.073Z"}}}},{"id":121,"title":"Rewards & Recognition","description":"<p>Maruti Techlabs has introduced many rewards and recognition programs to appreciate employees' hard work and dedication.&nbsp;</p>","image":{"data":{"id":3073,"attributes":{"name":"Rewards and Recognition.png","alternativeText":"reward & recognition","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Rewards and Recognition.png","hash":"thumbnail_Rewards_and_Recognition_1db70ab0be","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":63.97,"sizeInBytes":63972,"url":"https://cdn.marutitech.com//thumbnail_Rewards_and_Recognition_1db70ab0be.png"}},"hash":"Rewards_and_Recognition_1db70ab0be","ext":".png","mime":"image/png","size":38.19,"url":"https://cdn.marutitech.com//Rewards_and_Recognition_1db70ab0be.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:26.059Z","updatedAt":"2025-04-16T11:47:40.573Z"}}}},{"id":122,"title":"Accidental Insurance","description":"<p>We offer employees accidental insurance coverage to focus on their work without worrying about the unexpected.</p>","image":{"data":{"id":3074,"attributes":{"name":"Accidental Insurance.png","alternativeText":"accidental insurance","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Accidental Insurance.png","hash":"thumbnail_Accidental_Insurance_6050f324aa","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":44.62,"sizeInBytes":44620,"url":"https://cdn.marutitech.com//thumbnail_Accidental_Insurance_6050f324aa.png"}},"hash":"Accidental_Insurance_6050f324aa","ext":".png","mime":"image/png","size":22,"url":"https://cdn.marutitech.com//Accidental_Insurance_6050f324aa.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:37:45.426Z","updatedAt":"2025-04-16T11:50:56.378Z"}}}},{"id":123,"title":"Long Service Reward","description":"<p>We celebrate loyalty and dedication to recognize and appreciate our employees’ commitment to the company over time.</p>","image":{"data":{"id":3075,"attributes":{"name":"Long Service Reward.png","alternativeText":"Long Service Reward","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Long Service Reward.png","hash":"thumbnail_Long_Service_Reward_ae22add1f8","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":53.55,"sizeInBytes":53550,"url":"https://cdn.marutitech.com//thumbnail_Long_Service_Reward_ae22add1f8.png"}},"hash":"Long_Service_Reward_ae22add1f8","ext":".png","mime":"image/png","size":30.91,"url":"https://cdn.marutitech.com//Long_Service_Reward_ae22add1f8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:03.385Z","updatedAt":"2025-05-05T08:53:29.979Z"}}}},{"id":124,"title":"Paternity Benefit","description":"<p>We understand your family's importance and offer paternity leave to support new fathers and their families.</p>","image":{"data":{"id":3076,"attributes":{"name":"Paternity Benefit.png","alternativeText":"Paternity Benefit","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Paternity Benefit.png","hash":"thumbnail_Paternity_Benefit_90b494c0ef","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":51.74,"sizeInBytes":51741,"url":"https://cdn.marutitech.com//thumbnail_Paternity_Benefit_90b494c0ef.png"}},"hash":"Paternity_Benefit_90b494c0ef","ext":".png","mime":"image/png","size":37.44,"url":"https://cdn.marutitech.com//Paternity_Benefit_90b494c0ef.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:26.833Z","updatedAt":"2025-05-05T08:57:11.049Z"}}}},{"id":125,"title":"Team Dinner/ Outing","description":"<p>To create a sense of camaraderie among our employees and promote a positive work culture with our annual team dinners.</p>","image":{"data":{"id":3077,"attributes":{"name":"Team Dinner_ Outing.png","alternativeText":"Team Dinner/ Outing","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Team Dinner_ Outing.png","hash":"thumbnail_Team_Dinner_Outing_9d62e392ca","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":65.23,"sizeInBytes":65229,"url":"https://cdn.marutitech.com//thumbnail_Team_Dinner_Outing_9d62e392ca.png"}},"hash":"Team_Dinner_Outing_9d62e392ca","ext":".png","mime":"image/png","size":45.84,"url":"https://cdn.marutitech.com//Team_Dinner_Outing_9d62e392ca.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:38:45.088Z","updatedAt":"2025-05-05T09:10:08.851Z"}}}},{"id":126,"title":"Dinner Sponsor","description":"<p>We provide dinner sponsorships for employees working late to show our appreciation for their dedication and commitment.</p>","image":{"data":{"id":3078,"attributes":{"name":"Dinner Sponsor.png","alternativeText":"Dinner Sponsor","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Dinner Sponsor.png","hash":"thumbnail_Dinner_Sponsor_1f70c2ff9a","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":50.56,"sizeInBytes":50561,"url":"https://cdn.marutitech.com//thumbnail_Dinner_Sponsor_1f70c2ff9a.png"}},"hash":"Dinner_Sponsor_1f70c2ff9a","ext":".png","mime":"image/png","size":30.31,"url":"https://cdn.marutitech.com//Dinner_Sponsor_1f70c2ff9a.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:39:06.978Z","updatedAt":"2025-05-05T08:52:50.355Z"}}}},{"id":127,"title":"Leave Encashment","description":"<p>Maruti Techabs offers greater flexibility and financial stability by converting unused paid time off into additional compensation.</p>","image":{"data":{"id":3079,"attributes":{"name":"Leave Encashment.png","alternativeText":"Leave Encashment","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Leave Encashment.png","hash":"thumbnail_Leave_Encashment_bc969a23d6","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":45.11,"sizeInBytes":45114,"url":"https://cdn.marutitech.com//thumbnail_Leave_Encashment_bc969a23d6.png"}},"hash":"Leave_Encashment_bc969a23d6","ext":".png","mime":"image/png","size":28.35,"url":"https://cdn.marutitech.com//Leave_Encashment_bc969a23d6.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:39:19.739Z","updatedAt":"2025-05-05T08:57:26.298Z"}}}}],"first_slider":[{"id":57,"title":"Comprehensive Health Insurance","description":"<p>We support employee well-being by managing unexpected medical expenses and prioritizing a culture of physical and psychological health.</p>","image":{"data":{"id":3065,"attributes":{"name":"Health Insurance.png","alternativeText":"Comprehensive Health Insurance","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Health Insurance.png","hash":"thumbnail_Health_Insurance_b9beae284b","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":55.02,"sizeInBytes":55020,"url":"https://cdn.marutitech.com//thumbnail_Health_Insurance_b9beae284b.png"}},"hash":"Health_Insurance_b9beae284b","ext":".png","mime":"image/png","size":30.72,"url":"https://cdn.marutitech.com//Health_Insurance_b9beae284b.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:34:45.242Z","updatedAt":"2025-05-05T08:56:07.263Z"}}}},{"id":58,"title":"Maternity Benefit","description":"<p>We offer female employees generous maternity leave so that they can bond with newborns, regain strength, and rejoin once they are fit and healthy.</p>","image":{"data":{"id":3066,"attributes":{"name":"Maternity Benefit.png","alternativeText":"Maternity Benefit","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Maternity Benefit.png","hash":"thumbnail_Maternity_Benefit_6569cc1c43","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":50.1,"sizeInBytes":50103,"url":"https://cdn.marutitech.com//thumbnail_Maternity_Benefit_6569cc1c43.png"}},"hash":"Maternity_Benefit_6569cc1c43","ext":".png","mime":"image/png","size":26.41,"url":"https://cdn.marutitech.com//Maternity_Benefit_6569cc1c43.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:05.233Z","updatedAt":"2025-05-05T08:49:48.091Z"}}}},{"id":59,"title":"PF, ESIC, Gratuity","description":"<p>We offer employees comprehensive financial security, savings, and long-term stability benefits.</p>","image":{"data":{"id":3067,"attributes":{"name":"PF, ESIC, Gratuity.png","alternativeText":"PF, ESIC, Gratuity","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_PF, ESIC, Gratuity.png","hash":"thumbnail_PF_ESIC_Gratuity_be7003ff72","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":46.06,"sizeInBytes":46057,"url":"https://cdn.marutitech.com//thumbnail_PF_ESIC_Gratuity_be7003ff72.png"}},"hash":"PF_ESIC_Gratuity_be7003ff72","ext":".png","mime":"image/png","size":26.65,"url":"https://cdn.marutitech.com//PF_ESIC_Gratuity_be7003ff72.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:22.392Z","updatedAt":"2025-05-05T09:11:12.613Z"}}}},{"id":60,"title":"Surprise Performance Bonus","description":"<p>We ensure organizational excellence with surprise bonuses to motivate and attract high-performing employees.</p>","image":{"data":{"id":3068,"attributes":{"name":"Performance Bonus.png","alternativeText":"Surprise Performance Bonus","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Performance Bonus.png","hash":"thumbnail_Performance_Bonus_b01a294c2f","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":53.22,"sizeInBytes":53215,"url":"https://cdn.marutitech.com//thumbnail_Performance_Bonus_b01a294c2f.png"}},"hash":"Performance_Bonus_b01a294c2f","ext":".png","mime":"image/png","size":35.05,"url":"https://cdn.marutitech.com//Performance_Bonus_b01a294c2f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:47.275Z","updatedAt":"2025-05-05T08:54:14.355Z"}}}},{"id":61,"title":"Flexible Work Hours","description":"<p>Maruti Techlabs achieves work-life balance through time flexibility, remote work support, and a positive culture for job satisfaction.</p>","image":{"data":{"id":3069,"attributes":{"name":"Flexible Work Hours.png","alternativeText":"Flexible Work Hours","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Flexible Work Hours.png","hash":"thumbnail_Flexible_Work_Hours_f37b3367bb","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":47.4,"sizeInBytes":47398,"url":"https://cdn.marutitech.com//thumbnail_Flexible_Work_Hours_f37b3367bb.png"}},"hash":"Flexible_Work_Hours_f37b3367bb","ext":".png","mime":"image/png","size":26.11,"url":"https://cdn.marutitech.com//Flexible_Work_Hours_f37b3367bb.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:05.357Z","updatedAt":"2025-05-05T08:48:32.011Z"}}}},{"id":62,"title":"5 Working Days","description":"<p>Our firm belief is balancing work and personal life with a 5-day workweek policy while maintaining productivity and efficiency.</p>","image":{"data":{"id":3070,"attributes":{"name":"5 Working Days.png","alternativeText":"5 Day working ","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_5 Working Days.png","hash":"thumbnail_5_Working_Days_d6ba467d5d","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":38.35,"sizeInBytes":38348,"url":"https://cdn.marutitech.com//thumbnail_5_Working_Days_d6ba467d5d.png"}},"hash":"5_Working_Days_d6ba467d5d","ext":".png","mime":"image/png","size":23.56,"url":"https://cdn.marutitech.com//5_Working_Days_d6ba467d5d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:24.346Z","updatedAt":"2025-05-05T08:47:15.753Z"}}}},{"id":63,"title":"Learning & Development","description":"<p>We encourage attending training programs on industry trends and technologies to improve skills and advance careers.</p>","image":{"data":{"id":3071,"attributes":{"name":"Learning & Development.png","alternativeText":"Learning & Development","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Learning & Development.png","hash":"thumbnail_Learning_and_Development_56d837fb6f","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":52.7,"sizeInBytes":52700,"url":"https://cdn.marutitech.com//thumbnail_Learning_and_Development_56d837fb6f.png"}},"hash":"Learning_and_Development_56d837fb6f","ext":".png","mime":"image/png","size":35.55,"url":"https://cdn.marutitech.com//Learning_and_Development_56d837fb6f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:42.915Z","updatedAt":"2025-05-05T08:54:36.898Z"}}}},{"id":113,"title":"Comprehensive Health Insurance","description":"<p>We support employee well-being by managing unexpected medical expenses and prioritizing a culture of physical and psychological health.</p>","image":{"data":{"id":3065,"attributes":{"name":"Health Insurance.png","alternativeText":"Comprehensive Health Insurance","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Health Insurance.png","hash":"thumbnail_Health_Insurance_b9beae284b","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":55.02,"sizeInBytes":55020,"url":"https://cdn.marutitech.com//thumbnail_Health_Insurance_b9beae284b.png"}},"hash":"Health_Insurance_b9beae284b","ext":".png","mime":"image/png","size":30.72,"url":"https://cdn.marutitech.com//Health_Insurance_b9beae284b.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:34:45.242Z","updatedAt":"2025-05-05T08:56:07.263Z"}}}},{"id":114,"title":"Maternity Benefit","description":"<p>We offer female employees generous maternity leave so that they can bond with newborns, regain strength, and rejoin once they are fit and healthy.</p>","image":{"data":{"id":3066,"attributes":{"name":"Maternity Benefit.png","alternativeText":"Maternity Benefit","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Maternity Benefit.png","hash":"thumbnail_Maternity_Benefit_6569cc1c43","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":50.1,"sizeInBytes":50103,"url":"https://cdn.marutitech.com//thumbnail_Maternity_Benefit_6569cc1c43.png"}},"hash":"Maternity_Benefit_6569cc1c43","ext":".png","mime":"image/png","size":26.41,"url":"https://cdn.marutitech.com//Maternity_Benefit_6569cc1c43.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:05.233Z","updatedAt":"2025-05-05T08:49:48.091Z"}}}},{"id":115,"title":"PF, ESIC, Gratuity","description":"<p>We offer employees comprehensive financial security, savings, and long-term stability benefits.</p>","image":{"data":{"id":3067,"attributes":{"name":"PF, ESIC, Gratuity.png","alternativeText":"PF, ESIC, Gratuity","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_PF, ESIC, Gratuity.png","hash":"thumbnail_PF_ESIC_Gratuity_be7003ff72","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":46.06,"sizeInBytes":46057,"url":"https://cdn.marutitech.com//thumbnail_PF_ESIC_Gratuity_be7003ff72.png"}},"hash":"PF_ESIC_Gratuity_be7003ff72","ext":".png","mime":"image/png","size":26.65,"url":"https://cdn.marutitech.com//PF_ESIC_Gratuity_be7003ff72.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:22.392Z","updatedAt":"2025-05-05T09:11:12.613Z"}}}},{"id":116,"title":"Surprise Performance Bonus","description":"<p>We ensure organizational excellence with surprise bonuses to motivate and attract high-performing employees.</p>","image":{"data":{"id":3068,"attributes":{"name":"Performance Bonus.png","alternativeText":"Surprise Performance Bonus","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Performance Bonus.png","hash":"thumbnail_Performance_Bonus_b01a294c2f","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":53.22,"sizeInBytes":53215,"url":"https://cdn.marutitech.com//thumbnail_Performance_Bonus_b01a294c2f.png"}},"hash":"Performance_Bonus_b01a294c2f","ext":".png","mime":"image/png","size":35.05,"url":"https://cdn.marutitech.com//Performance_Bonus_b01a294c2f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:35:47.275Z","updatedAt":"2025-05-05T08:54:14.355Z"}}}},{"id":117,"title":"Flexible Work Hours","description":"<p>Maruti Techlabs achieves work-life balance through time flexibility, remote work support, and a positive culture for job satisfaction.</p>","image":{"data":{"id":3069,"attributes":{"name":"Flexible Work Hours.png","alternativeText":"Flexible Work Hours","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Flexible Work Hours.png","hash":"thumbnail_Flexible_Work_Hours_f37b3367bb","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":47.4,"sizeInBytes":47398,"url":"https://cdn.marutitech.com//thumbnail_Flexible_Work_Hours_f37b3367bb.png"}},"hash":"Flexible_Work_Hours_f37b3367bb","ext":".png","mime":"image/png","size":26.11,"url":"https://cdn.marutitech.com//Flexible_Work_Hours_f37b3367bb.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:05.357Z","updatedAt":"2025-05-05T08:48:32.011Z"}}}},{"id":118,"title":"5 Working Days","description":"<p>Our firm belief is balancing work and personal life with a 5-day workweek policy while maintaining productivity and efficiency.</p>","image":{"data":{"id":3070,"attributes":{"name":"5 Working Days.png","alternativeText":"5 Day working ","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_5 Working Days.png","hash":"thumbnail_5_Working_Days_d6ba467d5d","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":38.35,"sizeInBytes":38348,"url":"https://cdn.marutitech.com//thumbnail_5_Working_Days_d6ba467d5d.png"}},"hash":"5_Working_Days_d6ba467d5d","ext":".png","mime":"image/png","size":23.56,"url":"https://cdn.marutitech.com//5_Working_Days_d6ba467d5d.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:24.346Z","updatedAt":"2025-05-05T08:47:15.753Z"}}}},{"id":119,"title":"Learning & Development","description":"<p>We encourage attending training programs on industry trends and technologies to improve skills and advance careers.</p>","image":{"data":{"id":3071,"attributes":{"name":"Learning & Development.png","alternativeText":"Learning & Development","caption":null,"width":250,"height":250,"formats":{"thumbnail":{"name":"thumbnail_Learning & Development.png","hash":"thumbnail_Learning_and_Development_56d837fb6f","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":52.7,"sizeInBytes":52700,"url":"https://cdn.marutitech.com//thumbnail_Learning_and_Development_56d837fb6f.png"}},"hash":"Learning_and_Development_56d837fb6f","ext":".png","mime":"image/png","size":35.55,"url":"https://cdn.marutitech.com//Learning_and_Development_56d837fb6f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-17T12:36:42.915Z","updatedAt":"2025-05-05T08:54:36.898Z"}}}}]}}]}],["$","div",null,{"id":"employee-testimonials","children":["$","$L1a",null,{"EmployeeTestimonialData":{"id":1,"title":"How Our Employees Feel About Us?","description":"<p>The hard work and dedication of our employees is the only reason Maruti Techlabs has achieved this feat. Here’s what our workforce has to say about us.&nbsp;</p>","employee_box":[{"id":4,"emp_title":"Ankit Samani","box_description":"<p>Maruti Techlabs is the best place to grow and shape your career, learn new technologies, and align with the current trends. Apart from developing technical skills, it nurtures leadership and communication skills. For the last 11 years, Maruti has always encouraged us to try new things.</p>","emp_description":"Technical Project Manager","emp_image":{"data":{"id":3083,"attributes":{"name":"Ankit.png","alternativeText":"Ankit Samani","caption":null,"width":87,"height":87,"formats":null,"hash":"Ankit_b761fae055","ext":".png","mime":"image/png","size":3.02,"url":"https://cdn.marutitech.com//Ankit_b761fae055.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T03:51:54.590Z","updatedAt":"2025-05-05T08:58:42.211Z"}}}},{"id":2,"emp_title":"Vishal Savsani","box_description":"<p>I am a lead engineer and have been working with Maruti for 11 years. My greatest support has been my manager. With time, my responsibilities grew significantly, and I embraced the fast-paced culture. This has pushed me to become a capable leader, enhancing my analytical skills.</p>","emp_description":"Technical Architect","emp_image":{"data":{"id":3081,"attributes":{"name":"Vishal.png","alternativeText":"Vishal Savsani","caption":null,"width":87,"height":87,"formats":null,"hash":"Vishal_b797a58849","ext":".png","mime":"image/png","size":4.96,"url":"https://cdn.marutitech.com//Vishal_b797a58849.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T03:51:20.041Z","updatedAt":"2025-05-05T08:49:28.368Z"}}}},{"id":3,"emp_title":"Ronak Soni","box_description":"<p>It has been ten years since I started working for Maruti. It has been an incredible journey where I got many opportunities for professional growth. New opportunities have pushed me to learn new things. Over the years, I’ve gotten unparalleled support from management and my colleagues.</p>","emp_description":"QA Manager","emp_image":{"data":{"id":3082,"attributes":{"name":"Ronak.png","alternativeText":"Ronak Soni","caption":null,"width":87,"height":87,"formats":null,"hash":"Ronak_f2d586a659","ext":".png","mime":"image/png","size":3.32,"url":"https://cdn.marutitech.com//Ronak_f2d586a659.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T03:51:37.952Z","updatedAt":"2025-05-05T08:54:58.500Z"}}}},{"id":1,"emp_title":"Mirant Hingrajia","box_description":"<p>I started my journey with Maruti Techlabs as an intern nine years ago. Many other employees have been working here since day one of their careers, and that is because of the ample opportunities and new challenges we work on. We have competent teams here and unmatched camaraderie.</p>","emp_description":"Technical Architect","emp_image":{"data":{"id":3080,"attributes":{"name":"Mirant.png","alternativeText":"Mirant Hingrajia","caption":null,"width":87,"height":87,"formats":null,"hash":"Mirant_92adc66024","ext":".png","mime":"image/png","size":2.99,"url":"https://cdn.marutitech.com//Mirant_92adc66024.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-02-18T03:51:06.620Z","updatedAt":"2025-05-05T08:53:09.866Z"}}}}]}}]}]]}]
