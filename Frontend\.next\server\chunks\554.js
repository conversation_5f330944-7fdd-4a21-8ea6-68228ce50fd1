exports.id=554,exports.ids=[554],exports.modules={50606:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var i=t(95344);t(3729);var r=t(60646),s=t(89410),l=t(56506),n=t(2522),_=t(79829),o=t(81473),c=t(97906),d=t(18924),m=t(44469),b=t.n(m),h=t(22281),x=t(54393),u=t.n(x),g=t(25609);function p({meetOurTeamData:e,variant:a,variantWhite:t=!0}){let[m,x]=(0,n.Z)({dragFree:!0,align:"start"}),{selectedIndex:p,scrollSnaps:j,onDotButtonClick:N}=(0,c.Z)(x),v=(0,d.Z)({query:"(max-width: 768px)"});return(0,i.jsxs)(i.Fragment,{children:["about-us"===a&&(v?i.jsx(r.default,{fluid:!0,className:u().main_container_embla,children:(0,i.jsxs)("div",{className:u().inner_container,children:[i.jsx(o.Z,{headingType:"h2",title:e.title,className:u().heading}),(0,i.jsxs)("div",{className:u().embla,children:[i.jsx("div",{className:u().embla__viewport,ref:m,children:i.jsx("div",{className:u().embla__container,children:e?.our_people.map((e,a)=>i.jsx("div",{className:u().embla__slide,children:i.jsxs("div",{className:u().main_box,children:[i.jsx("div",{className:u().single_image,children:i.jsx(g.Z,{src:e?.image?.data?.attributes,width:380,height:300,alt:"background image",className:u().imageWrapper})}),i.jsxs("div",{className:u().text_icon_container,children:[i.jsx(l.default,{href:e?.link,className:u().small_icon,target:"_blank",children:i.jsx(s.default,{src:e?.logo?.data?.attributes?.url,width:24,height:24,alt:"logo image"})}),i.jsxs("div",{className:u().title_description,children:[i.jsx(o.Z,{headingType:"h3",title:e?.title,className:u().card_title}),i.jsx("div",{className:u().card_description,dangerouslySetInnerHTML:{__html:e?.description}})]})]})]})},a))})}),i.jsx("div",{className:u().embla__controls,children:i.jsx("div",{className:b().embla__dots,children:j.length>1&&j.map((e,a)=>i.jsx(_.Z,{onClick:()=>N(a),className:a===p?`${b().embla__dot} ${b().embla__dot_selected}`:t?(0,h.Z)(b().embla__dot,b().embla__dot_bg_white):b().embla__dot},a))})})]})]})}):i.jsx(r.default,{fluid:!0,className:u().main_container,children:(0,i.jsxs)("div",{className:u().inner_container,children:[i.jsx(o.Z,{headingType:"h2",title:e.title,className:u().heading}),i.jsx("div",{className:u().card_box_container,children:e?.our_people.map(e=>i.jsxs("div",{className:u().main_box,children:[i.jsx("div",{className:u().single_image,children:i.jsx(g.Z,{src:e?.image?.data?.attributes,width:380,height:300,alt:"background image",className:u().imageWrapper})},e?.id),i.jsxs("div",{className:u().text_icon_container,children:[i.jsx(l.default,{href:e?.link,className:u().small_icon,target:"_blank",children:i.jsx(s.default,{src:e?.logo?.data?.attributes?.url,width:24,height:24,alt:"logo image"})}),i.jsxs("div",{className:u().title_description,children:[i.jsx(o.Z,{headingType:"h3",title:e?.title,className:u().card_title}),i.jsx("div",{className:u().card_description,dangerouslySetInnerHTML:{__html:e?.description}})]})]})]},e?.id))})]})})),"partners"===a&&i.jsx(r.default,{fluid:!0,className:u().main_container_embla,children:(0,i.jsxs)("div",{className:u().inner_container,children:[i.jsx(o.Z,{headingType:"h2",title:e.title,className:u().heading}),(0,i.jsxs)("div",{className:u().embla,children:[i.jsx("div",{className:u().embla__viewport,ref:m,children:i.jsx("div",{className:u().embla__container,children:e?.our_people.map((e,a)=>i.jsx("div",{className:u().embla__slide,children:i.jsxs("div",{className:u().main_box,children:[i.jsx("div",{className:u().single_image,children:i.jsx(g.Z,{src:e?.image?.data?.attributes,width:380,height:300,alt:"background image",className:u().imageWrapper})}),i.jsxs("div",{className:u().text_icon_container,children:[i.jsx(l.default,{href:e?.link,className:u().small_icon,target:"_blank",children:i.jsx(s.default,{src:e?.logo?.data?.attributes?.url,width:24,height:24,alt:"logo image"})}),i.jsxs("div",{className:u().title_description,children:[i.jsx(o.Z,{headingType:"h3",title:e?.title,className:u().card_title}),i.jsx("div",{className:u().card_description,dangerouslySetInnerHTML:{__html:e?.description}})]})]})]})},a))})}),i.jsx("div",{className:u().embla__controls,children:i.jsx("div",{className:b().embla__dots,children:j.length>1&&j.map((e,a)=>i.jsx(_.Z,{onClick:()=>N(a),className:a===p?`${b().embla__dot} ${b().embla__dot_selected}`:t?(0,h.Z)(b().embla__dot,b().embla__dot_bg_white):b().embla__dot},a))})})]})]})})]})}},54393:(e,a,t)=>{var i=t(24640),r=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+i.colorBlack,colorWhite:""+i.colorWhite,brandColorOne:""+i.brandColorOne,brandColorTwo:""+i.brandColorTwo,brandColorThree:""+i.brandColorThree,brandColorFour:""+i.brandColorFour,brandColorFive:""+i.brandColorFive,fifteenSpace:""+i.fifteenSpace,grayBorder:""+i.grayBorder,gray300:""+i.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+r["breakpoint-md"],"breakpoint-xl-2000":""+r["breakpoint-xl-2000"],"breakpoint-sm-450":""+r["breakpoint-sm-450"],"breakpoint-sm-550":""+r["breakpoint-sm-550"],"breakpoint-sm-320":""+r["breakpoint-sm-320"],"breakpoint-xl-1024":""+r["breakpoint-xl-1024"],"breakpoint-xl-1440":""+r["breakpoint-xl-1440"],"breakpoint-lg":""+r["breakpoint-lg"],"breakpoint-lg-991px":""+r["breakpoint-lg-991px"],main_container_embla:"MeetOurTeam_main_container_embla__LFRFK",main_container:"MeetOurTeam_main_container__cLSLF",inner_container:"MeetOurTeam_inner_container__KQtTy",main_box:"MeetOurTeam_main_box__suhxd",heading:"MeetOurTeam_heading__2WMKW",card_box_container:"MeetOurTeam_card_box_container__8pk1c",single_image:"MeetOurTeam_single_image__Q9n23",title_description:"MeetOurTeam_title_description__9fe7_",text_icon_container:"MeetOurTeam_text_icon_container__cH1Is",card_title:"MeetOurTeam_card_title__vCUGj",card_description:"MeetOurTeam_card_description__tuiPT",imageWrapper:"MeetOurTeam_imageWrapper__m6Smd",button_main_container:"MeetOurTeam_button_main_container__RkJ_K",button_container:"MeetOurTeam_button_container__z0sAB",button_text_arrow_container:"MeetOurTeam_button_text_arrow_container__vK1wT",arrow_styling:"MeetOurTeam_arrow_styling__jMdPC",arrow_styling_hover:"MeetOurTeam_arrow_styling_hover__OelJz",arrow_container:"MeetOurTeam_arrow_container__tgUBe",embla:"MeetOurTeam_embla___mL56",embla__viewport:"MeetOurTeam_embla__viewport__GuImq",embla__container:"MeetOurTeam_embla__container__blUps",embla__slide:"MeetOurTeam_embla__slide__viGqq",embla__controls:"MeetOurTeam_embla__controls__2FEHc",small_icon:"MeetOurTeam_small_icon__NAsCe"}},38286:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let i=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\MeetOurTeam\MeetOurTeam.tsx`),{__esModule:r,$$typeof:s}=i,l=i.default}};