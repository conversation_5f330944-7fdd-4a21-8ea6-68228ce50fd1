(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5746],{32967:function(e,t,o){Promise.resolve().then(o.t.bind(o,81749,23)),Promise.resolve().then(o.t.bind(o,25250,23)),Promise.resolve().then(o.bind(o,88060)),Promise.resolve().then(o.t.bind(o,2111,23)),Promise.resolve().then(o.bind(o,13571)),Promise.resolve().then(o.t.bind(o,98633,23)),Promise.resolve().then(o.t.bind(o,26155,23)),Promise.resolve().then(o.t.bind(o,46282,23)),Promise.resolve().then(o.t.bind(o,25323,23))},80614:function(e,t){"use strict";let o=async()=>{let e="user_ip_location",t=localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){console.warn("Failed to parse cached IP data:",e)}try{let t=await fetch("https://api.ipify.org?format=json");if(!t.ok)throw Error("IP fetch error! Status: ".concat(t.status));let o=(await t.json()).ip||"",n=await fetch("https://ipapi.co/".concat(o,"/json/"));if(!n.ok)throw Error("Location fetch error! Status: ".concat(n.status));let a=await n.json(),r={ipAddress:o,location:{city:a.city||"",country:a.country_name||"",country_code:a.country_code||""}};return localStorage.setItem(e,JSON.stringify(r)),r}catch(e){return console.error("Error fetching IP or location:",e),{ipAddress:"",location:{city:"",country:"",country_code:""}}}};t.Z=o},88060:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return c}});var n=o(57437),a=o(18062),r=o(44061),i=o.n(r),l=o(22543),s=o(61344);function c(e){var t;let{dataAwards:o}=e,[r]=(0,a.Z)({loop:!0,align:"start",dragFree:!0},[(0,l.Z)({playOnInit:!0,speed:.7})]);return(0,n.jsx)("div",{className:i().embla,children:(0,n.jsx)("div",{className:i().embla__viewport,ref:r,children:(0,n.jsx)("div",{className:i().embla__container,children:null==o?void 0:null===(t=o.images)||void 0===t?void 0:t.data.map(e=>{var t;return(0,n.jsx)("div",{className:i().embla__slide,children:(0,n.jsx)(s.Z,{src:null==e?void 0:e.attributes,width:100,height:100,alt:null==e?void 0:null===(t=e.attributes)||void 0===t?void 0:t.alternativeText,className:i().image})},null==e?void 0:e.id)})})})})}},62806:function(e,t,o){"use strict";o.d(t,{Z:function(){return s}});var n=o(57437),a=o(8792),r=o(41396),i=o(15758),l=o.n(i);function s(e){let{label:t="",className:o="",type:i="button",isLink:s=!1,leftIcon:c=null,rightIcon:d=null,href:p="",children:u=null,isExternal:_=!1,onClick:m=()=>{},dataID:h=null,onMouseDown:b=()=>{},onMouseUp:x=()=>{},onTouchStart:f=()=>{},onTouchEnd:v=()=>{},scrollToForm:y}=e,g=(0,n.jsxs)("div",{className:l().innerWrapper,children:[c&&(0,n.jsx)("span",{className:l().leftWrapper,children:c}),t,d&&(0,n.jsx)("span",{className:l().rightWrapper,children:d})]}),F=e=>{y&&y(),m&&m(e)};return s?(0,n.jsx)(a.default,{href:p,target:_?"_blank":"_self",rel:_?"noreferrer":null,className:(0,r.Z)(l().link,o),"data-id":h,onClick:m,children:(0,n.jsx)("div",{children:g})}):(0,n.jsxs)("button",{type:i,className:(0,r.Z)(l().button,o),"data-id":h,onClick:e=>F(e),onMouseDown:b,onMouseUp:x,onTouchStart:f,onTouchEnd:v,children:[g,u]})}},13571:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return u}});var n=o(57437),a=o(39073),r=o.n(a),i=o(42101),l=o(62806);o(40561);var s=o(20167),c=o.n(s),d=o(2265),p=o(5461);function u(e){let{fields:t,button:o,text:a,source:s="HomePage"}=e,[u,_]=(0,d.useState)(!1),m=(0,p.Z)(),{values:h,errors:b,errorMessages:x,handleChange:f,handleBlur:v,handleSubmit:y}=(0,i.Z)({firstName:"",lastName:"",emailAddress:"",phoneNumber:"",howDidYouHearAboutUs:"",companyName:"",howCanWeHelpYou:"",consent:!1},{firstName:{empty:!1},lastName:{empty:!1},emailAddress:{empty:!1,invalid:!1},phoneNumber:{empty:!1,invalid:!1},consent:{empty:!1}},"default",s),g=async e=>{e.preventDefault(),_(!0);try{await y(e)}catch(e){console.error("Form submission failed:",e)}finally{_(!1)}};return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("form",{className:r().form,onSubmit:g,children:[(0,n.jsxs)("div",{className:r().nameFields,children:[(0,n.jsxs)("div",{className:r().firstName,children:[(0,n.jsxs)("label",{className:b.firstName.empty?"".concat(r().errorLabel," ").concat(r().label):r().label,htmlFor:"firstName",children:[t.fieldNameFor_FirstName,"*"]}),(0,n.jsx)("br",{}),(0,n.jsx)("input",{className:b.firstName.empty?"".concat(r().errorInput," ").concat(r().input):"".concat(r().input),type:"text",id:"firstName",name:"firstName",maxLength:50,value:h.firstName,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]}),(0,n.jsxs)("div",{className:r().lastName,children:[(0,n.jsxs)("label",{className:b.lastName.empty?"".concat(r().errorLabel," ").concat(r().label):r().label,htmlFor:"lastName",children:[t.fieldNameFor_LastName,"*"]}),(0,n.jsx)("br",{}),(0,n.jsx)("input",{className:b.lastName.empty?"".concat(r().errorInput," ").concat(r().input):"".concat(r().input),type:"text",id:"lastName",name:"lastName",maxLength:50,value:h.lastName,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]})]}),(0,n.jsxs)("div",{className:r().inputFields,children:[(0,n.jsxs)("label",{className:b.emailAddress.empty||b.emailAddress.invalid?"".concat(r().errorLabel," ").concat(r().label):r().label,htmlFor:"emailAddress",children:[t.fieldNameFor_EmailAddress,"*"]}),(0,n.jsx)("br",{}),(0,n.jsx)("input",{className:b.emailAddress.empty?"".concat(r().errorInput," ").concat(r().input):"".concat(r().input),type:"text",id:"emailAddress",name:"emailAddress",maxLength:50,value:h.emailAddress,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]}),(0,n.jsxs)("div",{className:r().inputFields,children:[(0,n.jsxs)("label",{className:b.phoneNumber.empty||b.phoneNumber.invalid?"".concat(r().errorLabel," ").concat(r().label):r().label,children:[t.fieldNameFor_PhoneNumber,"*"]}),(0,n.jsx)("br",{}),(0,n.jsx)("div",{children:(0,n.jsx)(c(),{inputProps:{id:"phoneNumber"},inputClass:b.phoneNumber.empty||b.phoneNumber.invalid?"".concat(r().errorPhoneInput," ").concat(r().phoneInput):r().phoneInput,buttonClass:b.phoneNumber.empty||b.phoneNumber.invalid?"".concat(r().errorPhoneButton," ").concat(r().phoneButton):r().phoneButton,placeholder:"",preferredCountries:["us","gb","sg","de","sa","in","nl","au","be","my"],country:m||"us",enableSearch:!0,value:h.phoneNumber,onChange:e=>f({value:e,name:"phoneNumber"}),onBlur:e=>v(null==e?void 0:e.target)})})]}),(0,n.jsxs)("div",{className:r().inputFields,children:[(0,n.jsx)("label",{htmlFor:"howDidYouHearAboutUs",className:r().label,children:t.fieldNameFor_HowDidYouHearAboutUs}),(0,n.jsx)("br",{}),(0,n.jsx)("input",{className:r().input,type:"text",id:"howDidYouHearAboutUs",name:"howDidYouHearAboutUs",maxLength:100,value:h.howDidYouHearAboutUs,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]}),(0,n.jsxs)("div",{className:r().inputFields,children:[(0,n.jsx)("label",{htmlFor:"companyName",className:r().label,children:t.fieldNameFor_CompanyName}),(0,n.jsx)("br",{}),(0,n.jsx)("input",{className:r().input,type:"text",id:"companyName",name:"companyName",maxLength:50,value:h.companyName,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]}),(0,n.jsxs)("div",{className:r().inputFields,children:[(0,n.jsx)("label",{className:r().label,htmlFor:"howCanWeHelpYou",children:t.fieldNameFor_HowCanWeHelpYou}),(0,n.jsx)("br",{}),(0,n.jsx)("textarea",{className:r().textarea,id:"howCanWeHelpYou",name:"howCanWeHelpYou",maxLength:2e3,value:h.howCanWeHelpYou,onChange:e=>f(null==e?void 0:e.target),onBlur:e=>v(null==e?void 0:e.target)})]}),(0,n.jsxs)("div",{className:r().checkbox,children:[(0,n.jsx)("input",{className:r().input,type:"checkbox",id:"consent",name:"consent",checked:h.consent,onChange:e=>f(null==e?void 0:e.target)}),(0,n.jsx)("label",{className:b.consent.empty?"".concat(r().errorLabel," ").concat(r().consentLabel):r().consentLabel,htmlFor:"consent",children:a})]}),u?(0,n.jsx)("div",{className:r().container_spinner,children:(0,n.jsx)("div",{className:r().spinner})}):(0,n.jsx)(l.Z,{className:r().button,label:o,type:"submit"}),(0,n.jsxs)("div",{className:r().errorMessages,children:[(0,n.jsx)("div",{children:x.empty&&x.empty}),(0,n.jsx)("div",{children:x.invalid&&x.invalid})]})]})})}},80257:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return l}});var n=o(57437),a=o(20703),r=o(97073),i=o(2265);function l(e){var t,o,l,s,c,d,p,u,_,m,h,b;let{src:x,width:f,height:v,fill:y=!1,alt:g="image alt text",loading:F="lazy",useThumbnail:N=!1,className:k,style:C}=e,w=(0,r.Z)({query:"(max-width: 576px)"}),S=(0,r.Z)({query:"(max-width: 768px)"});(0,r.Z)({query:"(max-width: 1600px)"});let[j,B]=(0,i.useState)(!1);if((0,i.useEffect)(()=>{B(!0)},[]),!j)return null;let A=N?(null==x?void 0:null===(o=x.format)||void 0===o?void 0:null===(t=o.small)||void 0===t?void 0:t.url)||(null==x?void 0:null===(s=x.formats)||void 0===s?void 0:null===(l=s.small)||void 0===l?void 0:l.url)||(null==x?void 0:x.url):w?(null==x?void 0:null===(d=x.format)||void 0===d?void 0:null===(c=d.small)||void 0===c?void 0:c.url)||(null==x?void 0:null===(u=x.formats)||void 0===u?void 0:null===(p=u.small)||void 0===p?void 0:p.url)||(null==x?void 0:x.url):S&&((null==x?void 0:null===(m=x.format)||void 0===m?void 0:null===(_=m.medium)||void 0===_?void 0:_.url)||(null==x?void 0:null===(b=x.formats)||void 0===b?void 0:null===(h=b.medium)||void 0===h?void 0:h.url))||(null==x?void 0:x.url);return(0,n.jsx)(a.default,{unoptimized:!0,src:A,alt:g,width:f,height:v,fill:y,loading:F,className:k,style:C})}},61344:function(e,t,o){"use strict";o.d(t,{Z:function(){return n.default}});var n=o(80257)},42101:function(e,t,o){"use strict";o.d(t,{Z:function(){return i}});var n=o(80614);let a=async()=>{try{let o=document.referrer||"",n=new URLSearchParams(window.location.search),a=n.get("utm_medium")||"",r=n.get("utm_source")||"",i=n.get("utm_campaign")||"",l="";if(window.clarity)try{l=window.clarity("get","userId")}catch(e){console.error("Error fetching Clarity ID:",e)}let s="";try{var e,t;s=(null===(t=globalThis.gaGlobal)||void 0===t?void 0:null===(e=t.vid.match(/\d+\.\d+$/))||void 0===e?void 0:e[0])||""}catch(e){console.error("Error fetching GA4 Client ID:",e)}return{clarity:l,utm_medium:a,utm_source:r,utm_campaign:i,referrer:o,ga_client_id:s}}catch(e){return console.error("Error fetching user tracking data:",e),{clarity:"",utm_medium:"",utm_source:"",utm_campaign:"",referrer:"",ga_client_id:""}}};var r=o(2265);function i(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"default",i=arguments.length>3?arguments[3]:void 0,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",[s,c]=(0,r.useState)(e),[d,p]=(0,r.useState)(t),[u,_]=(0,r.useState)({empty:"",invalid:""}),m="caseStudy"===o?["firstName","emailAddress","phoneNumber"]:["firstName","lastName","emailAddress","phoneNumber","consent"],h=e=>{var t,o,n,a;let r={...u};m.some(t=>{var o;return null===(o=e[t])||void 0===o?void 0:o.empty})?r.empty="Please fill the highlighted fields":r.empty="",(null===(t=e.emailAddress)||void 0===t?void 0:t.invalid)&&(null===(o=e.phoneNumber)||void 0===o?void 0:o.invalid)?r.invalid="Please enter valid Email ID and Phone Number":(null===(n=e.emailAddress)||void 0===n?void 0:n.invalid)?r.invalid="Please enter a valid Email ID":(null===(a=e.phoneNumber)||void 0===a?void 0:a.invalid)?r.invalid="Please enter a valid Phone Number":r.invalid="",_(r)},b=(e,o)=>{let n={...d};o?("emailAddress"!==e||/\S+@\S+\.\S+/.test(o))&&("phoneNumber"!==e||/.{6,}/.test(o))?n[e]=t[e]:n[e]={empty:!1,invalid:!0}:n[e]={empty:!0,invalid:!1},p(n),h(n)},x=()=>{let e={...d};return m.forEach(t=>{s[t]||(e[t]={empty:!0,invalid:!1})}),p(e),h(e),!Object.values(e).some(e=>e.empty||e.invalid)},f=async o=>{if(o.preventDefault(),x()){try{let e=await (0,n.Z)(),t=await a(),o={firstName:s.firstName||"",lastName:s.lastName||"",emailAddress:s.emailAddress||"",phoneNumber:s.phoneNumber||"",howDidYouHearAboutUs:s.howDidYouHearAboutUs||"",companyName:s.companyName||"",howCanWeHelpYou:s.howCanWeHelpYou||"",utm_campaign:t.utm_campaign||"",utm_medium:t.utm_medium||"",utm_source:t.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:t.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:i||"",clarity:t.clarity||"",url:window.location.href||"",referrer:t.referrer||"",consent:s.consent||!1},r=await fetch("".concat("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev","/contact-us"),{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(o)});r.ok?(("CaseStudy"===i||"eBooks"===i||"whitePapers"===i)&&l&&window.open(l,"_blank"),window.location.href="/thank-you/"):console.error("Error submitting form:",await r.json())}catch(e){console.error("Error in form submission:",e)}c(e),p(t)}},v=async(o,r,l)=>{if(x()){try{let e=await (0,n.Z)(),t=await a(),c={firstName:s.firstName||"",lastName:s.lastName||"",emailAddress:s.emailAddress||"",phoneNumber:s.phoneNumber||"",companyName:s.companyName||"",utm_campaign:t.utm_campaign||"",utm_medium:t.utm_medium||"",utm_source:t.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:t.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:i||"",clarity:t.clarity||"",url:window.location.href||"",referrer:t.referrer||"",consent:s.consent||!1,do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_:o[0][0][0]||"",how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_:o[0][1][0]||"",do_you_have_budget_allocated_for_your_ai_project_:o[0][2][0]||"",do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_:o[1][0][0]||"",which_of_the_below_db_tools_do_you_currently_use_:o[1][1][0]||"",is_the_relevant_data_for_the_ai_project_available_and_accessible_:o[1][2][0]||"",do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__:o[1][3][0]||"",how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib:o[1][4][0]||"",does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_:o[2][0][0]||"",do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_:o[3][0][0]||"",do_you_have_risk_management_strategies_in_place_for_the_ai_project_:o[3][1][0]||"",do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions:o[4][0][0]||"",strategy___leadership:r[0]||"",data_readiness___infrastructure:r[1]||"",talent___skills:r[2]||"",execution___monitoring:r[3]||"",impact_evaliation:r[4]||"",average_of_all_score:r.final||""},d=await fetch("".concat("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev","/ai-readiness"),{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(c)});d.ok?l(o.length):console.error("Error submitting form:",await d.json())}catch(e){console.error("Error in form submission:",e)}c(e),p(t)}};return{values:s,errors:d,errorMessages:u,handleChange:e=>{let{name:t,value:o,type:n="",checked:a=!1}=e;o="checkbox"===n?a:o;let r={...s};"firstName"===t||"lastName"===t?r[t]=o.replace(/[^a-zA-Z0-9 ]/g,"").trimStart():"emailAddress"===t?r[t]=o.replace(" ",""):r[t]=o,c(r),t in d&&b(t,o)},handleBlur:e=>{let{name:t,value:o}=e,n={...s};"string"==typeof o&&(n[t]=o.trim()),c(n),t in d&&b(t,o)},handleSubmit:f,handleSubmitAIReadiness:v}}},97073:function(e,t,o){"use strict";var n=o(2265);let a=void 0===window.matchMedia?()=>!1:e=>{let{query:t}=e,[o,a]=(0,n.useState)(!1),r=(0,n.useRef)(window.matchMedia(t)),i=(0,n.useCallback)(e=>{a(e.matches)},[a]);return(0,n.useEffect)(()=>{let e=r.current;return a(e.matches),e.addListener(i),()=>e.removeListener(i)},[a,i]),o};t.Z=a},41396:function(e,t,o){"use strict";function n(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return t.filter(Boolean).join(" ")}o.d(t,{Z:function(){return n}})},5461:function(e,t,o){"use strict";o.d(t,{Z:function(){return r}});var n=o(2265),a=o(80614);function r(){let[e,t]=(0,n.useState)("");return(0,n.useEffect)(()=>{(async()=>{try{var e,o;let n=await (0,a.Z)();t((null==n?void 0:null===(o=n.location)||void 0===o?void 0:null===(e=o.country_code)||void 0===e?void 0:e.toLowerCase())||"")}catch(e){t("us")}})()},[]),e}},44061:function(e){e.exports={breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":"768px",embla:"Awards_embla__iBhMB",embla__viewport:"Awards_embla__viewport__Otq_j",embla__container:"Awards_embla__container__flGSh",embla__slide:"Awards_embla__slide__5peip"}},15758:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",gray300:"#F3F3F3",colorWhite:"#FFFFFF",button:"Button_button__exqP_",link:"Button_link__9n7Et",innerWrapper:"Button_innerWrapper__ITLB1",leftWrapper:"Button_leftWrapper__fWtI9",rightWrapper:"Button_rightWrapper__GkIh_"}},2111:function(e){e.exports={variables:'"@styles/variables.module.css"',colorBlack:"#000000",colorWhite:"#FFFFFF",blueFonts:"undefined",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm-380":"380px","breakpoint-md":"768px","breakpoint-xl":"1200px","breakpoint-sm-450":"450px","breakpoint-sm":"576px",mainContainer:"ContactUs_mainContainer__vVFh3",formContainer:"ContactUs_formContainer__Ula4U",rightContainer:"ContactUs_rightContainer__a0Lg_",link:"ContactUs_link__fL1dF",title:"ContactUs_title__wkSFO",description:"ContactUs_description__llYIu",textBox:"ContactUs_textBox__Soq8l",exceptCarousel:"ContactUs_exceptCarousel___y0Db",flexContainer:"ContactUs_flexContainer__ezbk5",logo:"ContactUs_logo__hj_uD",gradientImageTop:"ContactUs_gradientImageTop__Art_z",gradientImageBottom:"ContactUs_gradientImageBottom__dA2Z7",trustedBy:"ContactUs_trustedBy__stE6C",companies:"ContactUs_companies__uGVpY",infoContainer:"ContactUs_infoContainer____vrQ",textCard:"ContactUs_textCard__56eVD",textCardHeading:"ContactUs_textCardHeading__oz8BM",content:"ContactUs_content__OaMYI",text:"ContactUs_text__ZgS7R",collab_title:"ContactUs_collab_title__9ctZF",collabBox:"ContactUs_collabBox__UImnj"}},39073:function(e){e.exports={variables:'"@styles/variables.module.css"',gray300:"#F3F3F3",colorBlack:"#000000",colorWhite:"#FFFFFF",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl":"1200px","breakpoint-md":"768px",form:"Form_form__ngj4g",inputFields:"Form_inputFields__DMgri",firstName:"Form_firstName__TeTaN",lastName:"Form_lastName__iMKwa",nameFields:"Form_nameFields__TsaeX",input:"Form_input__iehOS",textarea:"Form_textarea__gGRo5",checkbox:"Form_checkbox__vHS5b",button:"Form_button__fxuGQ",container_spinner:"Form_container_spinner__9Rt__",spinner:"Form_spinner__sAr9p",spin:"Form_spin__OV29H",errorInput:"Form_errorInput__KXhXK",errorMessages:"Form_errorMessages__KS3Aq",label:"Form_label__svn2q",consentLabel:"Form_consentLabel__jd6DP",errorLabel:"Form_errorLabel__CDN6_",phoneInput:"Form_phoneInput__h1iKK",errorPhoneInput:"Form_errorPhoneInput__il1HZ",phoneButton:"Form_phoneButton__dft1g",errorPhoneButton:"Form_errorPhoneButton__s1STo"}},46282:function(e){e.exports={center:"Heading_center__XBGsG",left:"Heading_left__ouHog",right:"Heading_right__jsN_Y"}},26155:function(e){e.exports={"breakpoint-xs":"0","breakpoint-sm-195":"195px","breakpoint-sm-270":"270px","breakpoint-sm-200":"200px","breakpoint-sm-320":"320px","breakpoint-sm-326":"326px","breakpoint-sm-390":"390px","breakpoint-sm-367":"367px","breakpoint-sm-365":"365px","breakpoint-sm-340":"340px","breakpoint-sm-350":"350px","breakpoint-sm-370":"370px","breakpoint-sm-380":"380px","breakpoint-sm-424":"424px","breakpoint-sm-427":"427px","breakpoint-sm-420":"420px","breakpoint-sm-430":"430px","breakpoint-sm-450":"450px","breakpoint-sm-460":"460px","breakpoint-sm-484":"484px","breakpoint-sm-480":"480px","breakpoint-sm-532":"532px","breakpoint-sm-550":"550px","breakpoint-sm":"576px","breakpoint-md-579":"579px","breakpoint-md-585":"585px","breakpoint-md-767":"767px","breakpoint-md":"768px","breakpoint-md-769":"769px","breakpoint-md-820":"820px","breakpoint-md-850":"850px","breakpoint-lg-901":"901px","breakpoint-lg":"992px","breakpoint-lg-991px":"991px","breakpoint-xl-1024":"1024px","breakpoint-xl-1051":"1051px","breakpoint-xl-1208":"1208px","breakpoint-xl-1023":"1023px","breakpoint-xl-1199":"1199px","breakpoint-xl-1188":"1188px","breakpoint-xl":"1200px","breakpoint-xl-1365":"1365px","breakpoint-xl-1366":"1366px","breakpoint-xl-1309":"1309px","breakpoint-xl-1400":"1400px","breakpoint-xl-1439":"1439px","breakpoint-xl-1440":"1440px","breakpoint-xl-1405":"1405px","breakpoint-xl-1406":"1406px","breakpoint-xl-1600":"1600px","breakpoint-xl-1800":"1800px","breakpoint-xl-2000":"2000px","breakpoint-xl-2100":"2100px","breakpoint-xl-2442":"2442px","breakpoint-xl-2559":"2559px","breakpoint-xl-2560":"2560px"}},25323:function(e){e.exports={variables:'"./variables.module.css"',h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",fontWeight600:"600",fontWeight700:"700",breakPoints:'"./breakpoints.module.css"',"breakpoint-sm-450":"450px",h1:"typography_h1__DecPZ",h2:"typography_h2__Dn0zf",h3:"typography_h3__o3Abb",h4:"typography_h4__lGrWj",h5:"typography_h5__DGJHL",h6:"typography_h6__vf_A0",caption:"typography_caption__hfk0A"}},98633:function(e){e.exports={brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",gray100:"#FCFCFC",gray200:"#F8F8F8",gray300:"#F3F3F3",gray400:"#E4E4E4",gray500:"#CDCDCD",gray600:"#B1B1B1",gray700:"#808080",gray800:"#646464",gray900:"#3A3A3A",error:"#FF6D60",success:"#23A881",grayBorder:"#8C8B8B",link:"#0075FF",grayBlueFonts:"#262531",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",halfSpace:"4px",oneSpace:"8px",twoSpace:"16px",threeSpace:"24px",fourSpace:"32px",fiveSpace:"40px",sixSpace:"48px",eightSpace:"64px",tenSpace:"80px",fifteenSpace:"120px",twentyFiveSpace:"200px",h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",bodyHeadingXL:"56px",bodyHeadingL:"24px",bodyHeadingM:"21px",bodyHeadingS:"20px",bodyHeadingXS:"18px",bodyHeadingXSS:"16px",buttonLabelXLargeFontSize:"26px",buttonLabelLargeFontSize:"20px",buttonLabelMediumFontSize:"16px",buttonLabelSmallFontSize:"14px",bodyTextXLarge:"26px",bodyTextLarge:"22px",bodyTextMedium:"20px",bodyTextSmall:"18px",bodyTextXSmall:"16px",bodyTextXXSmall:"14px",bodyTextXXXSSmall:"8px",bodyLinkXXLarge:"26px",bodyLinkXLarge:"22px",bodyLinkLarge:"19px",bodyLinkMedium:"18px",bodyLinkSmall:"17px",bodyLinkXSmall:"16px",bodyLinkXXSmall:"15px",fontWeight100:"100",fontWeight200:"200",fontWeight300:"300",fontWeight400:"400",fontWeight500:"500",fontWeight600:"600",fontWeight700:"700",fontWeight800:"800",fontWeight900:"900"}},22543:function(e,t,o){"use strict";o.d(t,{Z:function(){return a}});let n={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function a(){let e,t,o,r,i,l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=!1,c=!0,d=0;function p(){if(o||s||!c)return;t.emit("autoScroll:play");let n=t.internalEngine(),{ownerWindow:a}=n;d=a.setTimeout(()=>{n.scrollBody=function(o){let{location:n,target:a,scrollTarget:r,index:i,indexPrevious:l,limit:{reachedMin:s,reachedMax:c,constrain:d},options:{loop:p}}=o,_="forward"===e.direction?-1:1,m=()=>y,h=0,b=0,x=n.get(),f=0,v=!1,y={direction:()=>b,duration:()=>-1,velocity:()=>h,settled:()=>v,seek:function(){h=_*e.speed,x+=h,n.add(h),a.set(n),b=Math.sign(x-f),f=x;let o=r.byDistance(0,!1).index;i.get()!==o&&(l.set(i.get()),i.set(o),t.emit("select"));let m="forward"===e.direction?s(n.get()):c(n.get());if(!p&&m){v=!0;let e=d(n.get());n.set(e),a.set(n),u()}return y},useBaseFriction:m,useBaseDuration:m,useFriction:m,useDuration:m};return y}(n),n.animation.start()},r),s=!0}function u(){if(o||!s)return;t.emit("autoScroll:stop");let e=t.internalEngine(),{ownerWindow:n}=e;e.scrollBody=i,n.clearTimeout(d),d=0,s=!1}function _(){c&&p(),t.off("settle",_)}function m(){t.on("settle",_)}return{name:"autoScroll",options:l,init:function(s,d){t=s;let{mergeOptions:_,optionsAtMedia:h}=d,b=_(n,a.globalOptions);if(e=h(_(b,l)),t.scrollSnapList().length<=1)return;r=e.startDelay,o=!1,i=t.internalEngine().scrollBody;let{eventStore:x}=t.internalEngine(),f=t.rootNode(),v=e.rootNode&&e.rootNode(f)||f,y=t.containerNode();t.on("pointerDown",u),e.stopOnInteraction||t.on("pointerUp",m),e.stopOnMouseEnter&&(x.add(v,"mouseenter",()=>{c=!1,u()}),e.stopOnInteraction||x.add(v,"mouseleave",()=>{c=!0,p()})),e.stopOnFocusIn&&(x.add(y,"focusin",()=>{u(),t.scrollTo(t.selectedScrollSnap(),!0)}),e.stopOnInteraction||x.add(y,"focusout",p)),e.playOnInit&&p()},destroy:function(){t.off("pointerDown",u).off("pointerUp",m).off("settle",_),u(),o=!0,s=!1},play:function(e){void 0!==e&&(r=e),c=!0,p()},stop:function(){s&&u()},reset:function(){s&&(u(),m())},isPlaying:function(){return s}}}a.globalOptions=void 0}},function(e){e.O(0,[5250,1607,843,8062,2971,8069,1744],function(){return e(e.s=32967)}),_N_E=e.O()}]);