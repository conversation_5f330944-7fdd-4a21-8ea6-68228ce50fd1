"use strict";(()=>{var e={};e.id=3310,e.ids=[3310],e.modules={47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},91545:(e,t,a)=>{a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var r=a(50482),s=a(69108),n=a(62563),o=a.n(n),i=a(68300),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);a.d(t,p);let c=["",{children:["privacy-policy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,44502)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\privacy-policy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,24692)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,48206)),"C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\not-found.tsx"]}],l=["C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\privacy-policy\\page.tsx"],u="/privacy-policy/page",d={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/privacy-policy/page",pathname:"/privacy-policy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44502:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l,fetchPrivacyPolicyPageData:()=>p,generateMetadata:()=>c});var r=a(25036),s=a(23691),n=a(49256),o=a(35992),i=a(75509);async function p(){return await (0,i.Z)("privacy-policy","populate=rich_text,seo.schema")}async function c({}){let e=await (0,i.Z)("privacy-policy","populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema"),t=e?.data?.attributes?.seo;return(0,o.Z)(t)}async function l(){let e=await p();return(0,r.jsxs)(r.Fragment,{children:[e?.data?.attributes?.seo&&r.jsx(n.Z,{data:e?.data?.attributes?.seo}),e?.data?.attributes?.rich_text&&r.jsx(s.Z,{richTextData:e?.data?.attributes})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,42,3676,3401],()=>a(91545));module.exports=r})();