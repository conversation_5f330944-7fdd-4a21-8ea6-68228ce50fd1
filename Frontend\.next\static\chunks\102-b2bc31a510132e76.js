"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[102],{30375:function(t,n,e){e.r(n),e.d(n,{CountUp:function(){return i}});var r=function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=function(){function t(t,n,e){var i=this;this.endVal=n,this.options=e,this.version="2.8.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(t){i.startTime||(i.startTime=t);var n=t-i.startTime;i.remaining=i.duration-n,i.useEasing?i.countDown?i.frameVal=i.startVal-i.easingFn(n,0,i.startVal-i.endVal,i.duration):i.frameVal=i.easingFn(n,i.startVal,i.endVal-i.startVal,i.duration):i.frameVal=i.startVal+(i.endVal-i.startVal)*(n/i.duration);var e=i.countDown?i.frameVal<i.endVal:i.frameVal>i.endVal;i.frameVal=e?i.endVal:i.frameVal,i.frameVal=Number(i.frameVal.toFixed(i.options.decimalPlaces)),i.printValue(i.frameVal),n<i.duration?i.rAF=requestAnimationFrame(i.count):null!==i.finalEndVal?i.update(i.finalEndVal):i.options.onCompleteCallback&&i.options.onCompleteCallback()},this.formatNumber=function(t){var n,e,r,o=(Math.abs(t).toFixed(i.options.decimalPlaces)+"").split(".");if(n=o[0],e=o.length>1?i.options.decimal+o[1]:"",i.options.useGrouping){r="";for(var a=3,s=0,l=0,u=n.length;l<u;++l)i.options.useIndianSeparators&&4===l&&(a=2,s=1),0!==l&&s%a==0&&(r=i.options.separator+r),s++,r=n[u-l-1]+r;n=r}return i.options.numerals&&i.options.numerals.length&&(n=n.replace(/[0-9]/g,function(t){return i.options.numerals[+t]}),e=e.replace(/[0-9]/g,function(t){return i.options.numerals[+t]})),(t<0?"-":"")+i.options.prefix+n+e+i.options.suffix},this.easeOutExpo=function(t,n,e,r){return e*(1-Math.pow(2,-10*t/r))*1024/1023+n},this.options=r(r({},this.defaults),e),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(n),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,""===this.options.separator&&(this.options.useGrouping=!1),this.el="string"==typeof t?document.getElementById(t):t,this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined","undefined"!=typeof window&&this.options.enableScrollSpy&&(this.error?console.error(this.error,t):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return i.handleScroll(i)}),window.onscroll=function(){window.onScrollFns.forEach(function(t){return t()})},this.handleScroll(this)))}return t.prototype.handleScroll=function(t){if(t&&window&&!t.once){var n=window.innerHeight+window.scrollY,e=t.el.getBoundingClientRect(),r=e.top+window.pageYOffset,i=e.top+e.height+window.pageYOffset;i<n&&i>window.scrollY&&t.paused?(t.paused=!1,setTimeout(function(){return t.start()},t.options.scrollSpyDelay),t.options.scrollSpyOnce&&(t.once=!0)):(window.scrollY>i||r>n)&&!t.paused&&t.reset()}},t.prototype.determineDirectionAndSmartEasing=function(){var t=this.finalEndVal?this.finalEndVal:this.endVal;if(this.countDown=this.startVal>t,Math.abs(t-this.startVal)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=t;var n=this.countDown?1:-1;this.endVal=t+n*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=t,this.finalEndVal=null;null!==this.finalEndVal?this.useEasing=!1:this.useEasing=this.options.useEasing},t.prototype.start=function(t){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),t&&(this.options.onCompleteCallback=t),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},t.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},t.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},t.prototype.update=function(t){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(t),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,null==this.finalEndVal&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},t.prototype.printValue=function(t){var n;if(this.el){var e=this.formattingFn(t);(null===(n=this.options.plugin)||void 0===n?void 0:n.render)?this.options.plugin.render(this.el,e):"INPUT"===this.el.tagName?this.el.value=e:"text"===this.el.tagName||"tspan"===this.el.tagName?this.el.textContent=e:this.el.innerHTML=e}},t.prototype.ensureNumber=function(t){return"number"==typeof t&&!isNaN(t)},t.prototype.validateValue=function(t){var n=Number(t);return this.ensureNumber(n)?n:(this.error="[CountUp] invalid start or end value: ".concat(t),null)},t.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},t}()},75036:function(t,n,e){var r=e(2265),i=e(30375);function o(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function a(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?o(Object(e),!0).forEach(function(n){!function(t,n,e){var r;(n="symbol"==typeof(r=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(n,"string"))?r:String(r))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e}(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):o(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t}).apply(this,arguments)}function l(t,n){if(null==t)return{};var e,r,i=function(t,n){if(null==t)return{};var e,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)e=o[r],n.indexOf(e)>=0||(i[e]=t[e]);return i}(t,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)e=o[r],!(n.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(t,e)&&(i[e]=t[e])}return i}function u(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}var c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect;function f(t){var n=r.useRef(t);return c(function(){n.current=t}),r.useCallback(function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return n.current.apply(void 0,e)},[])}var p=function(t,n){var e=n.decimal,r=n.decimals,o=n.duration,a=n.easingFn,s=n.end,l=n.formattingFn,u=n.numerals,c=n.prefix,f=n.separator,p=n.start,d=n.suffix,h=n.useEasing,m=n.useGrouping,g=n.useIndianSeparators,y=n.enableScrollSpy,v=n.scrollSpyDelay,b=n.scrollSpyOnce,E=n.plugin;return new i.CountUp(t,s,{startVal:p,duration:o,decimal:e,decimalPlaces:r,easingFn:a,formattingFn:l,numerals:u,separator:f,prefix:c,suffix:d,plugin:E,useEasing:h,useIndianSeparators:g,useGrouping:m,enableScrollSpy:y,scrollSpyDelay:v,scrollSpyOnce:b})},d=["ref","startOnMount","enableReinitialize","delay","onEnd","onStart","onPauseResume","onReset","onUpdate"],h={decimal:".",separator:",",delay:null,prefix:"",suffix:"",duration:2,start:0,decimals:0,startOnMount:!0,enableReinitialize:!0,useEasing:!0,useGrouping:!0,useIndianSeparators:!1},m=function(t){var n=Object.fromEntries(Object.entries(t).filter(function(t){return void 0!==(function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,s=[],l=!0,u=!1;try{if(o=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;l=!1}else for(;!(l=(r=o.call(e)).done)&&(s.push(r.value),s.length!==n);l=!0);}catch(t){u=!0,i=t}finally{try{if(!l&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(t,2)||function(t,n){if(t){if("string"==typeof t)return u(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);if("Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(t,n)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[1]})),e=r.useMemo(function(){return a(a({},h),n)},[t]),i=e.ref,o=e.startOnMount,s=e.enableReinitialize,c=e.delay,m=e.onEnd,g=e.onStart,y=e.onPauseResume,v=e.onReset,b=e.onUpdate,E=l(e,d),S=r.useRef(),V=r.useRef(),O=r.useRef(!1),w=f(function(){return p("string"==typeof i?i:i.current,E)}),F=f(function(t){var n=S.current;if(n&&!t)return n;var e=w();return S.current=e,e}),j=f(function(){var t=function(){return F(!0).start(function(){null==m||m({pauseResume:P,reset:A,start:x,update:N})})};c&&c>0?V.current=setTimeout(t,1e3*c):t(),null==g||g({pauseResume:P,reset:A,update:N})}),P=f(function(){F().pauseResume(),null==y||y({reset:A,start:x,update:N})}),A=f(function(){F().el&&(V.current&&clearTimeout(V.current),F().reset(),null==v||v({pauseResume:P,start:x,update:N}))}),N=f(function(t){F().update(t),null==b||b({pauseResume:P,reset:A,start:x})}),x=f(function(){A(),j()}),D=f(function(t){o&&(t&&A(),j())});return r.useEffect(function(){O.current?s&&D(!0):(O.current=!0,D())},[s,O,D,c,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.formattingFn]),r.useEffect(function(){return function(){A()}},[A]),{start:x,pauseResume:P,reset:A,update:N,getCountUp:F}},g=["className","redraw","containerProps","children","style"];n.ZP=function(t){var n=t.className,e=t.redraw,i=t.containerProps,o=t.children,u=t.style,c=l(t,g),p=r.useRef(null),d=r.useRef(!1),h=m(a(a({},c),{},{ref:p,startOnMount:"function"!=typeof o||0===t.delay,enableReinitialize:!1})),y=h.start,v=h.reset,b=h.update,E=h.pauseResume,S=h.getCountUp,V=f(function(){y()}),O=f(function(n){t.preserveValue||v(),b(n)}),w=f(function(){if("function"==typeof t.children&&!(p.current instanceof Element)){console.error('Couldn\'t find attached element to hook the CountUp instance into! Try to attach "containerRef" from the render prop to a an Element, eg. <span ref={containerRef} />.');return}S()});r.useEffect(function(){w()},[w]),r.useEffect(function(){d.current&&O(t.end)},[t.end,O]);var F=e&&t;return(r.useEffect(function(){e&&d.current&&V()},[V,e,F]),r.useEffect(function(){!e&&d.current&&V()},[V,e,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.className,t.formattingFn]),r.useEffect(function(){d.current=!0},[]),"function"==typeof o)?o({countUpRef:p,start:y,reset:v,update:b,pauseResume:E,getCountUp:S}):r.createElement("span",s({className:n,ref:p,style:u},i),void 0!==t.start?S().formattingFn(t.start):"")}},66679:function(t,n,e){e.d(n,{Z:function(){return i}});let r={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function i(){let t,n,e,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=!1,s=!0,l=!1,u=0;function c(){if(e||!s)return;a||n.emit("autoplay:play");let{ownerWindow:r}=n.internalEngine();r.clearInterval(u),u=r.setInterval(m,t.delay),a=!0}function f(){if(e)return;a&&n.emit("autoplay:stop");let{ownerWindow:t}=n.internalEngine();t.clearInterval(u),u=0,a=!1}function p(){if(d())return s=a,f();s&&c()}function d(){let{ownerDocument:t}=n.internalEngine();return"hidden"===t.visibilityState}function h(t){void 0!==t&&(l=t),s=!0,c()}function m(){let{index:e}=n.internalEngine(),r=e.clone().add(1).get(),i=n.scrollSnapList().length-1;t.stopOnLastSnap&&r===i&&f(),n.canScrollNext()?n.scrollNext(l):n.scrollTo(0,l)}return{name:"autoplay",options:o,init:function(a,u){n=a;let{mergeOptions:h,optionsAtMedia:m}=u,g=h(r,i.globalOptions);if(t=m(h(g,o)),n.scrollSnapList().length<=1)return;l=t.jump,e=!1;let{eventStore:y,ownerDocument:v}=n.internalEngine(),b=n.rootNode(),E=t.rootNode&&t.rootNode(b)||b,S=n.containerNode();n.on("pointerDown",f),t.stopOnInteraction||n.on("pointerUp",c),t.stopOnMouseEnter&&(y.add(E,"mouseenter",()=>{s=!1,f()}),t.stopOnInteraction||y.add(E,"mouseleave",()=>{s=!0,c()})),t.stopOnFocusIn&&(y.add(S,"focusin",f),t.stopOnInteraction||y.add(S,"focusout",c)),y.add(v,"visibilitychange",p),t.playOnInit&&!d()&&c()},destroy:function(){n.off("pointerDown",f).off("pointerUp",c),f(),e=!0,a=!1},play:h,stop:function(){a&&f()},reset:function(){a&&h()},isPlaying:function(){return a}}}i.globalOptions=void 0},8102:function(t,n,e){function r(t,n,e){return Math.min(Math.max(t,n),e)}function i(t){return"number"==typeof t&&!isNaN(t)}function o(){let t,n,e,o,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=[],l=0,u=0,c=0,f=!1;function p(){g(t.selectedScrollSnap(),1)}function d(){f=!1}function h(){f=!1,l=0,u=0}function m(){let n=t.internalEngine().scrollBody.duration();u=n?0:1,f=!0,n||p()}function g(n,e){t.scrollSnapList().forEach((i,o)=>{let a=Math.abs(e),u=s[o],p=o===n,d=r(p?u+a:u-a,0,1);s[o]=d;let h=p&&f,m=t.previousScrollSnap();h&&(s[m]=1-d),p&&function(n,e){let{index:r,dragHandler:i,scrollSnaps:o}=t.internalEngine(),a=i.pointerDown(),s=1/(o.length-1),u=n,f=a?t.selectedScrollSnap():t.previousScrollSnap();if(a&&u===f){let t=-1*Math.sign(l);u=f,f=r.clone().set(f).add(t).get()}c=f*s+(u-f)*s*e}(n,d),function(n){let e=t.internalEngine().slideRegistry[n],{scrollSnaps:r,containerRect:i}=t.internalEngine(),o=s[n];e.forEach(e=>{let a=t.slideNodes()[e].style,s=parseFloat(o.toFixed(2)),l=s>0,u=function(n){let{axis:e}=t.internalEngine(),r=e.scroll.toUpperCase();return"translate".concat(r,"(").concat(e.direction(n),"px)")}(l?r[n]:i.width+2);l&&(a.transform=u),a.opacity=s.toString(),a.pointerEvents=o>.5?"auto":"none",l||(a.transform=u)})}(o)})}function y(){let{dragHandler:n,index:e,scrollBody:r}=t.internalEngine(),i=t.selectedScrollSnap();if(!n.pointerDown())return i;let o=Math.sign(r.velocity()),a=Math.sign(l),s=e.clone().set(i).add(-1*o).get();return o&&a?a===o?s:i:null}let v=e=>{let{dragHandler:r,scrollBody:o}=e.internalEngine(),a=r.pointerDown(),c=o.velocity(),f=o.duration(),p=y(),d=!i(p);if(a){if(!c)return;l+=c,u=Math.abs(c/n),function(n){let{scrollSnaps:e,location:r,target:o}=t.internalEngine();i(n)&&!(s[n]<.5)&&(r.set(e[n]),o.set(r))}(p)}if(!a){if(!f||d)return;u+=(1-s[p])/f,u*=.68}d||g(p,u)};function b(){let{target:n,location:e}=t.internalEngine(),r=n.get()-e.get(),o=y(),a=!i(o);return v(t),!(a||Math.abs(r)>=1)&&s[o]>.999}function E(){return c}return{name:"fade",options:a,init:function(i){let a=(t=i).selectedScrollSnap(),{scrollBody:l,containerRect:u,axis:c}=t.internalEngine();n=r(.75*c.measureSize(u),200,500),f=!1,s=t.scrollSnapList().map((t,n)=>n===a?1:0),e=l.settled,o=t.scrollProgress,l.settled=b,t.scrollProgress=E,t.on("select",m).on("slideFocus",p).on("pointerDown",h).on("pointerUp",d),function(){let{translate:n,slideLooper:e}=t.internalEngine();n.clear(),n.toggleActive(!1),e.loopPoints.forEach(t=>{let{translate:n}=t;n.clear(),n.toggleActive(!1)})}(),p()},destroy:function(){let{scrollBody:n}=t.internalEngine();n.settled=e,t.scrollProgress=o,t.off("select",m).off("slideFocus",p).off("pointerDown",h).off("pointerUp",d),t.slideNodes().forEach(t=>{let n=t.style;n.opacity="",n.transform="",n.pointerEvents="",t.getAttribute("style")||t.removeAttribute("style")})}}}e.d(n,{Z:function(){return o}}),o.globalOptions=void 0}}]);