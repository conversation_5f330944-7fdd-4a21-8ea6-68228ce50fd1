"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8062],{18062:function(t,e,n){n.d(e,{Z:function(){return k}});var r=n(2265);function i(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function o(t,e){let n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&n.every(n=>{let r=t[n],u=e[n];return"function"==typeof r?"".concat(r)==="".concat(u):i(r)&&i(u)?o(r,u):r===u})}function u(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function c(t){return"number"==typeof t}function a(t){return"string"==typeof t}function l(t){return"boolean"==typeof t}function s(t){return"[object Object]"===Object.prototype.toString.call(t)}function f(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return y(t).map(Number)}function g(t){return t[m(t)]}function m(t){return Math.max(0,t.length-1)}function h(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Array.from(Array(t),(t,n)=>e+n)}function y(t){return Object.keys(t)}function v(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function b(){let t=[],e={add:function(n,r,i){let o,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return"addEventListener"in n?(n.addEventListener(r,i,u),o=()=>n.removeEventListener(r,i,u)):(n.addListener(i),o=()=>n.removeListener(i)),t.push(o),e},clear:function(){t=t.filter(t=>t())}};return e}function x(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=f(t-e);function r(n){return n<t||n>e}return{length:n,max:e,min:t,constrain:function(n){return r(n)?n<t?t:e:n},reachedAny:r,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return n?t-n*Math.ceil((t-e)/n):t}}}function w(t){let e=t;function n(t){return c(t)?t:t.get()}return{get:function(){return e},set:function(t){e=n(t)},add:function(t){e+=n(t)},subtract:function(t){e-=n(t)}}}function S(t,e){let n="x"===t.scroll?function(t){return"translate3d(".concat(t,"px,0px,0px)")}:function(t){return"translate3d(0px,".concat(t,"px,0px)")},r=e.style,i=!1;return{clear:function(){i||(r.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){i||(r.transform=n(t.direction(e)))},toggleActive:function(t){i=!t}}}let E={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0};function O(t,e,n){let r,i,o,u,k;let D=t.ownerDocument,I=D.defaultView,L=function(t){function e(t,e){return function t(e,n){return[e,n].reduce((e,n)=>(y(n).forEach(r=>{let i=e[r],o=n[r],u=s(i)&&s(o);e[r]=u?t(i,o):o}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(n){let r=n.breakpoints||{},i=y(r).filter(e=>t.matchMedia(e).matches).map(t=>r[t]).reduce((t,n)=>e(t,n),{});return e(n,i)},optionsMediaQueries:function(e){return e.map(t=>y(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(I),A=(k=[],{init:function(t,e){return(k=e.filter(t=>{let{options:e}=t;return!1!==L.optionsAtMedia(e).active})).forEach(e=>e.init(t,L)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){k=k.filter(t=>t.destroy())}}),M=b(),F=function(){let t,e={},n={init:function(e){t=e},emit:function(r){return(e[r]||[]).forEach(e=>e(t,r)),n},off:function(t,r){return e[t]=(e[t]||[]).filter(t=>t!==r),n},on:function(t,r){return e[t]=(e[t]||[]).concat([r]),n},clear:function(){e={}}};return n}(),{mergeOptions:j,optionsAtMedia:N,optionsMediaQueries:P}=L,{on:T,off:H,emit:V}=F,z=!1,C=j(E,O.globalOptions),B=j(C),R=[];function q(e,n){!z&&(B=N(C=j(C,e)),R=n||R,function(){let{container:e,slides:n}=B;o=(a(e)?t.querySelector(e):e)||t.children[0];let r=a(n)?o.querySelectorAll(n):n;u=[].slice.call(r||o.children)}(),r=function e(n){let r=function(t,e,n,r,i,o,u){let s,E;let{align:O,axis:k,direction:D,startIndex:I,loop:L,duration:A,dragFree:M,dragThreshold:F,inViewThreshold:j,slidesToScroll:N,skipSnaps:P,containScroll:T,watchResize:H,watchSlides:V,watchDrag:z}=o,C={measure:function(t){let{offsetTop:e,offsetLeft:n,offsetWidth:r,offsetHeight:i}=t;return{top:e,right:n+r,bottom:e+i,left:n,width:r,height:i}}},B=C.measure(e),R=n.map(C.measure),q=function(t,e){let n="rtl"===e,r="y"===t,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(t){let{height:e,width:n}=t;return r?e:n},direction:function(t){return t*i}}}(k,D),U=q.measureSize(B),_={measure:function(t){return t/100*U}},J=function(t,e){let n={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(r,i){return a(t)?n[t](r):t(e,r,i)}}}(O,U),X=!L&&!!T,{slideSizes:Q,slideSizesWithGaps:Y,startGap:Z,endGap:G}=function(t,e,n,r,i,o){let{measureSize:u,startEdge:c,endEdge:a}=t,l=n[0]&&i,s=function(){if(!l)return 0;let t=n[0];return f(e[c]-t[c])}(),d=l?parseFloat(o.getComputedStyle(g(r)).getPropertyValue("margin-".concat(a))):0,p=n.map(u),h=n.map((t,e,n)=>{let r=e===m(n);return e?r?p[e]+d:n[e+1][c]-t[c]:p[e]+s}).map(f);return{slideSizes:p,slideSizesWithGaps:h,startGap:s,endGap:d}}(q,B,R,n,L||!!T,i),K=function(t,e,n,r,i,o,u,a,l){let{startEdge:s,endEdge:d,direction:h}=t,y=c(n);return{groupSlides:function(t){return y?p(t).filter(t=>t%n==0).map(e=>t.slice(e,e+n)):t.length?p(t).reduce((n,c,l)=>{let p=g(n)||0,y=c===m(t),v=i[s]-o[p][s],b=i[s]-o[c][d],x=r||0!==p?0:h(u),w=f(b-(!r&&y?h(a):0)-(v+x));return l&&w>e+2&&n.push(c),y&&n.push(t.length),n},[]).map((e,n,r)=>{let i=Math.max(r[n-1]||0);return t.slice(i,e)}):[]}}}(q,U,N,L,B,R,Z,G,0),{snaps:W,snapsAligned:$}=function(t,e,n,r,i){let{startEdge:o,endEdge:u}=t,{groupSlides:c}=i,a=c(r).map(t=>g(t)[u]-t[0][o]).map(f).map(e.measure),l=r.map(t=>n[o]-t[o]).map(t=>-f(t)),s=c(l).map(t=>t[0]).map((t,e)=>t+a[e]);return{snaps:l,snapsAligned:s}}(q,J,B,R,K),tt=-g(W)+g(Y),{snapsContained:te,scrollContainLimit:tn}=function(t,e,n,r,i){let o=x(-e+t,0),u=n.map((t,e)=>{let{min:r,max:i}=o,u=o.constrain(t),c=e===m(n);return e?c||1>f(r-u)?r:1>f(i-u)?i:u:i}).map(t=>parseFloat(t.toFixed(3))),c=function(){let t=u[0],e=g(u);return x(u.lastIndexOf(t),u.indexOf(e)+1)}();return{snapsContained:function(){if(e<=t+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=c;return u.slice(n,i)}(),scrollContainLimit:c}}(U,tt,$,T,0),tr=X?te:$,{limit:ti}=function(t,e,n){let r=e[0];return{limit:x(n?r-t:g(e),r)}}(tt,tr,L),to=function t(e,n,r){let{constrain:i}=x(0,e),o=e+1,u=c(n);function c(t){return r?f((o+t)%o):i(t)}function a(){return t(e,u,r)}let l={get:function(){return u},set:function(t){return u=c(t),l},add:function(t){return a().set(u+t)},clone:a};return l}(m(tr),I,L),tu=to.clone(),tc=p(n),ta=t=>{let{dragHandler:e,scrollBody:n,scrollBounds:r,options:{loop:i}}=t;i||r.constrain(e.pointerDown()),n.seek()},tl=(t,e)=>{let{scrollBody:n,translate:r,location:i,offsetLocation:o,scrollLooper:u,slideLooper:c,dragHandler:a,animation:l,eventHandler:s,options:{loop:f}}=t,d=n.velocity(),p=n.settled();p&&!a.pointerDown()&&(l.stop(),s.emit("settle")),p||s.emit("scroll"),o.set(i.get()-d+d*e),f&&(u.loop(n.direction()),c.loop()),r.to(o.get())},ts=function(t,e,n,r){let i=b(),o=1e3/60,u=null,c=0,a=0;function l(t){if(!a)return;u||(u=t);let i=t-u;for(u=t,c+=i;c>=o;)n(),c-=o;r(f(c/o)),a&&e.requestAnimationFrame(l)}function s(){e.cancelAnimationFrame(a),u=null,c=0,a=0}return{init:function(){i.add(t,"visibilitychange",()=>{t.hidden&&(u=null,c=0)})},destroy:function(){s(),i.clear()},start:function(){a||(a=e.requestAnimationFrame(l))},stop:s,update:n,render:r}}(r,i,()=>ta(tE),t=>tl(tE,t)),tf=tr[to.get()],td=w(tf),tp=w(tf),tg=w(tf),tm=function(t,e,n,r,i){let o=0,u=0,c=r,a=i,l=t.get(),s=0;function p(t){return c=t,m}function g(t){return a=t,m}let m={direction:function(){return u},duration:function(){return c},velocity:function(){return o},seek:function(){let e=n.get()-t.get(),r=0;return c?(o+=e/c,o*=a,l+=o,t.add(o),r=l-s):(o=0,t.set(n),r=e),u=d(r),s=l,m},settled:function(){return .001>f(n.get()-e.get())},useBaseFriction:function(){return g(i)},useBaseDuration:function(){return p(r)},useFriction:g,useDuration:p};return m}(td,tp,tg,A,.68),th=function(t,e,n,r,i){let{reachedAny:o,removeOffset:u,constrain:c}=r;function a(t){return t.concat().sort((t,e)=>f(t)-f(e))[0]}function l(e,r){let i=[e,e+n,e-n];if(!t)return e;if(!r)return a(i);let o=i.filter(t=>d(t)===r);return o.length?a(o):g(i)-n}return{byDistance:function(n,r){let a=i.get()+n,{index:s,distance:d}=function(n){let r=t?u(n):c(n),{index:i}=e.map((t,e)=>({diff:l(t-r,0),index:e})).sort((t,e)=>f(t.diff)-f(e.diff))[0];return{index:i,distance:r}}(a),p=!t&&o(a);if(!r||p)return{index:s,distance:n};let g=n+l(e[s]-d,0);return{index:s,distance:g}},byIndex:function(t,n){let r=l(e[t]-i.get(),n);return{index:t,distance:r}},shortcut:l}}(L,tr,tt,ti,tg),ty=function(t,e,n,r,i,o,u){function c(i){let c=i.distance,a=i.index!==e.get();o.add(c),c&&(r.duration()?t.start():(t.update(),t.render(1),t.update())),a&&(n.set(e.get()),e.set(i.index),u.emit("select"))}return{distance:function(t,e){c(i.byDistance(t,e))},index:function(t,n){let r=e.clone().set(t);c(i.byIndex(r.get(),n))}}}(ts,to,tu,tm,th,tg,u),tv=function(t){let{max:e,length:n}=t;return{get:function(t){return n?-((t-e)/n):0}}}(ti),tb=b(),tx=function(t,e,n,r){let i;let o={},u=null,c=null,a=!1;return{init:function(){i=new IntersectionObserver(t=>{a||(t.forEach(t=>{o[e.indexOf(t.target)]=t}),u=null,c=null,n.emit("slidesInView"))},{root:t.parentElement,threshold:r}),e.forEach(t=>i.observe(t))},destroy:function(){i&&i.disconnect(),a=!0},get:function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(t&&u)return u;if(!t&&c)return c;let e=y(o).reduce((e,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(t&&i||!t&&!i)&&e.push(r),e},[]);return t&&(u=e),t||(c=e),e}}}(e,n,u,j),{slideRegistry:tw}=function(t,e,n,r,i,o){let{groupSlides:u}=i,{min:c,max:a}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:t&&"keepSnaps"!==e?r.slice(c,a).map((t,e,n)=>{let r=e===m(n);return e?r?h(m(o)-g(n)[0]+1,g(n)[0]):t:h(g(n[0])+1)}):r}()}}(X,T,tr,tn,K,tc),tS=function(t,e,n,r,i,o,u){let a=0;function l(t){"Tab"===t.code&&(a=new Date().getTime())}function s(l){o.add(l,"focus",()=>{if(new Date().getTime()-a>10)return;t.scrollLeft=0;let o=e.indexOf(l),s=n.findIndex(t=>t.includes(o));c(s)&&(i.useDuration(0),r.index(s,0),u.emit("slideFocus"))},{passive:!0,capture:!0})}return{init:function(){o.add(document,"keydown",l,!1),e.forEach(s)}}}(t,n,tw,ty,tm,tb,u),tE={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:B,slideRects:R,animation:ts,axis:q,dragHandler:function(t,e,n,r,i,o,u,c,a,s,p,g,m,h,y,w,S,E,O){let{cross:k,direction:D}=t,I=["INPUT","SELECT","TEXTAREA"],L={passive:!1},A=b(),M=b(),F=x(50,225).constrain(h.measure(20)),j={mouse:300,touch:400},N={mouse:500,touch:600},P=y?43:25,T=!1,H=0,V=0,z=!1,C=!1,B=!1,R=!1;function q(t){if(!v(t,r)&&t.touches.length>=2)return U(t);let e=o.readPoint(t),n=o.readPoint(t,k),u=f(e-H),a=f(n-V);if(!C&&!R&&(!t.cancelable||!(C=u>a)))return U(t);let l=o.pointerMove(t);u>w&&(B=!0),s.useFriction(.3).useDuration(.75),c.start(),i.add(D(l)),t.preventDefault()}function U(t){let e=p.byDistance(0,!1).index!==g.get(),n=o.pointerUp(t)*(y?N:j)[R?"mouse":"touch"],r=function(t,e){let n=g.add(-1*d(t)),r=p.byDistance(t,!y).distance;return y||f(t)<F?r:S&&e?.5*r:p.byIndex(n.get(),0).distance}(D(n),e),i=function(t,e){var n,r;if(0===t||0===e||f(t)<=f(e))return 0;let i=(n=f(t),r=f(e),f(n-r));return f(i/t)}(n,r);C=!1,z=!1,M.clear(),s.useDuration(P-10*i).useFriction(.68+i/50),a.distance(r,!y),R=!1,m.emit("pointerUp")}function _(t){B&&(t.stopPropagation(),t.preventDefault(),B=!1)}return{init:function(t){O&&A.add(e,"dragstart",t=>t.preventDefault(),L).add(e,"touchmove",()=>void 0,L).add(e,"touchend",()=>void 0).add(e,"touchstart",c).add(e,"mousedown",c).add(e,"touchcancel",U).add(e,"contextmenu",U).add(e,"click",_,!0);function c(c){(l(O)||O(t,c))&&function(t){let c=v(t,r);R=c,B=y&&c&&!t.buttons&&T,T=f(i.get()-u.get())>=2,c&&0!==t.button||function(t){let e=t.nodeName||"";return I.includes(e)}(t.target)||(z=!0,o.pointerDown(t),s.useFriction(0).useDuration(0),i.set(u),function(){let t=R?n:e;M.add(t,"touchmove",q,L).add(t,"touchend",U).add(t,"mousemove",q,L).add(t,"mouseup",U)}(),H=o.readPoint(t),V=o.readPoint(t,k),m.emit("pointerDown"))}(c)}},destroy:function(){A.clear(),M.clear()},pointerDown:function(){return z}}}(q,t,r,i,tg,function(t,e){let n,r;function i(t){return t.timeStamp}function o(n,r){let i=r||t.scroll;return(v(n,e)?n:n.touches[0])["client".concat("x"===i?"X":"Y")]}return{pointerDown:function(t){return n=t,r=t,o(t)},pointerMove:function(t){let e=o(t)-o(r),u=i(t)-i(n)>170;return r=t,u&&(n=t),e},pointerUp:function(t){if(!n||!r)return 0;let e=o(r)-o(n),u=i(t)-i(n),c=i(t)-i(r)>170,a=e/u;return u&&!c&&f(a)>.1?a:0},readPoint:o}}(q,i),td,ts,ty,tm,th,to,u,_,M,F,P,0,z),eventStore:tb,percentOfView:_,index:to,indexPrevious:tu,limit:ti,location:td,offsetLocation:tp,options:o,resizeHandler:function(t,e,n,r,i,o,u){let c,a;let s=[],d=!1;function p(t){return i.measureSize(u.measure(t))}return{init:function(i){o&&(a=p(t),s=r.map(p),c=new ResizeObserver(u=>{!d&&(l(o)||o(i,u))&&function(o){for(let u of o){let o=u.target===t,c=r.indexOf(u.target),l=o?a:s[c];if(f(p(o?t:r[c])-l)>=.5){n.requestAnimationFrame(()=>{i.reInit(),e.emit("resize")});break}}}(u)}),[t].concat(r).forEach(t=>c.observe(t)))},destroy:function(){c&&c.disconnect(),d=!0}}}(e,u,i,n,q,H,C),scrollBody:tm,scrollBounds:function(t,e,n,r,i){let o=i.measure(10),u=i.measure(50),c=x(.1,.99),a=!1;return{constrain:function(i){if(!(!a&&t.reachedAny(n.get())&&t.reachedAny(e.get())))return;let l=t.reachedMin(e.get())?"min":"max",s=f(t[l]-e.get()),d=n.get()-e.get(),p=c.constrain(s/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(t.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(t){a=!t}}}(ti,tp,tg,tm,_),scrollLooper:function(t,e,n,r){let{reachedMin:i,reachedMax:o}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?o(n.get()):-1===e&&i(n.get())))return;let u=-1*e*t;r.forEach(t=>t.add(u))}}}(tt,ti,tp,[td,tp,tg]),scrollProgress:tv,scrollSnapList:tr.map(tv.get),scrollSnaps:tr,scrollTarget:th,scrollTo:ty,slideLooper:function(t,e,n,r,i,o,u,c,a){let l=p(i),s=g(d(p(i).reverse(),u[0]),n,!1).concat(g(d(l,e-u[0]-1),-n,!0));function f(t,e){return t.reduce((t,e)=>t-i[e],e)}function d(t,e){return t.reduce((t,n)=>f(t,e)>0?t.concat([n]):t,[])}function g(i,u,l){let s=o.map((t,n)=>({start:t-r[n]+.5+u,end:t+e-.5+u}));return i.map(e=>{let r=l?0:-n,i=l?n:0,o=s[e][l?"end":"start"];return{index:e,loopPoint:o,slideLocation:w(-1),translate:S(t,a[e]),target:()=>c.get()>o?r:i}})}return{canLoop:function(){return s.every(t=>{let{index:n}=t;return .1>=f(l.filter(t=>t!==n),e)})},clear:function(){s.forEach(t=>t.translate.clear())},loop:function(){s.forEach(t=>{let{target:e,translate:n,slideLocation:r}=t,i=e();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:s}}(q,U,tt,Q,Y,W,tr,tp,n),slideFocus:tS,slidesHandler:(E=!1,{init:function(t){V&&(s=new MutationObserver(e=>{!E&&(l(V)||V(t,e))&&function(e){for(let n of e)if("childList"===n.type){t.reInit(),u.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){s&&s.disconnect(),E=!0}}),slidesInView:tx,slideIndexes:tc,slideRegistry:tw,slidesToScroll:K,target:tg,translate:S(q,e)};return tE}(t,o,u,D,I,n,F);return n.loop&&!r.slideLooper.canLoop()?e(Object.assign({},n,{loop:!1})):r}(B),P([C,...R.map(t=>{let{options:e}=t;return e})]).forEach(t=>M.add(t,"change",U)),B.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(),r.eventHandler.init(Q),r.resizeHandler.init(Q),r.slidesHandler.init(Q),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(Q),i=A.init(Q,R)))}function U(t,e){let n=X();_(),q(j({startIndex:n},t),e),F.emit("reInit")}function _(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),A.destroy(),M.clear()}function J(t,e,n){B.active&&!z&&(r.scrollBody.useBaseFriction().useDuration(!0===e?0:B.duration),r.scrollTo.index(t,n||0))}function X(){return r.index.get()}let Q={canScrollNext:function(){return r.index.add(1).get()!==X()},canScrollPrev:function(){return r.index.add(-1).get()!==X()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){z||(z=!0,M.clear(),_(),F.emit("destroy"),F.clear())},off:H,on:T,emit:V,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:U,rootNode:function(){return t},scrollNext:function(t){J(r.index.add(1).get(),t,-1)},scrollPrev:function(t){J(r.index.add(-1).get(),t,1)},scrollProgress:function(){return r.scrollProgress.get(r.location.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:J,selectedScrollSnap:X,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return q(e,n),setTimeout(()=>F.emit("init"),0),Q}function k(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=(0,r.useRef)(t),i=(0,r.useRef)(e),[c,a]=(0,r.useState)(),[l,s]=(0,r.useState)(),f=(0,r.useCallback)(()=>{c&&c.reInit(n.current,i.current)},[c]);return(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&l){O.globalOptions=k.globalOptions;let t=O(l,n.current,i.current);return a(t),()=>t.destroy()}a(void 0)},[l,a]),(0,r.useEffect)(()=>{o(n.current,t)||(n.current=t,f())},[t,f]),(0,r.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let n=u(t),r=u(e);return n.every((t,e)=>o(t,r[e]))}(i.current,e)&&(i.current=e,f())},[e,f]),[s,c]}O.globalOptions=void 0,k.globalOptions=void 0}}]);