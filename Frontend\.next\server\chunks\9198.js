"use strict";exports.id=9198,exports.ids=[9198],exports.modules={2813:(e,t,i)=>{i.d(t,{default:()=>n.a});var r=i(37751),n=i.n(r)},16274:(e,t,i)=>{i.d(t,{default:()=>n.a});var r=i(48026),n=i.n(r)},96987:(e,t,i)=>{let{createProxy:r}=i(86843);e.exports=r("C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\node_modules\\next\\dist\\client\\image-component.js")},48026:(e,t,i)=>{let{createProxy:r}=i(86843);e.exports=r("C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\node_modules\\next\\dist\\client\\link.js")},34470:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(46031);let r=i(76184),n=i(64180);function o(e){return void 0!==e.default}function s(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i;let l,a,d,{src:u,sizes:c,unoptimized:f=!1,priority:m=!1,loading:g,className:h,quality:p,width:b,height:v,fill:w=!1,style:y,onLoad:x,onLoadingComplete:j,placeholder:S="empty",blurDataURL:_,fetchPriority:C,layout:P,objectFit:z,objectPosition:O,lazyBoundary:E,lazyRoot:I,...M}=e,{imgConf:D,showAltText:R,blurComplete:k,defaultLoader:G}=t,A=D||n.imageConfigDefault;if("allSizes"in A)l=A;else{let e=[...A.deviceSizes,...A.imageSizes].sort((e,t)=>e-t),t=A.deviceSizes.sort((e,t)=>e-t);l={...A,allSizes:e,deviceSizes:t}}let F=M.loader||G;delete M.loader,delete M.srcSet;let N="__next_img_default"in F;if(N){if("custom"===l.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=F;F=t=>{let{config:i,...r}=t;return e(r)}}if(P){"fill"===P&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(y={...y,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!c&&(c=t)}let B="",L=s(b),U=s(v);if("object"==typeof(i=u)&&(o(i)||void 0!==i.src)){let e=o(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(a=e.blurWidth,d=e.blurHeight,_=_||e.blurDataURL,B=e.src,!w){if(L||U){if(L&&!U){let t=L/e.width;U=Math.round(e.height*t)}else if(!L&&U){let t=U/e.height;L=Math.round(e.width*t)}}else L=e.width,U=e.height}}let W=!m&&("lazy"===g||void 0===g);(!(u="string"==typeof u?u:B)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,W=!1),l.unoptimized&&(f=!0),N&&u.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(f=!0),m&&(C="high");let V=s(p),H=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:O}:{},R?{}:{color:"transparent"},y),T=k||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:L,heightInt:U,blurWidth:a,blurHeight:d,blurDataURL:_||"",objectFit:H.objectFit})+'")':'url("'+S+'")',q=T?{backgroundSize:H.objectFit||"cover",backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:T}:{},J=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:o,sizes:s,loader:l}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:a,kind:d}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),u=a.length-1;return{sizes:s||"w"!==d?s:"100vw",srcSet:a.map((e,r)=>l({config:t,src:i,quality:o,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:l({config:t,src:i,quality:o,width:a[u]})}}({config:l,src:u,unoptimized:f,width:L,quality:V,sizes:c,loader:F});return{props:{...M,loading:W?"lazy":g,fetchPriority:C,width:L,height:U,decoding:"async",className:h,style:{...H,...q},sizes:J.sizes,srcSet:J.srcSet,src:J.src},meta:{unoptimized:f,priority:m,placeholder:S,fill:w}}}},76184:(e,t)=>{function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:o,objectFit:s}=e,l=r?40*r:t,a=n?40*n:i,d=l&&a?"viewBox='0 0 "+l+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},64180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},37751:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getImageProps:function(){return l},default:function(){return a}});let r=i(46783),n=i(34470),o=i(96987),s=r._(i(94534)),l=e=>{let{props:t}=(0,n.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}},a=o.Image},94534:(e,t)=>{function i(e){let{config:t,src:i,width:r,quality:n}=e;return t.path+"?url="+encodeURIComponent(i)+"&w="+r+"&q="+(n||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},46031:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}}};