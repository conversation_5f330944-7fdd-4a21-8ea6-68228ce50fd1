3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","api-gateway-in-microservices-architecture","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","api-gateway-in-microservices-architecture","d"],{"children":["__PAGE__?{\"blogDetails\":\"api-gateway-in-microservices-architecture\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","api-gateway-in-microservices-architecture","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
12:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
11:Ta83,[{"@context":"https://schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"What is the difference between an API and an API gateway?","acceptedAnswer":{"@type":"Answer","text":"An API conducts specific functions within a software or system. However, an API Gateway offers features like authentication and routing by sharing and controlling different requests to various APIs."}},{"@type":"Question","name":"How do microservices communicate with each other?","acceptedAnswer":{"@type":"Answer","text":"Microservices communicate using two basic types of messaging patterns.Synchronous Communication: Here, using protocols such as HTTP or gRPC, one service calls the API exposed by another service. This accounts for synchronous communication, as the caller awaits a response from the receiver.Asynchronous Communication: A service sends a message without awaiting a response, and other services attend to this message later."}},{"@type":"Question","name":"Is it possible to create microservices without an API gateway?","acceptedAnswer":{"@type":"Answer","text":"Without an API gateway, your applications observe a direct client-to-microservice pattern. Whether you have 10 or 100 services, an API gateway offers a unified entry point for users. This can quickly become chaotic, with numerous client requests directed to different microservice endpoints that offer no flexibility between client apps and microservices. Such complexity can hinder innovation, making it difficult to update your microservices."}},{"@type":"Question","name":"How do I secure an API gateway in microservices?","acceptedAnswer":{"@type":"Answer","text":"Here are 5 best practices for securing your API gateway: Authentication: Use short-lived tokens for token-based authentication Authorization: For all API endpoints, employ Role-Based Access Control (RBAC) Rate Limiting: Learning user behavior and context to implement dynamic, layered rate limiting. CORS: Specify and limit allowed origins Logging: Incorporate real-time monitoring and detection of anomalies."}},{"@type":"Question","name":"What tools or platforms can be used to configure an API Gateway for microservices?","acceptedAnswer":{"@type":"Answer","text":"Here are the most famous tools and platforms that can be used to configure your API gateways. API management and testing tools: Azure API Management, Tyk Messaging tools: Apache Kafka, Google Cloud Pub/Sub, RabbitMQ Toolkits: Fabric8, Seneca Architectural frameworks: Goa, Kong, Micronaut Orchestration tools: Kubernetes, Mesos, Conductor Monitoring tools: Graphite, Graylog, Prometheus(+ Grafana) Serverless tools: Serverless, Apache Openwhisk, Claudia, IronFunctions"}}]}]13:T5f1,<p>The demand for <a href="https://marutitech.com/microservices-architecture-in-2019/" target="_blank" rel="noopener">microservices</a> is multiplying with each day passing. When you decide to develop an application as a set of microservices, you must ensure how the client will interact with your microservices.&nbsp;</p><p>But the question is, why use a microservice architecture pattern over a monolithic application?</p><p>Microservices architecture patterns have their importance for enabling agile development and developing complex applications. Unlike monolithic applications, which have only one set of endpoints, microservices architecture consists of multiple sets of fine-grained endpoints.</p><p>Microservices also enable you to interact with a comparatively small and flexible code base, and hence you can decide which bit of code you want to interact with and where it came from.&nbsp;</p><p>Working with microservice architecture is essential to increase your application efficiency. Working with the API gateway in microservice architecture makes your application more efficient and reduces your coding efforts. Also, at the same time, it reduces the applications’ errors, making them more effective and essential to use.&nbsp;</p><p>Considering the importance of API gateway in Microservice architecture pattern, we have presented a short guide explaining what API gateway is, the need for API gateway, its working and functionality, and much more details. Let’s get started to learn them.&nbsp;</p>14:T6b4,<p>An API stands for Application Program Interface, containing the set of tools to build applications. API Gateway is a reverse proxy that accepts all API calls, applies various services to fulfill the calls, and returns the appropriate output. In simple words, an API gateway is a server that summarizes the internal system architecture of the application.&nbsp;</p><p>The API gateway has responsibilities to provide the application client with API, perform request routing, provide authentication, load balancing, monitoring, composition, and protocol translation. When a client makes a request, the request transmits to the API gateway, and further, it routes to appropriate microservices.&nbsp;</p><p>There is a total of two different methods by which the API gateway handles the request made by the client:</p><ul><li>It routes the request of the client to a suitable service.</li><li>It spreads out the request of clients to multiple services.</li></ul><p><img src="https://cdn.marutitech.com/Richardson_microservices_part2_3_api_gateway_d38fad1a89.png" alt="how to configure api gateway in microservices" srcset="https://cdn.marutitech.com/thumbnail_Richardson_microservices_part2_3_api_gateway_d38fad1a89.png 157w,https://cdn.marutitech.com/small_Richardson_microservices_part2_3_api_gateway_d38fad1a89.png 500w,https://cdn.marutitech.com/medium_Richardson_microservices_part2_3_api_gateway_d38fad1a89.png 750w," sizes="100vw"></p><p>The best example of an API gateway is the Netflix API gateway. As you know, you can use the Netflix services on different devices such as laptops, Tablets, Smartphones, Televisions, etc. Here, the API gateway helps to provide the size that fits all API for its services.</p>15:T1082,<p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Here is the 7-step implementation of the API gateway in microservices</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>1. Determining your API gateway solution</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Depending on your budget, infrastructure, and features, you can choose from several commercial and open-source API gateway tools. Some suitable options include Spring Cloud Gateway, Azure API Gateway, Kong, and AWS API Gateway.&nbsp;</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>2. Creating your API</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">This phase involves selecting the endpoints, response, and request formats that your public API will facilitate, including the overall structure and functionality.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>3. Incorporate with service discovery</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">The API gateway must be familiar with the corresponding microservices to route requests appropriately. Choose your service discovery mechanism, such as Eureka or Consul, to locate the microservice instances and configure your gateway accordingly.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>4. Executing Primary Functionalities</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">An API gateway offers numerous features that facilitate your overall API experience. Here is a list of basic functionalities to configure:</span><br><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>Routing:&nbsp;</strong>Based on pre-defined rules like path, method, headers, and path, the gateway directs incoming requests to the exact microservice.</span><br><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>Security:</strong> Manage access to your APIs using an existing authentication or JWT tokens.</span><br><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>Rate Limiting:</strong> To avoid misuse, limit the requests a client can make within a specific timeframe.</span><br><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>Monitoring and Logging:</strong> Learn issues and optimize resources by observing API metrics such as usage and performance.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>5. Prioritize Security</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Prioritizing security first is pivotal, as API gateways often offer a single-point entry. To strengthen security, you can employ best practices like strong encryption, appropriate access controls, and timely software updates.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>6. Don’t Rest Until you Test</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Before planning your final deployment, ensure you’ve conducted 360-degree testing of your API gateways. This should incorporate testing several scenarios, error handling, and security measures.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>7. Supervise and Improvise</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Stringently observe performance and security once you deploy your API gateway. Plan how you can introduce enhancements to enhance your microservice architecture.</span></p>16:T748,<p>The main aim of the microservices API gateway is to speed up the development of services independently. It provides the functionality for the team to publish, update and monitor the microservices independently and effectively. This functionality is not found in the traditional API gateway because it focuses on managing the APIs internally.&nbsp;</p><p>The industries and firms have worked on understanding this internal system thoroughly by defining the APIs. Therefore the challenge of revealing the thousands of APIs to the clients led to the exposure of API gateways. With passing time, the API gateway becomes the center of infrastructure to access these APIs.</p><p>The below table shows a better representation of the difference between Microservices API Gateway vs. Traditional API Gateways.</p><figure class="table"><table><tbody><tr><td><strong>Traditional API Gateway</strong></td><td><strong>Microservices API Gateway</strong></td></tr><tr><td>It comes up with a simple mechanism to create new services.</td><td>It makes use of tools quickly that allows service teams to easily and safely create new services.</td></tr><tr><td>It enables functionality for monitoring usage of API per client.</td><td>It monitors the user-visible metrics like availability to clients.</td></tr><tr><td>Routing manages to change API versions.</td><td>It integrates routing to roll out the new versions of services quickly.</td></tr></tbody></table></figure><p>Therefore, traditional API gateways solve the challenges that API management faces, but it fails to solve the difficulties adopted by microservices. Hence, the microservice API gateway integrated into your application allows service teams to monitor, update and publish the services safely and fast, enabling your organization to develop more software efficiently and effectively than ever before.&nbsp;</p>17:Tb9c,<p>Consider that you are building an eCommerce mobile application for clients where the application displays the product details. When making this application with a monolithic architecture, the client retrieves the data using a single REST call to the application. When we develop the same application with microservices architecture, each microservice reveals instructions for fine-grained endpoints.&nbsp;</p><p>Let’s make use of microservices architecture. The product details page displays the data by multiple microservices, for example, order services, shopping cart services, review services, shipping services, and much more. Here, the client directly calls each microservices and distributes requests across the available instances. But when clients directly call the microservices, there are many issues faced, such as mismatch between the client call and APIs or use of protocols that are not web-friendly. API Gateway can solve this issue effectively. API gateway merges the internal system of the application and allows an API that each client adapts.&nbsp;</p><p>The below image explains how the API gateway manages the API calls and Interacts with other architecture components.</p><figure class="image"><img src="https://cdn.marutitech.com/098ac40e_custom_service_api_gateway_min_9f54f9b7c2.webp" alt="API Gateway interaction with architecture components"></figure><p>While implementing the API gateway in microservices architecture, scalability and performance of API Gateway are usually essential. There are various technologies used to implement the scalability and performance of API gateway. The API Gateway is responsible for the functionalities including routing, billing, monitoring, rate limiting, authentication, adequate security, alerts, and policies, etc. It helps to intercept incoming requests from clients and passes them to the API management system to apply necessary functions and get output.&nbsp;</p><p>The API Gateway handles the request by routing them to the backend service. For example, the API gateway first retrieves the client’s profile containing the personal information before fetching the knowledge of the profile. It is best to write the API Gateway code rather than writing the API code using a traditional approach that is difficult to understand.&nbsp;</p><p>API gateway uses the system’s benefits, unlike any other service client, whether server-side discovery or client-side discovery. API Gateway always needs to know the IP address of each microservice to communicate. But deciding the application locations is not easy as application services have multiple locations assigned altogether.&nbsp;</p><p>The most important thing to consider while using API gateway is the problem of partial failure of the system when one service calls another service that is responding slowly or is absent. However, there are solutions to this problem using API Gateway depending on the scenario and service failure.&nbsp;</p>18:T6ce,<p>In the backend for the frontend pattern, the backend service from the microservices architecture pattern serves as the endpoint for requesting the frontend.&nbsp;</p><p>We know that APIs are part of the applications functioning with one another with a single purpose. But over time of usage, there are chances of cracks or faults in the small piece of code which helps everything stay aligned with one another. Therefore, the Backend for Frontend Pattern helps develop the backend niche for better user experiences individually. You can say that it is the layer between the frontend and the request backend calls.&nbsp;</p><p>Also, sometimes the output of data returned by the microservices to the front end is not in the exact format or the filter needed by the front end. To solve this issue, the frontend should have some logic to reformat the data, and therefore, we can use BFF to shift some of this logic to the intermediate layer. The primary function of the backend for frontend pattern is&nbsp;</p><ul><li>Get the required data by calling the appropriate microservice APIs</li><li>Format the frontend data representation&nbsp;</li><li>Send the formatted output to the frontend&nbsp;</li></ul><p>Hence, BFF helps to reduce the logic handling responsibility of the frontend and enables a well-formatted interface to the frontend of the application.</p><p><i>You could derive a lot more business value from your existing legacy applications. Our </i><a href="https://marutitech.com/services/technology-advisory/enterprise-application-modernization/" target="_blank" rel="noopener"><i>legacy application modernization services</i></a><i> can help you unlock business agility, productivity, and cost-savings in the long run.”</i></p>19:T195b,<p>After a brief understanding of the working of API gateway, now it’s time to learn the implementation of API gateway in microservices. Below we have discussed some design issues and solutions that you have to consider while implementing API gateway:</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Scalability and Performance</strong></span></li></ul><p>For every application, the scalability and performance of the API gateway are most important. Therefore, it is efficient to build the API gateway on the platform that supports asynchronous and non-blocking I/O. Different technologies can be used to implement the scalable API gateway. You can consider <a href="https://netty.io/" target="_blank" rel="noopener">Netty</a>, <a href="https://undertow.io/" target="_blank" rel="noopener">JBoss Undertow</a>, and many such NIO-based frameworks for JVM. If you are working with a non-JVM platform, <a href="https://nodejs.org/" target="_blank" rel="noopener">Node.js</a> is the best option built on chrome’s javascript engine.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Reactive Programming Model</strong></span></li></ul><p>The API gateway handles the requests by routing them to appropriate backend services. On the other hand, it also addresses some requests by invoking multiple backend services and combining the overall results. For instance, considering an eCommerce platform, the requests to backend services are independent of each other for a product details request.&nbsp;</p><p>The API gateway should be able to handle multiple requests simultaneously, to minimize the response time. But sometimes, there are dependencies between requests. Therefore, the API gateway validates the requests by calling the authentication service and then routes them to the backend services. Similarly, suppose you wish to fetch the details of products from the customer’s wishlist in an application. In that case, the API gateway has to retrieve the customer’s profile data to retrieve the product information.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Service Invocation</strong></span></li></ul><p>A microservice-based application is a distributed system and should be using an inter-process communication approach – which has two techniques. The first is by using an asynchronous messaging-based mechanism. Some implement this by using message brokers such as <a href="https://www.oracle.com/java/technologies/java-message-service.html" target="_blank" rel="noopener">JMS</a> and <a href="https://www.amqp.org/" target="_blank" rel="noopener">AMQP</a>, whereas others utilize brokerless and service communication directly, just like <a href="https://zeromq.org/" target="_blank" rel="noopener">Zeromq</a>. The other technique is a synchronous mechanism like HTTP or Thrift.&nbsp;</p><p>The API Gateway will support various communication mechanisms for microservices-based architecture. A system can use both asynchronous and synchronous techniques or multiple implementations of each method simultaneously.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Service Discovery</strong></span></li></ul><p>The API Gateway has to know the IP address and Port (location) of each microservice with which it communicates. In traditional architecture, you probably hardwire the location, but in the latest cloud-based microservice application, this is a non-trivial problem.&nbsp;</p><p>Infrastructure services like message brokers will contain a static location that can be specified via OS environment variables. However, it is not easy to identify the location of application services because it assigns the location dynamically. Also, due to auto-scaling and upgrades, the instances of the services change dynamically.&nbsp;</p><p>Therefore, API Gateway needs to use the system’s service discovery mechanism, i.e., Service-Side Discovery or Client-Side Discovery, just like any other service client system. If the system chooses client-side discovery services, then the API gateway should query the service registry, which contains the database of all microservice instances and their respective locations.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Handling Partial Failure</strong></span></li></ul><p>Another challenge you might face while implementing API Gateway is the partial failure of the system. This problem arises in all distributed systems when one service calls another, which is unavailable or responding slowly. The API gateway never blocks the indefinite waiting for the downstream service.&nbsp;</p><p>Handling this failure eventually depends on which service is failed and the given scenario. For instance, when considering a shopping application, if the recommendation service fails to respond during the product details scenario, the API Gateway will return the rest of the product detail to the user since they are still helpful, and the recommendation can be empty or be replaced. On the other hand, if the product detail service fails to respond, the API Gateway has to return an error to the user.&nbsp;</p><p>Also, note that the API Gateway can return the cache data if available. For example, the product prices for a shopping application change infrequently, and therefore the API Gateway can return the cached prices if the pricing service is unavailable. The API Gateway can cache the data by itself or stored it in an external cache such as <a href="https://memcached.org/" target="_blank" rel="noopener">Memcached</a>. The API Gateway ensures that the system failure does not impact the user experience by returning either default data or cached data.&nbsp;</p><p>Similarly, <a href="https://github.com/Netflix/Hystrix" target="_blank" rel="noopener">Netflix Hystrix</a> is one of the most valuable libraries for writing code that helps to invoke remote services. Hystrix implements a circuit breaker pattern to stop the client from waiting for the failed services to respond. It trips the circuit breaker, which eventually fails all the requests immediately if the error rate for the service exceeds the specified threshold. It helps to time out the calls that exceed the threshold. If you are working with JVM, it is recommended to use Hystrix, and if you are working with a non-JVM environment, you should use an equivalent library.&nbsp;&nbsp;</p>1a:T4ca,<p>API gateway generally does more than just reverse proxying, like performing API operations with the help of API composition. API gateway provides clients to retrieve the data using a single API request in an application using API Composition.&nbsp;</p><p>The below figure illustrates the working of API composition in detail. Suppose you create an eCommerce application and the client with various services like order services, shopping cart services, review services, shipping services, and much more. Here, the client makes a request call to fetch the order details like order bill, order delivery, ordered product information. The traditional API request call will create many API calls for calling each service from the backend.&nbsp;</p><figure class="image"><img src="https://cdn.marutitech.com/API_COMPOSITION_15efe0d7a4.png" alt="API Composition Pattern in microservices architecture"></figure><p>But at the same time, using API gateway, the client will make one API call, and parallelly the API gateway will call the services using a combination of multiple calls for higher performance in the output. Therefore, the API gateway enables the mobile client to retrieve the data using a single request only.&nbsp;</p>1b:T7a6,<p>The most frequently asked question while dealing with microservices is the difference between service mesh and API gateway as there is an overlap between API gateway and service mesh patterns.&nbsp;</p><p>API gateway is a central component of the architecture, while service mesh divides the application’s functionality into microservices further handled by the infrastructure layer. The API gateway and service mesh functionality include handling request routing, rate limiting, monitoring, authentication, etc. The difference between both is that the API gateway manages the traffic from the client to services. In contrast, the service mesh contains the traffic for service to service communication.</p><p>API gateway generally focuses on the external organizing resources to manage and control the services inside the network. Therefore, it is located between the network and application to manage the traffic from edge-level client to service communication. You can say that the primary function of API gateway is to route external and internal API calls.&nbsp;</p><p>At the same time, service mesh focuses on internal organizing resources that expose the services to the web. Therefore, service mesh reveals the services or API calls that enable the specific business function to manage the internal traffic service to service communication. So, you can use the service mesh to upgrade the portability with the internal architecture systems or microservices.&nbsp;</p><p>API gateway ensures the security requirements thoroughly, mainly when used within a service mesh. Still, there are some security compromises regarding service mesh as it focuses more on speeding the delivery of microservices.&nbsp;</p><p>Hence, we can say that API gateway is a mature technology to work with while building a microservice pattern in an application. In contrast, the service mesh is an emerging technology that risks working within current scenarios.&nbsp;</p>1c:T682,<p>Hackers find the gaps in the system and take advantage of such loose input validations to break the system. The attacker will use the existing inputs to determine what is accepted and push the request accordingly until the system integrity breaks down.&nbsp;</p><p>Some common input validations are</p><ul><li><span style="font-size:18px;"><strong>Message Size</strong></span></li></ul><p>It is good practice to have message size limitations when you are sure that you will not receive any message larger than a specific size. It will help you to filter out the messages and make the system effective and more secure.</p><ul><li><span style="font-size:18px;"><strong>JSON Threat Protection&nbsp;</strong></span></li></ul><p>JSON(JavaScript Object Notation) is a threat for content-level attacks. These attacks use JSON files to crash the system service and overwhelm the parser.&nbsp;</p><ul><li><strong>SQL Injection</strong></li></ul><p>SQL injection protection enables you to protect the system by blocking the request, which can cause the SQL injection or any similar threat.&nbsp;</p><ul><li><span style="font-size:16px;"><strong>XML Threat Protection</strong></span></li></ul><p>Malicious attacks on XML applications include SQL injections, XSLT, or recursive payloads to crash the system services.&nbsp;</p><ul><li><strong>Rate Limiting</strong></li></ul><p>The authentications for all API users and logging of all API calls enable the API suppliers to limit consumption for all clients. API gateway caps allow you to detect the number of API calls made by a single API resource and other constraints like consumption by seconds, minutes, or day.&nbsp;</p>1d:T12a1,<p>API Gateway helps the microservices to communicate among themselves internally by providing flexibility and completely independent protocols. It allows the developers to create the architecture subset in various forms without publicly exposing the endpoints. The API gateway offers the below benefits.</p><p><img src="https://cdn.marutitech.com/e9399451-benefits_of_api_gateway_for_microservices_copy.png" alt="API Gateway for Microservices" srcset="https://cdn.marutitech.com/e9399451-benefits_of_api_gateway_for_microservices_copy.png 1000w, https://cdn.marutitech.com/e9399451-benefits_of_api_gateway_for_microservices_copy-768x559.png 768w, https://cdn.marutitech.com/e9399451-benefits_of_api_gateway_for_microservices_copy-705x513.png 705w, https://cdn.marutitech.com/e9399451-benefits_of_api_gateway_for_microservices_copy-450x328.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Security Benefits</strong></span></li></ul><p>API gateway acts as a security layer between the front end and the backend microservices to ensure that the sensitive API endpoints are not exposed publicly. It helps protect the API from malicious attacks by hackers such as SQL injections and other threats that may benefit API’s vulnerability.&nbsp;</p><p>The API client can integrate the data information with the session information like Redis, specially created for the trusted clients. Therefore, without the API gateway, Redis would directly expose the client and increase the security issues.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Monitoring and Analytics&nbsp;</strong></span></li></ul><p>Some API gateway helps the developers debug and create the infrastructure that can gracefully scale. The best example is Dashbird.io which connects the AWS API gateway and gathers information like execution time, errors, and much more. Not all API gateway provides this service, so some third-party monitoring solutions figure out the scenario behind the scenes.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Decreases Microservices Complexity</strong></span></li></ul><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">API gateway in microservice helps reduce its complexity by managing the user access control, authentication issues, rate limiting and allows your API to focus on the primary goal.</span></p><p>This creates an effective advantage as your API doesn’t have to respond in any possible way. API gateway handles your routing, formats of the responses, and even the cache of the system.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Support for Mixing Communication Protocols&nbsp;</strong></span></li></ul><p>Internal microservices benefit from using different communication protocols by using API gateway. An API gateway can provide a unified REST-based API for various protocols to choose the best internal architecture for the applications.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Avoids Exposing Internal Concerns to External Clients</strong></span></li></ul><p>An API gateway helps to separate the external APIs from the internal microservices patterns. It hides the service history and versioning details from the clients for all your microservices. As output, you get the efficiency to refactor the microservice with time, removing all negative impacts of externally bound clients.&nbsp;</p><p>Apart from all these benefits, the microservices API gateway allows clients to create a single call request data. It helps clients to get several microservices and get enough information on just one screen. Each of these request calls makes use of network bandwidth and client code. This single call gets the result far more efficient in comparison to several calls at the same time. Microservices API gateway enables the protocol translations automatically so that everyone can speak in the same language, and as a result, it allows faster data flow between endpoints.&nbsp;</p><p>Summarizing the benefits of API gateway:</p><ul><li>It enables the clients to partition the applications into microservices.&nbsp;</li><li>It enables the clients to determine the location of service problems.</li><li>It creates the optimal API for a particular client individually.</li><li>API gateway reduces the number of request/response calls. It takes a single request call from the client to fetch multiple services at the same time with a single round trip.</li><li>It converts the standard public web-friendly protocol into the internal protocol.</li></ul>1e:T549,<p>There are many reasons to use the API gateway, but you should also consider some drawbacks of using the API gateway for microservices.&nbsp;</p><p>The first challenge you face while working with API gateway is to integrate it into the software server. Even when the installation and configuration time is less, it is essential to work precisely on this matter. Also, the API gateways often generate a failure at the application’s endpoint, which is very difficult to find. This failure can also cause the application to crash or find it challenging to communicate with the server.&nbsp;</p><p>While configuring the API gateway microservices, it is essential to manage the routing logic during the deployment. Managing routing logic ensures the proper routing from external API to the desired microservice. Also, after the API gateway configuration, interacting the API with the application through the API gateway will require another difficulty for the developers.&nbsp;</p><p>When it comes to building applications of high scalability, API gateway acts as the single point of failure, as API gateway will be the single point between the frontend and the application APIs. Also, performance and efficiency reduction is the primary concern as there are various scenarios where the API gateway can directly impact the speed of your applications.</p>1f:T76d,<p>In conclusion, you can say that the API gateway is the essential component to work with when it comes to microservices application patterns. API gateway represents a single entry point into the system, providing the request routing, security, protocol translation, and composition of microservices in an application. API gateway also enables to mark the failure in the backend services of the application by returning the default data.</p><p>Microservices are an architectural approach that enables the creation of cloud applications. This application is developed by a set of small separate services to operate and communicate on its own over APIs.</p><p>Making use of microservices API gateway enables efficiency and speed of the data transfer. Using the declarative style to write the application makes it possible for the API gateway to fulfill the clients’ requests and share the desired responses.&nbsp;</p><p><span style="font-family:;">Our engineering experts at Maruti Techlabs have conducted </span><a href="https://marutitech.com/legacy-application-modernization/" target="_blank" rel="noopener"><span style="font-family:;">legacy application modernization</span></a><span style="font-family:;"> for numerous industries and migrated complex and fully functional applications from </span><a href="https://marutitech.com/10-steps-monolith-to-microservices-migration/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:;">monolithic to microservices</span></a><span style="font-family:;"> architecture.</span></p><p>To level up with applications that provide scalability and customization in a secure, low-cost, customer-friendly package of <a href="https://marutitech.com/services/cloud-application-development/" target="_blank" rel="noopener">cloud application development services</a>, get in touch with us <a href="https://marutitech.com/contact-us/">here</a>.</p>20:T2529,<h3><strong>1. </strong><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>What is the difference between an API and an API gateway?&nbsp;</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">An API conducts specific functions within a software or system. However, an API Gateway offers features like authentication and routing by sharing and controlling different requests to various APIs.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>2. How do microservices communicate with each other?&nbsp;</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Microservices communicate using two basic types of messaging patterns.</span></p><ol><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Synchronous Communication: Here, using protocols such as HTTP or gRPC, one service calls the API exposed by another service. This accounts for synchronous communication, as the caller awaits a response from the receiver.</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Asynchronous Communication: A service sends a message without awaiting a response, and other services attend to this message later.</span></li></ol><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>3. Is it possible to create microservices without an API gateway?</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Without an API gateway, your applications observe a direct client-to-microservice pattern. Whether you have 10 or 100 services, an API gateway offers a unified entry point for users.&nbsp;</span><br><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">This can quickly become chaotic, with numerous client requests directed to different microservice endpoints that offer no flexibility between client apps and microservices. Such complexity can hinder innovation, making it difficult to update your microservices.</span></p><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>4. How do I secure an API gateway in microservices?</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">&nbsp;Here are 5 best practices for securing your API gateway:</span></p><ol><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Authentication: Use short-lived tokens for token-based authentication</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Authorization: For all API endpoints, employ Role-Based Access Control (RBAC)</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Rate Limiting: Learning user behavior and context to implement dynamic, layered rate limiting.</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">CORS: Specify and limit allowed origins</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Logging: Incorporate real-time monitoring and detection of anomalies.</span></li></ol><h3><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;"><strong>5. What tools or platforms can be used to configure an API Gateway for microservices?</strong></span></h3><p><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Here are the most famous tools and platforms that can be used to configure your API gateways.</span></p><ol><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">API management and testing tools: </span><a href="https://azure.microsoft.com/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Azure API Management</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://tyk.io/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Tyk</span></a></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Messaging tools: </span><a href="https://kafka.apache.org/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Apache Kafka</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://cloud.google.com/pubsub?hl=en" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Google Cloud Pub/Sub</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://www.rabbitmq.com/" target="_blank" rel="noopener"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">RabbitMQ</span></a></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Toolkits: </span><a href="https://fabric8.io/" target="_blank" rel="noopener"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Fabric8</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://senecajs.org/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Seneca</span></a></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Architectural frameworks: Goa, </span><a href="https://konghq.com/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Kong</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://micronaut.io/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Micronaut</span></a></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Orchestration tools: </span><a href="https://kubernetes.io/" target="_blank" rel="noopener"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Kubernetes</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, Mesos, </span><a href="https://www.itconductor.com/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Conductor</span></a></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Monitoring tools: </span><a href="https://graphiteapp.org/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Graphite</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://graylog.org/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Graylog</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://prometheus.io/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Prometheus</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">(+ Grafana)</span></li><li><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Serverless tools: </span><a href="https://www.serverless.com/" target="_blank" rel="noopener"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Serverless</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://openwhisk.apache.org/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Apache Openwhisk</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://claudiajs.com/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">Claudia</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">, </span><a href="https://open.iron.io/" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">IronFunctions</span></a><span style="background-color:#ffffff;color:#000000;font-family:'Work Sans',sans-serif;">.</span></li></ol>21:T64a,<p><span style="font-family:Raleway, sans-serif;font-size:16px;">Initially, the definition of serverless architecture was limited to applications which are dependent on third-party services in the cloud. These 3rd party apps or services would manage the server-side logic and state. Alongside a related term – Mobile backend as a service (MBaaS) also became popular. MBaaS is a form of cloud computing that makes it easier for developers to use ecosystem of cloud accessible databases such as Heroku, Firebase, and authentication services like Auth0 and AWS cognito.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">But now serverless architecture is defined by stateless compute containers and modeled for an event-driven solution. AWS Lambda is the perfect example of serverless architecture and employs Functions as a service (FaaS) model of cloud computing. Platform as a Service (PaaS) architectures popularized by Salesforce Heroku, AWS Elastic Beanstalk and Microsoft Azure simplify applications deployment for developers. And serverless architecture or FaaS is the next step in that direction.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">FaaS provides a platform allowing the developers to execute code in response to events without the complexity of building and maintaining the infrastructure. Thus despite the name ‘serverless’, it does require servers to run code. The term serverless signifies, the organization or person doesn’t need to purchase, rent or provision servers or virtual machines to develop the application.</span></p>22:T6c8,<p><span style="font-family:Raleway, sans-serif;font-size:16px;">Serverless code written using FaaS can be used in conjunction with code written in traditional server style, such as microservices. In a microservice architecture, monolithic applications are broken down into smaller services so you can develop, manage and scale them independently. And FaaS takes that a step further by breaking applications to the level of functions and events.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">There will always be a place for both microservices and FaaS. For example, the code for a web application is partly as a microservices and partly as a serverless code. Also, some things you can’t do with functions, like keep an open websocket connection for a bot for an instance. Here an API/microservice will almost always be able to respond faster since it can keep connections to databases and other things open and ready.</span></p><figure class="image"><img src="https://cdn.marutitech.com/5f38797c_monolith_to_microservice_to_faas_065df61b5d.png" alt="5f38797c-monolith-to-microservice-to-faas.png" srcset="https://cdn.marutitech.com/thumbnail_5f38797c_monolith_to_microservice_to_faas_065df61b5d.png 217w,https://cdn.marutitech.com/small_5f38797c_monolith_to_microservice_to_faas_065df61b5d.png 500w," sizes="100vw"></figure><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Another interesting explanation – you can have a microservice by grouping a set of functions together using an API gateway. So microservices and FaaS can coexist in a nice way. The end user is least bothered about your API is implemented as a single app or a bunch of functions, it still acts the same.</span></p>23:T9e1,<p><span style="font-family:Raleway, sans-serif;font-size:16px;">Serverless computing or FaaS completely alleviates the shortcomings of PaaS model. PaaS has operational concerns of scaling and friction between development and operations. With most of the PaaS applications you need to think about scaling, e.g. how many virtual images for AWS beanstalk or dynos for Heroku. Whereas services like AWS Lambda, Google’s Cloud Functions or Azure Functions allow developers to write just the request processing logic in a function. And other aspects of architecture such as middleware, bootstrapping and scaling are automatically handled.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">With a FaaS application scaling is completely transparent. Even if you setup your PaaS application to auto-scale you won’t be doing this to the level of individual requests unless you know the traffic trend. So a FaaS application is much more efficient when it comes to costs.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">In a FaaS system, the functions are expected to start within milliseconds to allow handling of individual requests. In a PaaS systems, by contrast, there is an application thread which keeps running for a long period of time and handles many requests. This difference is visible in the pricing. FaaS services charge per execution time of the function while PaaS services charge per running time of the thread in which the server application is running.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Another popular technology that serverless computing can disrupt is containerization. DevOps teams use containerization tools such as Docker, Mesos, and Kubernetes as an open platform that makes it easier for developers and sysadmins to push code from development to production. You don’t have to use different, clashing environments during the entire application lifecycle. Read more about containerization tools in the blog ‘</span><a href="https://marutitech.com/5-essential-devops-tools/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>5 Essential Tools For DevOps Adoption</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">’. Like PaaS, containers don’t offer automatic scaling. Kubernetes with ‘Horizontal Pod Autoscaling’ using smart traffic pattern analysis and load-implying metrics may implement automatic scaling in the future.</span></p>24:Tcd8,<p><span style="font-family:Raleway, sans-serif;font-size:16px;">A serverless solution consists of a web server, FaaS layer, security token service (STS), user authentication and database.</span></p><figure class="image"><img src="https://cdn.marutitech.com/718f8f87_serverless_application_d9428e8a5f.jpg" alt="718f8f87-serverless-application.jpg" srcset="https://cdn.marutitech.com/thumbnail_718f8f87_serverless_application_d9428e8a5f.jpg 245w,https://cdn.marutitech.com/small_718f8f87_serverless_application_d9428e8a5f.jpg 500w,https://cdn.marutitech.com/medium_718f8f87_serverless_application_d9428e8a5f.jpg 750w," sizes="100vw"></figure><p>Reference: <a href="http://blog.tonyfendall.com/2015/12/serverless-architectures-using-aws-lambda/" target="_blank" rel="noopener"><u>https://blog.tonyfendall.com/2015/12/serverless-architectures-using-aws-lambda/</u></a></p><ul><li><strong>Client Application – </strong>The UI of your application is best-rendered client side in Javascript which allows you to use a simple, static web server.</li><li><strong>Web Server</strong> – Amazon S3 provides a robust and simple web server. All of the static HTML, CSS and js files for your application can be served from S3.</li><li><strong>FaaS solution</strong> – It is the key enabler in serverless architecture. Some popular examples of FaaS are AWS Lambda, Google Cloud Functions, and Microsoft Azure Functions. AWS Lambda is used in this framework. The application services for logging in and accessing data will be built as Lambda functions. These functions will read and write from your database and provide JSON responses.</li><li><strong>Security Token Service (STS)</strong> will generate temporary AWS credentials (API key and secret key) for users of the application. These temporary credentials are used by the client application to invoke the AWS API (and thus invoke Lambda).</li><li><strong>User Authentication</strong> – AWS Cognito is an identity service which is integrated with AWS Lambda. With Amazon Cognito, you can easily add user sign-up and sign-in to your mobile and web apps. It also has the options to authenticate users through social identity providers such as Facebook, Twitter, or Amazon, with SAML identity solutions, or by using your own identity system.</li><li><strong>Database</strong> – AWS DynamoDB provides a fully managed NoSQL database, offering seamless scalability and high performance for modern applications. While DynamoDB is not strictly essential for every serverless application, it serves as a powerful example of how cloud-native databases can enhance <a href="https://marutitech.com/services/cloud-application-development/serverless-app-development/" target="_blank" rel="noopener">serverless app development services</a> by eliminating operational overhead.</li></ul><p>To gain a more comprehensive understanding of the serverless architecture framework and its potential benefits for your business, reach out to our <a href="https://marutitech.com/services/staff-augmentation/it-outsourcing/" target="_blank" rel="noopener"><span style="color:#f05443;">IT outsource consulting</span></a> team. Our experts are well-versed in serverless architecture and can provide valuable insights tailored to your specific business needs.&nbsp;</p>25:T12c5,<h3><strong>1. Easier operational management</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">The serverless platform provides a clear separation between infrastructure services and applications running on top of the platform. Automatic scaling functionality of FaaS not only reduces compute cost but also reduces operational management overheads. System Engineers and SREs can focus on managing and running the underlying platform and core services such as databases and load balancers while product engineers manage the functions running on top of the platform.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Compared to deploying an entire server, packaging and deploying a FaaS architecture is pretty simple. In purist terms, a serverless system won’t require continuous integration, continuous delivery or containerization tool. Developers can write the code directly in the vendor console. Thus a fully serverless solution will require zero system administration.</span></p><h3><strong>2. Faster innovation</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Product engineers can innovate at a rapid pace as serverless architecture has alleviated the problems of system engineering in the underlying platform. Thus less time for operations translates into a smooth application of DevOps and agile methodologies. Our teams have the flexibility to experiment in new things and update our technology stack. Also, regular concerns of an internet facing application like identity management, storage, etc are exposed to FaaS or handled by the underlying middleware. Product engineers can concentrate on developing the actual business logic of the application.</span></p><h3><strong>3. Reduced operational costs</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Similar to IaaS and PaaS, infrastructure and human resource cost reduction is the basic advantage of the serverless architecture. In the serverless solution, you pay for managed servers, databases and application logic. AWS Lambda bills you only for the time when the function is called. As a result, the cost of running FaaS Lambda functions can be 95% less than running a server for the entire month with the container on it. Now services that were renting servers in AWS costing thousands of dollars have reduced to less than $10. The savings can be incredible. The basic advantage of this technology is that you only pay for the time your function executes and the resources it needs to execute.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Stelligent have explained this point in their blog on </span><a href="https://stelligent.com/2016/03/17/serverless-delivery-architecture-part-1/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>Serverless Delivery: Architecture (Part 1)</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">. Applications running on AWS Lambda using AWS API Gateway will be cost effective only when the transaction volume is low. At higher scale using API gateway becomes cost prohibitive. Almost 98% of the cost of serverless deployment is due to API gateway while the cost of AWS Lambda as FaaS is negligible.</span></p><figure class="image"><img src="https://cdn.marutitech.com/887561c9_serverless_cost_comparison_73ba033bd7.jpg" alt="887561c9-serverless-cost-comparison.jpg" srcset="https://cdn.marutitech.com/thumbnail_887561c9_serverless_cost_comparison_73ba033bd7.jpg 239w,https://cdn.marutitech.com/small_887561c9_serverless_cost_comparison_73ba033bd7.jpg 500w,https://cdn.marutitech.com/medium_887561c9_serverless_cost_comparison_73ba033bd7.jpg 750w," sizes="100vw"></figure><p>Reference – https://stelligent.com/2016/03/17/serverless-delivery-architecture-part-1/</p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">The diagram above compares the pricing for running a Node.js application with Lambda and API Gateway versus a pair of EC2 instances and an ELB. Notice that for the m4.large, the break even is around two million requests per day.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">To fully leverage the benefits of serverless architecture for your business, consider our top-quality </span><a href="https://marutitech.com/services/software-product-engineering/web-application-development/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Raleway, sans-serif;font-size:16px;">web application development services</span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">, where we can design, develop, and deploy a custom web application that meets your specific needs and requirements.</span></p>26:T1979,<h3><strong>1. Problems due to third-party API system</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Vendor control, multitenancy problems, vendor lock-in and security concerns are some of the problems due to the use of 3rd party APIs. Giving up system control while implementing APIs can lead to system downtime, forced API upgrades, loss of functionality, unexpected limits and cost changes. Multitenancy problem is also seen in other cloud computing frameworks. Salesforce (PaaS) imposes governor limits due to its multitenant cloud structure and </span><a href="https://marutitech.com/mistakes-in-salesforce-development/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>developers have to avoid some mistakes while using Salesforce</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">. Multitenant solutions can have problems with security, robustness, and performance.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Switching vendors in serverless architecture can be little tricky. While switching you would need to update your tools, code, and design. Also migrating serverless functions from one vendor to another is easier than migrating all those other services with which a function must integrate. Parse is an example of BaaS which closed down and left many developers stranded. Many APIs in your serverless system exposes your ecosystem to malicious attacks and each API increases the number of security implementations.</span></p><h3><strong>2. Lack of operational tools</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">The developers are dependent on vendors for debugging and monitoring tools. Debugging Distributed Systems is difficult and usually requires access to a significant amount of relevant metrics to identify the root cause. IaaS and PaaS systems have exposure to traffic shaping and load balancer techniques such as Geo DNS and </span><a href="https://github.com/Netflix/zuul" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>Zuul</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">. In serverless architecture, we lose this flexibility as user requests are handled by opaque load balancers such as </span><a href="https://aws.amazon.com/api-gateway/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>AWS API Gateway</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">. In such cases, the platform chooses a lambda function deployed in the same region where the request arrives. So when there are outages in caching or data store services, it is hard to steer part of the traffic to other regions.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;">As the serverless architecture is adopted by more organizations we may see mature debugging tools. Also, platforms need not expose more operational metrics than they do today. Distributed tracing is a helpful technique in aiding in the understanding of how a request fans out across multiple services and helps in debugging systems based on the microservices architecture.</span></p><h3><strong>3. Architectural complexity</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;">Distributed computing architectures are complex and time-consuming to build. This applies to microservices and serverless architectures in equal measure. Decisions about how small (granular) the function should be, takes time to assess, implement and test. There should be a balance between the number of functions should an application call. It gets cumbersome to manage too many functions and ignoring granularity will end up creating mini-monoliths.</span></p><p style="margin-left:0px;"><span style="font-family:Raleway, sans-serif;font-size:16px;">Serverless architecture inherently provides benefits of low operational cost, scaling and less time to market. Due to which it has the potential to quickly become one of the foundational pieces of modern distributed systems. But it is still in the nascent stage where organizations adopting serverless systems should take into consideration the over-reliance on third-party APIs and architectural complexity. Organizations already using </span><a href="https://marutitech.com/5-reasons-why-cloud-can-transform-your-business/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>cloud technologies</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> will be the early adopters and we might see a marketplace for serverless functions, and widespread adoption of serverless architectures. </span><a href="https://marutitech.com/benefits-of-blockchain/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>Blockchain</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;">, IoT, gaming and enterprise middleware are some of the future applications of serverless architecture.</span></p><p style="margin-left:0px;"><span style="font-family:Raleway, sans-serif;font-size:16px;">Technology is constantly evolving. You need a reliable, trusted partner to help you adapt to the latest technologies and future-proof your investments. </span><span style="font-family:Work Sans,Arial;">Reach out to a company like Maruti Techlabs, offering top</span><a href="https://marutitech.com/services/staff-augmentation/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Work Sans,Arial;"> IT staff augmentation services</span></a><span style="font-family:Work Sans,Arial;"> for reliable, cost-effective, and sustainable solutions.</span><span style="font-family:Raleway, sans-serif;font-size:16px;"> Our </span><a href="https://marutitech.com/services/technology-advisory/enterprise-application-modernization/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;font-size:16px;"><u>enterprise application modernization services</u></span></a><span style="font-family:Raleway, sans-serif;font-size:16px;"> help businesses adopt new technologies with our custom migration and modernization services. We can help you modernize your applications and infrastructure while reducing costs and improving the end-user experience. Give us a call today and discover how you can modernize your business.</span></p>27:T2642,<p>Here are few advantages of microservices architecture:</p><ul><li>It gives you the liberty to create a microservice in a language of your choice, self-sufficiently release it at your speed, and measure it as per your benchmark.</li><li>Since microservices are developed independently by different teams, development and marketing can be done simultaneously.</li><li>Errors and fault identification happens in a way that does not impact the whole digital ecosystem of the organization.</li></ul><p><strong>What are the Best Practices under Microservices Architecture?</strong><br><br><span style="font-family:Raleway, sans-serif;font-size:16px;">Here’s a look at 12 of the microservices best practices that you should be following at all costs:</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Have a Dedicated Infrastructure For Your Microservice</strong></span></h3><p>A poor design of the hosting platform of your microservice will never earn you good results despite meeting all the parameters of microservice development. Separate your microservice infrastructure from other components to get fault isolation and better performance.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Have a Dedicated Database For Your Microservice</strong></span></h3><p>Pick the correct database, customize the infrastructure that it requires, and keep it exclusive to your microservice. If you use a shared database for all your microservice, then it won’t serve the purpose.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. The Principle of Single Responsibility</strong></span></h3><p>Microservices should be modeled in a style where a class should have only a single reason to alter. Creating bloated services that are subject to changes for numerous business contexts is not an ideal practice.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Comprehend the Cultural Shift</strong></span></h3><p>Prepare your developers who are working in an ongoing environment for the upcoming expectations. Help them understand that the cultural shift is for the long-term benefit of the company.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Break Down the Migration into Steps</strong></span></h3><p>If you have not handled such a migration in the past, you need to understand that it is not an easy task. Monolithic architectures often involve a web of repositories, deployment, monitoring, and other complex tasks. Changing (or migrating) all of this at once may not be feasible for teams and is bound to leave behind errors and gaps. Moreover, if you have made plans to maneuver shifts all at once, you need to go back to the drawing board.</p><p>One of the best ways to handle this is to retain the monolithic structure and develop any additional capability as a microservice. Once you have enough new services in place (and the teams have been sensitized about the new processes), figure out how to break down the old architecture into relevant components and begin migrating them one by one.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Build the Splitting System Right into the Mix</strong></span></h3><h3><img src="https://cdn.marutitech.com/b64ed643-micro-services-best-practices.jpg" alt="Build the Splitting System Right into the Mix"></h3><p>Not having a splitting system right from the beginning of the project can lead to massive hassles in the future. Defining the interactions and processes between different puzzle pieces is one of the critical microservices best practices that should be followed to make the bigger picture clearer, even more so if you are in the migration phase.</p><p>Every splitting system is unique to the architecture that is being built. It depends on the methodology you are following and the results you expect at the end.</p><p>One tip is to inspect the monolithic structure to understand the gaps it has and components causing the most trouble and then transform this part into a microservice.</p><p>Although, this is only possible if you have been monitoring the performance of individual components in the first place. So, if monitoring is not something that you have focused on, it is a great place to begin the cleaning process.</p><h3><img src="https://cdn.marutitech.com/a96a4744-microservices-tools-best-practices-845x684.jpg" alt="Microservices-Tools-Best-Practices"></h3><p>Tools that you can use for the monitoring process include:</p><ul><li><a href="https://newrelic.com/" target="_blank" rel="noopener"><span style="color:#f05443;"><strong>New Relic</strong></span></a></li><li><a href="https://www.datadoghq.com/" target="_blank" rel="noopener"><span style="color:#f05443;"><strong>Datadog</strong></span></a></li><li><span style="color:#f05443;"><strong>Influxdb</strong></span></li><li><a href="https://grafana.com/" target="_blank" rel="noopener"><span style="color:#f05443;"><strong>Grafana</strong></span></a></li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. Isolate the Runtime Processes</strong></span></h3><p>Since we now have different processes for different verticals, you are bound to have isolation at the runtime level too. You need to implement some form of distributed computing to pull this off from a pool of possible choices.</p><p>Do you need to adopt <a href="https://marutitech.com/containerization-and-devops/" target="_blank" rel="noopener">containerization</a>, event architectures, various HTTP management approaches, service meshes, and circuit breakers? Figure this out before it is too late to backtrack.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>8. Pair the Right Technology with the Right Microservice</strong></span></h3><p>While one member in your team may not give importance to the technology or language, another might opine that the product’s life depends on it. Whatever the case, implementing the technology directly and iteratively might make it easier to make changes or even replace it later.</p><p>The choice of the language can come down to personal preferences and the comfort level of your team members. But whatever you do, make sure that your team is equipped enough to handle the decision. For instance, choosing an architecture that involves a dozen different programming languages may also translate to a hiring spree, which is often not recommended.</p><p>If you are not sure which technology is best for your project, consider the following parameters during the decision-making process:</p><ul><li>Maintainability</li><li>Fault-tolerance</li><li>Scalability</li><li>Cost of architecture</li><li>Ease of deployment</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>9. Consider Using Domain-Driven Design</strong></span></h3><p>In one way, <a href="https://www.domaindrivendesign.org/" target="_blank" rel="noopener">Domain-Driven Design</a> is nothing more than Object Oriented Programming applied to business models. It is a type of design principle that uses practical rules and ideas to express an object-oriented model.</p><p>In simpler terms, microservices are designed around your business domains. It is used by platforms such as Netflix who use different servers to run their content delivery and related tracking services.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>10. Distinguish Between Dedicated and On-Demand Resources</strong></span></h3><p>If your primary aim is to deliver a superior customer experience, consider distinguishing between dedicated and on-demand resources. For instance, let’s take an e-commerce platform that builds its microservices and cloud architecture in ways that quickly (and securely) moves workloads between its on-premise and cloud environments. How does this help? Not only does it increase the response time, but it also makes migrating to a cloud-based working environment much more intuitive.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>11. Govern the Dependency on Open Source Tools</strong></span></h3><p>&nbsp;It is relatively common for developers to use open-source microservice tools for security, monitoring, debugging, and logging. However, ensure that they are not over-relied upon in ways that interfere with the performance or safety of the architecture. Depending on your development needs and the types of tools you are using, implement appropriate organizational policies regarding their usage. This can be related to:</p><ul><li>Establishing formal repositories for approved versions of the software</li><li>Understanding the open-source software supply chain</li><li>Establishing governance for exception processing</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>12. Leverage the Benefits of REST API</strong></span></h3><p>The <a href="https://restfulapi.net/" target="_blank" rel="noopener">REST (Representational State Transfer)</a> APIs can work wonders for microservices as developers need not install any additional software or libraries while creating a REST API. At the same time, they provide a great deal of flexibility since the data is not tied to any particular method or resource. The result is an ability to handle multiple types of calls, return different data formats, and alter the structure with the correct implementation of hypermedia.</p><p>You don’t even need a framework or SDK since HTTP requests are relatively sufficient. Out of the four levels of REST, simply begin at level 0 and make your way up to level 3, as proposed by Leonard Richardson, an expert in the subject of RESTful APIs.</p>28:Ta4f,<p>Before changing your system to microservices, it is vital to understand why you need to do it. Analyze your system and study the distinctive features in your system and notice which part of the system troubles you the most. At an early stage, consider a less critical part of the system and evaluate its functions as a microservice.</p><p>In addition to these microservices best practices, you also need to make sure that the project manager can handle end-to-end service-oriented architecture migrations and development. Only businesses who understand the nuances of the cultural shift towards microservices will leverage the technology to its full potential.</p><p>Many big tech giants and e-commerce sites like Netflix and Amazon have successfully migrated to microservices owing to their easy scalability and agility. However, hiring an agency that offers the <a href="https://marutitech.com/services/staff-augmentation/" target="_blank" rel="noopener"><span style="color:#f05443;">best IT talent &amp; staffing solutions</span></a> can be a smart idea if you do not have an expert in-house team to handle a smooth migration to microservices.</p><p>At <a href="https://marutitech.com/" target="_blank" rel="noopener"><strong>Maruti Techlabs</strong></a>, we assist you in outlining a high-performance microservices architecture that helps your organization maneuver operational overload and other challenges.&nbsp;</p><p>Our Engineering experts have successfully migrated fully-functional apps to microservices architecture and containerized them further. With the help of our <a href="https://marutitech.com/services/technology-advisory/enterprise-application-modernization/" target="_blank" rel="noopener">application containerization services</a>, your application can have easier traffic routing, selective scaling, faster deployment, and zero downtime.</p><p>For comprehensive <a href="https://marutitech.com/services/cloud-application-development/" target="_blank" rel="noopener">cloud application development services</a>, drop us a note on <a href="mailto:<EMAIL>"><EMAIL></a>, and let’s chat.</p><p><a href="https://marutitech.com/contact-us/"><img src="https://cdn.marutitech.com/725ab412-group-5614-2-min.png" alt="contact us - Maruti techlabs" srcset="https://cdn.marutitech.com/725ab412-group-5614-2-min.png 1210w, https://cdn.marutitech.com/725ab412-group-5614-2-min-768x347.png 768w, https://cdn.marutitech.com/725ab412-group-5614-2-min-705x318.png 705w, https://cdn.marutitech.com/725ab412-group-5614-2-min-450x203.png 450w" sizes="(max-width: 1210px) 100vw, 1210px" width="1210"></a></p>29:T415,<p>Backend technologies are evolving as fast as those that are customer-facing. As user demands are shifting and the mobile landscape is advancing, there is a need to level up the technology that keeps these systems working. We revolutionized the mobile development space with PaaS, IaaS, and SaaS, and are now leaning into <a href="https://en.wikipedia.org/wiki/Microservices" target="_blank" rel="noopener">microservices architecture</a> to create apps that can connect with a broader mobile landscape.</p><p>The term “microservices” is being thrown around a lot lately. There’s been a clear spike in interest over this term over the last few years and the trend doesn’t seem to be slowing down anytime soon. It is clear that Microservices Architecture is at the peak of high expectations when it comes to the&nbsp;<a href="http://www.gartner.com/technology/research/methodologies/hype-cycle.jsp" target="_blank" rel="noopener">Gartner Hype Cycle model</a>.</p><p>Let’s discover everything from what it means through to its uses.</p>2a:T793,<p>Microservices pose a lot of benefits for teams working in an Agile environment. <a href="https://www.nginx.com/blog/microservices-at-netflix-architectural-best-practices/" target="_blank" rel="noopener">Netflix</a>, <a href="https://www.nginx.com/blog/introduction-to-microservices/" target="_blank" rel="noopener">eBay</a>, Twitter, PayPal, and <a href="https://www.nginx.com/blog/introduction-to-microservices/" target="_blank" rel="noopener">Amazon</a> are some of the companies who have already made the shift from a monolithic architecture to microservices.</p><p>As opposed to microservices, a monolithic app is built as a single sturdy unit, which makes making changes to the software a tiresome process. In a monolithic software, creating a tiny shift or a small change in a small part of the software might require you to launch an entirely new version of the software.</p><p>Scaling specific functions is also a lot of hard work in a monolithic application since you need to scale all parts of the software.</p><p>Microservices solve these issues by allowing developers to create applications that are as modular as they can get. Simply put, applications developed with microservices can be viewed as a suite of services rather than a solidified mesh of services.</p><p>Let’s further explore what characterizes and differentiates a microservice-based application from a monolithic application.</p><p>If you're interested in adopting a microservices architecture for your business, our expert team at Maruti Techlabs can provide you with top-notch custom <a href="https://marutitech.com/services/software-product-engineering/web-application-development/" target="_blank" rel="noopener"><span style="color:#f05443;">web application development services</span></a>. We can help you transform your monolithic application into a modern, scalable, and cloud-based microservices architecture that will meet your business needs.</p>2b:Tf6e,<p>There are a few distinguishing traits of microservices architecture. Let’s have a look.</p><ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Multiple Components</strong></span></li></ul><p>Software solutions built with microservices can be broken down into components. Componentization is a crucial and one of the first steps of converting from monolithic to microservices based architecture. Services are broken down into components so that each service can then be tweaked, developed, and deployed on its own.</p><p>This least dependency state is the main benefit of microservices as it allows developers to change and redeploy specific parts of an application as opposed to the entire code. However, this characteristic of microservices comes with a cost. The cost of remote calls, remote APIs, and higher complexity as responsibilities are distributed among components.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Logical Functionality Boundaries</strong></span></li></ul><p>This characteristic is easy for us to say, but hard for developers to implement. Most teams face this issue when they first migrate software from monolithic to microservices.</p><p>Microservices have properly scoped functionality boundaries that prevent developers from running into confusion when any tiny change is needed. As with monolithic apps, when a team has to make a small tweak in one part of the code, they most often have to sync up with other teams to check dependencies and see how their change can affect other parts of the application.</p><p>However, in microservices, you should always be aware of how much you stuff into each service because this sets the boundaries of the level of modularity you can introduce in your app.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Easy Routing</strong></span></li></ul><p>Microservices act much like the classic UNIX system where the services receive requests, process them, and output a response accordingly which is in stark contrast with systems such as Enterprise Service Buses where systems are installed for message routing.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Decentralized</strong></span></li></ul><p>Microservices often involve a wide variety of platforms and technologies. Thus, they render centralized systems, not the optimal solution. The developers of microservices prefer decentralized systems as they continually strive to develop solutions to common problems – those which can be reutilized by other teams.</p><p>Microservices can also be characterized by a decentralized database management system where each service individually manages its own database.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Fail-proof</strong></span></li></ul><p>Microservices don’t shatter everything around them when they fail. Apps designed with microservice architecture are capable of managing failure. In a scenario where several unique services interact with one another, there is a possibility one of those might fail.</p><p>When that happens, the service, without much ado, allows its neighboring services to continue while graciously getting out of the way. Continuous monitoring of microservices can help detect such a failure.</p><p>However, this need for monitoring adds significant complexity to the overall application structure.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Evolutionary</strong></span></li></ul><p>Microservices architecture presents a solution to the issue when you can’t wholly anticipate the kinds of devices and platforms where your application might be accessed.</p><p>Monolithic applications that are now too rigid to continue can gracefully transition into microservices applications and interact with the underlying classic structure using APIs.</p>2c:T65b,<p><img src="https://cdn.marutitech.com/7da26869-micro-services-architecture.jpg" alt="Micro-Services-Architecture"></p><p>To understand better why microservices are revolutionary and essential for the future of app development, let’s understand what preceded them, i.e., the monolithic architecture.</p><p>Centralization lies at the core of a monolithic architecture, making updations and revisions a hectic task.</p><p>Here are a few challenges that existed with the monolithic architecture:</p><ul><li><strong>Lack of flexibility</strong> – Monolithic applications cannot be built using a mix of technologies.</li><li><strong>Unreliability </strong>– When a part of the system fails, the entire monolithic application halts.</li><li><strong>Non-scalability</strong> – Applications built with the monolithic architecture cannot be easily scaled since the entire system would need to be rebuilt.</li><li><strong>Interdependency </strong>– Developers lack independence as most modules await the development of some other modules so they can be completed.</li><li><strong>Development pace</strong> – Monolithic apps take a lot of time to reach the market since modules have to be developed one after another, considering the dependencies.</li></ul><p>Microservices resolve almost all of these issues by allowing developers to work side-by-side in cross-functional teams and deliver products on time.</p><p>Here are a few salient benefits of microservices architecture:</p><ul><li>Independent development and deployment</li><li>Fault isolation</li><li>Mixed tech stack</li><li>Granular (as-required) scaling</li></ul>2d:T634,<p>If you are starting to develop an application, you might want to keep it modular. For all the benefits modularity brings, you might be trying to demarcate boundaries of responsibilities within your application.</p><p>Businesses and companies aim for microservices-based apps to introduce extensionality and ease-of-maintenance in their solution. However, theoretically, modularity can even be introduced in a monolithic architecture. But, the problem lies in the way we develop an application with a monolithic architecture versus microservice architecture. In the former case, developers are typically in haste to develop and deploy the solution and get it off their slate as soon as they can.</p><p><img src="https://cdn.marutitech.com/5eac1ba8-microservices-in-2019.jpg" alt="Microservices-in-2019"></p><p>When this happens, boundaries of development blur and, consequently, the application loses its modularity. In the longer term, these overlapping services make it hard to scale and optimize applications. In essence of the architecture, even the simplest monolithic apps have a centralized database. This stands in stark contrast with microservices as decentralization lies at the core of microservices applications.</p><p>Therefore, the microservices first approach should be selected when modularity and decentralization are vital to the application, the app will have high volume traffic, long-term benefits can be preferred over short-term goals, the right set of resources is available to kick the project off, and when teams are committed to using latest technologies.</p>2e:T979,<p>One challenge that is often overlooked when implementing microservices is to decide whether or not it makes sense to employ the microservices architecture. When teams develop the first version of their application, they often don’t have the problems microservices solve. However, as the project progresses, they tend to face the same issues.</p><p>The distributed architecture of microservices slows down the development process and typically increases the need for human resources. This can be a hurdle for start-ups with limited talent in their pool and for businesses who are in haste to get their project off the ground.</p><p>Therefore, it is essential for enterprises to consider if microservices are the best bet for their application’s development. Most large scale websites such as Amazon, eBay, and Netflix have evolved their architectures from monolithic to microservices. Netflix, the popular video streaming service has a service-oriented architecture. Netflix handles over a billion calls every day and each call fans out six calls to backend services on an average. Amazon, on the other hand, originally had a two-tier architecture. But, when it came to scaling up, they migrated to a service-oriented architecture with hundreds of backend services.</p><p>Today, a lot of other websites call these services including the ones that implement the Amazon web service API. The Amazon.com website calls about 150 services to fetch the data that makes its webpage. eBay is another example of a website that evolved from a monolithic architecture to microservices. eBay is tiered according to the functionality so that each application tier implements the business logic for either buying or selling.</p><p><span style="font-family:;">However, the </span><a href="https://marutitech.com/legacy-application-modernization/" target="_blank" rel="noopener"><span style="font-family:;">legacy application modernization</span></a><span style="font-family:;"> process of migrating to microservices is a time-consuming and costly process.</span> Thus, if you are planning to migrate to microservices, make sure that it is the best bet for your application. You can reach out to <a href="https://marutitech.com/services/staff-augmentation/it-outsourcing/" target="_blank" rel="noopener"><span style="color:#f05443;">outsource IT consulting</span></a> firms to check the feasibility of the microservice architecture.</p>2f:T8ba,<p><a href="https://marutitech.com/10-steps-monolith-to-microservices-migration/" target="_blank" rel="noopener">Monolith to microservices migration</a> is not a new concept. They have previously been around in the form of Service Oriented Architecture, web services, and so on. However, the availability of the latest tools and technologies, the frustration of not getting the expected results with any other architecture, the massive adoption of IaaS and DevOps, and many other reasons have compiled on top of one another, leading to the surge in their popularity.</p><p>Down the line, we will see it growing to a level where software engineers will be making use of monolith for only prototyping. When it comes to deployment, why wouldn’t you choose a more modular, high performing as well as easy process to scale applications/systems?</p><p>In the next pieces that follow, we will explore how microservices are being implemented by companies, what are the challenges that line the path of microservice implementation, and how microservices can be successfully strategized and implemented in a business scenario. Microservices are here to stay, and it won’t be surprising to see many other giants circling back to microservices after their time with monolithic architecture.</p><p>Considering microservices but lack the necessary resources? Then <a href="https://marutitech.com/services/staff-augmentation" target="_blank" rel="noopener"><span style="color:#f05443;">software development staff augmentation</span></a> could be the perfect solution for you. At Maruti Techlabs, we deploy highly skilled and experienced software architects and developers who help you at every step, from feasibility analysis to the actual implementation of microservices.</p><p>Our <a href="https://marutitech.com/services/technology-advisory/enterprise-application-modernization/" target="_blank" rel="noopener">application containerization services</a> can help containerize your application to microservices architecture and get the most value from your information technology investments. Get in touch with our technical experts to help you transition to the cloud or upgrade your legacy application to a modern cloud-based application.</p>2:[["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"$11"}}],["$","$L12",null,{"blogData":{"data":[{"id":32,"attributes":{"createdAt":"2022-09-05T09:48:00.928Z","updatedAt":"2025-06-16T10:41:49.466Z","publishedAt":"2022-09-05T10:31:30.129Z","title":"How To Configure API Gateway in Microservices Architecture","description":"Considering the importance of API gateway, understand how they work and what they can do for you. ","type":"Software Development Practices","slug":"api-gateway-in-microservices-architecture","content":[{"id":12729,"title":null,"description":"$13","twitter_link":null,"twitter_link_text":null},{"id":12730,"title":"What is an API Gateway?","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":12731,"title":"Need for API Gateway in Microservices Architecture","description":"<p>As understood before, microservice architecture may have 10 to 100 or even more services altogether. <span style=\"background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;\">API Gateway in microservices helps us merge the entry point for clients, independent of the number of internal components.</span> There are many reasons why we need an API gateway in the microservices architecture pattern, which are as given below:</p><ul><li>API gateway helps to stop exposing internal concerns to external clients</li><li>API gateway helps to provide additional security to your microservices&nbsp;</li><li>API gateway helps to merge the communication protocols</li><li>As you have studied, API gateway helps to decrease the complexity of microservices, eventually improving the efficiency of the application.</li><li>API gateway helps separate the microservice API and other external APIs to virtualize the design requirements and testing.&nbsp;</li></ul>","twitter_link":null,"twitter_link_text":null},{"id":12732,"title":"How to Implement API Gateway in Microservices?","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":12733,"title":"Microservices API Gateway vs. Traditional API Gateway","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":12734,"title":"Working of an API Gateway in Microservices Architecture","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":12735,"title":"Backend for Frontend (BFF) Pattern","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":12736,"title":"Implementation of API Gateway – Issues and Solutions","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":12737,"title":"API Composition","description":"$1a","twitter_link":null,"twitter_link_text":null},{"id":12738,"title":"Service Mesh and API Gateway","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":12739,"title":"Policy Configuration in API Gateway","description":"<p>There are possibilities that the messages contain malicious data, including the characters, text patterns, or even SQL injections. Therefore, we implement the security process to make the network secure and safe for transaction and transmission.&nbsp;</p><p>Here, you can apply the CAP policies for securing the inbound and outbound traffic to protect the data against malicious attacks and SQL injections by limiting the HTTP versions and URL path. You can define a domain name or create a whitelist and blacklist the client’s IP address. Another option is to limit the query parameters and HTTP headers or generate a list of forbidden words or regular expressions that seem unknown and threaten the data.&nbsp;</p><p>The best practice is to apply this security policy to the message before routing it to any microservice for API calls. And also, the same security policy is applied before sending the output message to the client from the microservices.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12740,"title":"API Gateway’s Role in Security: Identity and Access","description":"<p>For an API Gateway technology, access control is one of the best security drivers that serve all sorts of organizations to manage the APIs and set rules on how to handle the data requests by the clients.&nbsp;</p><p>The beginning of the access control for API gateway generally starts from the authentication mechanism to identify the source of the API request call. In the current situation, you can use OAuth, a popular gateway to act as an intermediate for web resources without revealing the password to the services. Whereas working with the key-based authentication gateway, there are chances that the company may lose the data as it is challenging to maintain the authentication of the keys.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12741,"title":"Message Security","description":"<p>Using Gateway, you can route all API transactions with a single channel for evaluating and securing messages all over the organization. Message security is introduced between the API gateway’s internal services, making them more secure and enabling the message to pass between the encrypted services.</p><p>If you ignore the proper authentication, severe security problems will be caused in an organization. For example, if an API request is a mobile number, you can get personal details like email addresses and device identification data. Therefore, robust authentication mechanisms like OAuth are essential to protect the industry standards of organizations.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12742,"title":"Threat Protection ","description":"<p>The APIs in the API gateway and local services of the server are fundamentally insecure without threat protection. As you know, APIs are the primary source of digital connection with the world, but if there is any malicious user who attacks the backend system, the system is vulnerable.&nbsp;</p><p>The attacker can inject the SQL commands like drop, delete or even create random data available to APIs. The SQL injection enables the attackers to access the system database, codes, and system directories. There are chances that the attackers copy all the clients’ data from the database and use it for their interest. Apart from this SQL injection, there are many other forms of injection threats like RegExInjection or XML Injection.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12743,"title":"Logging ","description":"<p>There is a specific HTTP status code used for the different situations of the request call. For example, most of the developers use 200 for the success of the request call and 404 for the failure of the request call. The stack trace can threaten malicious users to identify the package name, class name, versions, server names, or SQL queries.&nbsp;</p><p>We can return the balanced error object with the HTTP status code and error message to reduce this challenge. Retuning the balance error code will help to enhance the error handling issues and secure the API from the attacker.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12744,"title":"Whitelists and Whitelist-Allowable Methods","description":"<p>There should be a familiar list of devices, network, and client IP addresses at the IP address level of API traffic. This list varies depending on the size of the network. As you know, multiple methods allow access to a given URL to perform various operations on the given entity. For instance, the POST method would create a new entity, and the DELETE method would delete the entity. In contrast, the GET request will read the entity while the PUT method would update the given entity.&nbsp;</p><p>Therefore, the service needs to limit the number of verbs to work, and all others will return a response code.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":12745,"title":"Input Validations ","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":12746,"title":"Benefits of API Gateway for Microservices ","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":12747,"title":"Drawbacks of API Gateway for Microservices ","description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":12748,"title":"Conclusion","description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":12749,"title":"FAQs","description":"$20","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":3584,"attributes":{"name":"How To Configure API Gateway in Microservices Architecture","alternativeText":null,"caption":null,"width":2048,"height":1168,"formats":{"small":{"name":"small_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"small_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":500,"height":285,"size":8.03,"sizeInBytes":8034,"url":"https://cdn.marutitech.com/small_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"medium":{"name":"medium_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"medium_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":750,"height":428,"size":13.15,"sizeInBytes":13152,"url":"https://cdn.marutitech.com/medium_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"thumbnail":{"name":"thumbnail_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"thumbnail_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":245,"height":140,"size":3.13,"sizeInBytes":3126,"url":"https://cdn.marutitech.com/thumbnail_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"large":{"name":"large_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"large_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":570,"size":18.22,"sizeInBytes":18222,"url":"https://cdn.marutitech.com/large_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"}},"hash":"freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","size":45.18,"url":"https://cdn.marutitech.com/freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-02T06:18:38.613Z","updatedAt":"2025-05-02T06:18:52.237Z"}}},"audio_file":{"data":null},"suggestions":{"id":1805,"blogs":{"data":[{"id":38,"attributes":{"createdAt":"2022-09-05T09:48:02.912Z","updatedAt":"2025-06-16T10:41:50.176Z","publishedAt":"2022-09-05T10:16:59.397Z","title":"Serverless Architecture The Future of Business Computing","description":"Learn more about serverless architecture, its benefits, and its drawbacks before adopting it to your organization. ","type":"Software Development Practices","slug":"serverless-architecture-business-computing","content":[{"id":12776,"title":"What Is Serverless Architecture? ","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":12777,"title":"Microservices to FaaS","description":"$22","twitter_link":null,"twitter_link_text":null},{"id":12778,"title":"Going Beyond PaaS and Containers","description":"$23","twitter_link":null,"twitter_link_text":null},{"id":12779,"title":"Framework of a Serverless Architecture","description":"$24","twitter_link":null,"twitter_link_text":null},{"id":12780,"title":"Benefits of Serverless Architecture","description":"$25","twitter_link":null,"twitter_link_text":null},{"id":12781,"title":"Drawbacks of Serverless Architecture","description":"$26","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":3582,"attributes":{"name":"serverless","alternativeText":null,"caption":null,"width":4608,"height":3687,"formats":{"small":{"name":"small_online-cloud-data-storage-concept-cloudscape-digital-online-server-global-network-business-web-database-backup-computer-private-infrastructure-technology.webp","hash":"small_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e","ext":".webp","mime":"image/webp","path":null,"width":500,"height":400,"size":16.02,"sizeInBytes":16022,"url":"https://cdn.marutitech.com/small_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e.webp"},"medium":{"name":"medium_online-cloud-data-storage-concept-cloudscape-digital-online-server-global-network-business-web-database-backup-computer-private-infrastructure-technology.webp","hash":"medium_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e","ext":".webp","mime":"image/webp","path":null,"width":750,"height":600,"size":31.33,"sizeInBytes":31326,"url":"https://cdn.marutitech.com/medium_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e.webp"},"thumbnail":{"name":"thumbnail_online-cloud-data-storage-concept-cloudscape-digital-online-server-global-network-business-web-database-backup-computer-private-infrastructure-technology.webp","hash":"thumbnail_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e","ext":".webp","mime":"image/webp","path":null,"width":195,"height":156,"size":3.77,"sizeInBytes":3772,"url":"https://cdn.marutitech.com/thumbnail_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e.webp"},"large":{"name":"large_online-cloud-data-storage-concept-cloudscape-digital-online-server-global-network-business-web-database-backup-computer-private-infrastructure-technology.webp","hash":"large_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":800,"size":51.62,"sizeInBytes":51616,"url":"https://cdn.marutitech.com/large_online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e.webp"}},"hash":"online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e","ext":".webp","mime":"image/webp","size":776.06,"url":"https://cdn.marutitech.com/online_cloud_data_storage_concept_cloudscape_digital_online_server_global_network_business_web_database_backup_computer_private_infrastructure_technology_1a7f68324e.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-02T06:08:23.972Z","updatedAt":"2025-05-02T06:08:59.177Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}},{"id":45,"attributes":{"createdAt":"2022-09-07T06:45:07.040Z","updatedAt":"2025-06-16T10:41:51.016Z","publishedAt":"2022-09-07T08:27:53.205Z","title":"12 Microservices Best Practices To Follow - 2025 Update","description":"Before changing your system to microservices, chek out the blog to understand why you need to do it","type":"Software Development Practices","slug":"microservices-best-practices","content":[{"id":12815,"title":null,"description":"<p><span style=\"font-weight: 400;\">If you deep dive into the conventional practices of developing applications, you will find that they were designed as monoliths, bundled into a bunch of code, and installed as a single unit. The practice of handling thousands of lines of code became cumbersome. It created obstacles in the path of architectural changes in large companies.</span></p><p><span style=\"font-weight: 400;\">In contemporary times, digital unicorns are developed and operated in no time. The digital revolution enables this process to occur at a brisk pace. The quantum leap in this field is made possible by flexible, scalable, and robust enterprise architecture that has been dubbed as </span><a href=\"https://marutitech.com/microservices-architecture-in-2019/\" target=\"_blank\" rel=\"noopener\"><span style=\"font-weight: 400;\">microservices architecture</span></a><span style=\"font-weight: 400;\">.</span></p>","twitter_link":null,"twitter_link_text":null},{"id":12816,"title":"What is Microservices Architecture?","description":"<p>Microservices architecture<span style=\"font-weight: 400;\"> is a method that structures an application as a collection of services that include the following:</span></p><ul>\n<li style=\"font-weight: 400;\" aria-level=\"1\"><span style=\"font-weight: 400;\">Testable and maintainable</span></li>\n<li style=\"font-weight: 400;\" aria-level=\"1\"><span style=\"font-weight: 400;\">Self-sufficiently deployable&nbsp;</span></li>\n<li style=\"font-weight: 400;\" aria-level=\"1\"><span style=\"font-weight: 400;\">Formed and organized around business abilities</span></li>\n<li style=\"font-weight: 400;\" aria-level=\"1\"><span style=\"font-weight: 400;\">Owned and managed by a small team</span></li>\n</ul><p><span style=\"font-weight: 400;\">Microservices architecture signifies many small, programmed, and self-contained services that carry out a single business operation. It facilitates speedy, periodic, and dependable delivery of large and complex applications.</span></p>","twitter_link":null,"twitter_link_text":null},{"id":12817,"title":"What are the Benefits of a Microservices Architecture?","description":"$27","twitter_link":null,"twitter_link_text":null},{"id":12818,"title":"Conclusion","description":"$28","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":3609,"attributes":{"name":"12 Microservices Best Practices To Follow - 2025 Update","alternativeText":null,"caption":null,"width":1344,"height":768,"formats":{"thumbnail":{"name":"thumbnail_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97727.webp","hash":"thumbnail_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae","ext":".webp","mime":"image/webp","path":null,"width":245,"height":140,"size":6.21,"sizeInBytes":6206,"url":"https://cdn.marutitech.com/thumbnail_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae.webp"},"small":{"name":"small_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97727.webp","hash":"small_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae","ext":".webp","mime":"image/webp","path":null,"width":500,"height":286,"size":15.54,"sizeInBytes":15542,"url":"https://cdn.marutitech.com/small_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae.webp"},"large":{"name":"large_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97727.webp","hash":"large_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":571,"size":36.54,"sizeInBytes":36536,"url":"https://cdn.marutitech.com/large_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae.webp"},"medium":{"name":"medium_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97727.webp","hash":"medium_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae","ext":".webp","mime":"image/webp","path":null,"width":750,"height":429,"size":25.67,"sizeInBytes":25670,"url":"https://cdn.marutitech.com/medium_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae.webp"}},"hash":"freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae","ext":".webp","mime":"image/webp","size":53.37,"url":"https://cdn.marutitech.com/freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97727_05e899d8ae.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-02T09:20:07.427Z","updatedAt":"2025-05-02T09:20:17.602Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}},{"id":47,"attributes":{"createdAt":"2022-09-07T06:45:08.007Z","updatedAt":"2025-06-16T10:41:51.261Z","publishedAt":"2022-09-07T08:33:28.858Z","title":"All You Need to Know about Microservices Architecture in 2025","description":"Get a crash course for all you need to know about microservice architecture in detail. ","type":"Software Development Practices","slug":"microservices-architecture-in-2019","content":[{"id":12821,"title":null,"description":"$29","twitter_link":null,"twitter_link_text":null},{"id":12822,"title":"What are Microservices?","description":"<p>Microservices or microservice architecture distinguishes an architectural style that encourages the development of smaller services with narrowly-focused interfaces that can be independently developed, deployed, scaled, and revised.</p><p>Microservices are a modern and alternative approach to the classic monolithic architecture which used to involve heavier tooling and more coordination efforts that ultimately added to developer friction.</p><p>The single-function modules built as part of microservices come along with clearly defined interfaces and operations. Microservices have grown more popular as enterprises look for more agility and move toward DevOps and a continuous testing framework.</p><p>Microservices are the answer to create scalable, testable software solutions that can be continually delivered within short bursts of time, as opposed to apps built with a monolithic architecture which take months/years to complete.</p>","twitter_link":null,"twitter_link_text":null},{"id":12823,"title":"How is Microservices Architecture Different than Monolithic Architecture","description":"$2a","twitter_link":null,"twitter_link_text":null},{"id":12824,"title":"Characteristics of Microservices","description":"$2b","twitter_link":null,"twitter_link_text":null},{"id":12825,"title":"Why Microservices are Important","description":"$2c","twitter_link":null,"twitter_link_text":null},{"id":12826,"title":"The Microservices First Approach","description":"$2d","twitter_link":null,"twitter_link_text":null},{"id":12827,"title":"When to Use Microservices and Its Known Uses","description":"$2e","twitter_link":null,"twitter_link_text":null},{"id":12828,"title":"Final Word","description":"$2f","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":3601,"attributes":{"name":"All You Need to Know about Microservices Architecture in 2025","alternativeText":null,"caption":null,"width":2048,"height":1168,"formats":{"thumbnail":{"name":"thumbnail_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97724.webp","hash":"thumbnail_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df","ext":".webp","mime":"image/webp","path":null,"width":245,"height":140,"size":6.68,"sizeInBytes":6684,"url":"https://cdn.marutitech.com/thumbnail_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df.webp"},"small":{"name":"small_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97724.webp","hash":"small_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df","ext":".webp","mime":"image/webp","path":null,"width":500,"height":285,"size":17.71,"sizeInBytes":17712,"url":"https://cdn.marutitech.com/small_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df.webp"},"medium":{"name":"medium_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97724.webp","hash":"medium_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df","ext":".webp","mime":"image/webp","path":null,"width":750,"height":428,"size":29.63,"sizeInBytes":29630,"url":"https://cdn.marutitech.com/medium_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df.webp"},"large":{"name":"large_freepik__the-style-is-dark-and-mysterious-inspired-by-neoex__97724.webp","hash":"large_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":570,"size":42,"sizeInBytes":42004,"url":"https://cdn.marutitech.com/large_freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df.webp"}},"hash":"freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df","ext":".webp","mime":"image/webp","size":104.45,"url":"https://cdn.marutitech.com/freepik_the_style_is_dark_and_mysterious_inspired_by_neoex_97724_e0079a67df.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-02T08:57:39.589Z","updatedAt":"2025-05-02T08:57:49.660Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1805,"title":"Product Development Team for SageData - Business Intelligence Platform","link":"https://marutitech.com/case-study/product-development-of-bi-platform/","cover_image":{"data":{"id":352,"attributes":{"name":"13 (1).png","alternativeText":"13 (1).png","caption":"13 (1).png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_13 (1).png","hash":"thumbnail_13_1_5acc5134e3","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":16.46,"sizeInBytes":16457,"url":"https://cdn.marutitech.com//thumbnail_13_1_5acc5134e3.png"},"medium":{"name":"medium_13 (1).png","hash":"medium_13_1_5acc5134e3","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":131.49,"sizeInBytes":131487,"url":"https://cdn.marutitech.com//medium_13_1_5acc5134e3.png"},"large":{"name":"large_13 (1).png","hash":"large_13_1_5acc5134e3","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":230.28,"sizeInBytes":230279,"url":"https://cdn.marutitech.com//large_13_1_5acc5134e3.png"},"small":{"name":"small_13 (1).png","hash":"small_13_1_5acc5134e3","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":60.64,"sizeInBytes":60638,"url":"https://cdn.marutitech.com//small_13_1_5acc5134e3.png"}},"hash":"13_1_5acc5134e3","ext":".png","mime":"image/png","size":67.37,"url":"https://cdn.marutitech.com//13_1_5acc5134e3.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:43:03.732Z","updatedAt":"2024-12-16T11:43:03.732Z"}}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]},"seo":{"id":2035,"title":"How To Configure API Gateway in Microservices Architecture","description":"An API manages communication between your application and users, ensuring user requests reach their destination. Learn how to configure API gateway in microservices. ","type":"article","url":"https://marutitech.com/api-gateway-in-microservices-architecture/","site_name":"Maruti Techlabs","locale":"en-US","schema":[{"@context":"https://schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"What is the difference between an API and an API gateway?","acceptedAnswer":{"@type":"Answer","text":"An API conducts specific functions within a software or system. However, an API Gateway offers features like authentication and routing by sharing and controlling different requests to various APIs."}},{"@type":"Question","name":"How do microservices communicate with each other?","acceptedAnswer":{"@type":"Answer","text":"Microservices communicate using two basic types of messaging patterns.Synchronous Communication: Here, using protocols such as HTTP or gRPC, one service calls the API exposed by another service. This accounts for synchronous communication, as the caller awaits a response from the receiver.Asynchronous Communication: A service sends a message without awaiting a response, and other services attend to this message later."}},{"@type":"Question","name":"Is it possible to create microservices without an API gateway?","acceptedAnswer":{"@type":"Answer","text":"Without an API gateway, your applications observe a direct client-to-microservice pattern. Whether you have 10 or 100 services, an API gateway offers a unified entry point for users. This can quickly become chaotic, with numerous client requests directed to different microservice endpoints that offer no flexibility between client apps and microservices. Such complexity can hinder innovation, making it difficult to update your microservices."}},{"@type":"Question","name":"How do I secure an API gateway in microservices?","acceptedAnswer":{"@type":"Answer","text":"Here are 5 best practices for securing your API gateway: Authentication: Use short-lived tokens for token-based authentication Authorization: For all API endpoints, employ Role-Based Access Control (RBAC) Rate Limiting: Learning user behavior and context to implement dynamic, layered rate limiting. CORS: Specify and limit allowed origins Logging: Incorporate real-time monitoring and detection of anomalies."}},{"@type":"Question","name":"What tools or platforms can be used to configure an API Gateway for microservices?","acceptedAnswer":{"@type":"Answer","text":"Here are the most famous tools and platforms that can be used to configure your API gateways. API management and testing tools: Azure API Management, Tyk Messaging tools: Apache Kafka, Google Cloud Pub/Sub, RabbitMQ Toolkits: Fabric8, Seneca Architectural frameworks: Goa, Kong, Micronaut Orchestration tools: Kubernetes, Mesos, Conductor Monitoring tools: Graphite, Graylog, Prometheus(+ Grafana) Serverless tools: Serverless, Apache Openwhisk, Claudia, IronFunctions"}}]}],"image":{"data":{"id":301,"attributes":{"name":"7d990f61-microservices-archietecture.jpg","alternativeText":"7d990f61-microservices-archietecture.jpg","caption":"7d990f61-microservices-archietecture.jpg","width":1000,"height":600,"formats":{"thumbnail":{"name":"thumbnail_7d990f61-microservices-archietecture.jpg","hash":"thumbnail_7d990f61_microservices_archietecture_83204947ea","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":147,"size":5.5,"sizeInBytes":5503,"url":"https://cdn.marutitech.com//thumbnail_7d990f61_microservices_archietecture_83204947ea.jpg"},"small":{"name":"small_7d990f61-microservices-archietecture.jpg","hash":"small_7d990f61_microservices_archietecture_83204947ea","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":300,"size":14.29,"sizeInBytes":14287,"url":"https://cdn.marutitech.com//small_7d990f61_microservices_archietecture_83204947ea.jpg"},"medium":{"name":"medium_7d990f61-microservices-archietecture.jpg","hash":"medium_7d990f61_microservices_archietecture_83204947ea","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":450,"size":25.3,"sizeInBytes":25296,"url":"https://cdn.marutitech.com//medium_7d990f61_microservices_archietecture_83204947ea.jpg"}},"hash":"7d990f61_microservices_archietecture_83204947ea","ext":".jpg","mime":"image/jpeg","size":39,"url":"https://cdn.marutitech.com//7d990f61_microservices_archietecture_83204947ea.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:40:13.735Z","updatedAt":"2024-12-16T11:40:13.735Z"}}}},"image":{"data":{"id":3584,"attributes":{"name":"How To Configure API Gateway in Microservices Architecture","alternativeText":null,"caption":null,"width":2048,"height":1168,"formats":{"small":{"name":"small_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"small_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":500,"height":285,"size":8.03,"sizeInBytes":8034,"url":"https://cdn.marutitech.com/small_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"medium":{"name":"medium_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"medium_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":750,"height":428,"size":13.15,"sizeInBytes":13152,"url":"https://cdn.marutitech.com/medium_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"thumbnail":{"name":"thumbnail_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"thumbnail_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":245,"height":140,"size":3.13,"sizeInBytes":3126,"url":"https://cdn.marutitech.com/thumbnail_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"},"large":{"name":"large_freepik__a-modern-digital-illustration-of-an-api-gateway-co__97721.webp","hash":"large_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":570,"size":18.22,"sizeInBytes":18222,"url":"https://cdn.marutitech.com/large_freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp"}},"hash":"freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c","ext":".webp","mime":"image/webp","size":45.18,"url":"https://cdn.marutitech.com/freepik_a_modern_digital_illustration_of_an_api_gateway_co_97721_7e6fe0069c.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-02T06:18:38.613Z","updatedAt":"2025-05-02T06:18:52.237Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
30:T6ee,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/api-gateway-in-microservices-architecture/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#webpage","url":"https://marutitech.com/api-gateway-in-microservices-architecture/","inLanguage":"en-US","name":"How To Configure API Gateway in Microservices Architecture","isPartOf":{"@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#website"},"about":{"@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#primaryimage","url":"https://cdn.marutitech.com//7d990f61_microservices_archietecture_83204947ea.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/api-gateway-in-microservices-architecture/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"An API manages communication between your application and users, ensuring user requests reach their destination. Learn how to configure API gateway in microservices. "}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"How To Configure API Gateway in Microservices Architecture"}],["$","meta","3",{"name":"description","content":"An API manages communication between your application and users, ensuring user requests reach their destination. Learn how to configure API gateway in microservices. "}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$30"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/api-gateway-in-microservices-architecture/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"How To Configure API Gateway in Microservices Architecture"}],["$","meta","9",{"property":"og:description","content":"An API manages communication between your application and users, ensuring user requests reach their destination. Learn how to configure API gateway in microservices. "}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/api-gateway-in-microservices-architecture/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//7d990f61_microservices_archietecture_83204947ea.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"How To Configure API Gateway in Microservices Architecture"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"How To Configure API Gateway in Microservices Architecture"}],["$","meta","19",{"name":"twitter:description","content":"An API manages communication between your application and users, ensuring user requests reach their destination. Learn how to configure API gateway in microservices. "}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//7d990f61_microservices_archietecture_83204947ea.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
