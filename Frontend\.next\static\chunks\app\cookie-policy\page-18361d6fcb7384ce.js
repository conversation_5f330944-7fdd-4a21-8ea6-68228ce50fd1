(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3800,3310],{39006:function(e,t,o){Promise.resolve().then(o.bind(o,97753)),Promise.resolve().then(o.t.bind(o,49515,23)),Promise.resolve().then(o.t.bind(o,98633,23)),Promise.resolve().then(o.t.bind(o,26155,23)),Promise.resolve().then(o.t.bind(o,46282,23)),Promise.resolve().then(o.t.bind(o,25323,23))},97753:function(e,t,o){"use strict";o.r(t);var n=o(16480),r=o.n(n),i=o(2265),p=o(12865),a=o(57437);let x=i.forwardRef((e,t)=>{let{bsPrefix:o,fluid:n=!1,as:i="div",className:x,...b}=e,l=(0,p.vE)(o,"container");return(0,a.jsx)(i,{ref:t,...b,className:r()(x,n?"".concat(l).concat("string"==typeof n?"-".concat(n):"-fluid"):l)})});x.displayName="Container",t.default=x},12865:function(e,t,o){"use strict";o.d(t,{SC:function(){return l},pi:function(){return x},vE:function(){return a},zG:function(){return b}});var n=o(2265);o(57437);let r=n.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"],minBreakpoint:"xs"}),{Consumer:i,Provider:p}=r;function a(e,t){let{prefixes:o}=(0,n.useContext)(r);return e||o[t]||t}function x(){let{breakpoints:e}=(0,n.useContext)(r);return e}function b(){let{minBreakpoint:e}=(0,n.useContext)(r);return e}function l(){let{dir:e}=(0,n.useContext)(r);return"rtl"===e}},46282:function(e){e.exports={center:"Heading_center__XBGsG",left:"Heading_left__ouHog",right:"Heading_right__jsN_Y"}},49515:function(e){e.exports={variables:'"@styles/variables.module.css"',gray300:"#F3F3F3",colorBlack:"#000000",colorWhite:"#FFFFFF",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":"1440px","breakpoint-lg":"992px","breakpoint-md":"768px","breakpoint-sm":"576px","breakpoint-sm-427":"427px",container:"RichText_container__wi5qG",containerForAllServicesPage:"RichText_containerForAllServicesPage__4Usfa",containerForCaseStudy:"RichText_containerForCaseStudy__FF9bM",containerForAboutUsPage:"RichText_containerForAboutUsPage__idhOI",caseStudy_desc:"RichText_caseStudy_desc__Cim4Q",challenges_desc:"RichText_challenges_desc__kSEfc",resultsAndSolutions_desc:"RichText_resultsAndSolutions_desc__4PakA",grayBackground:"RichText_grayBackground__cVdD6"}},26155:function(e){e.exports={"breakpoint-xs":"0","breakpoint-sm-195":"195px","breakpoint-sm-270":"270px","breakpoint-sm-200":"200px","breakpoint-sm-320":"320px","breakpoint-sm-326":"326px","breakpoint-sm-390":"390px","breakpoint-sm-367":"367px","breakpoint-sm-365":"365px","breakpoint-sm-340":"340px","breakpoint-sm-350":"350px","breakpoint-sm-370":"370px","breakpoint-sm-380":"380px","breakpoint-sm-424":"424px","breakpoint-sm-427":"427px","breakpoint-sm-420":"420px","breakpoint-sm-430":"430px","breakpoint-sm-450":"450px","breakpoint-sm-460":"460px","breakpoint-sm-484":"484px","breakpoint-sm-480":"480px","breakpoint-sm-532":"532px","breakpoint-sm-550":"550px","breakpoint-sm":"576px","breakpoint-md-579":"579px","breakpoint-md-585":"585px","breakpoint-md-767":"767px","breakpoint-md":"768px","breakpoint-md-769":"769px","breakpoint-md-820":"820px","breakpoint-md-850":"850px","breakpoint-lg-901":"901px","breakpoint-lg":"992px","breakpoint-lg-991px":"991px","breakpoint-xl-1024":"1024px","breakpoint-xl-1051":"1051px","breakpoint-xl-1208":"1208px","breakpoint-xl-1023":"1023px","breakpoint-xl-1199":"1199px","breakpoint-xl-1188":"1188px","breakpoint-xl":"1200px","breakpoint-xl-1365":"1365px","breakpoint-xl-1366":"1366px","breakpoint-xl-1309":"1309px","breakpoint-xl-1400":"1400px","breakpoint-xl-1439":"1439px","breakpoint-xl-1440":"1440px","breakpoint-xl-1405":"1405px","breakpoint-xl-1406":"1406px","breakpoint-xl-1600":"1600px","breakpoint-xl-1800":"1800px","breakpoint-xl-2000":"2000px","breakpoint-xl-2100":"2100px","breakpoint-xl-2442":"2442px","breakpoint-xl-2559":"2559px","breakpoint-xl-2560":"2560px"}},25323:function(e){e.exports={variables:'"./variables.module.css"',h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",fontWeight600:"600",fontWeight700:"700",breakPoints:'"./breakpoints.module.css"',"breakpoint-sm-450":"450px",h1:"typography_h1__DecPZ",h2:"typography_h2__Dn0zf",h3:"typography_h3__o3Abb",h4:"typography_h4__lGrWj",h5:"typography_h5__DGJHL",h6:"typography_h6__vf_A0",caption:"typography_caption__hfk0A"}},98633:function(e){e.exports={brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",gray100:"#FCFCFC",gray200:"#F8F8F8",gray300:"#F3F3F3",gray400:"#E4E4E4",gray500:"#CDCDCD",gray600:"#B1B1B1",gray700:"#808080",gray800:"#646464",gray900:"#3A3A3A",error:"#FF6D60",success:"#23A881",grayBorder:"#8C8B8B",link:"#0075FF",grayBlueFonts:"#262531",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",halfSpace:"4px",oneSpace:"8px",twoSpace:"16px",threeSpace:"24px",fourSpace:"32px",fiveSpace:"40px",sixSpace:"48px",eightSpace:"64px",tenSpace:"80px",fifteenSpace:"120px",twentyFiveSpace:"200px",h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",bodyHeadingXL:"56px",bodyHeadingL:"24px",bodyHeadingM:"21px",bodyHeadingS:"20px",bodyHeadingXS:"18px",bodyHeadingXSS:"16px",buttonLabelXLargeFontSize:"26px",buttonLabelLargeFontSize:"20px",buttonLabelMediumFontSize:"16px",buttonLabelSmallFontSize:"14px",bodyTextXLarge:"26px",bodyTextLarge:"22px",bodyTextMedium:"20px",bodyTextSmall:"18px",bodyTextXSmall:"16px",bodyTextXXSmall:"14px",bodyTextXXXSSmall:"8px",bodyLinkXXLarge:"26px",bodyLinkXLarge:"22px",bodyLinkLarge:"19px",bodyLinkMedium:"18px",bodyLinkSmall:"17px",bodyLinkXSmall:"16px",bodyLinkXXSmall:"15px",fontWeight100:"100",fontWeight200:"200",fontWeight300:"300",fontWeight400:"400",fontWeight500:"500",fontWeight600:"600",fontWeight700:"700",fontWeight800:"800",fontWeight900:"900"}},16480:function(e,t){"use strict";var o;!/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/function(){var n={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var o=arguments[t];o&&(e=i(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var o in e)n.call(e,o)&&e[o]&&(t=i(t,o));return t}(o)))}return e}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0!==(o=(function(){return r}).apply(t,[]))&&(e.exports=o)}()}},function(e){e.O(0,[2971,8069,1744],function(){return e(e.s=39006)}),_N_E=e.O()}]);