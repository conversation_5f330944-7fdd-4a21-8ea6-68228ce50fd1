3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","artificial-intelligence-fake-news","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","artificial-intelligence-fake-news","d"],{"children":["__PAGE__?{\"blogDetails\":\"artificial-intelligence-fake-news\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","artificial-intelligence-fake-news","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
12:T558,<p><a href="http://www.theverge.com/2014/10/22/7028983/fake-news-sites-are-using-facebook-to-spread-ebola-panic" target="_blank" rel="noopener">The Verge</a> reported that the news around Ebola was wreaking havoc in Texas towns. It was spreading like wildfire on the social landscape and these were messages from sources that sounded like newspapers. This happened in 2014 and what followed were trails of fake news sending shock waves across the media and user world.</p><p>As per <a href="https://www.nytimes.com/2016/11/09/us/politics/debunk-fake-news-election-day.html?_r=0" target="_blank" rel="noopener">The New York Times</a>, just before the presidential elections in the US, fake news and memes became the tools for perpetrators to influence the outcome of elections. Fake news is rearing its ugly head time and time again. Not for nothing are the tech behemoths, Facebook and Google as well as media companies waging war on fake news. Is there a way to differentiate the fake news from the truth?</p><p>The trouble begins when too much of information is shared through the internet – information that needs more than a human mind to identify the fake enjoying the status like the original. In this realm, artificial intelligence and big data have emerged as potent tools to track news stories and identify fake news items playing the trick on the user.</p>13:T941,<p>Any news becomes a fake news if the information presented is incorrect or information doesn’t represent facts that it is expected to carry. When it comes to information, it is also about assuring the veracity of information as it is about moving, processing and securing information. In short, fake news and information are more of a big data veracity issue.</p><p>When it comes to handling fake news, none have put a braver face than Facebook. With trillions of user posts, Facebook realized that manual fact-checking wouldn’t do any good to solve the fake news problem. Facebook turned to artificial intelligence to arrest this problem. <a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">Artificial intelligence services</span></a><span style="font-family:Arial;"> are employed to combat the spread of fake news, utilizing advanced algorithms to analyze and verify information in real time, enhancing news integrity and accuracy.</span></p><p>Artificial intelligence is now looked upon as the cornerstone to separate the good from bad in the news field. That is because artificial intelligence makes it easy to learn behaviors, possible through pattern recognition. Harnessing artificial intelligence’s power, fake news can be identified by taking a cue from articles that were flagged as inaccurate by people in the past.</p><p>As the volume of data grows bigger by the day, so is the chance of handling misinformation as it challenges the human ability to uncover the truth. Artificial intelligence has turned into a beacon of hope for to assure data veracity, and more importantly, identify fake news.</p><p><img src="https://cdn.marutitech.com/Is_artificial_intelligence_the_key_to_combat_fake_news_v2_7c452a77bc.jpg" alt="Is-artificial-intelligence-the-key-to-combat-fake-news_v2.jpg" srcset="https://cdn.marutitech.com/thumbnail_Is_artificial_intelligence_the_key_to_combat_fake_news_v2_7c452a77bc.jpg 134w,https://cdn.marutitech.com/small_Is_artificial_intelligence_the_key_to_combat_fake_news_v2_7c452a77bc.jpg 430w,https://cdn.marutitech.com/medium_Is_artificial_intelligence_the_key_to_combat_fake_news_v2_7c452a77bc.jpg 645w,https://cdn.marutitech.com/large_Is_artificial_intelligence_the_key_to_combat_fake_news_v2_7c452a77bc.jpg 860w," sizes="100vw"></p>14:Taa4,<p>As the world gets ready to tackle fake news, technology has set the trend by showing us how to identify fake news. Here are some ways leveraged to fight fake news.</p><h3 style="margin-left:0px;"><span style="color:rgb(0,0,0);font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Score web pages</strong></span></h3><p style="margin-left:0px;">Scoring web pages is a method pioneered by the tech giant Google. Google takes the accuracy of facts presented to score web pages. The technology has grown in significance as it makes an attempt to understand pages’ context without relying on third party signals.</p><h3 style="margin-left:0px;"><span style="color:rgb(0,0,0);font-family:Poppins, sans-serif;font-size:18px;"><strong>2.&nbsp;Weigh facts</strong></span></h3><p style="margin-left:0px;">To combat fake news, it is imperative to weigh facts that the news in context purports to share. Artificial intelligence is now at the core of ascertaining the semantic meaning of a web article. For instance, an NLP engine can go through the subject of a story, headline, main body text and the geo-location. Further, artificial intelligence will find out if other sites are reporting the same facts. In this way, facts are weighed against reputed media sources using artificial intelligence.</p><h3 style="margin-left:0px;"><span style="color:rgb(0,0,0);font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Predict reputation</strong></span></h3><p style="margin-left:0px;">Even before eyeballs capture news items, knowing the reputation of the source sharing the news will do a world of good to nip fake news problem in the bud.</p><p style="margin-left:0px;">The reference to The Wall Street Journal would raise no qualms about the reputation of this source. This becomes stronger when it is compared with another source that is unknown. It is now possible to determine the authenticity of a website. By creating a machine learning model, a website’s reputation can be predicted, considering features like domain name and Alexa web rank.</p><h3 style="margin-left:0px;"><span style="color:rgb(0,0,0);font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Discover sensational words</strong></span></h3><p style="margin-left:0px;">When it comes to news items, the headline is the key to capture the attention of the audience. It is for this reason that sensational headlines become a handy tool to capture readers’ interest. When sensational words are used to spread fake news, it becomes a lure to attract more eyeballs and spread the news faster and wider. Not anymore, as artificial intelligence has been instrumental in discovering and flagging fake news headlines by using keyword analytics.</p>15:T758,<p>To combat negative forces, you will have to fight fire with fire and in this case, fight fake news with robust tools. &nbsp;French news media has already sprung into action by opening a fact-checking service to stop fake news items in their tracks. There are robust tools that have proved useful in debunking false news items. <a href="https://www.forbes.com/sites/bernardmarr/2017/03/01/fake-news-how-big-data-and-ai-can-help/#1d403dc70d56" target="_blank" rel="noopener">Forbes.com</a> had given a summary of some tools used to fight fake news.</p><ul><li>Spike is a tool leveraged to identify and predict breakout stories as well as viral stories. The tool analyzes mountains of data from the world of news and predicts what’s going to drive engagement</li><li>Hoaxy is a tool that helps users to identify fake news sites</li><li>Snopes is a website that helps spot fake stories</li><li>CrowdTangle is a tool that helps discover social content early and monitor content</li><li>Check is a tool from Meedan that helps verify news breaking online</li><li>Google Trends proves its worth by watching searches</li><li>Le Decodex from Le Monde is a database that houses websites that are tagged as ‘fake’, ‘real’ among others</li><li>Pheme has made a technology leap to read the veracity of user-generated and online content</li></ul><p>Fake news is now a growing menace in the media world. With artificial intelligence and big data showing the way to tackle fake news items, the belief that truth will dawn on the reader gets stronger by the day. But, this is just the beginning.</p><p>We have not yet realized the true potentials of artificial intelligence in combating fake news. The future holds good for more sophisticated tools that harness the power of artificial intelligence, big data and machine learning to stop fake news making ripples in the user world.</p>16:T56f,<p>Automation of services has picked up its fastest pace by now, giving users the much needed facility to fulfill their regular tasks. <span style="font-family:Arial;">With advanced systems powered by </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">AI solutions</span></a><span style="font-family:Arial;">, users can now book a restaurant reservation, order a pizza, book a movie ticket, hotel room, and even make a clinic appointment.&nbsp;</span> Customer service industry is gaining much momentum especially due to disruption of Artificial Intelligence – a technological breakthrough that has taken almost every business industry by storm.</p><p>By transforming customer service interactions, <a href="https://wotnot.io/" target="_blank" rel="noopener">AI-powered digital solutions</a> are prepared to improve every aspect of your business including online customer experience, loyalty, brand reputation, preventive assistance and even generation of revenue streams. Digital market moguls project that by 2020 more than 85% of all customer support communications will be conducted without engaging any customer service representatives.</p><p>This blog delves into the subject a little more to convey how AI-powered customer service can possibly help customer support agents online.</p>17:Tfa5,<p><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">According to a recent&nbsp;</span><a href="https://www.zendesk.com/resources/customer-service-and-lifetime-customer-value/" target="_blank" rel="noopener">Zendesk study</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">, as much as 42% of B2C customers showed more interest in purchasing after experiencing good customer service. The same study also goes to claim that 52% of them stopped purchasing due to a single disappointing customer support interaction.</span></p><p><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">There is no argument that forward thinkers consider AI technology as a solution that will open the doors for real-time self-service for customer service platforms. Also, it is true that the technology has power enough to change the way customer service solutions are designed. However, there is a massive hype floating around about how AI assisted responses will completely replace the need for human agents.</span></p><p>Though most of the excitement about AI is due to its two major capabilities:</p><ol><li>a) Machine learning and</li><li>b) Natural language processing (NLP)</li></ol><p><a href="https://marutitech.com/artificial-intelligence-and-machine-learning/" target="_blank" rel="noopener">Machine learning</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;"> is attributed to a powerful computing system that churns a large amount of data to learn from it. Facebook messenger, request suggestions and spam folders are everyday examples of AI machine learning process.</span></p><p><a href="https://marutitech.com/how-is-natural-language-processing-applied-in-business/" target="_blank" rel="noopener">Natural language processing</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;"> supports your daily interactions with AI software using its ability to process and interpret spoken/written messages. Siri, Cortana, Alexa are best examples of evolved NLP.</span></p><p><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">Artificial Intelligence mainly revolves around these two innovative capabilities to power the job of customer support agents. Its </span><a href="https://marutitech.com/cognitive-computing-features-scope-limitations/" target="_blank" rel="noopener">cognitive computing</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;"> power enables businesses to offer efficient services to customers.</span></p><p><a href="https://www.zendesk.com/resources/gartner-explores-customer-experience-innovation-2017/" target="_blank" rel="noopener">A recent Gartner report</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">&nbsp;suggests that 55% of established companies either have started making investments in the potential of artificial intelligence or are planning to do so by 2020.</span></p><p><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">Let’s learn more about how much AI can really do for today’s customer service representative working in a call center and for businesses they work for.</span><img src="https://cdn.marutitech.com/AI_1024x535_a9bfb651f4.png" alt="better call center support with ai" srcset="https://cdn.marutitech.com/thumbnail_AI_1024x535_a9bfb651f4.png 245w,https://cdn.marutitech.com/small_AI_1024x535_a9bfb651f4.png 500w,https://cdn.marutitech.com/medium_AI_1024x535_a9bfb651f4.png 750w,https://cdn.marutitech.com/large_AI_1024x535_a9bfb651f4.png 1000w," sizes="100vw"></p>18:Tc40,<p>AI is swiftly disrupting the customer service space with its massive power to multi-task and quick-respond with automated queries. By limiting research time and offering considerable action plans, AI-assisted automation of customer service platforms can generate responses with accuracy and speed that humans can’t deliver.</p><p>According to Forrester <a href="https://www-01.ibm.com/marketing/iwm/dre/signup?source=urx-19703&amp;S_PKG=ov61199" target="_blank" rel="noopener">report</a> on customer service trends, we have already stepped into the era of automated, smarter and more strategic customer service. Individuals will appreciate pre-emptive actions delivered by intelligent agents fuelled with artificial intelligence.</p><p><a href="https://marutitech.com/complete-guide-chatbots/" target="_blank" rel="noopener">AI for customer service</a> will not only make self-service interfaces more intuitive and economical, but its intelligence will help anticipate specific customer needs learning from their contexts, previous chat history and preferences. AI integrated system will capture infinite online data in order to:</p><ul><li>Identify customer issues</li><li>Process and learn from gathered information</li><li>Define customer behavior pattern</li><li>Determine their frequent decisions and preferences</li><li>Respond with solutions and suitable products</li><li>Prompt with proactive alert messages</li><li>Suggest personalized offers and discounts</li><li>Offer real-time support (FAQs, help blogs, reports)</li><li>Resolve issues before they arrive</li><li>Minimize customer abandonment rate and complaints</li></ul><p>With such wide scope of intelligent assistance and pre-emptive recommendations, companies will leave behind rich customer experience.</p><h3><strong>One-time investment for timeless merits</strong></h3><p>Stinting on cost is the first priority for businesses as on today. When it comes to call center practices, it takes a good deal of money and time in hiring and training staff for customer service, as well as in erecting the whole brick-and-mortar infrastructure. Just 10 support individuals can cost you as much as $35000, or even more if recruits frequently quit (attrition being quite high in the call center industry) – which is a nightmare.</p><p>On the other hand, automating responses via AI enabled customer service platforms can minimize this burden by reducing cost and time. This is what <a href="https://www.ibm.com/watson/call-center-ai/?cm_mmc=OSocial_Blog-_-Watson+Core_Watson+Core+-+Conversation-_-WW_WW-_-10+reasons+why+Blog+2+10+2&amp;cm_mmca1=000027BD&amp;cm_mmca2=10004432&amp;" target="_blank" rel="noopener">Watson</a>&nbsp;as an AI platform does. It is a pre-programmed intelligent system stuffed with domain-specific knowledge base. All it requires is to be trained, just once. Upon introducing new process changes, just re-configure the software instead of retraining your entire support staff.</p><p>Such AI assisted platforms take over the same routine customer requests, enabling call center employees to work on more important and grueling tasks at hand.</p>19:Te8c,<p>AI technology is not just for giving direct assistance to customers, but it can also be used to usher customer service path. At times when issues get complicated, an intelligent support system will have a certain capability to direct customers towards parallel support channels. For instance, if a telecommunication customer service agent is unable to resolve queries regarding technical network issues, the chat AI can identify the problem as specific to dedicated support channel and shift customers towards it.</p><p>Thus, AI for customer service process brings comprehensive balance in the support system. While customers receive efficient solutions, agents fulfill their service commitments and relieve loaded support channels from the hectic rush.</p><h3><strong>AI machine learning for extra support</strong></h3><p>If not directly, AI functions best even indirectly for customers and service agents alike. Human representatives can take extra assistance they need to serve the B2C customers. It can speed up the resolution process by discovering and delivering solutions in time on behalf of agents. By learning from repeated issues that are frequently resolved, machine learning power enables customer support to be ready for tough challenges that chatbots sometimes fail to address.</p><p>Any call center with AI machine learning capabilities can perform well by suggesting accurate solutions to specific issues. AI’s learning potential to sense human behavior patterns can contribute to both agents and customers.</p><h3><strong>Precise predictions and insight</strong></h3><p>You must have felt surprised at how Amazon e-commerce app knows what you would like based on your frequent page visits, cart items selection and social sharing. That right there is the essence of machine learning algorithm, and it can be also used to predict the kind of places, entertainment or merchandises you prefer. Similarly, AI can make predictions about what customers would want, which ultimately benefits customer service agents. Such insightful predictions can be translated into future actions to be taken by customers based on their choices, likes and visited contents.</p><p>AI suggests next best action for agents by learning about the most suitable responses to the customer-generated ticket. This is quite helpful in a business where product range and number of actions are high. Agents who are new to the business especially get a great amount of help and direction.</p><p>Not only that, once <a href="https://marutitech.com/machine-learning-predictive-analytics/" target="_blank" rel="noopener">predictive analytics</a> tools are integrated into customer support, it will be easy for agents to grasp their interaction quality by knowing in advance – the customer satisfaction level and overall customer experience.</p><h3><strong>Uninterrupted momentum of service</strong></h3><p>Who doesn’t appreciate customer support with fast response and uninterrupted service? One of the surprising benefits from using AI for automating responses is its independence from time constraints and holiday offs. This means that at any given moment customers will be able to interact with AI robot to resolve issues. Such uninterrupted customer service helps organizations stay responsive 24/7 to address incoming customer inquiries. As there will be an assurance of consistent support, problems faced in case of human customer service reps will be effectively eliminated.</p><p>The results are:</p><ul><li>No wait time</li><li>Quick resolution</li><li>Prompt escalation</li><li>Enhanced customer satisfaction</li><li>High-grade service solutions</li><li>Improved commitment level</li><li>Increased brand reputation</li></ul>1a:Tbec,<p><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">As customers’ needs evolve, businesses that are determined to serve the best quality have to integrate unique methods of assistance to offer unquestionable reliability and flexibility. In a tech-rich era, consumers expect a great level of maturity in the way enterprises propose service solutions. Using the cognitive knowledge base of </span><a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">intelligent chatbots</a><span style="background-color:rgb(255,255,255);color:rgb(56,56,56);font-family:Raleway, sans-serif;font-size:16px;">, service-based industries can power their everyday interactions with their customers.</span></p><p>If manipulated correctly, AI technology yields such magnitude of reliability that is hard for human counterparts to achieve. The inclusion of chatbots helps surmount all possible barriers and pain points experienced in case of human customer support agents. Chatbots can:</p><ul><li>Offer freedom from obstacles caused by humans</li><li>Eliminate all biases and barriers</li><li>Bridge the gap virtually between business and customers</li><li>Establish connection of reliability and trust</li><li>Improve brand reputation through quick, single-attempt assistance</li><li>Be designed to provide frictionless, flawless communication</li><li>Escalate customer inquiries when unable to solve themselves</li><li>Surpass negative human emotions (anger, annoyance, arguments, aggression, and forcefulness)</li><li>Bring repeated business<strong>&nbsp;</strong></li></ul><h3><strong>Email support is thinkable with AI</strong></h3><p>Even after Amazon’s sensible Alexa and Apple’s Siri, we can say AI technology is still getting smarter while going through the process of improvement and innovations. Despite its role as Artificial Intelligence for customer service, machine learning capabilities of AI software still lack certain points where it needs refinement and human-like sensibility.</p><p>When it comes to handling email support, AI robot should ideally be making suggestions and writing a proper draft to answer customer inquiries through emails. Email support is where automated responses tossed directly to customers do not produce many results, giving businesses hard time to cope with incoming queries. However, this scenario can be something AI-powered customer service platforms can work on.</p><p>With gradually developed ability to learn from the large dataset, AI email support can offer certain meaningful solutions just like chatbots. It can suggest a help article using natural language processing system. It can even fetch some part of email draft for people working in a call center.</p><p>Since it requires accurate learning, AI can turn out to be a thinkable investment for service structures where the overall volume of support conversations is in thousands on monthly basis. Intelligent services can then be an efficient solution.</p>1b:T759,<p>The human brain has limited capacity and is often subject to issues of inaccuracies and flaws when it comes to serving people to the best of their performance caliber. On the other hand, AI assisted service solutions conform to predetermined standards and well-programmed efficiency, resulting in high-quality, straightforward customer experience delivered with minimal AHT (Average Handling Time).</p><p>Due to the highly capable machine learning process of AI-enabled chatbots, businesses can be sure their deliverables will be unscathed and immensely satisfying to customers’ expectations. Thus, we can conclude that inclusion of automating responses of AI-powered robots can pull off business target with utmost precision, without consuming much of customer’s time and resources.</p><h3><strong>Data mine transformed into personalization</strong></h3><p>In the online space, we all leave an enormous pile of data behind in our lifetime. But only 1/3<sup>rd</sup> of it is actually worth analysis. If analyzed and harnessed properly, organizations can leverage it to transform their businesses and boost brand engagement. Enterprises collecting such gigantic data can use the combined power of Big Data, AI and its machine learning capabilities to make customer journey more enlivened and personalized.</p><p>Brands can weave engaging product theories or personalized recommendations for each customer, creating an unparalleled stream of customers each day. Based on customer reviews and feedback, it becomes easy to navigate around their needs and browsing pattern and customize web design to individual customer’s taste. Such level of AI technology intervention for personalization greatly impacts on:</p><ul><li>Customer service interaction</li><li>Engagement level</li><li>CSAT</li><li>Customer retention</li><li>Repeat business</li><li>Conversion metrics</li></ul>1c:T57b,<p>AI-augmented customer service is maturing as sophisticated enterprises turn to strategic investment in artificial intelligence for their innovative front-end chatbot service. AI blows trumpet across the globe with its attractive benefits such as efficiency improvement, fast resolution, accurate assistance, brand reputation and increased revenue.</p><p><a href="https://www.oracle.com/webfolder/s/delivery_production/docs/FY16h1/doc35/CXResearchVirtualExperiences.pdf" target="_blank" rel="noopener">Oracle in its study of AI as a customer service</a> says that nearly 8 out of 10 businesses have adopted or are planning to adopt the power of AI for customer care solutions by 2020.</p><p>Instead of implementing fully automated front-end AI-powered bots, many enterprises prefer to invest in AI-assisted human agent model where human customer service representatives are supported by AI technology.</p><p>Front-end AI chatbots handle common first-level queries learning from historical tickets, FAQs and support documents, and helps optimize AHT (Agent Handle Time) to a good extent.<strong> </strong><span style="color:rgb(56,56,56)!important;font-family:Raleway, sans-serif;font-size:16px;"><strong>Machine learning</strong></span><strong> </strong>of AI gives intelligent agents ability to minimize escalation events, promote FCR (first contact resolution) and cuts down agent training cost.</p>1d:Ta3b,<p>A&nbsp;Tata consultancy services&nbsp;recent survey unfolds that almost 31.7% of major companies are now using AI in customer service space.</p><p>In the domain of customer care, the bank that has massively leveraged AI technology is China Merchant Bank, a leading credit card issuer in China. The bank’s front-end bot powered by WeChat messenger handles as much as nearly 2 million customer inquiries on daily basis. Since most queries are quite common, automated responses via AI chatbot proves to be a cost-effective solution, eliminating the need for hiring thousands of employees.</p><p>When it comes to AI-assisted human agent model, LivePerson as a customer service platform provider delivers appreciable results, increasing efficiency by 35%.</p><p>KLM, the Netherlands airline, turned to <a href="https://news.klm.com/klm-runs-pilot-with-artificial-intelligence-provided-by-digitalgenius" target="_blank" rel="noopener">DigitalGenius</a> to provide AI-powered customer service solution and diminish waiting time before the queries are answered. The solution has AI learning from live support interactions, adapting to reply format and suggesting responses to the human reps.<img src="https://cdn.marutitech.com/use_cases_of_ai_for_customer_service_whats_working_now_4_1200x565_digitalgenius_c7a652d1ec.png" alt="The present glory of AI for customer service" srcset="https://cdn.marutitech.com/thumbnail_use_cases_of_ai_for_customer_service_whats_working_now_4_1200x565_digitalgenius_c7a652d1ec.png 245w,https://cdn.marutitech.com/small_use_cases_of_ai_for_customer_service_whats_working_now_4_1200x565_digitalgenius_c7a652d1ec.png 500w,https://cdn.marutitech.com/medium_use_cases_of_ai_for_customer_service_whats_working_now_4_1200x565_digitalgenius_c7a652d1ec.png 750w,https://cdn.marutitech.com/large_use_cases_of_ai_for_customer_service_whats_working_now_4_1200x565_digitalgenius_c7a652d1ec.png 1000w," sizes="100vw"></p><p>Most popular food chains like Subway,&nbsp;Dominos,&nbsp;Starbucks&nbsp;have all recently embraced AI to enable customers to place orders without any human involvement. They can rely on Facebook Messenger chatbots or simply tell Amazon’s AI bot Alexa, to order a bite.</p><p>Like other financial structures, Bank of America is also determined to roll out&nbsp;<a href="https://promo.bankofamerica.com/erica/" target="_blank" rel="noopener">Erica</a>, an intelligent virtual <a href="https://marutitech.com/banking-need-digital-voice-assistant/" target="_blank" rel="noopener">banking assistant based on AI technology</a>, which will take digital banking to far next level.</p>1e:T52a,<p><a href="https://marutitech.com/chatbots-approaching-edge-call-centers/" target="_blank" rel="noopener">AI powered chatbots for customer support</a> is pushing the envelope of innovation and revolutionizing the way customers are assisted. AI means high-quality customer experience, personalized support, speed &amp; efficiency and cost saving. Of all business segments, customer service is the one where Artificial Intelligence is hugely embraced and companies are confident about how chatbots can efficiently handle first-level queries and significantly minimize operational cost. We are most likely to experience further innovations in AI-powered applications for improving customer service solutions. Currently, major industries that rely on artificial intelligence in customer support space are food, travel, finance, retail, airline and clothing.</p><p><a href="https://marutitech.com/" target="_blank" rel="noopener">Maruti Techlabs</a> offers an unparalled and automated customer support experience with chatbots that provide answers in real time. Companies can easily <a href="https://marutitech.com/custom-chatbots/" target="_blank" rel="noopener">customize the chatbot</a> to fit specific business needs, resolve customer queries, provide custom content while simultaneously matching brand voice and tone.</p>1f:T1618,<p><span style="font-size:16px;">The finance industry has undergone a massive transformation over the years, with the integration of technology. The most evident transformation has been in the way we look at payment transactions now. The digital payments market has seen a phenomenal growth in the last few years.</span></p><p><span style="font-size:16px;">In 2020, the total value of digital payment transactions is projected at USD 4,934,741 million, </span><a href="https://www.statista.com/outlook/296/100/digital-payments/worldwide#market-revenue" target="_blank" rel="noopener"><span style="font-size:16px;">reports Statista</span></a><span style="font-size:16px;">. The same report states that the number of users in Mobile POS Payments is expected to reach 1800.4 million by the year 2024.</span></p><p><span style="font-size:16px;">With digital payments now having become the norm, more and more companies are vying for opportunities in this segment to ease out payments and make it more user-friendly &amp; customer-centric. Some of the recent examples include:</span></p><ul><li><span style="font-size:16px;">Alibaba’s Alipay and M-Pesa have entered a deal which will offer </span><a href="https://www.mobileworldlive.com/money/news-money/safaricom-boosts-m-pesa-reach-with-alibaba-deal" target="_blank" rel="noopener"><span style="color:#f05443;font-size:16px;">M-Pesa as a payment option for Aliexpress.com</span></a><span style="color:#f05443;font-size:16px;"> </span><span style="font-size:16px;">users.</span></li><li><span style="font-size:16px;">A more recent addition has been </span><a href="https://www.thehindu.com/business/payments-on-whatsapp-go-live-in-india/article33037143.ece" target="_blank" rel="noopener"><span style="color:#f05443;font-size:16px;">WhatsApp Pay</span></a><span style="font-size:16px;"> by WhatsApp</span></li></ul><p><span style="font-size:16px;">As digital payments have become commonplace, so have digital frauds. Fraud management has been painful for the banking and commerce industry. Fraudsters have become adept at finding loopholes. are phishing for naïve people and extracting money from them in creative ways.</span></p><p><span style="font-size:16px;">As a result, companies have started to efficiently manage the vulnerabilities and close the loopholes within their payment systems through fraud detection via </span><a href="https://marutitech.com/machine-learning-predictive-analytics/" target="_blank" rel="noopener"><span style="font-size:16px;">machine learning and predictive analytics</span></a><span style="font-size:16px;">. According to a study by VynZ Research, the fraud detection and prevention market is </span><a href="https://www.vynzresearch.com/ict-media/fraud-detection-and-prevention-market" target="_blank" rel="noopener"><span style="font-size:16px;">expected to reach USD 85.3 billion</span></a><span style="font-size:16px;">, growing at a CAGR of 17.8% during 2020-2025.</span></p><p><span style="font-size:16px;">The main challenge for the companies attempting to full-proof their payment systems happen to be:</span></p><ul><li><span style="font-size:16px;">Acquiring excellent tools that can minimize payment risks and improve experiences</span></li><li><span style="font-size:16px;">Getting skilled professionals who can help with fraud detection and innovate payment experiences.</span></li></ul><p><span style="font-size:16px;">Let us understand why </span><a href="https://marutitech.com/machine-learning-services/" target="_blank" rel="noopener"><span style="font-size:16px;">machine learning</span></a><span style="font-size:16px;"> is the most suitable method of fraud detection and how it can help organizations authenticate their payment systems.</span></p><p><span style="font-size:16px;">First, let’s get a brief idea of machine learning.</span></p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is Machine Learning?</strong></span></p><p style="margin-left:0px;"><span style="font-family:inherit;">Artificial Intelligence is the brainpower depicted by machines due to their ability to load and decipher the information offered to them. With AI, devices mimic humans. </span><a href="https://marutitech.com/artificial-intelligence-and-machine-learning/" target="_blank" rel="noopener"><span style="font-family:inherit;">Machine Learning is a subset of AI</span></a><span style="font-family:inherit;">. Computers learn from the data provided to them to perform the tasks assigned.&nbsp;</span></p><p style="margin-left:0px;"><span style="font-family:inherit;">In machine learning, the computer builds training data using the information provided, which helps with predictions and decisions. As information is loaded to the machine, the data set improves, and the algorithm’s capability enhances, which can help in many ways, some of which are:</span></p><ul><li><span style="font-family:inherit;">Sales Forecasting – The machines, based on the past sales date and the current sales transactions, can forecast the sales for the upcoming year. You will know which products will sell and how much quantity, thus helping with inventory management.&nbsp;</span></li><li><span style="font-family:inherit;">Personalization – Machine Learning details out your order history, your browsing behavior, as well as your demographics. It helps apps like Amazon &amp; Netflix to arrive at recommendations that will enhance your app experience.&nbsp;</span></li></ul><p style="margin-left:0px;"><span style="font-family:inherit;">For fraud detection, machine learning ensures quicker resolutions and effective transactions.</span></p>20:T1176,<p>Machines are much better than humans at processing large datasets. They are able to detect and recognize thousands of patterns on a user’s purchasing journey instead of the few captured by creating rules.</p><p><img src="https://cdn.marutitech.com/9e884173_ml_in_fraud_detection_1_6036e7b74d.png" alt="Benefits of machine learning in fraud detection" srcset="https://cdn.marutitech.com/thumbnail_9e884173_ml_in_fraud_detection_1_6036e7b74d.png 149w,https://cdn.marutitech.com/small_9e884173_ml_in_fraud_detection_1_6036e7b74d.png 478w,https://cdn.marutitech.com/medium_9e884173_ml_in_fraud_detection_1_6036e7b74d.png 716w,https://cdn.marutitech.com/large_9e884173_ml_in_fraud_detection_1_6036e7b74d.png 955w," sizes="100vw"></p><p>We can predict fraud in a large volume of transactions by applying <a href="https://marutitech.com/cognitive-computing-features-scope-limitations/">cognitive computing technologies</a> to raw data. This is the reason why we use machine learning algorithms for preventing fraud for our clients.</p><p>Some of the benefits of fraud detection using machine learning are as follows –</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Faster &amp; Efficient Detection</strong></span></li></ul><p>Machine Learning offers an insight into how your user interacts with the apps. This includes an understanding of their app usage, payments, and even transaction methods.&nbsp;</p><p>As a result, the machine can quickly identify if the user has drifted from their regular app behavior. If there is a sudden spike in the amount that the user has shopped for from your site, it could be an anomaly. An approval from the user is needed for a go-ahead.&nbsp;</p><p>Machine Learning can quickly identify this anomaly in real-time, thus minimizing risk and securing the transaction.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Increased Accuracy</strong></span></li></ul><p>With Machine Learning, you can enable your analysts’ team to work faster and with greater accuracy. You are just giving them the power of data and insights, which means the time spent on manual analysis is reduced.&nbsp;</p><p>Let’s say your trained model has sufficient data. It would be able to differentiate between genuine and fraud customers. This would help ensure that your precision rate is high. As a result, fewer genuine customers would be blocked.&nbsp;</p><p>A customer has added a new card or a new payment method, which is not their ordinary course of behavior. Based on past data, the model can track the authenticity of the payment method as well as the customer’s records to understand if the transaction is fraudulent or not.</p><figure class="image"><a href="https://marutitech.com/case-study/build-an-image-search-engine-using-python/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/machine_learning_facilitates_3f817a0838.png"></a></figure><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Better Prediction with Larger Datasets</strong></span></li></ul><p>Machine-learning improves with more data because the ML model can pick out the differences and similarities between multiple behaviors. Once told which transactions are genuine and which are fraudulent, the systems can work through them and begin to pick out those which fit either bucket.</p><p>These can also predict them in the future when dealing with fresh transactions. There is a risk in scaling at a fast pace. If there is an undetected fraud in the training data machine learning will train the system to ignore that type of fraud in the future.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Cost-effective Detection Technique</strong></span></li></ul><p>The fraud detection team had to handle the analysis and insight building of a large amount of data, which is time-consuming and tedious. The results may or may not be accurate, which would result in genuine customers being blocked at the payment gateways.</p><p>However, with Machine Learning at the core, your team will be less burdened and more efficient. The algorithms can analyze large datasets in milliseconds while offering data in real-time for better decision-making capabilities.</p><p>On the other hand, your core team can monitor and optimize the Machine Learning Fraud Detection algorithm to meet the end user’s requirements, thus improving the outcomes.</p>21:T1c4c,<p>Fraud detection process using machine learning starts with gathering and segmenting the data. Then, the machine learning model is fed with training sets to predict the probability of fraud.</p><p><img src="https://cdn.marutitech.com/2623bf37_ml_in_fraud_detection_2_ed1b180161.png" alt="Fraud Detection Machine Learning Steps" srcset="https://cdn.marutitech.com/thumbnail_2623bf37_ml_in_fraud_detection_2_ed1b180161.png 245w,https://cdn.marutitech.com/small_2623bf37_ml_in_fraud_detection_2_ed1b180161.png 500w,https://cdn.marutitech.com/medium_2623bf37_ml_in_fraud_detection_2_ed1b180161.png 750w," sizes="100vw"></p><p>Let’s take a look at each of the elements in this process.</p><p><strong>&nbsp; &nbsp; 1. Input Data</strong>&nbsp;–&nbsp;There should be sufficient data available for Machine Learning to develop its algorithm.&nbsp;</p><p>There is too much noise available with the data you receive. The algorithm should be able to differentiate between good data, which consists of genuine customers and bad data, i.e., fraudsters.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">When you segment this data, your model will be able to comprehend better and deliver results efficiently.&nbsp;</span></h3><p><strong>&nbsp; &nbsp; 2. Extract Features</strong> – The features will help determine the signals that will help identify frauds.</p><p>&nbsp; &nbsp; The features important for fraud discoveries include:</p><ul><li>Customer’s identity (email addresses, credit card numbers, etc.)</li><li>The past order details</li><li>Their preferred payment methods,&nbsp;</li><li>The locations they have used for the transactions&nbsp;</li><li>Their network (emails, phone numbers, and payment details entered with the online account).</li></ul><p><strong>&nbsp; &nbsp; 3. Train Algorithm</strong> –&nbsp;At this point, you will need to help the machine understand the difference between a fraudulent and a normal transaction. For this, you need to create an algorithm, train it using the learning data set, and help the machine make accurate predictions.</p><p>The features that you have added to the algorithm for fraud detection unsupervised learning along with the input data, will help train the machine towards better predictions.</p><p><strong>&nbsp; &nbsp; 4. Create Model</strong> –&nbsp;The training set will help the model understand and comprehend the algorithm defined. Once the training of the machine is over, you will get the exact model required for fraud detection.&nbsp;</p><p>The model will need to be improvised whenever new data or features are added to the system.</p><p>To help predict the models and ensure consistent results, different techniques are used to build models:</p><h3>&nbsp;<span style="font-family:Poppins, sans-serif;font-size:18px;"> &nbsp;&nbsp;<strong>a. Logistic Regression</strong></span></h3><p>This technique uses a cause-effect relationship to devise structured data sets. Regression analysis tends to become more sophisticated when applied to fraud detection due to the number of variables and size of the data sets. It can provide value by assessing the predictive power of individual variables or combinations of variables as part of a larger fraud strategy.&nbsp;</p><p>In this technique, the authentic transactions are compared with the fraud ones to create an algorithm. This model (algorithm) will predict whether a new transaction is fraudulent or not. For very large merchants these models are specific to their customer base, but usually, general models will apply.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">&nbsp; &nbsp;<strong> b. Decision Tree</strong></span></h3><p>This is a mature machine learning algorithm family used to automate the creation of rules for classification tasks. Decision Tree algorithms can be used for classification or regression predictive modeling problems. They are essentially a set of rules which are trained using examples of fraud that clients are facing.</p><p>The creation of a tree ignores irrelevant features and does not require extensive normalization of the data. A tree can be inspected and we can understand why a decision was made by following the list of rules triggered by a certain customer. The output of the machine learning algorithm might be a model like the following decision tree. This gives a probability score of fraud based on earlier scenarios.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; c. Random Forest</strong></span></h3><p>Random Forest technique uses a combination of multiple decision trees to improve the performance of the classification or regression. It allows us to smooth the error which might exist in a single tree. It increases the overall performance and accuracy of the model while maintaining our ability to interpret the results and provide explainable scores to our users.</p><p>Random forest runtimes are quite fast, and they are able to deal with unbalanced and missing data. Random Forest weaknesses are that when used for regression they cannot predict beyond the range in the training data and that they may over-fit data sets that are particularly noisy. Of course, the best test of any algorithm is how well it works upon your own data set.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">&nbsp; &nbsp;&nbsp;<strong>d.</strong></span><a href="https://marutitech.com/computer-vision-neural-networks/" target="_blank" rel="noopener"><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> </strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Neural Networks</strong></span></a></h3><p>It is an excellent complement to other techniques and improves with exposure to data. The neural network is a part of cognitive computing technology where the machine mimics how the human brain works and how it observes patterns.</p><p>The neural networks are completely adaptive; able to learn from patterns of legitimate behavior. These can adapt to the change in the behavior of normal transactions and identify patterns of fraud transactions. The process of the neural networks is extremely fast and can make decisions in real time.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Choosing a Model for Real-time Credit Card Fraud Detection Using Machine Learning</strong></span></h3><p>The model that you choose should be able to identify these common anomalies in the system easily.</p><ul><li>If there are multiple payment methods added from a single account within an hour, then it is a trigger that this account may be fraudulent.</li><li>If the customer is buying premium goods in large quantities, then your algorithm should be able to detect this fraud.</li><li>The location or the address added to the profile is fraudulent; i.e., it does not exist.</li><li>The email ID seems suspicious.</li><li>There is a mismatch in the account name as well as the name of the card.&nbsp;</li></ul><p>Your training set should consist of data about these frauds. It is important to note that the model you choose also depends on your datasets as they work differently on datasets of different patterns.</p>22:T152a,<p>Let’s take a look at some of the fraud cases that exist in the real world and how ML can help detect them. You have likely experienced these frauds in one way or the other.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Email Phishing</strong></span></h3><p>In this technique, the fraudsters tend to con the recipients into answering the email with their data. Using the data, they can hack into your system and rob you of your money.&nbsp;</p><p>Machine Learning uses its algorithm to differentiate between actual and spam email addresses, thus preventing these frauds. They will read into the subject lines, the content of the email, as well as the sender’s email details before segmenting them into good or fraud email.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Identity Theft</strong></span></h3><p>This is another kind of fraud that needs to be brought to notice. In this case, the criminals tend to rob you of your identity connected with the bank accounts. They will change the IDs or the passwords, thus preventing entry into these accounts.&nbsp;</p><p>Machine Learning will ensure that nobody can change the password or update the identity associated with an account. As soon as anyone tries to hack into your account or plans to change the details, you will be notified. Two-factor security and other measures, along with human-like intelligence, help assure better prevention of frauds.&nbsp;&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Credit Card Theft</strong></span></h3><p>Either through phishing or other methods, the fraudsters can get your credit card details and use it in systems that don’t need the physical presence of the cards. You will have to pay for the purchases you have not made.&nbsp;</p><p>Credit card fraud detection machine learning can prevent such compromises. The past purchases will tell a little about the customer’s buying behavior. It will also detail out the amount they are likely to spend, the kind of purchases they make, and the locations. If the purchase is abnormal, then the algorithm will detect and prevent fraud.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Document Forgery</strong></span></h3><p>Fakes IDs are available on the eCommerce market too, which can cause a lot of issues for the owner of these Ids. Machine Learning can ably identify the forged identity.</p><p>The algorithm has trained its neural network to differentiate between a fake and original identity, thus creating a full-proof system.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 5. Formjacking Credit Card Details</strong></span></h3><p>It is the hijacking of your credit card details. While you are entering the details into a particular form online, the hacker would be ready with their tools to hijack the information and use it elsewhere.</p><p>This can be detected by the Machine Learning algorithm added to your website. It will secure the information and ensure that the data is not given to the attackers.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 6. Fake Applications</strong></span></h3><p>If they have access to your IDs and other details, these fraudsters can use it to create a credit card. They will use the card while you will have to pay out the bills. The theft detection models have been devised for this specific reason, which accesses neural models to understand whether the application is real or fake.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 7. Payment Fraud</strong></span></h3><p>The payment fraud includes lost credit cards, stolen cards as well as counterfeit cards. The fraudsters complete the payments while the owner of the cards has to pay these bills.</p><p>They are mainly used in transactions where the physical card is not essential and on vulnerable sites. There are separate detection models that identify the payment features and methods used in the past against the current technique.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 8. Mimicking Buyer Behaviour</strong></span></h3><p>This is the new kind of fraud, where the criminal studies the buyer’s behavior and tries to imitate that. An in-depth understanding of the data can give Machine Learning the difference between the actual buyer and the fraudster.</p><p>Identifying the location spoofing details, knowing where the fraudster is making these purchases from, and other details need to be added to the ML algorithm for better &amp; accurate results.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 9. Advanced Software</strong></span></h3><p>Experienced hackers tend to use advanced anti-piracy and detection software, which can prevent regular browsers from recognizing them. They will create virtual IPs and machines, which allows them to commit the crime.</p><p>Machine Learning algorithms need to be fed with this data that can help them identify virtual IPs, machine anomaly, and fraudulent behavior. As a result, you can save the payment gateways from being crashed by frauds.&nbsp;&nbsp;</p>23:Ta46,<p>Machine Learning is a very useful technology that allows us to find patterns of an anomaly in everyday transactions. They are indeed superior to human review and rule-based methods, which were employed by earlier organizations.&nbsp;</p><p>But, as with any other technology, this technique of fraud detection has its own limitations:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Inspectability Issues</strong></span></h3><p>At Maruti Techlabs we maintain the backend machine learning model for our client. Thus we are required to explain the reasons for a buyer or seller being flagged as a fraudster and prevented from using the system. We also need to do this so that our client can confirm fraud and therefore train the system. In fact, machine learning is only as good as the human data scientists behind it.</p><p>Even the most advanced technology cannot replace the expertise and judgment it takes to effectively filter and process data and evaluate the meaning of the risk score. So while we have eliminated this problem through rule-based techniques, lack of inspectability can be a drawback of certain other machine learning-based approaches.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Cold Start</strong></span></h3><p>It takes a significant amount of data for machine learning models to become accurate. For large organizations, this data volume is not an issue but for others, there must be enough data points to identify legitimate cause and effect relationships.</p><p>Without the appropriate data, the machines may learn the wrong inferences and make erroneous or irrelevant fraud assessments. It’s often better to apply a basic set of rules initially and allow the machine learning models to ‘warm up’ with more data. We often apply this approach with smaller datasets.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Blind to Data Connections</strong></span></h3><p>Machine learning models work on actions, behavior, and activity. Initially, when the dataset is small, they are blind to connections in data. The model can overlook a seemingly obvious connection such as a shared card between two accounts. To counter this we enhance our models with Graph networks.</p><p>Graph technique can find multiple bogus actors for every single one prevented through scoring. Graph databases allow us to block suspect and bogus accounts before they have taken any fraudulent action. Following image shows a simple buyer insurance fraud case represented as a graph.</p>24:T681,<p>To detect suspicious activity, and more importantly to separate false alarms from true fraud, <a href="https://www.americanbanker.com/news/how-paypal-is-taking-a-chance-on-ai-to-fight-fraud" target="_blank" rel="noopener">PayPal uses a homegrown AI engine built with open-source tools</a>. As a result of this human and AI solution, Paypal has decreased its false alarm rate to half.&nbsp;</p><p>Machine learning techniques are obviously more reliable than human review and transaction rules. The machine learning solutions are efficient, scalable and process a large number of transactions in real time.</p><p>Maruti Techlabs is focused on improving customer experiences through technology. Having worked on challenging projects from around the world, we understand how to navigate through strict regulations and risk of replacing existing technology when it comes to automation.&nbsp;</p><p>Our <a href="https://marutitech.com/services/artificial-intelligence-consulting/machine-learning/" target="_blank" rel="noopener">machine learning experts</a> enable rapid decision making, increased productivity, business process automation, and faster anomaly detection through a myriad of techniques. <span style="font-family:Arial;">To get on a call with our </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">artificial intelligence experts</span></a><span style="font-family:Arial;">, drop us a note </span><a href="https://marutitech.com/contact-us/"><span style="color:#1155cc;font-family:Arial;"><u>here</u></span></a><span style="font-family:Arial;">.</span></p>25:T719,<p>Lately, Artificial Intelligence and Machine Learning is a hot topic in the tech industry. Perhaps more than our daily lives Artificial Intelligence (AI) is impacting the business world more. There was about $300 million in venture capital invested in AI startups in 2014, a 300% increase than a year before (<a href="http://www.bloomberg.com/news/articles/2015-02-03/i-ll-be-back-the-return-of-artificial-intelligence" target="_blank" rel="noopener">Bloomberg</a>).</p><p><i>Hey there! This blog is almost about <strong>1000+ words</strong> long and may take <strong>~5 mins</strong> to go through the whole thing. We understand that you might not have that much time.</i></p><p><i>This is precisely why we made a <strong>short video</strong> on the topic. It is less than 2 mins, and simplifies <strong>Artificial intelligence &amp; Machine learning.</strong> We hope this helps you learn more and save your time. Cheers!</i></p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/bjG3gS3Mh1U" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div><p>AI is everywhere, from gaming stations to maintaining complex information at work. Computer Engineers and Scientists are working hard to impart intelligent behavior in the machines making them think and respond to real-time situations. AI is transiting from just a research topic to the early stages of enterprise adoption. Tech giants like Google and Facebook have placed huge bets on Artificial Intelligence and Machine Learning and are already using it in their products. But this is just the beginning, over the next few years, we may see AI steadily glide into one product after another.</p>26:Tabe,<p>According to Stanford Researcher, John McCarthy, <i>“Artificial Intelligence is the science and engineering of making intelligent machines, especially intelligent computer programs. Artificial Intelligence is related to the similar task of using computers to understand human intelligence, but AI does not have to confine itself to methods that are biologically observable.”</i></p><p>Simply put, AI’s goal is to make computers/computer programs smart enough to imitate the human mind behaviour.</p><p>Knowledge Engineering is an essential part of AI research. Machines and programs need to have bountiful information related to the world to often act and react like human beings. AI must have access to properties, categories, objects and relations between all of them to implement knowledge engineering. <a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">AI solutions</span></a><span style="font-family:Arial;"> initiate common sense, problem-solving, and analytical reasoning power in machines, which is a complex and tedious job.</span></p><p>AI services can be classified into Vertical or Horizontal AI</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is Vertical AI?</strong></span></h3><p>These are services focus on the single job, whether that’s scheduling meeting, automating repetitive work, etc. Vertical AI Bots performs just one job for you and do it so well, that we might mistake them for a human.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is Horizontal AI?</strong></span></h3><p>These services are such that they are able to handle multiple tasks. There is no single job to be done. Cortana, Siri and Alexa are some of the examples of Horizontal AI. These services work more massively as the question and answer settings, such as “What is the temperature in New York?” or “Call Alex”. They work for multiple tasks and not just for a particular task entirely.</p><p>AI is achieved by analysing how the human brain works while solving an issue and then using that analytical problem-solving techniques to build complex algorithms to perform similar tasks. AI is an automated decision-making system, which continuously learn, adapt, suggest and take actions automatically. At the core, they require algorithms which are able to learn from their experience. This is where Machine Learning comes into the picture.</p><figure class="image"><a href="https://marutitech.com/case-study/medical-record-processing-using-nlp/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/medical_records_processing_using_nlp_ef68ec502a.png"></a></figure>27:T652,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Weak AI, known as Narrow AI, is designed with predefined rules catering to specific tasks. It operates within set parameters and excels at solving a particular problem or automating a single process. Weak AI possesses the human mind's cognitive abilities, but unlike general intelligence, it’s tailored to fulfill distinct assignments intelligently.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">A great example of weak AI is John Searle’s room thought experiment. In this experiment, two individuals converse in Chinese, one outside and one inside a room. Here, the person inside the room is given instructions on how to reply when speaking in Chinese.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Though it may appear to the person outside that the person inside is proficient in speaking Chinese, their capability is rooted in following provided instructions. In reality, the person inside the room is adept at following instructions and not speaking Chinese.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Narrow AI has specific intelligence and doesn’t possess general intelligence. Therefore, an AI programmed to guide you on how to reach from point A to point B isn’t capable of playing a game of chess with you. Similarly, a type of AI that pretends to converse in Chinese cannot fold your clothes or wash your car.</span></p>28:T94e,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Strong AI, or artificial general intelligence, possesses mental capabilities like the human brain. They’re intelligible systems whose actions and decision-making replicate that of a human being, including their power of understanding and consciousness.&nbsp;</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Strong AI can clone distinctive human features like beliefs, cognitive abilities, and perception. Defining intelligence, setting boundaries, and predicting success ratio are some of the most arduous challenges when working with strong AI.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Due to the above reasons, weak AI is preferred, as it performs designated tasks optimally. It doesn’t need a comprehensive intelligence, and its development is modular and manageable.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Devices or systems powered by Strong AI use their cognitive abilities to learn and solve problems similar to or, in some cases, better than humans. This fuels continual growth and investment in this domain.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Finance industries significantly benefit from using AI. Leveraging subsets of AI, like ML and cognitive computing, assisted by techs like big data, cloud services, and hyper-processing systems, the finance industry can develop chatbots and personal assistants.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">As predicted by thought leaders, replacing humans is the ultimate future of AI. Yet, achieving this feat is difficult as AI grapples with bias, lack of trust, and regulatory compliance. Therefore, companies seek to balance automation and assistance by employing augmented intelligence.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI creates job opportunities in financial audits, tax analysis, and intelligent decision-making. The primary goal for businesses using AI is to achieve harmony between human and machine intelligence.</span></p>29:Td61,<p>Artificial Intelligence and <a href="https://marutitech.com/machine-learning-services/" target="_blank" rel="noopener">Machine Learning</a> are much trending and also confused terms nowadays. Machine Learning (ML) is a subset of Artificial Intelligence. ML is a science of designing and applying algorithms that are able to learn things from past cases. If some behaviour exists in past, then you may predict if or it can happen again. Means if there are no past cases then there is no prediction.</p><p>ML can be applied to solve tough issues like credit card fraud detection, enable self-driving cars and face detection and recognition. ML uses complex algorithms that constantly iterate over large data sets, analyzing the patterns in data and facilitating machines to respond different situations for which they have not been explicitly programmed. The machines learn from the history to produce reliable results. The ML algorithms use Computer Science and Statistics to predict rational outputs.</p><p>There are 3 major areas of ML:</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Supervised Learning</strong></span></h3><p>In supervised learning, training datasets are provided to the system. Supervised learning algorithms analyse the data and produce an inferred function. The correct solution thus produced can be used for mapping new examples. Credit card fraud detection is one of the examples of Supervised Learning algorithm.</p><p><img src="https://cdn.marutitech.com/AI-supervised-and-unsupervised-learning-1.png" alt="Supervised vs unsupervised learning"></p><p>Supervised Learning and Unsupervised Learning (Reference: http://dataconomy.com/whats-the-difference-between-supervised-and-unsupervised-learning/)</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Unsupervised Learning</strong></span></h3><p>Unsupervised Learning algorithms are much harder because the data to be fed is unclustered instead of datasets. Here the goal is to have the machine learn on its own without any supervision. The correct solution of any problem is not provided. The algorithm itself finds the patterns in the data. One of the examples of supervised learning is <a href="https://marutitech.com/recommendation-engine-benefits/" target="_blank" rel="noopener">Recommendation engines</a> which are there on all e-commerce sites or also on Facebook friend request suggestion mechanism.</p><p><img src="https://cdn.marutitech.com/AI-recommendation-engine.png" alt="Recommendation Engine"></p><p style="text-align:center;">Recommendation Engine</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement Learning</strong></span></h3><p>This type of Machine Learning algorithms allows software agents and machines to automatically determine the ideal behaviour within a specific context, to maximise its performance. Reinforcement learning is defined by characterising a learning problem and not by characterising learning methods. Any method which is well suited to solve the problem, we consider it to be the reinforcement learning method. Reinforcement learning assumes that a software agent i.e. a robot, or a computer program or a bot, connect with a dynamic environment to attain a definite goal. This technique selects the action that would give expected output efficiently and rapidly.</p>2a:T5be,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and Machine Learning (ML) can work in tandem to comprehend and study vast sums of data, extract relevant insights, and make future forecasts based on past trends and patterns. When used in a suitable capacity, these techs can revolutionize the software industry by enhancing overall productivity and efficiency of internal processes.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and ML allow applications to inculcate intricate functionalities like voice recognition, predictive analytics, and natural language processing while facilitating intelligent automation. This opens new avenues for diverse industries like marketing, healthcare, and customer service.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Harnessing the power of AI and ML allows programmers to craft innovative systems with the potential to craft tailored solutions, learn user preferences, and automate daily tasks. They aid user satisfaction and enable businesses to optimize operations and create new revenue streams.&nbsp;</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and machine learning herald a new era of software, with perceptive systems that can execute tasks once confined to human abilities.</span></p>2b:T4af,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Artificial Intelligence and Machine Learning always interest and surprise us with their innovations. AI and ML have reached industries like customer service, e-commerce, finance, and others. By 2020, 85% of the customer interactions will be managed without a human (</span><a href="http://www.gartner.com/imagesrv/summits/docs/na/customer-360/C360_2011_brochure_FINAL.pdf" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:transparent;color:#1155cc;font-family:'Work Sans',sans-serif;"><u>Gartner</u></span></a><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">). There are specific implications of AI and ML to incorporate data analysis like descriptive analytics, predictive analytics, and predictive analytics, discussed in our next blog:&nbsp;</span><a href="https://marutitech.com/machine-learning-predictive-analytics/" target="_blank" rel="noopener"><span style="background-color:transparent;color:#1155cc;font-family:'Work Sans',sans-serif;"><u>How can machine learning boost your predictive analytics?</u></span></a></p>2:[null,["$","$L11",null,{"blogData":{"data":[{"id":196,"attributes":{"createdAt":"2022-09-14T11:28:54.775Z","updatedAt":"2025-06-16T10:42:10.705Z","publishedAt":"2022-09-15T05:12:23.288Z","title":"Is artificial intelligence the key to combat fake news?","description":"Explore how artificial intelligence-driven data is the key to the lens of misinformation. ","type":"Artificial Intelligence and Machine Learning","slug":"artificial-intelligence-fake-news","content":[{"id":13740,"title":null,"description":"$12","twitter_link":null,"twitter_link_text":null},{"id":13741,"title":"IS ARTIFICIAL INTELLIGENCE-DRIVEN DATA VERACITY THE LENS ON MISINFORMATION?","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13742,"title":"HOW TO DEAL WITH MISINFORMATION DYNAMICS?","description":"<p>Spreading misinformation across the social landscape is not wholly about accidental inaccuracies; it is more about intentional misinformation that is dynamic. Misinformation dynamics is all about connecting fake news to the new big data concept called the data veracity.</p><p>Borge-Holthoefer and Berti-Équille came up with the staggering revelation that traditional approaches stand unequal to deal with this intentional misinformation. Where misinformation spreads like fire, dealing with fake news calls for sophisticated approaches based on artificial intelligence to determine data veracity and the authenticity of information.</p>","twitter_link":null,"twitter_link_text":null},{"id":13743,"title":"HOW IS FAKE NEWS UNEARTHED?","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13744,"title":"ARE THERE TOOLS TO COMBAT FAKE NEWS?","description":"$15","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":3626,"attributes":{"name":"artificial intelligence the key to combat fake news.webp","alternativeText":"artificial intelligence the key to combat fake news","caption":null,"width":3494,"height":2330,"formats":{"small":{"name":"small_artificial intelligence the key to combat fake news.webp","hash":"small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":500,"height":333,"size":21.89,"sizeInBytes":21890,"url":"https://cdn.marutitech.com/small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"thumbnail":{"name":"thumbnail_artificial intelligence the key to combat fake news.webp","hash":"thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":234,"height":156,"size":7.81,"sizeInBytes":7810,"url":"https://cdn.marutitech.com/thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"medium":{"name":"medium_artificial intelligence the key to combat fake news.webp","hash":"medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":750,"height":500,"size":37.26,"sizeInBytes":37256,"url":"https://cdn.marutitech.com/medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"large":{"name":"large_artificial intelligence the key to combat fake news.webp","hash":"large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":667,"size":54.04,"sizeInBytes":54042,"url":"https://cdn.marutitech.com/large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"}},"hash":"artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","size":262.81,"url":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-08T08:39:57.860Z","updatedAt":"2025-05-08T08:39:57.860Z"}}},"audio_file":{"data":null},"suggestions":{"id":1962,"blogs":{"data":[{"id":175,"attributes":{"createdAt":"2022-09-14T11:16:50.522Z","updatedAt":"2025-06-16T10:42:08.056Z","publishedAt":"2022-09-15T06:52:28.955Z","title":"How can Artificial Intelligence for Customer Support assist Businesses?","description":"Discover how artificial intelligence can hugely embrance the customer support service for your business.","type":"Artificial Intelligence and Machine Learning","slug":"artificial-intelligence-for-customer-service-2","content":[{"id":13601,"title":"","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13602,"title":"AI for customer service: what is real?","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13603,"title":"AI as a brand messenger","description":"<p>In last 5 years, we have seen social media flooded with people devouring messaging apps. They are generously relying on messaging apps not just to communicate with their closed ones, but also to engage with brands they are curious about or familiar with. This is why AI-powered, customized, real-time messaging bot services could provide an incredible opportunity for businesses to connect with new and existing customers and foster a unique revenue stream.</p><p>Facebook Messenger leverages powerful chatbots integrated with cognitive capabilities based on this idea. Other leading industries that are now seen galloping towards this space include fashion, tourism, food chains, airline, e-commerce, hotels, etc. Consumers are thrilled to welcome new AI technology for services they avail, and they are happy to interact with their favorite brands to book flights, hotel accommodation, travel trip, or get fashion tips. The world is watching eagerly for next industries to adopt the trend.</p>","twitter_link":null,"twitter_link_text":null},{"id":13604,"title":"AI for well-informed actions","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13605,"title":"AI controlled Multi channels of support","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13606,"title":"Artificial Intelligence offers reliability","description":"$1a","twitter_link":null,"twitter_link_text":null},{"id":13607,"title":"AI robots mean precision","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13608,"title":"The rise of AI-assisted human agent model","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13609,"title":"The present glory of AI for customer service","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13610,"title":"Conclusion","description":"$1e","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":417,"attributes":{"name":"dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","alternativeText":"dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","caption":"dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","width":1000,"height":563,"formats":{"thumbnail":{"name":"thumbnail_dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","hash":"thumbnail_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":8.05,"sizeInBytes":8050,"url":"https://cdn.marutitech.com//thumbnail_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed.jpg"},"small":{"name":"small_dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","hash":"small_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":22.2,"sizeInBytes":22201,"url":"https://cdn.marutitech.com//small_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed.jpg"},"medium":{"name":"medium_dbbe0271-how-will-artificial-intelligence-powered-customer-service-help-customer-support-agent.jpg","hash":"medium_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":39.25,"sizeInBytes":39253,"url":"https://cdn.marutitech.com//medium_dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed.jpg"}},"hash":"dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed","ext":".jpg","mime":"image/jpeg","size":59.21,"url":"https://cdn.marutitech.com//dbbe0271_how_will_artificial_intelligence_powered_customer_service_help_customer_support_agent_2dc77817ed.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:42.288Z","updatedAt":"2024-12-16T11:46:42.288Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":185,"attributes":{"createdAt":"2022-09-14T11:21:26.301Z","updatedAt":"2025-06-16T10:42:09.433Z","publishedAt":"2022-09-15T04:59:23.660Z","title":"A comprehensive guide for fraud detection with machine learning","description":"Check how machine learning has undergone a massive transformation to facilitate fraud detection. ","type":"Artificial Intelligence and Machine Learning","slug":"machine-learning-fraud-detection","content":[{"id":13684,"title":null,"description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":13685,"title":"Benefits of Fraud Detection via Machine Learning","description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13686,"title":"How does Machine Learning Facilitate Credit Card Fraud Detection?","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13687,"title":"9 Common Fraud Scenarios – Application of Machine Learning Fraud Detection","description":"$22","twitter_link":null,"twitter_link_text":null},{"id":13688,"title":"Limitations of Using Machine Learning for Fraud Detection","description":"$23","twitter_link":null,"twitter_link_text":null},{"id":13689,"title":"Concluding Thoughts","description":"$24","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":494,"attributes":{"name":"digital-crime-by-anonymous-hacker (1).jpg","alternativeText":"digital-crime-by-anonymous-hacker (1).jpg","caption":"digital-crime-by-anonymous-hacker (1).jpg","width":5422,"height":4004,"formats":{"thumbnail":{"name":"thumbnail_digital-crime-by-anonymous-hacker (1).jpg","hash":"thumbnail_digital_crime_by_anonymous_hacker_1_320860547a","ext":".jpg","mime":"image/jpeg","path":null,"width":211,"height":156,"size":6.76,"sizeInBytes":6757,"url":"https://cdn.marutitech.com//thumbnail_digital_crime_by_anonymous_hacker_1_320860547a.jpg"},"small":{"name":"small_digital-crime-by-anonymous-hacker (1).jpg","hash":"small_digital_crime_by_anonymous_hacker_1_320860547a","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":369,"size":26.17,"sizeInBytes":26174,"url":"https://cdn.marutitech.com//small_digital_crime_by_anonymous_hacker_1_320860547a.jpg"},"medium":{"name":"medium_digital-crime-by-anonymous-hacker (1).jpg","hash":"medium_digital_crime_by_anonymous_hacker_1_320860547a","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":554,"size":50.74,"sizeInBytes":50743,"url":"https://cdn.marutitech.com//medium_digital_crime_by_anonymous_hacker_1_320860547a.jpg"},"large":{"name":"large_digital-crime-by-anonymous-hacker (1).jpg","hash":"large_digital_crime_by_anonymous_hacker_1_320860547a","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":739,"size":80.74,"sizeInBytes":80738,"url":"https://cdn.marutitech.com//large_digital_crime_by_anonymous_hacker_1_320860547a.jpg"}},"hash":"digital_crime_by_anonymous_hacker_1_320860547a","ext":".jpg","mime":"image/jpeg","size":1084.11,"url":"https://cdn.marutitech.com//digital_crime_by_anonymous_hacker_1_320860547a.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:52:32.904Z","updatedAt":"2024-12-16T11:52:32.904Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":190,"attributes":{"createdAt":"2022-09-14T11:28:52.467Z","updatedAt":"2025-06-16T10:42:10.034Z","publishedAt":"2022-09-15T05:09:27.664Z","title":"Understanding the Basics of Artificial Intelligence and Machine Learning","description":"Explore how artificial intelligence and machine learning are hot topic in tech industry. ","type":"Artificial Intelligence and Machine Learning","slug":"artificial-intelligence-and-machine-learning","content":[{"id":13712,"title":null,"description":"$25","twitter_link":null,"twitter_link_text":null},{"id":13713,"title":"What is Artificial Intelligence?","description":"$26","twitter_link":null,"twitter_link_text":null},{"id":13714,"title":"What is Weak AI?","description":"$27","twitter_link":null,"twitter_link_text":null},{"id":13715,"title":"What is Strong AI?","description":"$28","twitter_link":null,"twitter_link_text":null},{"id":13716,"title":"What is Machine Learning?","description":"$29","twitter_link":null,"twitter_link_text":null},{"id":13717,"title":"Future of AI and Machine Learning","description":"$2a","twitter_link":null,"twitter_link_text":null},{"id":13718,"title":"Conclusion","description":"$2b","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":501,"attributes":{"name":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","alternativeText":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","caption":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","width":7359,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"thumbnail_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":134,"size":4.36,"sizeInBytes":4357,"url":"https://cdn.marutitech.com//thumbnail_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"small":{"name":"small_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"small_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":274,"size":12.87,"sizeInBytes":12865,"url":"https://cdn.marutitech.com//small_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"medium":{"name":"medium_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"medium_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":411,"size":24.02,"sizeInBytes":24019,"url":"https://cdn.marutitech.com//medium_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"large":{"name":"large_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"large_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":547,"size":38.12,"sizeInBytes":38121,"url":"https://cdn.marutitech.com//large_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"}},"hash":"businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","size":652.97,"url":"https://cdn.marutitech.com//businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:07.759Z","updatedAt":"2024-12-16T11:53:07.759Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1962,"title":"Machine Learning Model Accelerates Healthcare Record Processing by 87%","link":"https://marutitech.com/case-study/medical-record-processing-using-nlp/","cover_image":{"data":{"id":675,"attributes":{"name":"2.png","alternativeText":"2.png","caption":"2.png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":21,"sizeInBytes":21002,"url":"https://cdn.marutitech.com//thumbnail_2_d22fbc1184.png"},"small":{"name":"small_2.png","hash":"small_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":73.7,"sizeInBytes":73702,"url":"https://cdn.marutitech.com//small_2_d22fbc1184.png"},"medium":{"name":"medium_2.png","hash":"medium_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":163.79,"sizeInBytes":163790,"url":"https://cdn.marutitech.com//medium_2_d22fbc1184.png"},"large":{"name":"large_2.png","hash":"large_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":292.75,"sizeInBytes":292746,"url":"https://cdn.marutitech.com//large_2_d22fbc1184.png"}},"hash":"2_d22fbc1184","ext":".png","mime":"image/png","size":93.1,"url":"https://cdn.marutitech.com//2_d22fbc1184.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-31T09:40:15.084Z","updatedAt":"2024-12-31T09:40:15.084Z"}}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]},"seo":{"id":2192,"title":"Is artificial intelligence the key to combat fake news?","description":"Fake news is a growing menace tarnishing the reputation of original source. Enterprises now leverage artificial intelligence to combat fake news.","type":"article","url":"https://marutitech.com/artificial-intelligence-fake-news/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":3626,"attributes":{"name":"artificial intelligence the key to combat fake news.webp","alternativeText":"artificial intelligence the key to combat fake news","caption":null,"width":3494,"height":2330,"formats":{"small":{"name":"small_artificial intelligence the key to combat fake news.webp","hash":"small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":500,"height":333,"size":21.89,"sizeInBytes":21890,"url":"https://cdn.marutitech.com/small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"thumbnail":{"name":"thumbnail_artificial intelligence the key to combat fake news.webp","hash":"thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":234,"height":156,"size":7.81,"sizeInBytes":7810,"url":"https://cdn.marutitech.com/thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"medium":{"name":"medium_artificial intelligence the key to combat fake news.webp","hash":"medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":750,"height":500,"size":37.26,"sizeInBytes":37256,"url":"https://cdn.marutitech.com/medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"large":{"name":"large_artificial intelligence the key to combat fake news.webp","hash":"large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":667,"size":54.04,"sizeInBytes":54042,"url":"https://cdn.marutitech.com/large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"}},"hash":"artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","size":262.81,"url":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-08T08:39:57.860Z","updatedAt":"2025-05-08T08:39:57.860Z"}}}},"image":{"data":{"id":3626,"attributes":{"name":"artificial intelligence the key to combat fake news.webp","alternativeText":"artificial intelligence the key to combat fake news","caption":null,"width":3494,"height":2330,"formats":{"small":{"name":"small_artificial intelligence the key to combat fake news.webp","hash":"small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":500,"height":333,"size":21.89,"sizeInBytes":21890,"url":"https://cdn.marutitech.com/small_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"thumbnail":{"name":"thumbnail_artificial intelligence the key to combat fake news.webp","hash":"thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":234,"height":156,"size":7.81,"sizeInBytes":7810,"url":"https://cdn.marutitech.com/thumbnail_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"medium":{"name":"medium_artificial intelligence the key to combat fake news.webp","hash":"medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":750,"height":500,"size":37.26,"sizeInBytes":37256,"url":"https://cdn.marutitech.com/medium_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"},"large":{"name":"large_artificial intelligence the key to combat fake news.webp","hash":"large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":667,"size":54.04,"sizeInBytes":54042,"url":"https://cdn.marutitech.com/large_artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"}},"hash":"artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08","ext":".webp","mime":"image/webp","size":262.81,"url":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-08T08:39:57.860Z","updatedAt":"2025-05-08T08:39:57.860Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
2c:T695,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/artificial-intelligence-fake-news/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/artificial-intelligence-fake-news/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/artificial-intelligence-fake-news/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/artificial-intelligence-fake-news/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/artificial-intelligence-fake-news/#webpage","url":"https://marutitech.com/artificial-intelligence-fake-news/","inLanguage":"en-US","name":"Is artificial intelligence the key to combat fake news?","isPartOf":{"@id":"https://marutitech.com/artificial-intelligence-fake-news/#website"},"about":{"@id":"https://marutitech.com/artificial-intelligence-fake-news/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/artificial-intelligence-fake-news/#primaryimage","url":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/artificial-intelligence-fake-news/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Fake news is a growing menace tarnishing the reputation of original source. Enterprises now leverage artificial intelligence to combat fake news."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Is artificial intelligence the key to combat fake news?"}],["$","meta","3",{"name":"description","content":"Fake news is a growing menace tarnishing the reputation of original source. Enterprises now leverage artificial intelligence to combat fake news."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$2c"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/artificial-intelligence-fake-news/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"Is artificial intelligence the key to combat fake news?"}],["$","meta","9",{"property":"og:description","content":"Fake news is a growing menace tarnishing the reputation of original source. Enterprises now leverage artificial intelligence to combat fake news."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/artificial-intelligence-fake-news/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"}],["$","meta","14",{"property":"og:image:alt","content":"Is artificial intelligence the key to combat fake news?"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"Is artificial intelligence the key to combat fake news?"}],["$","meta","19",{"name":"twitter:description","content":"Fake news is a growing menace tarnishing the reputation of original source. Enterprises now leverage artificial intelligence to combat fake news."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com/artificial_intelligence_the_key_to_combat_fake_news_c00f7c2f08.webp"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
