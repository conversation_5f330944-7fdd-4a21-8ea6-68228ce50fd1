3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","businesses-reinforcement-learning","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","businesses-reinforcement-learning","d"],{"children":["__PAGE__?{\"blogDetails\":\"businesses-reinforcement-learning\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","businesses-reinforcement-learning","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
12:T4b7,<p><span style="font-family:Raleway, sans-serif;">It branches out from Artificial Intelligence and is classified as a Machine Learning type. Leveraging reinforcement learning, software agents and machines are made to ascertain the ideal behavior in a specific context with the aim of maximizing its performance.</span></p><p><img src="https://cdn.marutitech.com/RL_1_3932c27f5d.jpg" alt="Reinforcement learning in business" srcset="https://cdn.marutitech.com/thumbnail_RL_1_3932c27f5d.jpg 218w,https://cdn.marutitech.com/small_RL_1_3932c27f5d.jpg 500w," sizes="100vw"></p><p><span style="font-family:Raleway, sans-serif;">The reinforcement learning model prophesies interaction between two elements – Environment and the learning agent. The learning agent leverages two mechanisms namely exploration and exploitation. When the learning agent acts on trial and error, it is termed as exploration, and when it acts based on the knowledge gained from the environment, it is referred to as exploitation. The environment rewards the agent for correct actions, which is the reinforcement signal. Leveraging the rewards obtained, the agent improves its environment knowledge to select the next action.</span></p>13:T1b6c,<p><span style="font-family:Raleway, sans-serif;">Now, artificial agents are being created to perform the tasks as a human. These agents have made their presence felt in businesses, and the use of agents driven by reinforcement learning is cut across industries.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Robots driven by reinforcement learning in factory</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">A peep into a factory gives the inside information of tasks getting carried out here. Take, for instance, the task of picking a device from one box and putting it in a container. Robots are now training themselves to do this job with great speed and precision. </span><a href="https://www.technologyreview.com/s/601045/this-factory-robot-learns-a-new-job-overnight/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;">Fanuc</span></a><span style="font-family:Raleway, sans-serif;">, a Japanese company, takes pride in the industrial robot that is clever enough to train itself to do this job.</span></p><p><span style="font-family:Raleway, sans-serif;">This robot uses deep reinforcement learning to get trained to learn and perform a new task. While it picks an object, it also captures the video footage of this process. Whether it succeeds or fails, it memorizes the object and gains knowledge as part of the deep learning model controlling the actions of the robot.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement learning optimizes space management in warehouse</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">Optimizing space utilization is a challenge that drives warehouse managers to seek best solutions. The high volumes of inventory, fluctuating demands for inventories and slow replenishing rates of inventory are hurdles to cross before using warehouse space in the best possible way. Reinforcement learning algorithms can be built to reduce transit time for stocking as well as retrieving products in the warehouse for optimizing space utilization and warehouse operations.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>How dynamic pricing is made possible through reinforcement learning</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">Dynamic pricing is a well-suited strategy to adjust prices depending on supply and demand to maximize revenue from products. Techniques like Q-learning can be leveraged to provide solutions addressing dynamic pricing problems. Reinforcement learning algorithms serve businesses to optimize pricing during interactions with customers.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement learning for customer delivery</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">A manufacturer wants to deliver products for customers with a fleet of trucks ready to serve customer demands. With the aim to make split deliveries and realize savings in the process, the manufacturer opts for Split Delivery Vehicle Routing Problem. The prime objective of the manufacturer is to reduce total fleet cost while meeting all demands of the customers.</span></p><p><span style="font-family:Raleway, sans-serif;">For this manufacturer, agent approach that hinges on reinforcement learning comes good to meet desired results. By introducing the multi-agents system, agents are made to communicate and cooperate with one another, learn through reinforcement learning. Q-learning is then leveraged to serve appropriate customers with just one vehicle. The manufacturer reaps benefits by improving execution time and by reducing the number of trucks used for meeting the demands of customers.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>eCommerce personalization brought about by reinforcement learning</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">For retailers and e-commerce merchants, it has grown into an absolute imperative to tailor communications and promotions fitting customer purchasing habits – Personalization is at the core of promoting relevant shopping experiences to capture customer loyalty. Reinforcement learning algorithms are proving their worth by allowing e-commerce merchants to learn and analyze customer behaviors and tailor products and services to suit customer interests.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement learning driving financial investment decisions</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">Pit.ai is at the forefront leveraging reinforcement learning for evaluating trading strategies. It is turning out to be a robust tool for training systems to optimize financial objectives. John Moody and Matthew Saffell have demonstrated how reinforcement learning can be used for optimizing trading systems built for single trading security or trading portfolios.</span></p><p><span style="font-family:Raleway, sans-serif;">hiHedge is the proof of how reinforcement learning is leveraged in trading scenarios. It uses AI trader which is involved in continuous learning for generating trading strategies for users and helping them realize their investment goals.</span></p><p><span style="font-family:Arial;">Companies seeking to improve their AI capabilities often turn to specialized </span><a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">AI development services</span></a><span style="font-family:Arial;"> for reinforcement training expertise.</span></p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement training to the advantage of medical industry</strong></span></h3><p><span style="font-family:Raleway, sans-serif;">A dynamic treatment regime (DTR) is a subject of medical research setting rules for finding effective treatments for patients. Diseases like cancer demand treatments for a long period where drugs and treatment levels are administered over a long period. Reinforcement learning addresses this DTR problem where RI algorithms help in processing clinical data to come up with a treatment strategy, using various clinical indicators collected from patients as inputs.</span></p><p><span style="font-family:Raleway, sans-serif;">As humankind is searching for ways to make the machine perform the tasks of human, technology has emerged the driving force making this possible. Where there is a big gap between the idea and reality, reinforcement learning has given hope by driving robots and machines to perform tasks that were unimaginable at one time. This is just the beginning. It is emerging as an innovative technology that can drive business value.&nbsp;</span></p>14:T719,<p>Lately, Artificial Intelligence and Machine Learning is a hot topic in the tech industry. Perhaps more than our daily lives Artificial Intelligence (AI) is impacting the business world more. There was about $300 million in venture capital invested in AI startups in 2014, a 300% increase than a year before (<a href="http://www.bloomberg.com/news/articles/2015-02-03/i-ll-be-back-the-return-of-artificial-intelligence" target="_blank" rel="noopener">Bloomberg</a>).</p><p><i>Hey there! This blog is almost about <strong>1000+ words</strong> long and may take <strong>~5 mins</strong> to go through the whole thing. We understand that you might not have that much time.</i></p><p><i>This is precisely why we made a <strong>short video</strong> on the topic. It is less than 2 mins, and simplifies <strong>Artificial intelligence &amp; Machine learning.</strong> We hope this helps you learn more and save your time. Cheers!</i></p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/bjG3gS3Mh1U" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div><p>AI is everywhere, from gaming stations to maintaining complex information at work. Computer Engineers and Scientists are working hard to impart intelligent behavior in the machines making them think and respond to real-time situations. AI is transiting from just a research topic to the early stages of enterprise adoption. Tech giants like Google and Facebook have placed huge bets on Artificial Intelligence and Machine Learning and are already using it in their products. But this is just the beginning, over the next few years, we may see AI steadily glide into one product after another.</p>15:Tabe,<p>According to Stanford Researcher, John McCarthy, <i>“Artificial Intelligence is the science and engineering of making intelligent machines, especially intelligent computer programs. Artificial Intelligence is related to the similar task of using computers to understand human intelligence, but AI does not have to confine itself to methods that are biologically observable.”</i></p><p>Simply put, AI’s goal is to make computers/computer programs smart enough to imitate the human mind behaviour.</p><p>Knowledge Engineering is an essential part of AI research. Machines and programs need to have bountiful information related to the world to often act and react like human beings. AI must have access to properties, categories, objects and relations between all of them to implement knowledge engineering. <a href="https://marutitech.com/services/artificial-intelligence-consulting/" target="_blank" rel="noopener"><span style="font-family:Arial;">AI solutions</span></a><span style="font-family:Arial;"> initiate common sense, problem-solving, and analytical reasoning power in machines, which is a complex and tedious job.</span></p><p>AI services can be classified into Vertical or Horizontal AI</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is Vertical AI?</strong></span></h3><p>These are services focus on the single job, whether that’s scheduling meeting, automating repetitive work, etc. Vertical AI Bots performs just one job for you and do it so well, that we might mistake them for a human.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is Horizontal AI?</strong></span></h3><p>These services are such that they are able to handle multiple tasks. There is no single job to be done. Cortana, Siri and Alexa are some of the examples of Horizontal AI. These services work more massively as the question and answer settings, such as “What is the temperature in New York?” or “Call Alex”. They work for multiple tasks and not just for a particular task entirely.</p><p>AI is achieved by analysing how the human brain works while solving an issue and then using that analytical problem-solving techniques to build complex algorithms to perform similar tasks. AI is an automated decision-making system, which continuously learn, adapt, suggest and take actions automatically. At the core, they require algorithms which are able to learn from their experience. This is where Machine Learning comes into the picture.</p><figure class="image"><a href="https://marutitech.com/case-study/medical-record-processing-using-nlp/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/medical_records_processing_using_nlp_ef68ec502a.png"></a></figure>16:T652,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Weak AI, known as Narrow AI, is designed with predefined rules catering to specific tasks. It operates within set parameters and excels at solving a particular problem or automating a single process. Weak AI possesses the human mind's cognitive abilities, but unlike general intelligence, it’s tailored to fulfill distinct assignments intelligently.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">A great example of weak AI is John Searle’s room thought experiment. In this experiment, two individuals converse in Chinese, one outside and one inside a room. Here, the person inside the room is given instructions on how to reply when speaking in Chinese.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Though it may appear to the person outside that the person inside is proficient in speaking Chinese, their capability is rooted in following provided instructions. In reality, the person inside the room is adept at following instructions and not speaking Chinese.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Narrow AI has specific intelligence and doesn’t possess general intelligence. Therefore, an AI programmed to guide you on how to reach from point A to point B isn’t capable of playing a game of chess with you. Similarly, a type of AI that pretends to converse in Chinese cannot fold your clothes or wash your car.</span></p>17:T94e,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Strong AI, or artificial general intelligence, possesses mental capabilities like the human brain. They’re intelligible systems whose actions and decision-making replicate that of a human being, including their power of understanding and consciousness.&nbsp;</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Strong AI can clone distinctive human features like beliefs, cognitive abilities, and perception. Defining intelligence, setting boundaries, and predicting success ratio are some of the most arduous challenges when working with strong AI.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Due to the above reasons, weak AI is preferred, as it performs designated tasks optimally. It doesn’t need a comprehensive intelligence, and its development is modular and manageable.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Devices or systems powered by Strong AI use their cognitive abilities to learn and solve problems similar to or, in some cases, better than humans. This fuels continual growth and investment in this domain.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Finance industries significantly benefit from using AI. Leveraging subsets of AI, like ML and cognitive computing, assisted by techs like big data, cloud services, and hyper-processing systems, the finance industry can develop chatbots and personal assistants.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">As predicted by thought leaders, replacing humans is the ultimate future of AI. Yet, achieving this feat is difficult as AI grapples with bias, lack of trust, and regulatory compliance. Therefore, companies seek to balance automation and assistance by employing augmented intelligence.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI creates job opportunities in financial audits, tax analysis, and intelligent decision-making. The primary goal for businesses using AI is to achieve harmony between human and machine intelligence.</span></p>18:Td61,<p>Artificial Intelligence and <a href="https://marutitech.com/machine-learning-services/" target="_blank" rel="noopener">Machine Learning</a> are much trending and also confused terms nowadays. Machine Learning (ML) is a subset of Artificial Intelligence. ML is a science of designing and applying algorithms that are able to learn things from past cases. If some behaviour exists in past, then you may predict if or it can happen again. Means if there are no past cases then there is no prediction.</p><p>ML can be applied to solve tough issues like credit card fraud detection, enable self-driving cars and face detection and recognition. ML uses complex algorithms that constantly iterate over large data sets, analyzing the patterns in data and facilitating machines to respond different situations for which they have not been explicitly programmed. The machines learn from the history to produce reliable results. The ML algorithms use Computer Science and Statistics to predict rational outputs.</p><p>There are 3 major areas of ML:</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Supervised Learning</strong></span></h3><p>In supervised learning, training datasets are provided to the system. Supervised learning algorithms analyse the data and produce an inferred function. The correct solution thus produced can be used for mapping new examples. Credit card fraud detection is one of the examples of Supervised Learning algorithm.</p><p><img src="https://cdn.marutitech.com/AI-supervised-and-unsupervised-learning-1.png" alt="Supervised vs unsupervised learning"></p><p>Supervised Learning and Unsupervised Learning (Reference: http://dataconomy.com/whats-the-difference-between-supervised-and-unsupervised-learning/)</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Unsupervised Learning</strong></span></h3><p>Unsupervised Learning algorithms are much harder because the data to be fed is unclustered instead of datasets. Here the goal is to have the machine learn on its own without any supervision. The correct solution of any problem is not provided. The algorithm itself finds the patterns in the data. One of the examples of supervised learning is <a href="https://marutitech.com/recommendation-engine-benefits/" target="_blank" rel="noopener">Recommendation engines</a> which are there on all e-commerce sites or also on Facebook friend request suggestion mechanism.</p><p><img src="https://cdn.marutitech.com/AI-recommendation-engine.png" alt="Recommendation Engine"></p><p style="text-align:center;">Recommendation Engine</p><h3><span style="color:#000000;font-family:Poppins, sans-serif;font-size:18px;"><strong>Reinforcement Learning</strong></span></h3><p>This type of Machine Learning algorithms allows software agents and machines to automatically determine the ideal behaviour within a specific context, to maximise its performance. Reinforcement learning is defined by characterising a learning problem and not by characterising learning methods. Any method which is well suited to solve the problem, we consider it to be the reinforcement learning method. Reinforcement learning assumes that a software agent i.e. a robot, or a computer program or a bot, connect with a dynamic environment to attain a definite goal. This technique selects the action that would give expected output efficiently and rapidly.</p>19:T5be,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and Machine Learning (ML) can work in tandem to comprehend and study vast sums of data, extract relevant insights, and make future forecasts based on past trends and patterns. When used in a suitable capacity, these techs can revolutionize the software industry by enhancing overall productivity and efficiency of internal processes.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and ML allow applications to inculcate intricate functionalities like voice recognition, predictive analytics, and natural language processing while facilitating intelligent automation. This opens new avenues for diverse industries like marketing, healthcare, and customer service.</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Harnessing the power of AI and ML allows programmers to craft innovative systems with the potential to craft tailored solutions, learn user preferences, and automate daily tasks. They aid user satisfaction and enable businesses to optimize operations and create new revenue streams.&nbsp;</span></p><p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">AI and machine learning herald a new era of software, with perceptive systems that can execute tasks once confined to human abilities.</span></p>1a:T4af,<p><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">Artificial Intelligence and Machine Learning always interest and surprise us with their innovations. AI and ML have reached industries like customer service, e-commerce, finance, and others. By 2020, 85% of the customer interactions will be managed without a human (</span><a href="http://www.gartner.com/imagesrv/summits/docs/na/customer-360/C360_2011_brochure_FINAL.pdf" target="_blank" rel="noopener noreferrer nofollow"><span style="background-color:transparent;color:#1155cc;font-family:'Work Sans',sans-serif;"><u>Gartner</u></span></a><span style="background-color:transparent;color:#000000;font-family:'Work Sans',sans-serif;">). There are specific implications of AI and ML to incorporate data analysis like descriptive analytics, predictive analytics, and predictive analytics, discussed in our next blog:&nbsp;</span><a href="https://marutitech.com/machine-learning-predictive-analytics/" target="_blank" rel="noopener"><span style="background-color:transparent;color:#1155cc;font-family:'Work Sans',sans-serif;"><u>How can machine learning boost your predictive analytics?</u></span></a></p>1b:T624,<p>We have always heard “This call may be recorded for quality and training purposes” when we call the company’s call centres for required services. Although some calls are used for training purposes, but these are even used to improve natural language processing algorithms. From onsite customer behaviour to daily or seasonal trends, the typical data warehouse can contain a diverse blend of data. The insights gained from this information have driven businesses into a new domain of customer understanding, but limiting the analytics to this type of highly structured format excludes the majority of the data that’s being created at present. 80% of the data created is unstructured. It’s generated from conversations with customer service representatives and on social media sites, as well as other places. Organisations are turning to Natural Language Processing (NLP) technology to derive understanding from the countless unstructured data available online and in call logs.</p><p>In short, Natural Language Processing gives machines the ability to read, understand and derive meaning from the human languages. The challenge here with Natural Language Processing is that computers normally requires humans to talk in the programming language, which has to be explicit and highly structured, although natural language is anything but explicit. Due to highly structured languages, it’s always been difficult for machines to grasp the context of human language. But with the help of Machine Learning computers determine the uncertainty of human language.</p>1c:T7d8,<p>Sentiment analysis is widely used in the web and social media monitoring as it allows businesses to gain a broad public opinion on the organization and its services. The ability to extract insights from the text and emoticons from social media is a practice that is widely adopted by the organizations worldwide. The capacity to hastily understand customer’s attitudes and responses accordingly is something that companies like Expedia took advantage of. Digital media represents an enormous opportunity for businesses of any industry to acquire the needs, opinions and intent that users share on the web and social media. Listening to consumer’s voice requires a deep understanding of what customer’s express in Natural Language: NLP is the best way to understand the human language and crack the sentiment behind it.</p><p style="text-align:center;"><img src="https://cdn.marutitech.com/sentiment-analysis-2.jpg" alt="Sentiment Analysis"></p><p style="text-align:center;">Sentiment Analysis</p><p>Although companies always consider sentiments (positive or negative) as the most significant value of the opinions users express through social media, the reality is that emotions provide a lot of information that addresses customer’s choices and it even determines their decisions. Due to this, NLP for sentiment analysis focused on emotions reveals itself extremely favourable. With the help of NLP, companies can understand their customers better to improve their experience, which will help the businesses change their market position.</p><p>For example: If customer complaints through message or email about their issues with service or product, a Natural Language Processing system would recognize the emotions, analyze the text and mark it for a quick automatic reply accordingly. All this can save company’s time and money too. Or even companies can search for mentions on the web and social media about their Brands and quantify whether the context was negative, neutral or positive.</p>1d:T534,<p>Many important decisions in businesses are progressively moving away from human oversight and control. Many of the business decisions in industries like Finance are driven by sentiments influenced by the news. The majority of the news content is present in the form of text, infographics and images. A considerable task, of Natural Language Processing, is taking these text, analyze and extract the related information in a format that can be used in decision-making capabilities. For example, news of a big merger can impact business decisions and integrated into trading algorithms which can have profit implications in the millions of dollars.</p><p>With the arrival of advanced statistical algorithms, programs are now capable of using statistical inference to understand the human conversation by calculating the probability of certain results. The program incorporating Natural Language processing and Machine Learning can constantly improve itself with more data it processes. All the insights hidden in the unstructured data are becoming more feasible with technology advancement. <a href="https://marutitech.com/services/artificial-intelligence-consulting/natural-language-processing/" target="_blank" rel="noopener">Natural Language Processing</a> is gaining huge traction and enormous potential for the businesses.</p>2:[null,["$","$L11",null,{"blogData":{"data":[{"id":187,"attributes":{"createdAt":"2022-09-14T11:28:51.721Z","updatedAt":"2025-06-16T10:42:09.676Z","publishedAt":"2022-09-15T05:08:24.455Z","title":"A Complete Guide to Boost Your Business with Reinforcement Learning","description":"Check how businesses can seek value from reinforcement learning.","type":"Artificial Intelligence and Machine Learning","slug":"businesses-reinforcement-learning","content":[{"id":13699,"title":null,"description":"<p><span style=\"font-family:Raleway, sans-serif;\">Among other things, </span><a href=\"https://www.forbes.com/sites/janetwburns/2016/07/26/elon-musk-wants-to-end-housework-with-these-self-training-robots/#e6dcf4669aeb\" target=\"_blank\" rel=\"noopener\"><span style=\"font-family:Raleway, sans-serif;\">Elon Musk wants to create self-training robots</span></a><span style=\"font-family:Raleway, sans-serif;\"> that will put an end to housework. Machine learning experts at OpenAI have demonstrated that deep reinforcement through observation and exploration could prove decisive in making a lot of tasks easier. It is driving many a robot to do the job of a human, primarily through observation and exploration.&nbsp;</span></p>","twitter_link":null,"twitter_link_text":null},{"id":13700,"title":"WHAT IS REINFORCEMENT LEARNING?","description":"$12","twitter_link":null,"twitter_link_text":null},{"id":13701,"title":"HOW DO BUSINESSES SEEK VALUE FROM REINFORCEMENT LEARNING?","description":"$13","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":496,"attributes":{"name":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","alternativeText":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","caption":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","width":3611,"height":2000,"formats":{"thumbnail":{"name":"thumbnail_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":136,"size":5.34,"sizeInBytes":5342,"url":"https://cdn.marutitech.com//thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"small":{"name":"small_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":277,"size":15.96,"sizeInBytes":15956,"url":"https://cdn.marutitech.com//small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"large":{"name":"large_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":554,"size":47.68,"sizeInBytes":47675,"url":"https://cdn.marutitech.com//large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"medium":{"name":"medium_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":416,"size":30.41,"sizeInBytes":30409,"url":"https://cdn.marutitech.com//medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"}},"hash":"3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","size":256.4,"url":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:52:43.351Z","updatedAt":"2024-12-16T11:52:43.351Z"}}},"audio_file":{"data":null},"suggestions":{"id":1954,"blogs":{"data":[{"id":190,"attributes":{"createdAt":"2022-09-14T11:28:52.467Z","updatedAt":"2025-06-16T10:42:10.034Z","publishedAt":"2022-09-15T05:09:27.664Z","title":"Understanding the Basics of Artificial Intelligence and Machine Learning","description":"Explore how artificial intelligence and machine learning are hot topic in tech industry. ","type":"Artificial Intelligence and Machine Learning","slug":"artificial-intelligence-and-machine-learning","content":[{"id":13712,"title":null,"description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13713,"title":"What is Artificial Intelligence?","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13714,"title":"What is Weak AI?","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13715,"title":"What is Strong AI?","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13716,"title":"What is Machine Learning?","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13717,"title":"Future of AI and Machine Learning","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13718,"title":"Conclusion","description":"$1a","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":501,"attributes":{"name":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","alternativeText":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","caption":"businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","width":7359,"height":4024,"formats":{"thumbnail":{"name":"thumbnail_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"thumbnail_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":134,"size":4.36,"sizeInBytes":4357,"url":"https://cdn.marutitech.com//thumbnail_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"small":{"name":"small_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"small_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":274,"size":12.87,"sizeInBytes":12865,"url":"https://cdn.marutitech.com//small_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"medium":{"name":"medium_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"medium_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":411,"size":24.02,"sizeInBytes":24019,"url":"https://cdn.marutitech.com//medium_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"},"large":{"name":"large_businessman-protecting-data-personal-information-cyber-security-data-concept-padlock-internet-technology (1).jpg","hash":"large_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":547,"size":38.12,"sizeInBytes":38121,"url":"https://cdn.marutitech.com//large_businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg"}},"hash":"businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4","ext":".jpg","mime":"image/jpeg","size":652.97,"url":"https://cdn.marutitech.com//businessman_protecting_data_personal_information_cyber_security_data_concept_padlock_internet_technology_1_45d86d98d4.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:53:07.759Z","updatedAt":"2024-12-16T11:53:07.759Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}},{"id":191,"attributes":{"createdAt":"2022-09-14T11:28:52.734Z","updatedAt":"2025-06-16T10:42:10.230Z","publishedAt":"2022-09-15T05:28:54.630Z","title":"How is natural language processing applied in business?","description":"Find out how natural language processing can be applied to various business sectors. ","type":"Artificial Intelligence and Machine Learning","slug":"how-is-natural-language-processing-applied-in-business","content":[{"id":13719,"title":"HOW IS NATURAL LANGUAGE PROCESSING APPLIED IN BUSINESS? ","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13720,"title":"SENTIMENT ANALYSIS","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13721,"title":"EMAIL FILTERS","description":"<p>Email filters are one of the common use cases of Natural Language Processing. By analyzing the text in the emails that flow through the servers, email providers can stop spam based email contents from entering their mailbox.</p><p style=\"text-align:center;\"><img src=\"https://cdn.marutitech.com/EmailFilters2.jpg\" alt=\"Email Filters\"></p><p style=\"text-align:center;\">Email Filtering to avoid Spam emails</p>","twitter_link":null,"twitter_link_text":null},{"id":13722,"title":"VOICE RECOGNITION","description":"<p>There are tools developed with the help of Natural Language Processing that enable companies to create intelligent voice driven interfaces for any system. Businesses are employing Natural Language Processing technologies to understand human language and queries. Instead of trying to understand concepts based on normal human language usage patterns, the company’s platform depends on a custom knowledge graph that is created for each application and perform a much better job identifying concepts that are relevant in the customer domain.</p>","twitter_link":null,"twitter_link_text":null},{"id":13723,"title":"INFORMATION EXTRACTION","description":"$1d","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":403,"attributes":{"name":"How-is-Natural-Language-Processing-applied-in-Business.jpg","alternativeText":"How-is-Natural-Language-Processing-applied-in-Business.jpg","caption":"How-is-Natural-Language-Processing-applied-in-Business.jpg","width":1000,"height":563,"formats":{"thumbnail":{"name":"thumbnail_How-is-Natural-Language-Processing-applied-in-Business.jpg","hash":"thumbnail_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":5.01,"sizeInBytes":5011,"url":"https://cdn.marutitech.com//thumbnail_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6.jpg"},"small":{"name":"small_How-is-Natural-Language-Processing-applied-in-Business.jpg","hash":"small_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":13.44,"sizeInBytes":13442,"url":"https://cdn.marutitech.com//small_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6.jpg"},"medium":{"name":"medium_How-is-Natural-Language-Processing-applied-in-Business.jpg","hash":"medium_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":23.51,"sizeInBytes":23505,"url":"https://cdn.marutitech.com//medium_How_is_Natural_Language_Processing_applied_in_Business_b272f032e6.jpg"}},"hash":"How_is_Natural_Language_Processing_applied_in_Business_b272f032e6","ext":".jpg","mime":"image/jpeg","size":34.89,"url":"https://cdn.marutitech.com//How_is_Natural_Language_Processing_applied_in_Business_b272f032e6.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:45:57.382Z","updatedAt":"2024-12-16T11:45:57.382Z"}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1954,"title":"Machine Learning Model Accelerates Healthcare Record Processing by 87%","link":"https://marutitech.com/case-study/medical-record-processing-using-nlp/","cover_image":{"data":{"id":675,"attributes":{"name":"2.png","alternativeText":"2.png","caption":"2.png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":21,"sizeInBytes":21002,"url":"https://cdn.marutitech.com//thumbnail_2_d22fbc1184.png"},"small":{"name":"small_2.png","hash":"small_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":73.7,"sizeInBytes":73702,"url":"https://cdn.marutitech.com//small_2_d22fbc1184.png"},"medium":{"name":"medium_2.png","hash":"medium_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":163.79,"sizeInBytes":163790,"url":"https://cdn.marutitech.com//medium_2_d22fbc1184.png"},"large":{"name":"large_2.png","hash":"large_2_d22fbc1184","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":292.75,"sizeInBytes":292746,"url":"https://cdn.marutitech.com//large_2_d22fbc1184.png"}},"hash":"2_d22fbc1184","ext":".png","mime":"image/png","size":93.1,"url":"https://cdn.marutitech.com//2_d22fbc1184.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-31T09:40:15.084Z","updatedAt":"2024-12-31T09:40:15.084Z"}}}},"authors":{"data":[{"id":11,"attributes":{"createdAt":"2022-09-02T07:15:55.995Z","updatedAt":"2025-06-16T10:42:34.260Z","publishedAt":"2022-09-02T07:15:57.268Z","name":"Pinakin Ariwala","designation":null,"description":"<p><br><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Pinakin is the VP of Data Science and Technology at Maruti Techlabs. With about two decades of experience leading diverse teams and projects, his technological competence is unmatched.</span></p>","slug":"pinakin-ariwala","linkedin_link":"https://www.linkedin.com/in/pinakinariwala/","twitter_link":"https://twitter.com/pinakinariwala","image":{"data":[{"id":533,"attributes":{"name":"Pinakin Ariwala.jpg","alternativeText":"Pinakin Ariwala.jpg","caption":"Pinakin Ariwala.jpg","width":1620,"height":1620,"formats":{"thumbnail":{"name":"thumbnail_Pinakin Ariwala.jpg","hash":"thumbnail_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":3.87,"sizeInBytes":3868,"url":"https://cdn.marutitech.com//thumbnail_Pinakin_Ariwala_aac9220fa9.jpg"},"small":{"name":"small_Pinakin Ariwala.jpg","hash":"small_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.89,"sizeInBytes":23890,"url":"https://cdn.marutitech.com//small_Pinakin_Ariwala_aac9220fa9.jpg"},"medium":{"name":"medium_Pinakin Ariwala.jpg","hash":"medium_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.68,"sizeInBytes":51678,"url":"https://cdn.marutitech.com//medium_Pinakin_Ariwala_aac9220fa9.jpg"},"large":{"name":"large_Pinakin Ariwala.jpg","hash":"large_Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":91.03,"sizeInBytes":91034,"url":"https://cdn.marutitech.com//large_Pinakin_Ariwala_aac9220fa9.jpg"}},"hash":"Pinakin_Ariwala_aac9220fa9","ext":".jpg","mime":"image/jpeg","size":179.34,"url":"https://cdn.marutitech.com//Pinakin_Ariwala_aac9220fa9.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:30.309Z","updatedAt":"2024-12-16T11:55:30.309Z"}}]}}}]},"seo":{"id":2184,"title":"A Complete Guide to Boost Your Business with Reinforcement Learning","description":"Reinforcement learning is used across many industries such as manufacturing, e-commerce and finance to automate and maximize the performance of the system.","type":"article","url":"https://marutitech.com/businesses-reinforcement-learning/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":496,"attributes":{"name":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","alternativeText":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","caption":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","width":3611,"height":2000,"formats":{"thumbnail":{"name":"thumbnail_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":136,"size":5.34,"sizeInBytes":5342,"url":"https://cdn.marutitech.com//thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"small":{"name":"small_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":277,"size":15.96,"sizeInBytes":15956,"url":"https://cdn.marutitech.com//small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"large":{"name":"large_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":554,"size":47.68,"sizeInBytes":47675,"url":"https://cdn.marutitech.com//large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"medium":{"name":"medium_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":416,"size":30.41,"sizeInBytes":30409,"url":"https://cdn.marutitech.com//medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"}},"hash":"3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","size":256.4,"url":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:52:43.351Z","updatedAt":"2024-12-16T11:52:43.351Z"}}}},"image":{"data":{"id":496,"attributes":{"name":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","alternativeText":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","caption":"3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","width":3611,"height":2000,"formats":{"thumbnail":{"name":"thumbnail_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":136,"size":5.34,"sizeInBytes":5342,"url":"https://cdn.marutitech.com//thumbnail_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"small":{"name":"small_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":277,"size":15.96,"sizeInBytes":15956,"url":"https://cdn.marutitech.com//small_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"large":{"name":"large_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":554,"size":47.68,"sizeInBytes":47675,"url":"https://cdn.marutitech.com//large_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"},"medium":{"name":"medium_3d-illustration-robot-humanoid-reading-book-solving-math (1).jpg","hash":"medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":416,"size":30.41,"sizeInBytes":30409,"url":"https://cdn.marutitech.com//medium_3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"}},"hash":"3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2","ext":".jpg","mime":"image/jpeg","size":256.4,"url":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:52:43.351Z","updatedAt":"2024-12-16T11:52:43.351Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
1e:T6b2,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/businesses-reinforcement-learning/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/businesses-reinforcement-learning/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/businesses-reinforcement-learning/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/businesses-reinforcement-learning/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/businesses-reinforcement-learning/#webpage","url":"https://marutitech.com/businesses-reinforcement-learning/","inLanguage":"en-US","name":"A Complete Guide to Boost Your Business with Reinforcement Learning","isPartOf":{"@id":"https://marutitech.com/businesses-reinforcement-learning/#website"},"about":{"@id":"https://marutitech.com/businesses-reinforcement-learning/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/businesses-reinforcement-learning/#primaryimage","url":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/businesses-reinforcement-learning/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Reinforcement learning is used across many industries such as manufacturing, e-commerce and finance to automate and maximize the performance of the system."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"A Complete Guide to Boost Your Business with Reinforcement Learning"}],["$","meta","3",{"name":"description","content":"Reinforcement learning is used across many industries such as manufacturing, e-commerce and finance to automate and maximize the performance of the system."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$1e"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/businesses-reinforcement-learning/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"A Complete Guide to Boost Your Business with Reinforcement Learning"}],["$","meta","9",{"property":"og:description","content":"Reinforcement learning is used across many industries such as manufacturing, e-commerce and finance to automate and maximize the performance of the system."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/businesses-reinforcement-learning/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"A Complete Guide to Boost Your Business with Reinforcement Learning"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"A Complete Guide to Boost Your Business with Reinforcement Learning"}],["$","meta","19",{"name":"twitter:description","content":"Reinforcement learning is used across many industries such as manufacturing, e-commerce and finance to automate and maximize the performance of the system."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//3d_illustration_robot_humanoid_reading_book_solving_math_1_92c6281bb2.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
