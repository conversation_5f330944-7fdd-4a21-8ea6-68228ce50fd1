(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5533],{64170:function(e,n,t){Promise.resolve().then(t.bind(t,85417))},80614:function(e,n){"use strict";let t=async()=>{let e="user_ip_location",n=localStorage.getItem(e);if(n)try{return JSON.parse(n)}catch(e){console.warn("Failed to parse cached IP data:",e)}try{let n=await fetch("https://api.ipify.org?format=json");if(!n.ok)throw Error("IP fetch error! Status: ".concat(n.status));let t=(await n.json()).ip||"",a=await fetch("https://ipapi.co/".concat(t,"/json/"));if(!a.ok)throw Error("Location fetch error! Status: ".concat(a.status));let r=await a.json(),s={ipAddress:t,location:{city:r.city||"",country:r.country_name||"",country_code:r.country_code||""}};return localStorage.setItem(e,JSON.stringify(s)),s}catch(e){return console.error("Error fetching IP or location:",e),{ipAddress:"",location:{city:"",country:"",country_code:""}}}};n.Z=t},85417:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return D}});var a=t(57437),r=t(2265),s=t(47907),o=t(20703),l=t(80110),i=t.n(l),c=t(97073),d=t(26155),u=t.n(d),_=t(53764),m=t.n(_);function h(e){let{sectionIndex:n,sectionQuestions:t,sectionData:s,sectionError:o,handleData:l,handleError:i}=e,d=(0,c.Z)({query:"(max-width: ".concat(u()["breakpoint-xl-1024"],")")}),[_,h]=(0,r.useState)(()=>null!==localStorage.getItem("subAnswer")?JSON.parse(localStorage.getItem("subAnswer")):[[],[,,,].fill(null)]),p=(e,n)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),n())};function g(e,n,t,a,r){let s=[..._];s[0][e]=n,s[1][e]=t;let o=0;for(let e=0;e<_[1].length;e++)null!==s[1][e]&&(o+=33.33);localStorage.setItem("subAnswer",JSON.stringify(s)),h(s),i(a,r,!1),l(a,r,s[0].join(","),Math.round(o))}return(0,a.jsx)(a.Fragment,{children:t.map((e,r)=>(0,a.jsxs)("div",{className:m().container,children:[(0,a.jsxs)("div",{className:m().question_container,children:[(0,a.jsxs)("div",{className:o[r]?"".concat(m().question_number," ").concat(m().error_message):m().question_number,children:[e.number<10?"0":"",e.number,"."]}),(0,a.jsx)("div",{className:m().question_name,children:e.name})]}),"mcq"===e.type?(0,a.jsx)("div",{className:m().mcqs_container,children:0!==e.sub_question.length?(0,a.jsx)(a.Fragment,{children:function(e,n,t){let r=e.sub_question,s=e.answers,o=[],l=0;for(let e=0;e<r.length;e++){let i=[(0,a.jsx)("div",{className:m().sub_question_name,children:r[e].name},e)];for(let o=0;o<r[e].value;o++){if(l>=s.length){console.warn("Answer index ".concat(l," exceeds answers array length ").concat(s.length));break}let o=s[l],c=(0,a.jsxs)("label",{className:_[1][e]===o.id?" ".concat(m().mcq," ").concat(m().selected_mcq):m().mcq,htmlFor:o.id,tabIndex:0,onKeyDown:a=>p(a,()=>{g(e,o.name,Number(o.id),n,t)}),children:[(0,a.jsx)("input",{type:"radio",id:o.id,name:r[e].name,"data-name":o.name,value:o.id,onChange:a=>{g(e,a.target.dataset.name,Number(a.target.value),n,t)}}),o.name]},o.id);l+=1,i.push(c)}o.push(i)}return o}(e,n,r)}):(0,a.jsx)(a.Fragment,{children:e.answers.map((t,o)=>(0,a.jsxs)("label",{className:s[r][1]===t.value?" ".concat(m().mcq," ").concat(m().selected_mcq):m().mcq,htmlFor:t.id,tabIndex:0,onKeyDown:e=>p(e,()=>{i(n,r,!1),l(n,r,t.name,Number(t.value))}),children:[(0,a.jsx)("input",{type:"radio",id:t.id,name:e.name,value:t.value,onChange:e=>{i(n,r,!1),l(n,r,t.name,Number(e.target.value))}}),t.name]},o))})}):(0,a.jsx)(a.Fragment,{children:d?(0,a.jsxs)("div",{className:m().number_wrapper,children:[(0,a.jsx)("div",{className:m().draggable_container_tablet,children:e.answers.map((t,o)=>(0,a.jsxs)("label",{className:s[r][1]===t.value?" ".concat(m().number," ").concat(m().selected_number):m().number,htmlFor:t.id,tabIndex:0,onKeyDown:e=>p(e,()=>{i(n,r,!1),l(n,r,t.name,Number(t.value))}),children:[(0,a.jsx)("input",{type:"radio",id:t.id,name:e.name,value:t.value,onChange:e=>{i(n,r,!1),l(n,r,t.name,Number(e.target.value))}}),o+1]},o))}),(0,a.jsxs)("div",{className:m().number_label,children:[(0,a.jsx)("span",{children:e.answers[0].name}),(0,a.jsx)("span",{children:e.answers[e.answers.length-1].name})]})]}):(0,a.jsxs)("div",{className:m().draggable_container,children:[(0,a.jsx)("input",{type:"range",id:"range",name:"range",min:"0",max:"100",step:"25",className:m().draggable_input,value:s[r][1],onChange:e=>{let a;i(n,r,!1),l(n,r,(a=Math.round(e.target.value/25))>=t[r].answers.length?(console.warn("Answer index ".concat(a," exceeds answers array length ").concat(t[r].answers.length)),""):t[r].answers[a].name,Number(e.target.value))},style:{background:"linear-gradient(to right, #30AD43 0%, #30AD43 ".concat(s[r][1],"%, #ccc ").concat(s[r][1],"%, #ccc 100%)")}}),(0,a.jsx)("div",{className:m().draggable_wrapper,children:e.answers.map((e,t)=>(0,a.jsx)("div",{className:s[r][1]===e.value?"".concat(m().draggable_label," ").concat(m().selected_draggable_label):m().draggable_label,onClick:t=>l(n,r,e.name,Number(e.value)),onKeyDown:t=>p(t,()=>{l(n,r,e.name,Number(e.value))}),tabIndex:0,role:"button","aria-pressed":s[r][1]===e.value,children:e.name},t))})]})})]},r))})}var p=t(21768),g=t(62806),b=t(79277),v=t(15121),f=t.n(v);let x=["Strategy & Leadership","Data Readiness & Infrastructure","Talent & Skills","Execution & Monitoring","Impact Evaluation"];var y=e=>{let{visibleCount:n,onStepClick:t}=e,[s,o]=(0,r.useState)(1);return(0,r.useEffect)(()=>{s<n+1&&o(n+1)},[n,s]),(0,a.jsx)("div",{className:f().container,children:x.map((e,r)=>{let l=r+1,i=l<=n+1,c=l===s;return(0,a.jsxs)("div",{className:f().stepWrapper,onClick:()=>{l<=n+1&&(o(l),t(l))},children:[(0,a.jsx)("div",{className:"\n                ".concat(f().circle,"\n                ").concat(i?f().active_circle:"","\n                ").concat(c?f().selected_circle:"","\n              "),children:l}),(0,a.jsx)("p",{className:"\n                ".concat(f().label,"\n                ").concat(i?f().active_text:"","\n                ").concat(c?f().selected_text:"","\n              "),children:e}),r<x.length-1&&(0,a.jsx)("div",{className:"".concat(f().line," ").concat(l<n+1?f().active:"")})]},r)})})},N=t(62884),w=t(13712),j=t.n(w),F=e=>{let{percentage:n,tag_list:t,tag_color:r}=e,s=Math.max(0,Math.min(n,100)),{tag:o,color:l}=s<=30?{tag:t[0].name,color:r[0]}:s<=60?{tag:t[1].name,color:r[1]}:s<=85?{tag:t[2].name,color:r[2]}:{tag:t[3].name,color:r[3]},i=[{limit:30,color:r[0],label:"0–30"},{limit:60,color:r[1],label:"31–60"},{limit:85,color:r[2],label:"61–85"},{limit:100,color:r[3],label:"86–100"}];return(0,a.jsxs)("div",{className:j().gauge_container,children:[(0,a.jsx)(N.ZP,{type:"semicircle",arc:{width:.2,padding:.005,cornerRadius:5,subArcs:i.map(e=>({limit:e.limit,color:e.color,showTick:!0,label:e.label}))},pointer:{color:"#000000",baseColor:"#ffff",length:.7,width:12},className:j().gauge,value:s,labels:{valueLabel:{formatTextValue:()=>""}}}),(0,a.jsxs)("div",{className:j().result_text,style:{backgroundColor:l},children:[o,": ",Math.round(s),"%"]})]})},A=e=>{let{percentage:n=50,radius:t=20,strokeWidth:r=7}=e,s=t-r/2,o=2*s*Math.PI;return(0,a.jsxs)("svg",{height:2*t,width:2*t,children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"gradient",x1:"0",y1:"0",x2:"1",y2:"0.15",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#febe10"}),(0,a.jsx)("stop",{offset:"30.56%",stopColor:"#f47a37"}),(0,a.jsx)("stop",{offset:"53.47%",stopColor:"#f05443"}),(0,a.jsx)("stop",{offset:"75.75%",stopColor:"#d91a5f"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#b41f5e"})]})}),(0,a.jsx)("circle",{stroke:"#ddd",fill:"transparent",strokeWidth:r,r:s,cx:t,cy:t}),(0,a.jsx)("circle",{stroke:"url(#gradient)",fill:"transparent",strokeWidth:r,strokeDasharray:o,strokeDashoffset:o-n/100*o,strokeLinecap:"round",r:s,cx:t,cy:t,transform:"rotate(-90 ".concat(t," ").concat(t,")"),style:{transition:"stroke-dashoffset 0.5s ease"}})]})},I=t(97753),S=t(41396),k=t(45536),C=t.n(k),R=t(42101),B=t(20167),E=t.n(B);t(40561);var W=t(5461);function L(e){let{formData:n,source:t="AIReadiness",handleResult:s,handleVisibleSection:l}=e,{title:i,instructions:c,consent_statement:d,LinkedInButton_title:u,button:_,formFields:{fieldNameFor_FirstName:m,fieldNameFor_LastName:h,fieldNameFor_EmailAddress:b,fieldNameFor_CompanyName:v,fieldNameFor_PhoneNumber:f,fieldNameFor_HowCanWeHelpYou:x}}=n,[y,N]=(0,r.useState)(!1),w=(0,W.Z)(),{values:j,errors:F,errorMessages:A,handleChange:k,handleSubmitAIReadiness:B}=(0,R.Z)({firstName:"",lastName:"",emailAddress:"",phoneNumber:"",howDidYouHearAboutUs:"",companyName:"",howCanWeHelpYou:"",consent:!1},{firstName:{empty:!1},lastName:{empty:!1},emailAddress:{empty:!1,invalid:!1},phoneNumber:{empty:!1,invalid:!1},consent:{empty:!1}},"default",t),L=async e=>{e.preventDefault(),N(!0);try{let{data:e,newResult:n}=s();await B(e,n,l)}catch(e){console.error("Form submission failed:",e)}finally{N(!1)}};return(0,a.jsx)(I.default,{fluid:!0,children:(0,a.jsxs)("div",{className:C().formWrapper,children:[(0,a.jsx)(p.Z,{title:"Contact Details",headingType:"h2",className:C().heading}),(0,a.jsxs)("form",{className:C().form,onSubmit:L,children:[(0,a.jsx)("div",{className:C().formFields,children:(0,a.jsx)("div",{className:C().personalDetailsWrapper,children:(0,a.jsx)("div",{className:(0,S.Z)(C().row,C().firstRow),children:(0,a.jsxs)("div",{className:(0,S.Z)(C().row,C().nameFields),children:[(0,a.jsxs)("div",{className:(0,S.Z)(C().nameAndInputWrapper),children:[(0,a.jsxs)("label",{htmlFor:"firstName",className:F.firstName.empty?C().errorLabel:C().formLabel,children:[m,"*"]}),(0,a.jsx)("input",{className:F.firstName.empty?"".concat(C().errorInput," ").concat(C().formInput):"".concat(C().formInput),type:"text",id:"firstName",name:"firstName",maxLength:50,value:j.firstName,onChange:e=>k(null==e?void 0:e.target),onBlur:e=>k(null==e?void 0:e.target)})]}),(0,a.jsxs)("div",{className:(0,S.Z)(C().nameAndInputWrapper),children:[(0,a.jsxs)("label",{htmlFor:"lastName",className:F.lastName.empty?C().errorLabel:C().formLabel,children:[h,"*"]}),(0,a.jsx)("input",{className:F.lastName.empty?"".concat(C().errorInput," ").concat(C().formInput):"".concat(C().formInput),type:"text",id:"lastName",name:"lastName",maxLength:50,value:j.lastName,onChange:e=>k(null==e?void 0:e.target),onBlur:e=>k(null==e?void 0:e.target)})]}),(0,a.jsxs)("div",{className:C().nameAndInputWrapper,children:[(0,a.jsxs)("label",{htmlFor:"emailAddress",className:F.emailAddress.empty||F.emailAddress.invalid?C().errorLabel:C().formLabel,children:[b,"*"]}),(0,a.jsx)("input",{className:F.emailAddress.empty?"".concat(C().errorInput," ").concat(C().formInput):"".concat(C().formInput),type:"text",id:"emailAddress",name:"emailAddress",maxLength:50,value:j.emailAddress,onChange:e=>k(null==e?void 0:e.target),onBlur:e=>k(null==e?void 0:e.target)})]}),(0,a.jsxs)("div",{className:C().nameAndInputWrapper,children:[(0,a.jsxs)("label",{htmlFor:"phoneNumber",className:F.phoneNumber.empty||F.phoneNumber.invalid?C().errorLabel:C().formLabel,children:[f,"*"]}),(0,a.jsx)("div",{className:C().phoneInputWrapper,children:(0,a.jsx)(E(),{inputProps:{id:"phoneNumber"},placeholder:"",inputClass:F.phoneNumber.empty||F.phoneNumber.invalid?"".concat(C().errorInput," ").concat(C().formInputPhone):C().formInputPhone,buttonClass:F.phoneNumber.empty||F.phoneNumber.invalid?"".concat(C().errorInput," ").concat(C().formInputPhone_dial_icon):C().formInputPhone_dial_icon,dropdownClass:C().ph_number_countries_dropdown,preferredCountries:["us","gb","sg","de","sa","in","nl","au","be","my"],country:w||"us",value:j.phoneNumber,onChange:e=>k({value:e,name:"phoneNumber"}),onBlur:e=>k(null==e?void 0:e.target)})})]}),(0,a.jsxs)("div",{className:(0,S.Z)(C().nameAndInputWrapper,C().companyNameWrapper),children:[(0,a.jsx)("label",{htmlFor:"companyName",className:C().formLabel,children:v}),(0,a.jsx)("input",{className:C().formInput,type:"text",id:"companyName",name:"companyName",maxLength:50,value:j.companyName,onChange:e=>k(null==e?void 0:e.target)})]})]})})})}),(0,a.jsx)("div",{className:C().consentRow,children:(0,a.jsxs)("label",{className:F.consent.empty?"".concat(C().errorLabel_consentText," ").concat(C().consentText):C().consentText,htmlFor:"consent",onClick:()=>{k({name:"consent",type:"checkbox",value:"",checked:!j.consent})},children:[(0,a.jsx)("input",{type:"checkbox",id:"consent",name:"consent",checked:j.consent}),(0,a.jsx)("span",{children:d})]})}),(0,a.jsxs)("div",{className:C().submitButtonRow,children:[y?(0,a.jsx)("div",{className:C().container_spinner,children:(0,a.jsx)("div",{className:C().spinner})}):(0,a.jsx)(g.Z,{type:"submit",className:C().result_button,label:"Check my AI Readiness Score"}),u&&(0,a.jsxs)("a",{className:C().linkedInButton,href:"#",children:[u,(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/linkedin_c13ca9a536.png"),width:32,height:32,alt:"LinkedIn Logo"})]})]})]}),(0,a.jsxs)("div",{className:C().errorMessages,children:[(0,a.jsx)("div",{children:A.empty&&A.empty}),(0,a.jsx)("div",{children:A.invalid&&A.invalid})]})]})})}function D(e){var n,t,l,d,u,_,m,v,f,x;let{body:N,formData:w}=e,[j,I]=(0,r.useState)(null),[S,k]=(0,r.useState)(null),[C,R]=(0,r.useState)(null),[B,E]=(0,r.useState)(null),[W,D]=(0,r.useState)([]),T=(0,s.useRouter)(),q=(0,c.Z)({query:"(max-width: 700px)"});(0,r.useEffect)(()=>{var e;let n=(null==N?void 0:null===(e=N.ai_readiness_components)||void 0===e?void 0:e.data.map(e=>e.attributes.heading.toLowerCase().replaceAll(" ","-").replaceAll("&","and")))||[];function t(){let e=window.location.hash.substring(1);e===n[n.length-2]&&null!==localStorage.getItem("result")||e===n[n.length-1]&&null===localStorage.getItem("result")?G():H(n.indexOf(e))}return n.push("result"),D(n),P(),t(),window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),(0,r.useEffect)(()=>{var e;if(!W.length)return;let n=C<W.length?W[C]:"result";window.location.hash!=="#"+n&&(window.location.hash="#"+n);let t=document.getElementById(null==N?void 0:null===(e=N.hero_section)||void 0===e?void 0:e.button_link);t&&t.scrollIntoView({behavior:"smooth"})},[C,W]);let Z=null==N?void 0:N.tag_list,M=["#FF5656","#FF8888","#84BD32","#30AD43"];function P(){let e=[],n=[],t=0,a=null;if(null!==localStorage.getItem("result")&&(a=JSON.parse(localStorage.getItem("result"))),null!==localStorage.getItem("data")&&null!==localStorage.getItem("error"))e=JSON.parse(localStorage.getItem("data")),n=JSON.parse(localStorage.getItem("error"));else{var r,s,o,l,i,c,d,u,_,m;for(let t=0;t<(null==N?void 0:null===(r=N.ai_readiness_components)||void 0===r?void 0:r.data.length);t++){let a=[],r=[];for(let e=0;e<(null==N?void 0:null===(l=N.ai_readiness_components)||void 0===l?void 0:null===(o=l.data[t])||void 0===o?void 0:null===(s=o.attributes)||void 0===s?void 0:s.question.length);e++)(null==N?void 0:null===(d=N.ai_readiness_components)||void 0===d?void 0:null===(c=d.data[t])||void 0===c?void 0:null===(i=c.attributes)||void 0===i?void 0:i.question[e].type)==="mcq"?(a.push([null,null]),r.push(null)):(a.push([null==N?void 0:null===(m=N.ai_readiness_components)||void 0===m?void 0:null===(_=m.data[t])||void 0===_?void 0:null===(u=_.attributes)||void 0===u?void 0:u.question[e].answers[0].name,0]),r.push(!1));e[t]=a,n[t]=r}}null!==localStorage.getItem("visibleSection")&&(t=JSON.parse(localStorage.getItem("visibleSection"))),I(e),k(n),R(t),E(a)}function z(e,n,t,a){let r=[...j];r[e][n][0]=t,r[e][n][1]=a,localStorage.setItem("data",JSON.stringify(r)),I(r)}function O(e,n,t){let a=[...S];a[e][n]=t,localStorage.setItem("error",JSON.stringify(a)),k(a)}function H(e){-1===e&&(e=0),localStorage.setItem("visibleSection",JSON.stringify(e)),R(e)}function Q(){H(C-1)}function J(){if(!(S[C].includes(null)||S[C].includes(!0)))return!0;{let e=[...S];for(let n=0;n<e[C].length;n++)null===e[C][n]&&(e[C][n]=!0);return localStorage.setItem("error",JSON.stringify(e)),k(e),!1}}function Y(){if(J()){let n={};n.final=0;for(let t=0;t<j.length;t++){var e;let a=null==N?void 0:null===(e=N.ai_readiness_components)||void 0===e?void 0:e.data[t].attributes.section_weight,r=0;for(let e=0;e<j[t].length;e++)r+=j[t][e][1];n[t]=Math.round(r/j[t].length),n.final=n.final+n[t]*a}return n.final=Math.round(n.final),console.log("data",j),console.log("result",n),localStorage.setItem("result",JSON.stringify(n)),E(n),{data:j,newResult:n}}}function G(){var e;localStorage.removeItem("data"),localStorage.removeItem("error"),localStorage.removeItem("visibleSection"),localStorage.removeItem("result"),localStorage.removeItem("subAnswer"),window.location.href="/ai-readiness-audit#"+(null==N?void 0:null===(e=N.ai_readiness_components)||void 0===e?void 0:e.data[0].attributes.heading.toLowerCase().replaceAll(" ","-").replaceAll("&","and")),P()}function U(){let e={...B};delete e.final;let n=0,t=100;for(let a in e)e[a]<t&&(t=e[a],n=Number(a));return n}return(0,a.jsxs)(a.Fragment,{children:[j&&C<j.length&&(0,a.jsx)(b.Z,{heroData:null==N?void 0:N.hero_section,variant:"ai-readiness"}),j&&(0,a.jsxs)("div",{className:i().container,id:null==N?void 0:null===(n=N.hero_section)||void 0===n?void 0:n.button_link,children:[C<j.length&&(0,a.jsx)(a.Fragment,{children:q?(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:i().button_wrapper_mobile,children:[C>0&&C<j.length&&(0,a.jsx)("button",{onClick:Q,children:(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/black_chevron_left_5adc2eb9de.svg"),alt:"previous section",width:25,height:25})}),C+1,"/0",W.length-1,C<j.length-1&&(0,a.jsx)("button",{onClick:()=>{J()&&H(C+1)},children:(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/black_chevron_left_6d81dc24e5.svg"),alt:"next section",width:25,height:25})})]})}):(0,a.jsx)(y,{visibleCount:C,onStepClick:e=>H(e-1)})}),null==N?void 0:null===(t=N.ai_readiness_components)||void 0===t?void 0:t.data.map((e,n)=>{var t;return(0,a.jsxs)("div",{className:C===n?i().section_wrapper:i().hidden,children:[(0,a.jsx)("div",{className:i().heading,children:(0,a.jsxs)("h2",{children:[n+1,". ",null==e?void 0:null===(t=e.attributes)||void 0===t?void 0:t.heading]})}),C!==j.length&&(0,a.jsx)(h,{sectionIndex:n,sectionQuestions:null==e?void 0:e.attributes.question,sectionData:j[n],sectionError:S[n],handleData:z,handleError:O}),(0,a.jsx)("span",{id:"error",children:C<j.length&&S[C].includes(!0)&&(0,a.jsx)("div",{className:i().error_message,children:"Please fill all the required fields."})}),C===j.length-1&&(0,a.jsx)(L,{formData:w,handleResult:Y,handleVisibleSection:H}),(0,a.jsxs)("div",{className:i().button_wrapper,children:[C>0&&C<j.length&&(0,a.jsx)("button",{onClick:Q,children:(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/chevron_left_7f3e8fa9d6.svg"),alt:"previous section",width:50,height:50})}),C<j.length-1&&(0,a.jsx)("button",{onClick:()=>{J()&&H(C+1)},children:(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/chevron_right_0f9e1dff3c.svg"),alt:"next section",width:50,height:50})})]})]},n)}),C===j.length&&B&&(0,a.jsxs)("div",{className:i().result_section,children:[(0,a.jsx)("div",{className:i().button_wrapper,children:(0,a.jsxs)(g.Z,{className:i().restart_button,onClick:G,children:[(0,a.jsx)(o.default,{src:"".concat("https://dev-cdn.marutitech.com","/restart_button_831deeb022.svg"),alt:"restart assessment",width:24,height:24}),null==N?void 0:null===(l=N.restart_button)||void 0===l?void 0:l.title]})}),(0,a.jsx)("div",{className:i().heading,children:(0,a.jsxs)("h2",{children:[null==N?void 0:null===(d=N.tag)||void 0===d?void 0:d.title,":"," ",(0,a.jsxs)("span",{style:{color:M[function(e){let n="",t=0;for(let a=0;a<Z.length;a++)if(B[e]<=Z[a].value){n=Z[a].name,t=a;break}return[n,t]}(U())[1]]},children:[null==N?void 0:null===(m=N.ai_readiness_components)||void 0===m?void 0:null===(_=m.data[U()])||void 0===_?void 0:null===(u=_.attributes)||void 0===u?void 0:u.heading," ",": ",B[U()],"%"]})]})}),(0,a.jsx)("div",{className:i().gauge_wrapper,children:(0,a.jsx)(F,{percentage:B.final,tag_list:Z,tag_color:M})}),(0,a.jsx)("div",{className:i().tags,children:Z.map((e,n)=>(0,a.jsxs)("span",{style:{color:M[n]},children:[0===n?"<".concat(Z[n].value):n===Z.length-1?">".concat(Z[n-1].value):"".concat(Z[n-1].value+1," - ").concat(Z[n].value),"%: ",Z[n].name,n<Z.length-1?" | ":""]},n))}),(0,a.jsx)("div",{className:i().description,dangerouslySetInnerHTML:{__html:null==N?void 0:null===(v=N.tag)||void 0===v?void 0:v.description}}),(0,a.jsx)("div",{children:(0,a.jsx)(g.Z,{className:i().consultation_button,label:null==N?void 0:null===(f=N.consultation_button)||void 0===f?void 0:f.title,type:"button",onClick:()=>{var e;T.push(null==N?void 0:null===(e=N.consultation_button)||void 0===e?void 0:e.link)}})}),(0,a.jsx)(p.Z,{title:null==N?void 0:N.score_heading,headingType:"h2",className:i().heading}),(0,a.jsx)("div",{className:i().score_cards_wrapper,children:null==N?void 0:null===(x=N.ai_readiness_components)||void 0===x?void 0:x.data.map((e,n)=>{var t;return(0,a.jsxs)("div",{className:i().score_cards,children:[(0,a.jsxs)("div",{style:{display:"flex",gap:"20px",alignItems:"center"},children:[(0,a.jsx)(A,{percentage:B[n]}),B[n],"%"]}),(0,a.jsx)("div",{children:(0,a.jsx)("b",{children:null==e?void 0:null===(t=e.attributes)||void 0===t?void 0:t.heading})})]},n)})})]})]})]})}},62806:function(e,n,t){"use strict";t.d(n,{Z:function(){return i}});var a=t(57437),r=t(8792),s=t(41396),o=t(15758),l=t.n(o);function i(e){let{label:n="",className:t="",type:o="button",isLink:i=!1,leftIcon:c=null,rightIcon:d=null,href:u="",children:_=null,isExternal:m=!1,onClick:h=()=>{},dataID:p=null,onMouseDown:g=()=>{},onMouseUp:b=()=>{},onTouchStart:v=()=>{},onTouchEnd:f=()=>{},scrollToForm:x}=e,y=(0,a.jsxs)("div",{className:l().innerWrapper,children:[c&&(0,a.jsx)("span",{className:l().leftWrapper,children:c}),n,d&&(0,a.jsx)("span",{className:l().rightWrapper,children:d})]}),N=e=>{x&&x(),h&&h(e)};return i?(0,a.jsx)(r.default,{href:u,target:m?"_blank":"_self",rel:m?"noreferrer":null,className:(0,s.Z)(l().link,t),"data-id":p,onClick:h,children:(0,a.jsx)("div",{children:y})}):(0,a.jsxs)("button",{type:o,className:(0,s.Z)(l().button,t),"data-id":p,onClick:e=>N(e),onMouseDown:g,onMouseUp:b,onTouchStart:v,onTouchEnd:f,children:[y,_]})}},21768:function(e,n,t){"use strict";t.d(n,{Z:function(){return c}});var a=t(57437);t(2265);var r=t(41396),s=t(25323),o=t.n(s),l=t(46282),i=t.n(l);function c(e){let{headingType:n,title:t,position:s,style:l,className:c,richTextValue:d}=e;return(0,a.jsx)("div",{className:(0,r.Z)(i()[s],c),children:((e,n)=>{switch(e){case"h1":return d?(0,a.jsx)("h1",{className:o().h1,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h1",{className:o().h1,style:l,children:n});case"h2":return d?(0,a.jsx)("h2",{className:o().h2,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h2",{className:o().h2,style:l,children:n});case"h3":return d?(0,a.jsx)("h3",{className:o().h3,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h3",{className:o().h3,style:l,children:n});case"h4":return d?(0,a.jsx)("h4",{className:o().h4,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h4",{className:o().h4,style:l,children:n});case"h5":return d?(0,a.jsx)("h5",{className:o().h5,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h5",{className:o().h5,style:l,children:n});case"h6":return d?(0,a.jsx)("h6",{className:o().h6,style:l,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("h6",{className:o().h6,style:l,children:n});default:return null}})(n,t)})}},79277:function(e,n,t){"use strict";t.d(n,{Z:function(){return a.default}});var a=t(98060)},42101:function(e,n,t){"use strict";t.d(n,{Z:function(){return o}});var a=t(80614);let r=async()=>{try{let t=document.referrer||"",a=new URLSearchParams(window.location.search),r=a.get("utm_medium")||"",s=a.get("utm_source")||"",o=a.get("utm_campaign")||"",l="";if(window.clarity)try{l=window.clarity("get","userId")}catch(e){console.error("Error fetching Clarity ID:",e)}let i="";try{var e,n;i=(null===(n=globalThis.gaGlobal)||void 0===n?void 0:null===(e=n.vid.match(/\d+\.\d+$/))||void 0===e?void 0:e[0])||""}catch(e){console.error("Error fetching GA4 Client ID:",e)}return{clarity:l,utm_medium:r,utm_source:s,utm_campaign:o,referrer:t,ga_client_id:i}}catch(e){return console.error("Error fetching user tracking data:",e),{clarity:"",utm_medium:"",utm_source:"",utm_campaign:"",referrer:"",ga_client_id:""}}};var s=t(2265);function o(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"default",o=arguments.length>3?arguments[3]:void 0,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",[i,c]=(0,s.useState)(e),[d,u]=(0,s.useState)(n),[_,m]=(0,s.useState)({empty:"",invalid:""}),h="caseStudy"===t?["firstName","emailAddress","phoneNumber"]:["firstName","lastName","emailAddress","phoneNumber","consent"],p=e=>{var n,t,a,r;let s={..._};h.some(n=>{var t;return null===(t=e[n])||void 0===t?void 0:t.empty})?s.empty="Please fill the highlighted fields":s.empty="",(null===(n=e.emailAddress)||void 0===n?void 0:n.invalid)&&(null===(t=e.phoneNumber)||void 0===t?void 0:t.invalid)?s.invalid="Please enter valid Email ID and Phone Number":(null===(a=e.emailAddress)||void 0===a?void 0:a.invalid)?s.invalid="Please enter a valid Email ID":(null===(r=e.phoneNumber)||void 0===r?void 0:r.invalid)?s.invalid="Please enter a valid Phone Number":s.invalid="",m(s)},g=(e,t)=>{let a={...d};t?("emailAddress"!==e||/\S+@\S+\.\S+/.test(t))&&("phoneNumber"!==e||/.{6,}/.test(t))?a[e]=n[e]:a[e]={empty:!1,invalid:!0}:a[e]={empty:!0,invalid:!1},u(a),p(a)},b=()=>{let e={...d};return h.forEach(n=>{i[n]||(e[n]={empty:!0,invalid:!1})}),u(e),p(e),!Object.values(e).some(e=>e.empty||e.invalid)},v=async t=>{if(t.preventDefault(),b()){try{let e=await (0,a.Z)(),n=await r(),t={firstName:i.firstName||"",lastName:i.lastName||"",emailAddress:i.emailAddress||"",phoneNumber:i.phoneNumber||"",howDidYouHearAboutUs:i.howDidYouHearAboutUs||"",companyName:i.companyName||"",howCanWeHelpYou:i.howCanWeHelpYou||"",utm_campaign:n.utm_campaign||"",utm_medium:n.utm_medium||"",utm_source:n.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:n.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:o||"",clarity:n.clarity||"",url:window.location.href||"",referrer:n.referrer||"",consent:i.consent||!1},s=await fetch("".concat("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev","/contact-us"),{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(t)});s.ok?(("CaseStudy"===o||"eBooks"===o||"whitePapers"===o)&&l&&window.open(l,"_blank"),window.location.href="/thank-you/"):console.error("Error submitting form:",await s.json())}catch(e){console.error("Error in form submission:",e)}c(e),u(n)}},f=async(t,s,l)=>{if(b()){try{let e=await (0,a.Z)(),n=await r(),c={firstName:i.firstName||"",lastName:i.lastName||"",emailAddress:i.emailAddress||"",phoneNumber:i.phoneNumber||"",companyName:i.companyName||"",utm_campaign:n.utm_campaign||"",utm_medium:n.utm_medium||"",utm_source:n.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:n.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:o||"",clarity:n.clarity||"",url:window.location.href||"",referrer:n.referrer||"",consent:i.consent||!1,do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_:t[0][0][0]||"",how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_:t[0][1][0]||"",do_you_have_budget_allocated_for_your_ai_project_:t[0][2][0]||"",do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_:t[1][0][0]||"",which_of_the_below_db_tools_do_you_currently_use_:t[1][1][0]||"",is_the_relevant_data_for_the_ai_project_available_and_accessible_:t[1][2][0]||"",do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__:t[1][3][0]||"",how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib:t[1][4][0]||"",does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_:t[2][0][0]||"",do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_:t[3][0][0]||"",do_you_have_risk_management_strategies_in_place_for_the_ai_project_:t[3][1][0]||"",do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions:t[4][0][0]||"",strategy___leadership:s[0]||"",data_readiness___infrastructure:s[1]||"",talent___skills:s[2]||"",execution___monitoring:s[3]||"",impact_evaliation:s[4]||"",average_of_all_score:s.final||""},d=await fetch("".concat("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev","/ai-readiness"),{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(c)});d.ok?l(t.length):console.error("Error submitting form:",await d.json())}catch(e){console.error("Error in form submission:",e)}c(e),u(n)}};return{values:i,errors:d,errorMessages:_,handleChange:e=>{let{name:n,value:t,type:a="",checked:r=!1}=e;t="checkbox"===a?r:t;let s={...i};"firstName"===n||"lastName"===n?s[n]=t.replace(/[^a-zA-Z0-9 ]/g,"").trimStart():"emailAddress"===n?s[n]=t.replace(" ",""):s[n]=t,c(s),n in d&&g(n,t)},handleBlur:e=>{let{name:n,value:t}=e,a={...i};"string"==typeof t&&(a[n]=t.trim()),c(a),n in d&&g(n,t)},handleSubmit:v,handleSubmitAIReadiness:f}}},97073:function(e,n,t){"use strict";var a=t(2265);let r=void 0===window.matchMedia?()=>!1:e=>{let{query:n}=e,[t,r]=(0,a.useState)(!1),s=(0,a.useRef)(window.matchMedia(n)),o=(0,a.useCallback)(e=>{r(e.matches)},[r]);return(0,a.useEffect)(()=>{let e=s.current;return r(e.matches),e.addListener(o),()=>e.removeListener(o)},[r,o]),t};n.Z=r},41396:function(e,n,t){"use strict";function a(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];return n.filter(Boolean).join(" ")}t.d(n,{Z:function(){return a}})},5461:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var a=t(2265),r=t(80614);function s(){let[e,n]=(0,a.useState)("");return(0,a.useEffect)(()=>{(async()=>{try{var e,t;let a=await (0,r.Z)();n((null==a?void 0:null===(t=a.location)||void 0===t?void 0:null===(e=t.country_code)||void 0===e?void 0:e.toLowerCase())||"")}catch(e){n("us")}})()},[]),e}},80110:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorWhite:"#FFFFFF",colorBlack:"#000000",gray300:"#F3F3F3",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":"576px","breakpoint-xl":"1200px","breakpoint-xl-1024":"1024px","breakpoint-xl-1440":"1440px",container:"AIReadinessBody_container__tGdLN",step_container:"AIReadinessBody_step_container__gW60D",hidden:"AIReadinessBody_hidden__QgV9b",section_wrapper:"AIReadinessBody_section_wrapper__Zm43X",heading:"AIReadinessBody_heading__L0Jtf",error_message:"AIReadinessBody_error_message__g_WNZ",button_wrapper_mobile:"AIReadinessBody_button_wrapper_mobile__S0aI1",button_wrapper:"AIReadinessBody_button_wrapper__P9uSU",result_button:"AIReadinessBody_result_button__pwAt1",tags:"AIReadinessBody_tags__eraaj",result_section:"AIReadinessBody_result_section__sI57L",restart_button:"AIReadinessBody_restart_button__ngofd",consultation_button:"AIReadinessBody_consultation_button__CtKyT",description:"AIReadinessBody_description__g__hx",score_cards_wrapper:"AIReadinessBody_score_cards_wrapper__Q1Vk_",score_cards:"AIReadinessBody_score_cards__tIR6g",gauge_wrapper:"AIReadinessBody_gauge_wrapper__cUmfs"}},45536:function(e){e.exports={variables:'"@styles/variables.module.css"',colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",gray300:"#F3F3F3",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm-450":"450px","breakpoint-md":"768px","breakpoint-xl-1024":"1024px","breakpoint-xl-1400":"1400px",heading:"AIReadinessForm_heading__zDVKs",formWrapper:"AIReadinessForm_formWrapper__gZIkR",form:"AIReadinessForm_form__uu90e",formFields:"AIReadinessForm_formFields__WEfzC",personalDetailsWrapper:"AIReadinessForm_personalDetailsWrapper__MA09e",row:"AIReadinessForm_row__0FoYJ",nameAndInputWrapper:"AIReadinessForm_nameAndInputWrapper__Jkgg7",firstRow:"AIReadinessForm_firstRow__4fvbq",formLabel:"AIReadinessForm_formLabel__Sz516",formInput:"AIReadinessForm_formInput__LiTq7",phoneInputWrapper:"AIReadinessForm_phoneInputWrapper__uxK7a",formInputPhone:"AIReadinessForm_formInputPhone__Eq6EL",formInputPhone_dial_icon:"AIReadinessForm_formInputPhone_dial_icon__H1ZPl",ph_number_countries_dropdown:"AIReadinessForm_ph_number_countries_dropdown__5VtDb",formInputForHowCanWeHelpYou:"AIReadinessForm_formInputForHowCanWeHelpYou__7etT_",consentRow:"AIReadinessForm_consentRow__YAlpQ",consentText:"AIReadinessForm_consentText__jcT47",submitButtonRow:"AIReadinessForm_submitButtonRow__H9FgZ",submitButton:"AIReadinessForm_submitButton__lgj0Z",linkedInButton:"AIReadinessForm_linkedInButton__UlLiV",errorInput:"AIReadinessForm_errorInput__jFI3x",errorMessages:"AIReadinessForm_errorMessages__lMe0y",errorLabel:"AIReadinessForm_errorLabel__Dj5XK",errorLabel_consentText:"AIReadinessForm_errorLabel_consentText__xUfN3",container_spinner:"AIReadinessForm_container_spinner__hGhLh",spinner:"AIReadinessForm_spinner__YaMdM",spin:"AIReadinessForm_spin__WCWUD",result_button:"AIReadinessForm_result_button__M_E4e"}},15121:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorWhite:"#FFFFFF",colorBlack:"#000000",gray300:"#F3F3F3",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md-820":"820px",container:"AIReadinessStep_container__tmP3g",stepWrapper:"AIReadinessStep_stepWrapper__rRQUI",circle:"AIReadinessStep_circle__1cIeN",label:"AIReadinessStep_label__04lGp",line:"AIReadinessStep_line__NFSN3",active_circle:"AIReadinessStep_active_circle__6Ersp",active_text:"AIReadinessStep_active_text__zOSsX",active:"AIReadinessStep_active__ODLL_"}},15758:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",gray300:"#F3F3F3",colorWhite:"#FFFFFF",button:"Button_button__exqP_",link:"Button_link__9n7Et",innerWrapper:"Button_innerWrapper__ITLB1",leftWrapper:"Button_leftWrapper__fWtI9",rightWrapper:"Button_rightWrapper__GkIh_"}},46282:function(e){e.exports={center:"Heading_center__XBGsG",left:"Heading_left__ouHog",right:"Heading_right__jsN_Y"}},53764:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",gray300:"#F3F3F3",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm-450":"450px","breakpoint-xl":"1200px","breakpoint-xl-1024":"1024px","breakpoint-md":"768px",container:"QuestionAndAnswers_container__vG2qU",question_container:"QuestionAndAnswers_question_container__EtWck",question_number:"QuestionAndAnswers_question_number__7um4F",sub_question_name:"QuestionAndAnswers_sub_question_name__dvCuz",question_name:"QuestionAndAnswers_question_name__YK_Qn",error_message:"QuestionAndAnswers_error_message__gZobY",mcqs_container:"QuestionAndAnswers_mcqs_container__bbuHx",selected_mcq:"QuestionAndAnswers_selected_mcq__esm12",mcq:"QuestionAndAnswers_mcq__th1Qm",draggable_container:"QuestionAndAnswers_draggable_container__rxl5V",draggable_input:"QuestionAndAnswers_draggable_input__vA3MW",draggable_wrapper:"QuestionAndAnswers_draggable_wrapper__g618z",draggable_label:"QuestionAndAnswers_draggable_label__7V9Md",selected_draggable_label:"QuestionAndAnswers_selected_draggable_label__Z5WuC",number_wrapper:"QuestionAndAnswers_number_wrapper__wYtxa",draggable_container_tablet:"QuestionAndAnswers_draggable_container_tablet__4FnSm",number:"QuestionAndAnswers_number__nXewR",number_label:"QuestionAndAnswers_number_label__g9Yhw",selected_number:"QuestionAndAnswers_selected_number__aNKCd"}},13712:function(e){e.exports={variables:'"@styles/variables.module.css"',brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",gray300:"#F3F3F3",colorWhite:"#FFFFFF",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":"576px","breakpoint-xl":"1200px","breakpoint-xl-1024":"1024px","breakpoint-md":"768px",gauge_container:"RatingGuage_gauge_container__mXDVv",result_text:"RatingGuage_result_text__AdeSw",gauge:"RatingGuage_gauge__Co87l"}},25323:function(e){e.exports={variables:'"./variables.module.css"',h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",fontWeight600:"600",fontWeight700:"700",breakPoints:'"./breakpoints.module.css"',"breakpoint-sm-450":"450px",h1:"typography_h1__DecPZ",h2:"typography_h2__Dn0zf",h3:"typography_h3__o3Abb",h4:"typography_h4__lGrWj",h5:"typography_h5__DGJHL",h6:"typography_h6__vf_A0",caption:"typography_caption__hfk0A"}}}]);