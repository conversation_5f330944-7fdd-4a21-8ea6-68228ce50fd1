exports.id=5235,exports.ids=[5235],exports.modules={1063:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(95344);a(3729);var r=a(60646),i=a(56506),l=a(31905),n=a(36067),d=a(2522),o=a(76846),c=a(18924),m=a(22281),_=a(72885),b=a.n(_),u=a(44469),x=a.n(u),p=a(58404),h=a(23990),j=a.n(h),k=a(81473);function C({industriesCardData:e}){let[s,a]=(0,d.Z)({loop:!0},[(0,n.Z)(),(0,l.Z)({delay:6e3,stopOnInteraction:!0})]),{selectedIndex:_,scrollSnaps:u,onDotButtonClick:h}=(0,p.Y)(a),C=(0,c.Z)({query:`(max-width: ${b()["breakpoint-sm"]})`}),{title:g,industriesCardsBox:N}=e,v={"--first-box-bg-image":`url(${N[0]?.backgroundImage?.data?.attributes?.format?.large?.url||N[0]?.backgroundImage?.data?.attributes?.formats?.large?.url||N[0]?.backgroundImage?.data?.attributes?.url})`},I={"--second-box-bg-image":`url(${N[1]?.backgroundImage?.data?.attributes?.format?.large?.url||N[1]?.backgroundImage?.data?.attributes?.formats?.large?.url||N[1]?.backgroundImage?.data?.attributes?.url})`},T={"--third-box-bg-image":`url(${N[2]?.backgroundImage?.data?.attributes?.format?.large?.url||N[2]?.backgroundImage?.data?.attributes?.formats?.large?.url||N[2]?.backgroundImage?.data?.attributes?.url})`};return t.jsx(t.Fragment,{children:C?t.jsx("section",{className:j().embla,children:t.jsx("div",{className:j().embla__viewport,ref:s,children:(0,t.jsxs)("div",{className:j().embla__container,children:[(0,t.jsxs)("div",{className:j().embla__slide_mobile_container,children:[(0,t.jsxs)(i.default,{href:`${N[0]?.button?.link}`,prefetch:!1,className:j().mobile_box_link,children:[t.jsx("div",{style:v,className:j().embla__slide_mobileOne}),(0,t.jsxs)("div",{className:j().embla__slide_mobile,children:[t.jsx(k.Z,{headingType:"h2",title:g,className:[j().title,j().titleForMobile].join(" ")}),(0,t.jsxs)("div",{className:j().contentWrapperForMobile,children:[t.jsx(k.Z,{headingType:"h3",title:N[0]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapperForMobile,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[0]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})]})]}),t.jsx("div",{style:{padding:"30px 0"},className:x().embla__dots,children:u.length>1&&u.map((e,s)=>t.jsx(p.H,{onClick:()=>h(s),className:x().embla__dot.concat(s===_?` ${x().embla__dot_selected}`:"")},s))})]}),(0,t.jsxs)("div",{className:j().embla__slide_mobile_container,children:[(0,t.jsxs)(i.default,{href:`${N[1]?.button?.link}`,prefetch:!1,className:j().mobile_box_link,children:[t.jsx("div",{style:I,className:j().embla__slide_mobileTwo}),(0,t.jsxs)("div",{className:j().embla__slide_mobile,children:[t.jsx(k.Z,{headingType:"h2",title:g,className:[j().title,j().titleForMobile].join(" ")}),(0,t.jsxs)("div",{className:j().contentWrapperForMobile,children:[t.jsx(k.Z,{headingType:"h3",title:N[1]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapperForMobile,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[1]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})]})]}),t.jsx("div",{style:{padding:"30px 0"},className:x().embla__dots,children:u.length>1&&u.map((e,s)=>t.jsx(p.H,{onClick:()=>h(s),className:x().embla__dot.concat(s===_?` ${x().embla__dot_selected}`:"")},s))})]}),(0,t.jsxs)("div",{className:j().embla__slide_mobile_container,children:[(0,t.jsxs)(i.default,{href:`${N[2]?.button?.link}`,prefetch:!1,className:j().mobile_box_link,children:[t.jsx("div",{style:T,className:j().embla__slide_mobileThree}),(0,t.jsxs)("div",{className:j().embla__slide_mobile,children:[t.jsx(k.Z,{headingType:"h2",title:g,className:[j().title,j().titleForMobile].join(" ")}),(0,t.jsxs)("div",{className:j().contentWrapperForMobile,children:[t.jsx(k.Z,{headingType:"h3",title:N[2]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapperForMobile,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[2]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})]})]}),t.jsx("div",{style:{padding:"30px 0"},className:x().embla__dots,children:u.length>1&&u.map((e,s)=>t.jsx(p.H,{onClick:()=>h(s),className:x().embla__dot.concat(s===_?` ${x().embla__dot_selected}`:"")},s))})]})]})})}):(0,t.jsxs)(r.default,{fluid:!0,style:v,className:j().container,children:[t.jsx(k.Z,{headingType:"h2",title:g,className:j().title}),t.jsx("div",{className:(0,m.Z)(j().box,j().boxOne),children:t.jsx(i.default,{href:`${N[0]?.button?.link}`,prefetch:!1,className:j().link,children:(0,t.jsxs)("div",{className:j().contentWrapper,children:[t.jsx(k.Z,{headingType:"h3",title:N[0]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapper,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[0]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})},N[0]?.id)}),t.jsx("div",{style:I,className:(0,m.Z)(j().box,j().boxTwo),children:t.jsx(i.default,{href:`${N[1]?.button?.link}`,prefetch:!1,className:j().link,children:(0,t.jsxs)("div",{className:j().contentWrapper,children:[t.jsx(k.Z,{headingType:"h3",title:N[1]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapper,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[1]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})},N[1]?.id)}),t.jsx("div",{style:T,className:(0,m.Z)(j().box,j().boxThree),children:t.jsx(i.default,{href:`${N[2]?.button?.link}`,prefetch:!1,className:j().link,children:(0,t.jsxs)("div",{className:j().contentWrapper,children:[t.jsx(k.Z,{headingType:"h3",title:N[2]?.title,className:j().industriesCardsBoxTitle}),(0,t.jsxs)("div",{className:j().descAndButtonWrapper,children:[t.jsx("div",{className:j().industriesCardsBoxDescription,dangerouslySetInnerHTML:{__html:N[2]?.description}}),t.jsx(o.Z,{variant:"small"})]})]})},N[2]?.id)})]})})}},23990:(e,s,a)=>{var t=a(24640),r=a(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+t.colorBlack,colorWhite:""+t.colorWhite,brandColorOne:""+t.brandColorOne,brandColorTwo:""+t.brandColorTwo,brandColorThree:""+t.brandColorThree,brandColorFour:""+t.brandColorFour,brandColorFive:""+t.brandColorFive,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+r["breakpoint-md"],"breakpoint-sm-320":""+r["breakpoint-sm-320"],"breakpoint-sm-427":""+r["breakpoint-sm-427"],"breakpoint-xl-1024":""+r["breakpoint-xl-1024"],"breakpoint-xl-1440":""+r["breakpoint-xl-1440"],"breakpoint-lg":""+r["breakpoint-lg"],"breakpoint-lg-991px":""+r["breakpoint-lg-991px"],"breakpoint-xl-2100":""+r["breakpoint-xl-2100"],"breakpoint-md-769":""+r["breakpoint-md-769"],"breakpoint-sm":""+r["breakpoint-sm"],"breakpoint-sm-430":""+r["breakpoint-sm-430"],"breakpoint-sm-390":""+r["breakpoint-sm-390"],"breakpoint-sm-326":""+r["breakpoint-sm-326"],"breakpoint-md-850":""+r["breakpoint-md-850"],"breakpoint-xl":""+r["breakpoint-xl"],"breakpoint-xl-1439":""+r["breakpoint-xl-1439"],"breakpoint-xl-1400":""+r["breakpoint-xl-1400"],"breakpoint-xl-2000":""+r["breakpoint-xl-2000"],"breakpoint-xl-2559":""+r["breakpoint-xl-2559"],"breakpoint-xl-2560":""+r["breakpoint-xl-2560"],embla:"IndustriesCard_embla__AwyOU",embla__container:"IndustriesCard_embla__container__lngSV",embla__slide:"IndustriesCard_embla__slide__MTR3U",embla__slide_mobile_container:"IndustriesCard_embla__slide_mobile_container__eMJmO",embla__slide_mobile:"IndustriesCard_embla__slide_mobile__7yOdF",mobile_box_link:"IndustriesCard_mobile_box_link__O_nVG",embla__slide_mobileOne:"IndustriesCard_embla__slide_mobileOne__bRMJB",embla__slide_mobileTwo:"IndustriesCard_embla__slide_mobileTwo__n2D2M",embla__slide_mobileThree:"IndustriesCard_embla__slide_mobileThree__j_O6z",titleForMobile:"IndustriesCard_titleForMobile__8N7_x",contentWrapperForMobile:"IndustriesCard_contentWrapperForMobile__eKzMM",embla__controls:"IndustriesCard_embla__controls__cXpMq",mobileDots:"IndustriesCard_mobileDots__CQQaq",container:"IndustriesCard_container__WSeB3",title:"IndustriesCard_title__aTUJ5",box:"IndustriesCard_box__IdQ67",fadeInUp:"IndustriesCard_fadeInUp__xFkU4",contentWrapper:"IndustriesCard_contentWrapper__ZszlT",descAndButtonWrapper:"IndustriesCard_descAndButtonWrapper__DOpfW",boxTwo:"IndustriesCard_boxTwo__jEbs6",boxThree:"IndustriesCard_boxThree__L27kX",boxOne:"IndustriesCard_boxOne__vGszW",link:"IndustriesCard_link__2sb0o",industriesCardsBoxTitle:"IndustriesCard_industriesCardsBoxTitle__Ca6ht",industriesCardsBoxDescription:"IndustriesCard_industriesCardsBoxDescription__NvjeT"}},52534:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\CaseStudyCard\CaseStudyCard.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},42083:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\ContactUsForm\ContactUsForm.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},13907:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\IndustriesCard\IndustriesCard.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},46218:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\Insights\Insights.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},76771:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\ServicesCard\ServicesCard.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default},4485:(e,s,a)=>{"use strict";a.d(s,{Z:()=>l});let t=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\TrustedPartners\TrustedPartners.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default}};