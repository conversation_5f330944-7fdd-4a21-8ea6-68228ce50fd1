(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7476],{5450:function(e,t,n){"use strict";n.d(t,{h:function(){return r}});let i=n(2265).createContext(null),r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return null!=e?String(e):t||null};t.Z=i},20014:function(e){"use strict";/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */e.exports=function(){let e=new Map,t={set(t,n,i){e.has(t)||e.set(t,new Map);let r=e.get(t);r.has(n)||0===r.size?r.set(n,i):console.error("Bootstrap doesn't allow more than one instance per element. Bound instance: ".concat(Array.from(r.keys())[0],"."))},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;let i=e.get(t);i.delete(n),0===i.size&&e.delete(t)}},n="transitionend",i=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>"#".concat(CSS.escape(t)))),e),r=e=>{e.dispatchEvent(new Event(n))},o=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),s=e=>o(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(i(e)):null,a=e=>{if(!o(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){let t=e.closest("summary");if(t&&t.parentNode!==n||null===t)return!1}return t},l=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),c=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){let t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?c(e.parentNode):null},u=()=>{},d=e=>{e.offsetHeight},h=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,f=[],p=()=>"rtl"===document.documentElement.dir,m=e=>{var t;t=()=>{let t=h();if(t){let n=e.NAME,i=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=i,e.jQueryInterface)}},"loading"===document.readyState?(f.length||document.addEventListener("DOMContentLoaded",()=>{for(let e of f)e()}),f.push(t)):t()},g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;return"function"==typeof e?e(...t):n},_=function(e,t){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!i)return void g(e);let o=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e),i=Number.parseFloat(t),r=Number.parseFloat(n);return i||r?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5,s=!1,a=i=>{let{target:r}=i;r===t&&(s=!0,t.removeEventListener(n,a),g(e))};t.addEventListener(n,a),setTimeout(()=>{s||r(t)},o)},b=(e,t,n,i)=>{let r=e.length,o=e.indexOf(t);return -1===o?!n&&i?e[r-1]:e[0]:(o+=n?1:-1,i&&(o=(o+r)%r),e[Math.max(0,Math.min(o,r-1))])},v=/[^.]*(?=\..*)\.|.*/,y=/\..*/,w=/::\d+$/,E={},x=1,C={mouseenter:"mouseover",mouseleave:"mouseout"},O=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function A(e,t){return t&&"".concat(t,"::").concat(x++)||e.uidEvent||x++}function k(e){let t=A(e);return e.uidEvent=t,E[t]=E[t]||{},E[t]}function T(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===n)}function S(e,t,n){let i="string"==typeof t,r=L(e);return O.has(r)||(r=e),[i,i?n:t||n,r]}function N(e,t,n,i,r){var o,s,a;if("string"!=typeof t||!e)return;let[l,c,u]=S(t,n,i);t in C&&(o=c,c=function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return o.call(this,e)});let d=k(e),h=d[u]||(d[u]={}),f=T(h,c,l?n:null);if(f)return void(f.oneOff=f.oneOff&&r);let p=A(c,t.replace(v,"")),m=l?(s=c,function t(i){let r=e.querySelectorAll(n);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(let a of r)if(a===o)return D(i,{delegateTarget:o}),t.oneOff&&I.off(e,i.type,n,s),s.apply(o,[i])}):(a=c,function t(n){return D(n,{delegateTarget:e}),t.oneOff&&I.off(e,n.type,a),a.apply(e,[n])});m.delegationSelector=l?n:null,m.callable=c,m.oneOff=r,m.uidEvent=p,h[p]=m,e.addEventListener(u,m,l)}function j(e,t,n,i,r){let o=T(t[n],i,r);o&&(e.removeEventListener(n,o,!!r),delete t[n][o.uidEvent])}function L(e){return C[e=e.replace(y,"")]||e}let I={on(e,t,n,i){N(e,t,n,i,!1)},one(e,t,n,i){N(e,t,n,i,!0)},off(e,t,n,i){if("string"!=typeof t||!e)return;let[r,o,s]=S(t,n,i),a=s!==t,l=k(e),c=l[s]||{},u=t.startsWith(".");if(void 0===o){if(u)for(let n of Object.keys(l))!function(e,t,n,i){for(let[r,o]of Object.entries(t[n]||{}))r.includes(i)&&j(e,t,n,o.callable,o.delegationSelector)}(e,l,n,t.slice(1));for(let[n,i]of Object.entries(c)){let r=n.replace(w,"");a&&!t.includes(r)||j(e,l,s,i.callable,i.delegationSelector)}}else{if(!Object.keys(c).length)return;j(e,l,s,o,r?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;let i=h(),r=null,o=!0,s=!0,a=!1;t!==L(t)&&i&&(r=i.Event(t,n),i(e).trigger(r),o=!r.isPropagationStopped(),s=!r.isImmediatePropagationStopped(),a=r.isDefaultPrevented());let l=D(new Event(t,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),s&&e.dispatchEvent(l),l.defaultPrevented&&r&&r.preventDefault(),l}};function D(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(let[n,i]of Object.entries(t))try{e[n]=i}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>i})}return e}function P(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function M(e){return e.replace(/[A-Z]/g,e=>"-".concat(e.toLowerCase()))}let B={setDataAttribute(e,t,n){e.setAttribute("data-bs-".concat(M(t)),n)},removeDataAttribute(e,t){e.removeAttribute("data-bs-".concat(M(t)))},getDataAttributes(e){if(!e)return{};let t={};for(let n of Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"))){let i=n.replace(/^bs/,"");t[i=i.charAt(0).toLowerCase()+i.slice(1,i.length)]=P(e.dataset[n])}return t},getDataAttribute:(e,t)=>P(e.getAttribute("data-bs-".concat(M(t))))};class R{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){let n=o(t)?B.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...o(t)?B.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.constructor.DefaultType;for(let[n,i]of Object.entries(t)){let t=e[n],r=o(t)?"element":null==t?"".concat(t):Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(r))throw TypeError("".concat(this.constructor.NAME.toUpperCase(),': Option "').concat(n,'" provided type "').concat(r,'" but expected type "').concat(i,'".'))}}}class H extends R{dispose(){for(let e of(t.remove(this._element,this.constructor.DATA_KEY),I.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this)))this[e]=null}_queueCallback(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];_(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return t.get(s(e),this.DATA_KEY)}static getOrCreateInstance(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return"bs.".concat(this.NAME)}static get EVENT_KEY(){return".".concat(this.DATA_KEY)}static eventName(e){return"".concat(e).concat(this.EVENT_KEY)}constructor(e,n){super(),(e=s(e))&&(this._element=e,this._config=this._getConfig(n),t.set(this._element,this.constructor.DATA_KEY,this))}}let F=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n="#".concat(n.split("#")[1])),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map(e=>i(e)).join(","):null},W={find:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return Element.prototype.querySelector.call(t,e)},children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){let n=[],i=e.parentNode.closest(t);for(;i;)n.push(i),i=i.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>"".concat(e,':not([tabindex^="-"])')).join(",");return this.find(t,e).filter(e=>!l(e)&&a(e))},getSelectorFromElement(e){let t=F(e);return t&&W.findOne(t)?t:null},getElementFromSelector(e){let t=F(e);return t?W.findOne(t):null},getMultipleElementsFromSelector(e){let t=F(e);return t?W.find(t):[]}},q=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"hide",n="click.dismiss".concat(e.EVENT_KEY),i=e.NAME;I.on(document,n,'[data-bs-dismiss="'.concat(i,'"]'),function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),l(this))return;let r=W.getElementFromSelector(this)||this.closest(".".concat(i));e.getOrCreateInstance(r)[t]()})},z=".bs.alert",V="close".concat(z),K="closed".concat(z);class Z extends H{static get NAME(){return"alert"}close(){if(I.trigger(this._element,V).defaultPrevented)return;this._element.classList.remove("show");let e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),I.trigger(this._element,K),this.dispose()}static jQueryInterface(e){return this.each(function(){let t=Z.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError('No method named "'.concat(e,'"'));t[e](this)}})}}q(Z,"close"),m(Z);let U='[data-bs-toggle="button"]';class Y extends H{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){let t=Y.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}I.on(document,"click.bs.button.data-api",U,e=>{e.preventDefault();let t=e.target.closest(U);Y.getOrCreateInstance(t).toggle()}),m(Y);let X=".bs.swipe",Q="touchstart".concat(X),J="touchmove".concat(X),$="touchend".concat(X),G="pointerdown".concat(X),ee="pointerup".concat(X),et={endCallback:null,leftCallback:null,rightCallback:null},en={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ei extends R{static get Default(){return et}static get DefaultType(){return en}static get NAME(){return"swipe"}dispose(){I.off(this._element,X)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){let e=Math.abs(this._deltaX);if(e<=40)return;let t=e/this._deltaX;this._deltaX=0,t&&g(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(I.on(this._element,G,e=>this._start(e)),I.on(this._element,ee,e=>this._end(e)),this._element.classList.add("pointer-event")):(I.on(this._element,Q,e=>this._start(e)),I.on(this._element,J,e=>this._move(e)),I.on(this._element,$,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}constructor(e,t){super(),this._element=e,e&&ei.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}}let er=".bs.carousel",eo=".data-api",es="next",ea="prev",el="left",ec="right",eu="slide".concat(er),ed="slid".concat(er),eh="keydown".concat(er),ef="mouseenter".concat(er),ep="mouseleave".concat(er),em="dragstart".concat(er),eg="load".concat(er).concat(eo),e_="click".concat(er).concat(eo),eb="carousel",ev="active",ey=".active",ew=".carousel-item",eE=ey+ew,ex={ArrowLeft:ec,ArrowRight:el},eC={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},eO={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class eA extends H{static get Default(){return eC}static get DefaultType(){return eO}static get NAME(){return"carousel"}next(){this._slide(es)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(ea)}pause(){this._isSliding&&r(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?I.one(this._element,ed,()=>this.cycle()):this.cycle())}to(e){let t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void I.one(this._element,ed,()=>this.to(e));let n=this._getItemIndex(this._getActive());n!==e&&this._slide(e>n?es:ea,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&I.on(this._element,eh,e=>this._keydown(e)),"hover"===this._config.pause&&(I.on(this._element,ef,()=>this.pause()),I.on(this._element,ep,()=>this._maybeEnableCycle())),this._config.touch&&ei.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let e of W.find(".carousel-item img",this._element))I.on(e,em,e=>e.preventDefault());this._swipeHelper=new ei(this._element,{leftCallback:()=>this._slide(this._directionToOrder(el)),rightCallback:()=>this._slide(this._directionToOrder(ec)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}})}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;let t=ex[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;let t=W.findOne(ey,this._indicatorsElement);t.classList.remove(ev),t.removeAttribute("aria-current");let n=W.findOne('[data-bs-slide-to="'.concat(e,'"]'),this._indicatorsElement);n&&(n.classList.add(ev),n.setAttribute("aria-current","true"))}_updateInterval(){let e=this._activeElement||this._getActive();if(!e)return;let t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this._isSliding)return;let n=this._getActive(),i=e===es,r=t||b(this._getItems(),n,i,this._config.wrap);if(r===n)return;let o=this._getItemIndex(r),s=t=>I.trigger(this._element,t,{relatedTarget:r,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:o});if(s(eu).defaultPrevented||!n||!r)return;let a=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=r;let l=i?"carousel-item-start":"carousel-item-end",c=i?"carousel-item-next":"carousel-item-prev";r.classList.add(c),d(r),n.classList.add(l),r.classList.add(l),this._queueCallback(()=>{r.classList.remove(l,c),r.classList.add(ev),n.classList.remove(ev,c,l),this._isSliding=!1,s(ed)},n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return W.findOne(eE,this._element)}_getItems(){return W.find(ew,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return p()?e===el?ea:es:e===el?es:ea}_orderToDirection(e){return p()?e===ea?el:ec:e===ea?ec:el}static jQueryInterface(e){return this.each(function(){let t=eA.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError('No method named "'.concat(e,'"'));t[e]()}}else t.to(e)})}constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=W.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===eb&&this.cycle()}}I.on(document,e_,"[data-bs-slide], [data-bs-slide-to]",function(e){let t=W.getElementFromSelector(this);if(!t||!t.classList.contains(eb))return;e.preventDefault();let n=eA.getOrCreateInstance(t),i=this.getAttribute("data-bs-slide-to");return i?n.to(i):"next"===B.getDataAttribute(this,"slide")?n.next():n.prev(),void n._maybeEnableCycle()}),I.on(window,eg,()=>{for(let e of W.find('[data-bs-ride="carousel"]'))eA.getOrCreateInstance(e)}),m(eA);let ek=".bs.collapse",eT="show".concat(ek),eS="shown".concat(ek),eN="hide".concat(ek),ej="hidden".concat(ek),eL="show",eI="collapse",eD="collapsing",eP=":scope .".concat(eI," .").concat(eI),eM='[data-bs-toggle="collapse"]',eB={parent:null,toggle:!0},eR={parent:"(null|element)",toggle:"boolean"};class eH extends H{static get Default(){return eB}static get DefaultType(){return eR}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>eH.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning||I.trigger(this._element,eT).defaultPrevented)return;for(let t of e)t.hide();let t=this._getDimension();this._element.classList.remove(eI),this._element.classList.add(eD),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let n="scroll".concat(t[0].toUpperCase()+t.slice(1));this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eD),this._element.classList.add(eI,eL),this._element.style[t]="",I.trigger(this._element,eS)},this._element,!0),this._element.style[t]="".concat(this._element[n],"px")}hide(){if(this._isTransitioning||!this._isShown()||I.trigger(this._element,eN).defaultPrevented)return;let e=this._getDimension();for(let t of(this._element.style[e]="".concat(this._element.getBoundingClientRect()[e],"px"),d(this._element),this._element.classList.add(eD),this._element.classList.remove(eI,eL),this._triggerArray)){let e=W.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(eD),this._element.classList.add(eI),I.trigger(this._element,ej)},this._element,!0)}_isShown(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._element;return e.classList.contains(eL)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=s(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent)for(let e of this._getFirstLevelChildren(eM)){let t=W.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(e){let t=W.find(eP,this._config.parent);return W.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(let n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){let t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){let n=eH.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw TypeError('No method named "'.concat(e,'"'));n[e]()}})}constructor(e,t){for(let n of(super(e,t),this._isTransitioning=!1,this._triggerArray=[],W.find(eM))){let e=W.getSelectorFromElement(n),t=W.find(e).filter(e=>e===this._element);null!==e&&t.length&&this._triggerArray.push(n)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}}I.on(document,"click".concat(ek,".data-api"),eM,function(e){for(let t of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),W.getMultipleElementsFromSelector(this)))eH.getOrCreateInstance(t,{toggle:!1}).toggle()}),m(eH);var eF="bottom",eW="right",eq="left",ez="auto",eV=["top",eF,eW,eq],eK="start",eZ="clippingParents",eU="viewport",eY="popper",eX="reference",eQ=eV.reduce(function(e,t){return e.concat([t+"-"+eK,t+"-end"])},[]),eJ=[].concat(eV,[ez]).reduce(function(e,t){return e.concat([t,t+"-"+eK,t+"-end"])},[]),e$="beforeRead",eG="read",e0="afterRead",e1="beforeMain",e2="main",e5="afterMain",e3="beforeWrite",e6="write",e4="afterWrite",e9=[e$,eG,e0,e1,e2,e5,e3,e6,e4];function e7(e){return e?(e.nodeName||"").toLowerCase():null}function e8(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function te(e){return e instanceof e8(e).Element||e instanceof Element}function tt(e){return e instanceof e8(e).HTMLElement||e instanceof HTMLElement}function tn(e){return"undefined"!=typeof ShadowRoot&&(e instanceof e8(e).ShadowRoot||e instanceof ShadowRoot)}let ti={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},i=t.attributes[e]||{},r=t.elements[e];tt(r)&&e7(r)&&(Object.assign(r.style,n),Object.keys(i).forEach(function(e){var t=i[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var i=t.elements[e],r=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});tt(i)&&e7(i)&&(Object.assign(i.style,o),Object.keys(r).forEach(function(e){i.removeAttribute(e)}))})}},requires:["computeStyles"]};function tr(e){return e.split("-")[0]}var to=Math.max,ts=Math.min,ta=Math.round;function tl(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function tc(){return!/^((?!chrome|android).)*safari/i.test(tl())}function tu(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),r=1,o=1;t&&tt(e)&&(r=e.offsetWidth>0&&ta(i.width)/e.offsetWidth||1,o=e.offsetHeight>0&&ta(i.height)/e.offsetHeight||1);var s=(te(e)?e8(e):window).visualViewport,a=!tc()&&n,l=(i.left+(a&&s?s.offsetLeft:0))/r,c=(i.top+(a&&s?s.offsetTop:0))/o,u=i.width/r,d=i.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function td(e){var t=tu(e),n=e.offsetWidth,i=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-i)&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function th(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&tn(n)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function tf(e){return e8(e).getComputedStyle(e)}function tp(e){return((te(e)?e.ownerDocument:e.document)||window.document).documentElement}function tm(e){return"html"===e7(e)?e:e.assignedSlot||e.parentNode||(tn(e)?e.host:null)||tp(e)}function tg(e){return tt(e)&&"fixed"!==tf(e).position?e.offsetParent:null}function t_(e){for(var t=e8(e),n=tg(e);n&&["table","td","th"].indexOf(e7(n))>=0&&"static"===tf(n).position;)n=tg(n);return n&&("html"===e7(n)||"body"===e7(n)&&"static"===tf(n).position)?t:n||function(e){var t=/firefox/i.test(tl());if(/Trident/i.test(tl())&&tt(e)&&"fixed"===tf(e).position)return null;var n=tm(e);for(tn(n)&&(n=n.host);tt(n)&&0>["html","body"].indexOf(e7(n));){var i=tf(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||t}function tb(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function tv(e,t,n){return to(e,ts(t,n))}function ty(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function tw(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}let tE={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,i=e.name,r=e.options,o=n.elements.arrow,s=n.modifiersData.popperOffsets,a=tr(n.placement),l=tb(a),c=[eq,eW].indexOf(a)>=0?"height":"width";if(o&&s){var u,d=ty("number"!=typeof(u="function"==typeof(u=r.padding)?u(Object.assign({},n.rects,{placement:n.placement})):u)?u:tw(u,eV)),h=td(o),f="y"===l?"top":eq,p="y"===l?eF:eW,m=n.rects.reference[c]+n.rects.reference[l]-s[l]-n.rects.popper[c],g=s[l]-n.rects.reference[l],_=t_(o),b=_?"y"===l?_.clientHeight||0:_.clientWidth||0:0,v=d[f],y=b-h[c]-d[p],w=b/2-h[c]/2+(m/2-g/2),E=tv(v,w,y);n.modifiersData[i]=((t={})[l]=E,t.centerOffset=E-w,t)}},effect:function(e){var t=e.state,n=e.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=t.elements.popper.querySelector(i)))&&th(t.elements.popper,i)&&(t.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function tx(e){return e.split("-")[1]}var tC={top:"auto",right:"auto",bottom:"auto",left:"auto"};function tO(e){var t,n=e.popper,i=e.popperRect,r=e.placement,o=e.variation,s=e.offsets,a=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,h=s.x,f=void 0===h?0:h,p=s.y,m=void 0===p?0:p,g="function"==typeof u?u({x:f,y:m}):{x:f,y:m};f=g.x,m=g.y;var _=s.hasOwnProperty("x"),b=s.hasOwnProperty("y"),v=eq,y="top",w=window;if(c){var E=t_(n),x="clientHeight",C="clientWidth";E===e8(n)&&"static"!==tf(E=tp(n)).position&&"absolute"===a&&(x="scrollHeight",C="scrollWidth"),("top"===r||(r===eq||r===eW)&&"end"===o)&&(y=eF,m-=(d&&E===w&&w.visualViewport?w.visualViewport.height:E[x])-i.height,m*=l?1:-1),r!==eq&&("top"!==r&&r!==eF||"end"!==o)||(v=eW,f-=(d&&E===w&&w.visualViewport?w.visualViewport.width:E[C])-i.width,f*=l?1:-1)}var O,A,k,T,S,N,j=Object.assign({position:a},c&&tC),L=!0===u?(O={x:f,y:m},A=e8(n),k=O.x,T=O.y,{x:ta(k*(S=A.devicePixelRatio||1))/S||0,y:ta(T*S)/S||0}):{x:f,y:m};return f=L.x,m=L.y,l?Object.assign({},j,((N={})[y]=b?"0":"",N[v]=_?"0":"",N.transform=1>=(w.devicePixelRatio||1)?"translate("+f+"px, "+m+"px)":"translate3d("+f+"px, "+m+"px, 0)",N)):Object.assign({},j,((t={})[y]=b?m+"px":"",t[v]=_?f+"px":"",t.transform="",t))}let tA={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,i=n.gpuAcceleration,r=n.adaptive,o=n.roundOffsets,s=void 0===o||o,a={placement:tr(t.placement),variation:tx(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===i||i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,tO(Object.assign({},a,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===r||r,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,tO(Object.assign({},a,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var tk={passive:!0};let tT={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,i=e.options,r=i.scroll,o=void 0===r||r,s=i.resize,a=void 0===s||s,l=e8(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(e){e.addEventListener("scroll",n.update,tk)}),a&&l.addEventListener("resize",n.update,tk),function(){o&&c.forEach(function(e){e.removeEventListener("scroll",n.update,tk)}),a&&l.removeEventListener("resize",n.update,tk)}},data:{}};var tS={left:"right",right:"left",bottom:"top",top:"bottom"};function tN(e){return e.replace(/left|right|bottom|top/g,function(e){return tS[e]})}var tj={start:"end",end:"start"};function tL(e){return e.replace(/start|end/g,function(e){return tj[e]})}function tI(e){var t=e8(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function tD(e){return tu(tp(e)).left+tI(e).scrollLeft}function tP(e){var t=tf(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+i)}function tM(e,t){void 0===t&&(t=[]);var n,i=function e(t){return["html","body","#document"].indexOf(e7(t))>=0?t.ownerDocument.body:tt(t)&&tP(t)?t:e(tm(t))}(e),r=i===(null==(n=e.ownerDocument)?void 0:n.body),o=e8(i),s=r?[o].concat(o.visualViewport||[],tP(i)?i:[]):i,a=t.concat(s);return r?a:a.concat(tM(tm(s)))}function tB(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function tR(e,t,n){var i,r,o,s,a,l,c,u,d,h;return t===eU?tB(function(e,t){var n=e8(e),i=tp(e),r=n.visualViewport,o=i.clientWidth,s=i.clientHeight,a=0,l=0;if(r){o=r.width,s=r.height;var c=tc();(c||!c&&"fixed"===t)&&(a=r.offsetLeft,l=r.offsetTop)}return{width:o,height:s,x:a+tD(e),y:l}}(e,n)):te(t)?((i=tu(t,!1,"fixed"===n)).top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i):tB((r=tp(e),s=tp(r),a=tI(r),l=null==(o=r.ownerDocument)?void 0:o.body,c=to(s.scrollWidth,s.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),u=to(s.scrollHeight,s.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),d=-a.scrollLeft+tD(r),h=-a.scrollTop,"rtl"===tf(l||s).direction&&(d+=to(s.clientWidth,l?l.clientWidth:0)-c),{width:c,height:u,x:d,y:h}))}function tH(e){var t,n=e.reference,i=e.element,r=e.placement,o=r?tr(r):null,s=r?tx(r):null,a=n.x+n.width/2-i.width/2,l=n.y+n.height/2-i.height/2;switch(o){case"top":t={x:a,y:n.y-i.height};break;case eF:t={x:a,y:n.y+n.height};break;case eW:t={x:n.x+n.width,y:l};break;case eq:t={x:n.x-i.width,y:l};break;default:t={x:n.x,y:n.y}}var c=o?tb(o):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case eK:t[c]=t[c]-(n[u]/2-i[u]/2);break;case"end":t[c]=t[c]+(n[u]/2-i[u]/2)}}return t}function tF(e,t){void 0===t&&(t={});var n,i,r,o,s,a,l=t,c=l.placement,u=void 0===c?e.placement:c,d=l.strategy,h=void 0===d?e.strategy:d,f=l.boundary,p=void 0===f?eZ:f,m=l.rootBoundary,g=l.elementContext,_=void 0===g?eY:g,b=l.altBoundary,v=l.padding,y=void 0===v?0:v,w=ty("number"!=typeof y?y:tw(y,eV)),E=_===eY?eX:eY,x=e.rects.popper,C=e.elements[void 0!==b&&b?E:_],O=(n=te(C)?C:C.contextElement||tp(e.elements.popper),s=(o=[].concat("clippingParents"===p?(i=tM(tm(n)),te(r=["absolute","fixed"].indexOf(tf(n).position)>=0&&tt(n)?t_(n):n)?i.filter(function(e){return te(e)&&th(e,r)&&"body"!==e7(e)}):[]):[].concat(p),[void 0===m?eU:m]))[0],(a=o.reduce(function(e,t){var i=tR(n,t,h);return e.top=to(i.top,e.top),e.right=ts(i.right,e.right),e.bottom=ts(i.bottom,e.bottom),e.left=to(i.left,e.left),e},tR(n,s,h))).width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a),A=tu(e.elements.reference),k=tH({reference:A,element:x,strategy:"absolute",placement:u}),T=tB(Object.assign({},x,k)),S=_===eY?T:A,N={top:O.top-S.top+w.top,bottom:S.bottom-O.bottom+w.bottom,left:O.left-S.left+w.left,right:S.right-O.right+w.right},j=e.modifiersData.offset;if(_===eY&&j){var L=j[u];Object.keys(N).forEach(function(e){var t=[eW,eF].indexOf(e)>=0?1:-1,n=["top",eF].indexOf(e)>=0?"y":"x";N[e]+=L[n]*t})}return N}let tW={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var r=n.mainAxis,o=void 0===r||r,s=n.altAxis,a=void 0===s||s,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,h=n.altBoundary,f=n.flipVariations,p=void 0===f||f,m=n.allowedAutoPlacements,g=t.options.placement,_=tr(g),b=l||(_!==g&&p?function(e){if(tr(e)===ez)return[];var t=tN(e);return[tL(e),t,tL(t)]}(g):[tN(g)]),v=[g].concat(b).reduce(function(e,n){var i,r,o,s,a,l,h,f,g,_,b,v;return e.concat(tr(n)===ez?(r=(i={placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:p,allowedAutoPlacements:m}).placement,o=i.boundary,s=i.rootBoundary,a=i.padding,l=i.flipVariations,f=void 0===(h=i.allowedAutoPlacements)?eJ:h,0===(b=(_=(g=tx(r))?l?eQ:eQ.filter(function(e){return tx(e)===g}):eV).filter(function(e){return f.indexOf(e)>=0})).length&&(b=_),Object.keys(v=b.reduce(function(e,n){return e[n]=tF(t,{placement:n,boundary:o,rootBoundary:s,padding:a})[tr(n)],e},{})).sort(function(e,t){return v[e]-v[t]})):n)},[]),y=t.rects.reference,w=t.rects.popper,E=new Map,x=!0,C=v[0],O=0;O<v.length;O++){var A=v[O],k=tr(A),T=tx(A)===eK,S=["top",eF].indexOf(k)>=0,N=S?"width":"height",j=tF(t,{placement:A,boundary:u,rootBoundary:d,altBoundary:h,padding:c}),L=S?T?eW:eq:T?eF:"top";y[N]>w[N]&&(L=tN(L));var I=tN(L),D=[];if(o&&D.push(j[k]<=0),a&&D.push(j[L]<=0,j[I]<=0),D.every(function(e){return e})){C=A,x=!1;break}E.set(A,D)}if(x)for(var P=function(e){var t=v.find(function(t){var n=E.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return C=t,"break"},M=p?3:1;M>0&&"break"!==P(M);M--);t.placement!==C&&(t.modifiersData[i]._skip=!0,t.placement=C,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function tq(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function tz(e){return["top",eW,eF,eq].some(function(t){return e[t]>=0})}let tV={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,i=t.rects.reference,r=t.rects.popper,o=t.modifiersData.preventOverflow,s=tF(t,{elementContext:"reference"}),a=tF(t,{altBoundary:!0}),l=tq(s,i),c=tq(a,r,o),u=tz(l),d=tz(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},tK={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,r=n.offset,o=void 0===r?[0,0]:r,s=eJ.reduce(function(e,n){var i,r,s,a,l,c;return e[n]=(i=t.rects,s=[eq,"top"].indexOf(r=tr(n))>=0?-1:1,l=(a="function"==typeof o?o(Object.assign({},i,{placement:n})):o)[0],c=a[1],l=l||0,c=(c||0)*s,[eq,eW].indexOf(r)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),a=s[t.placement],l=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[i]=s}},tZ={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=tH({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},tU={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name,r=n.mainAxis,o=n.altAxis,s=n.boundary,a=n.rootBoundary,l=n.altBoundary,c=n.padding,u=n.tether,d=void 0===u||u,h=n.tetherOffset,f=void 0===h?0:h,p=tF(t,{boundary:s,rootBoundary:a,padding:c,altBoundary:l}),m=tr(t.placement),g=tx(t.placement),_=!g,b=tb(m),v="x"===b?"y":"x",y=t.modifiersData.popperOffsets,w=t.rects.reference,E=t.rects.popper,x="function"==typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,C="number"==typeof x?{mainAxis:x,altAxis:x}:Object.assign({mainAxis:0,altAxis:0},x),O=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,A={x:0,y:0};if(y){if(void 0===r||r){var k,T="y"===b?"top":eq,S="y"===b?eF:eW,N="y"===b?"height":"width",j=y[b],L=j+p[T],I=j-p[S],D=d?-E[N]/2:0,P=g===eK?w[N]:E[N],M=g===eK?-E[N]:-w[N],B=t.elements.arrow,R=d&&B?td(B):{width:0,height:0},H=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},F=H[T],W=H[S],q=tv(0,w[N],R[N]),z=_?w[N]/2-D-q-F-C.mainAxis:P-q-F-C.mainAxis,V=_?-w[N]/2+D+q+W+C.mainAxis:M+q+W+C.mainAxis,K=t.elements.arrow&&t_(t.elements.arrow),Z=K?"y"===b?K.clientTop||0:K.clientLeft||0:0,U=null!=(k=null==O?void 0:O[b])?k:0,Y=tv(d?ts(L,j+z-U-Z):L,j,d?to(I,j+V-U):I);y[b]=Y,A[b]=Y-j}if(void 0!==o&&o){var X,Q,J="x"===b?"top":eq,$="x"===b?eF:eW,G=y[v],ee="y"===v?"height":"width",et=G+p[J],en=G-p[$],ei=-1!==["top",eq].indexOf(m),er=null!=(Q=null==O?void 0:O[v])?Q:0,eo=ei?et:G-w[ee]-E[ee]-er+C.altAxis,es=ei?G+w[ee]+E[ee]-er-C.altAxis:en,ea=d&&ei?(X=tv(eo,G,es))>es?es:X:tv(d?eo:et,G,d?es:en);y[v]=ea,A[v]=ea-G}t.modifiersData[i]=A}},requiresIfExists:["offset"]};var tY={placement:"bottom",modifiers:[],strategy:"absolute"};function tX(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function tQ(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,i=void 0===n?[]:n,r=t.defaultOptions,o=void 0===r?tY:r;return function(e,t,n){void 0===n&&(n=o);var r,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},tY,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},a=[],l=!1,c={state:s,setOptions:function(n){var r="function"==typeof n?n(s.options):n;u(),s.options=Object.assign({},o,s.options,r),s.scrollParents={reference:te(e)?tM(e):e.contextElement?tM(e.contextElement):[],popper:tM(t)};var l,d,h,f,p,m=(l=Object.keys(p=[].concat(i,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return p[e]}),d=new Map,h=new Set,f=[],l.forEach(function(e){d.set(e.name,e)}),l.forEach(function(e){h.has(e.name)||function e(t){h.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!h.has(t)){var n=d.get(t);n&&e(n)}}),f.push(t)}(e)}),e9.reduce(function(e,t){return e.concat(f.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=m.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,i=e.effect;if("function"==typeof i){var r=i({state:s,name:t,instance:c,options:void 0===n?{}:n});a.push(r||function(){})}}),c.update()},forceUpdate:function(){if(!l){var e=s.elements,t=e.reference,n=e.popper;if(tX(t,n)){s.rects={reference:(i=t_(n),r="fixed"===s.options.strategy,d=tt(i),h=tt(i)&&(a=ta((o=i.getBoundingClientRect()).width)/i.offsetWidth||1,u=ta(o.height)/i.offsetHeight||1,1!==a||1!==u),f=tp(i),p=tu(t,h,r),m={scrollLeft:0,scrollTop:0},g={x:0,y:0},(d||!d&&!r)&&(("body"!==e7(i)||tP(f))&&(m=i!==e8(i)&&tt(i)?{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}:tI(i)),tt(i)?((g=tu(i,!0)).x+=i.clientLeft,g.y+=i.clientTop):f&&(g.x=tD(f))),{x:p.left+m.scrollLeft-g.x,y:p.top+m.scrollTop-g.y,width:p.width,height:p.height}),popper:td(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var i,r,o,a,u,d,h,f,p,m,g,_=0;_<s.orderedModifiers.length;_++)if(!0!==s.reset){var b=s.orderedModifiers[_],v=b.fn,y=b.options,w=void 0===y?{}:y,E=b.name;"function"==typeof v&&(s=v({state:s,options:w,name:E,instance:c})||s)}else s.reset=!1,_=-1}}},update:function(){return r||(r=new Promise(function(e){Promise.resolve().then(function(){r=void 0,e(new Promise(function(e){c.forceUpdate(),e(s)}))})})),r},destroy:function(){u(),l=!0}};if(!tX(e,t))return c;function u(){a.forEach(function(e){return e()}),a=[]}return c.setOptions(n).then(function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)}),c}}var tJ=tQ(),t$=tQ({defaultModifiers:[tT,tZ,tA,ti]}),tG=tQ({defaultModifiers:[tT,tZ,tA,ti,tK,tW,tU,tE,tV]});let t0=Object.freeze(Object.defineProperty({__proto__:null,afterMain:e5,afterRead:e0,afterWrite:e4,applyStyles:ti,arrow:tE,auto:ez,basePlacements:eV,beforeMain:e1,beforeRead:e$,beforeWrite:e3,bottom:eF,clippingParents:eZ,computeStyles:tA,createPopper:tG,createPopperBase:tJ,createPopperLite:t$,detectOverflow:tF,end:"end",eventListeners:tT,flip:tW,hide:tV,left:eq,main:e2,modifierPhases:e9,offset:tK,placements:eJ,popper:eY,popperGenerator:tQ,popperOffsets:tZ,preventOverflow:tU,read:eG,reference:eX,right:eW,start:eK,top:"top",variationPlacements:eQ,viewport:eU,write:e6},Symbol.toStringTag,{value:"Module"})),t1="dropdown",t2=".bs.dropdown",t5=".data-api",t3="ArrowDown",t6="hide".concat(t2),t4="hidden".concat(t2),t9="show".concat(t2),t7="shown".concat(t2),t8="click".concat(t2).concat(t5),ne="keydown".concat(t2).concat(t5),nt="keyup".concat(t2).concat(t5),nn="show",ni='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',nr="".concat(ni,".").concat(nn),no=".dropdown-menu",ns=p()?"top-end":"top-start",na=p()?"top-start":"top-end",nl=p()?"bottom-end":"bottom-start",nc=p()?"bottom-start":"bottom-end",nu=p()?"left-start":"right-start",nd=p()?"right-start":"left-start",nh={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},nf={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class np extends H{static get Default(){return nh}static get DefaultType(){return nf}static get NAME(){return t1}toggle(){return this._isShown()?this.hide():this.show()}show(){if(l(this._element)||this._isShown())return;let e={relatedTarget:this._element};if(!I.trigger(this._element,t9,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(let e of[].concat(...document.body.children))I.on(e,"mouseover",u);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(nn),this._element.classList.add(nn),I.trigger(this._element,t7,e)}}hide(){if(l(this._element)||!this._isShown())return;let e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!I.trigger(this._element,t6,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.off(e,"mouseover",u);this._popper&&this._popper.destroy(),this._menu.classList.remove(nn),this._element.classList.remove(nn),this._element.setAttribute("aria-expanded","false"),B.removeDataAttribute(this._menu,"popper"),I.trigger(this._element,t4,e)}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!o(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw TypeError("".concat(t1.toUpperCase(),': Option "reference" provided type "object" without a required "getBoundingClientRect" method.'));return e}_createPopper(){if(void 0===t0)throw TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=this._parent:o(this._config.reference)?e=s(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);let t=this._getPopperConfig();this._popper=tG(e,this._menu,t)}_isShown(){return this._menu.classList.contains(nn)}_getPlacement(){let e=this._parent;if(e.classList.contains("dropend"))return nu;if(e.classList.contains("dropstart"))return nd;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";let t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?na:ns:t?nc:nl}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){let e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(B.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...g(this._config.popperConfig,[e])}}_selectMenuItem(e){let{key:t,target:n}=e,i=W.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>a(e));i.length&&b(i,n,t===t3,!i.includes(n)).focus()}static jQueryInterface(e){return this.each(function(){let t=np.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError('No method named "'.concat(e,'"'));t[e]()}})}static clearMenus(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key))for(let t of W.find(nr)){let n=np.getInstance(t);if(!n||!1===n._config.autoClose)continue;let i=e.composedPath(),r=i.includes(n._menu);if(i.includes(n._element)||"inside"===n._config.autoClose&&!r||"outside"===n._config.autoClose&&r||n._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;let o={relatedTarget:n._element};"click"===e.type&&(o.clickEvent=e),n._completeHide(o)}}static dataApiKeydownHandler(e){let t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,i=["ArrowUp",t3].includes(e.key);if(!i&&!n||t&&!n)return;e.preventDefault();let r=this.matches(ni)?this:W.prev(this,ni)[0]||W.next(this,ni)[0]||W.findOne(ni,e.delegateTarget.parentNode),o=np.getOrCreateInstance(r);if(i)return e.stopPropagation(),o.show(),void o._selectMenuItem(e);o._isShown()&&(e.stopPropagation(),o.hide(),r.focus())}constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=W.next(this._element,no)[0]||W.prev(this._element,no)[0]||W.findOne(no,this._parent),this._inNavbar=this._detectNavbar()}}I.on(document,ne,ni,np.dataApiKeydownHandler),I.on(document,ne,no,np.dataApiKeydownHandler),I.on(document,t8,np.clearMenus),I.on(document,nt,np.clearMenus),I.on(document,t8,ni,function(e){e.preventDefault(),np.getOrCreateInstance(this).toggle()}),m(np);let nm="backdrop",ng="show",n_="mousedown.bs.".concat(nm),nb={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},nv={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ny extends R{static get Default(){return nb}static get DefaultType(){return nv}static get NAME(){return nm}show(e){if(!this._config.isVisible)return void g(e);this._append();let t=this._getElement();this._config.isAnimated&&d(t),t.classList.add(ng),this._emulateAnimation(()=>{g(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(ng),this._emulateAnimation(()=>{this.dispose(),g(e)})):g(e)}dispose(){this._isAppended&&(I.off(this._element,n_),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=s(e.rootElement),e}_append(){if(this._isAppended)return;let e=this._getElement();this._config.rootElement.append(e),I.on(e,n_,()=>{g(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){_(e,this._getElement(),this._config.isAnimated)}constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}}let nw=".bs.focustrap",nE="focusin".concat(nw),nx="keydown.tab".concat(nw),nC="backward",nO={autofocus:!0,trapElement:null},nA={autofocus:"boolean",trapElement:"element"};class nk extends R{static get Default(){return nO}static get DefaultType(){return nA}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),I.off(document,nw),I.on(document,nE,e=>this._handleFocusin(e)),I.on(document,nx,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,I.off(document,nw))}_handleFocusin(e){let{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;let n=W.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===nC?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?nC:"forward")}constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}}let nT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",nS=".sticky-top",nN="padding-right",nj="margin-right";class nL{getWidth(){let e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,nN,t=>t+e),this._setElementAttributes(nT,nN,t=>t+e),this._setElementAttributes(nS,nj,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,nN),this._resetElementAttributes(nT,nN),this._resetElementAttributes(nS,nj)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){let i=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+i)return;this._saveInitialAttribute(e,t);let r=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,"".concat(n(Number.parseFloat(r)),"px"))})}_saveInitialAttribute(e,t){let n=e.style.getPropertyValue(t);n&&B.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{let n=B.getDataAttribute(e,t);null!==n?(B.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(o(e))t(e);else for(let n of W.find(e,this._element))t(n)}constructor(){this._element=document.body}}let nI=".bs.modal",nD="hide".concat(nI),nP="hidePrevented".concat(nI),nM="hidden".concat(nI),nB="show".concat(nI),nR="shown".concat(nI),nH="resize".concat(nI),nF="click.dismiss".concat(nI),nW="mousedown.dismiss".concat(nI),nq="keydown.dismiss".concat(nI),nz="click".concat(nI,".data-api"),nV="modal-open",nK="show",nZ="modal-static",nU={backdrop:!0,focus:!0,keyboard:!0},nY={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class nX extends H{static get Default(){return nU}static get DefaultType(){return nY}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||I.trigger(this._element,nB,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(nV),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(I.trigger(this._element,nD).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(nK),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){I.off(window,nI),I.off(this._dialog,nI),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ny({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new nk({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let t=W.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),d(this._element),this._element.classList.add(nK),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,I.trigger(this._element,nR,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){I.on(this._element,nq,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),I.on(window,nH,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),I.on(this._element,nW,e=>{I.one(this._element,nF,t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(nV),this._resetAdjustments(),this._scrollBar.reset(),I.trigger(this._element,nM)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(I.trigger(this._element,nP).defaultPrevented)return;let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(nZ)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(nZ),this._queueCallback(()=>{this._element.classList.remove(nZ),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){let e=p()?"paddingLeft":"paddingRight";this._element.style[e]="".concat(t,"px")}if(!n&&e){let e=p()?"paddingRight":"paddingLeft";this._element.style[e]="".concat(t,"px")}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){let n=nX.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw TypeError('No method named "'.concat(e,'"'));n[e](t)}})}constructor(e,t){super(e,t),this._dialog=W.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new nL,this._addEventListeners()}}I.on(document,nz,'[data-bs-toggle="modal"]',function(e){let t=W.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),I.one(t,nB,e=>{e.defaultPrevented||I.one(t,nM,()=>{a(this)&&this.focus()})});let n=W.findOne(".modal.show");n&&nX.getInstance(n).hide(),nX.getOrCreateInstance(t).toggle(this)}),q(nX),m(nX);let nQ=".bs.offcanvas",nJ=".data-api",n$="load".concat(nQ).concat(nJ),nG="show",n0="showing",n1="hiding",n2=".offcanvas.show",n5="show".concat(nQ),n3="shown".concat(nQ),n6="hide".concat(nQ),n4="hidePrevented".concat(nQ),n9="hidden".concat(nQ),n7="click".concat(nQ).concat(nJ),n8="keydown.dismiss".concat(nQ),ie={backdrop:!0,keyboard:!0,scroll:!1},it={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ii extends H{static get Default(){return ie}static get DefaultType(){return it}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||I.trigger(this._element,n5,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new nL).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(n0),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(nG),this._element.classList.remove(n0),I.trigger(this._element,n3,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&(I.trigger(this._element,n6).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(n1),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(nG,n1),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new nL).reset(),I.trigger(this._element,n9)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let e=!!this._config.backdrop;return new ny({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():I.trigger(this._element,n4)}:null})}_initializeFocusTrap(){return new nk({trapElement:this._element})}_addEventListeners(){I.on(this._element,n8,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():I.trigger(this._element,n4))})}static jQueryInterface(e){return this.each(function(){let t=ii.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError('No method named "'.concat(e,'"'));t[e](this)}})}constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}}I.on(document,n7,'[data-bs-toggle="offcanvas"]',function(e){let t=W.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),l(this))return;I.one(t,n9,()=>{a(this)&&this.focus()});let n=W.findOne(n2);n&&n!==t&&ii.getInstance(n).hide(),ii.getOrCreateInstance(t).toggle(this)}),I.on(window,n$,()=>{for(let e of W.find(n2))ii.getOrCreateInstance(e).show()}),I.on(window,"resize".concat(nQ),()=>{for(let e of W.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&ii.getOrCreateInstance(e).hide()}),q(ii),m(ii);let ir={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},io=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),is=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ia=(e,t)=>{let n=e.nodeName.toLowerCase();return t.includes(n)?!io.has(n)||!!is.test(e.nodeValue):t.filter(e=>e instanceof RegExp).some(e=>e.test(n))},il={allowList:ir,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ic={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},iu={entry:"(string|element|function|null)",selector:"(string|element)"};class id extends R{static get Default(){return il}static get DefaultType(){return ic}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){let e=document.createElement("div");for(let[t,n]of(e.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(e,n,t);let t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(let[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},iu)}_setContent(e,t,n){let i=W.findOne(n,e);i&&((t=this._resolvePossibleFunction(t))?o(t)?this._putElementInTemplate(s(t),i):this._config.html?i.innerHTML=this._maybeSanitize(t):i.textContent=t:i.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);let i=(new window.DOMParser).parseFromString(e,"text/html");for(let e of[].concat(...i.body.querySelectorAll("*"))){let n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}let i=[].concat(...e.attributes),r=[].concat(t["*"]||[],t[n]||[]);for(let t of i)ia(t,r)||e.removeAttribute(t.nodeName)}return i.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return g(e,[this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}constructor(e){super(),this._config=this._getConfig(e)}}let ih=new Set(["sanitize","allowList","sanitizeFn"]),ip="fade",im="show",ig=".modal",i_="hide.bs.modal",ib="hover",iv="focus",iy={AUTO:"auto",TOP:"top",RIGHT:p()?"left":"right",BOTTOM:"bottom",LEFT:p()?"right":"left"},iw={allowList:ir,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},iE={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ix extends H{static get Default(){return iw}static get DefaultType(){return iE}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),I.off(this._element.closest(ig),i_,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;let e=I.trigger(this._element,this.constructor.eventName("show")),t=(c(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();let n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));let{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(n),I.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(im),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.on(e,"mouseover",u);this._queueCallback(()=>{I.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!I.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(im),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))I.off(e,"mouseover",u);this._activeTrigger.click=!1,this._activeTrigger[iv]=!1,this._activeTrigger[ib]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),I.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){let t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ip,im),t.classList.add("bs-".concat(this.constructor.NAME,"-auto"));let n=(e=>{do e+=Math.floor(1e6*Math.random());while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(ip),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new id({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ip)}_isShown(){return this.tip&&this.tip.classList.contains(im)}_createPopper(e){let t=iy[g(this._config.placement,[this,e,this._element]).toUpperCase()];return tG(this._element,e,this._getPopperConfig(t))}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return g(e,[this._element])}_getPopperConfig(e){let t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:".".concat(this.constructor.NAME,"-arrow")}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...g(this._config.popperConfig,[t])}}_setListeners(){for(let e of this._config.trigger.split(" "))if("click"===e)I.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{this._initializeOnDelegatedTarget(e).toggle()});else if("manual"!==e){let t=e===ib?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=e===ib?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");I.on(this._element,t,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?iv:ib]=!0,t._enter()}),I.on(this._element,n,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?iv:ib]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},I.on(this._element.closest(ig),i_,this._hideModalHandler)}_fixTitle(){let e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){let t=B.getDataAttributes(this._element);for(let e of Object.keys(t))ih.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:s(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){let e={};for(let[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){let t=ix.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError('No method named "'.concat(e,'"'));t[e]()}})}constructor(e,t){if(void 0===t0)throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}}m(ix);let iC={...ix.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},iO={...ix.DefaultType,content:"(null|string|element|function)"};class iA extends ix{static get Default(){return iC}static get DefaultType(){return iO}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){let t=iA.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError('No method named "'.concat(e,'"'));t[e]()}})}}m(iA);let ik=".bs.scrollspy",iT="activate".concat(ik),iS="click".concat(ik),iN="active",ij="[href]",iL=".nav-link",iI="".concat(iL,", .nav-item > ").concat(iL,", .list-group-item"),iD={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},iP={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class iM extends H{static get Default(){return iD}static get DefaultType(){return iP}static get NAME(){return"scrollspy"}refresh(){for(let e of(this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver(),this._observableSections.values()))this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=s(e.target)||document.body,e.rootMargin=e.offset?"".concat(e.offset,"px 0px -30%"):e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(I.off(this._config.target,iS),I.on(this._config.target,iS,ij,e=>{let t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();let n=this._rootElement||window,i=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:i,behavior:"smooth"});n.scrollTop=i}}))}_getNewObserver(){let e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),e)}_observerCallback(e){let t=e=>this._targetLinks.get("#".concat(e.target.id)),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},i=(this._rootElement||document.documentElement).scrollTop,r=i>=this._previousScrollData.parentScrollTop;for(let o of(this._previousScrollData.parentScrollTop=i,e)){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(o));continue}let e=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(r&&e){if(n(o),!i)return}else r||e||n(o)}}_initializeTargetsAndObservables(){for(let e of(this._targetLinks=new Map,this._observableSections=new Map,W.find(ij,this._config.target))){if(!e.hash||l(e))continue;let t=W.findOne(decodeURI(e.hash),this._element);a(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(iN),this._activateParents(e),I.trigger(this._element,iT,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))W.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(iN);else for(let t of W.parents(e,".nav, .list-group"))for(let e of W.prev(t,iI))e.classList.add(iN)}_clearActiveClass(e){for(let t of(e.classList.remove(iN),W.find("".concat(ij,".").concat(iN),e)))t.classList.remove(iN)}static jQueryInterface(e){return this.each(function(){let t=iM.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError('No method named "'.concat(e,'"'));t[e]()}})}constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}}I.on(window,"load".concat(ik,".data-api"),()=>{for(let e of W.find('[data-bs-spy="scroll"]'))iM.getOrCreateInstance(e)}),m(iM);let iB=".bs.tab",iR="hide".concat(iB),iH="hidden".concat(iB),iF="show".concat(iB),iW="shown".concat(iB),iq="click".concat(iB),iz="keydown".concat(iB),iV="load".concat(iB),iK="ArrowRight",iZ="ArrowDown",iU="Home",iY="active",iX="fade",iQ="show",iJ=".dropdown-toggle",i$=":not(".concat(iJ,")"),iG='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',i0=".nav-link".concat(i$,", .list-group-item").concat(i$,', [role="tab"]').concat(i$,", ").concat(iG),i1=".".concat(iY,'[data-bs-toggle="tab"], .').concat(iY,'[data-bs-toggle="pill"], .').concat(iY,'[data-bs-toggle="list"]');class i2 extends H{static get NAME(){return"tab"}show(){let e=this._element;if(this._elemIsActive(e))return;let t=this._getActiveElem(),n=t?I.trigger(t,iR,{relatedTarget:e}):null;I.trigger(e,iF,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(iY),this._activate(W.getElementFromSelector(e)),this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),I.trigger(e,iW,{relatedTarget:t})):e.classList.add(iQ)},e,e.classList.contains(iX)))}_deactivate(e,t){e&&(e.classList.remove(iY),e.blur(),this._deactivate(W.getElementFromSelector(e)),this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),I.trigger(e,iH,{relatedTarget:t})):e.classList.remove(iQ)},e,e.classList.contains(iX)))}_keydown(e){let t;if(!["ArrowLeft",iK,"ArrowUp",iZ,iU,"End"].includes(e.key))return;e.stopPropagation(),e.preventDefault();let n=this._getChildren().filter(e=>!l(e));if([iU,"End"].includes(e.key))t=n[e.key===iU?0:n.length-1];else{let i=[iK,iZ].includes(e.key);t=b(n,e.target,i,!0)}t&&(t.focus({preventScroll:!0}),i2.getOrCreateInstance(t).show())}_getChildren(){return W.find(i0,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){for(let n of(this._setAttributeIfNotExists(e,"role","tablist"),t))this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);let t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){let t=W.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby","".concat(e.id)))}_toggleDropDown(e,t){let n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;let i=(e,i)=>{let r=W.findOne(e,n);r&&r.classList.toggle(i,t)};i(iJ,iY),i(".dropdown-menu",iQ),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(iY)}_getInnerElement(e){return e.matches(i0)?e:W.findOne(i0,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){let t=i2.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError('No method named "'.concat(e,'"'));t[e]()}})}constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),I.on(this._element,iz,e=>this._keydown(e)))}}I.on(document,iq,iG,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),l(this)||i2.getOrCreateInstance(this).show()}),I.on(window,iV,()=>{for(let e of W.find(i1))i2.getOrCreateInstance(e)}),m(i2);let i5=".bs.toast",i3="mouseover".concat(i5),i6="mouseout".concat(i5),i4="focusin".concat(i5),i9="focusout".concat(i5),i7="hide".concat(i5),i8="hidden".concat(i5),re="show".concat(i5),rt="shown".concat(i5),rn="hide",ri="show",rr="showing",ro={animation:"boolean",autohide:"boolean",delay:"number"},rs={animation:!0,autohide:!0,delay:5e3};class ra extends H{static get Default(){return rs}static get DefaultType(){return ro}static get NAME(){return"toast"}show(){I.trigger(this._element,re).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(rn),d(this._element),this._element.classList.add(ri,rr),this._queueCallback(()=>{this._element.classList.remove(rr),I.trigger(this._element,rt),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(I.trigger(this._element,i7).defaultPrevented||(this._element.classList.add(rr),this._queueCallback(()=>{this._element.classList.add(rn),this._element.classList.remove(rr,ri),I.trigger(this._element,i8)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(ri),super.dispose()}isShown(){return this._element.classList.contains(ri)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();let n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){I.on(this._element,i3,e=>this._onInteraction(e,!0)),I.on(this._element,i6,e=>this._onInteraction(e,!1)),I.on(this._element,i4,e=>this._onInteraction(e,!0)),I.on(this._element,i9,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){let t=ra.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError('No method named "'.concat(e,'"'));t[e](this)}})}constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}}return q(ra),m(ra),{Alert:Z,Button:Y,Carousel:eA,Collapse:eH,Dropdown:np,Modal:nX,Offcanvas:ii,Popover:iA,ScrollSpy:iM,Tab:i2,Toast:ra,Tooltip:ix}}()},94825:function(e,t,n){"use strict";var i,r,o;void 0!==(r="function"==typeof(i=o=function(){function e(){for(var e=0,t={};e<arguments.length;e++){var n=arguments[e];for(var i in n)t[i]=n[i]}return t}function t(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}return function n(i){function r(){}function o(t,n,o){if("undefined"!=typeof document){"number"==typeof(o=e({path:"/"},r.defaults,o)).expires&&(o.expires=new Date(new Date*1+864e5*o.expires)),o.expires=o.expires?o.expires.toUTCString():"";try{var s=JSON.stringify(n);/^[\{\[]/.test(s)&&(n=s)}catch(e){}n=i.write?i.write(n,t):encodeURIComponent(String(n)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=encodeURIComponent(String(t)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var a="";for(var l in o)o[l]&&(a+="; "+l,!0!==o[l]&&(a+="="+o[l].split(";")[0]));return document.cookie=t+"="+n+a}}function s(e,n){if("undefined"!=typeof document){for(var r={},o=document.cookie?document.cookie.split("; "):[],s=0;s<o.length;s++){var a=o[s].split("="),l=a.slice(1).join("=");n||'"'!==l.charAt(0)||(l=l.slice(1,-1));try{var c=t(a[0]);if(l=(i.read||i)(l,c)||t(l),n)try{l=JSON.parse(l)}catch(e){}if(r[c]=l,e===c)break}catch(e){}}return e?r[e]:r}}return r.set=o,r.get=function(e){return s(e,!1)},r.getJSON=function(e){return s(e,!0)},r.remove=function(t,n){o(t,"",e(n,{expires:-1}))},r.defaults={},r.withConverter=n,r}(function(){})})?i.call(t,n,t,e):i)&&(e.exports=r),e.exports=o()},13313:function(e,t){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return i},isEqualNode:function(){return o},default:function(){return s}});let i={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function r(e){let{type:t,props:n}=e,r=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let o=i[e]||e.toLowerCase();"script"===t&&("async"===o||"defer"===o||"noModule"===o)?r[o]=!!n[e]:r.setAttribute(o,n[e])}let{children:o,dangerouslySetInnerHTML:s}=n;return s?r.innerHTML=s.__html||"":o&&(r.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),r}function o(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let i=t.cloneNode(!0);return i.setAttribute("nonce",""),i.nonce=n,n===e.nonce&&e.isEqualNode(i)}}return e.isEqualNode(t)}function s(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let i=t.title?t.title[0]:null,r="";if(i){let{children:e}=i.props;r="string"==typeof e?e:Array.isArray(e)?e.join(""):""}r!==document.title&&(document.title=r),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],i=n.querySelector("meta[name=next-head-count]"),s=Number(i.content),a=[];for(let t=0,n=i.previousElementSibling;t<s;t++,n=(null==n?void 0:n.previousElementSibling)||null){var l;(null==n?void 0:null==(l=n.tagName)?void 0:l.toLowerCase())===e&&a.push(n)}let c=t.map(r).filter(e=>{for(let t=0,n=a.length;t<n;t++)if(o(a[t],e))return a.splice(t,1),!1;return!0});a.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),c.forEach(e=>n.insertBefore(e,i)),i.content=(s-a.length+c.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85935:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleClientScriptLoad:function(){return g},initScriptLoader:function(){return _},default:function(){return v}});let i=n(86921),r=n(91884),o=n(57437),s=i._(n(54887)),a=r._(n(2265)),l=n(27484),c=n(13313),u=n(52185),d=new Map,h=new Set,f=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],p=e=>{if(s.default.preinit){e.forEach(e=>{s.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},m=e=>{let{src:t,id:n,onLoad:i=()=>{},onReady:r=null,dangerouslySetInnerHTML:o,children:s="",strategy:a="afterInteractive",onError:l,stylesheets:u}=e,m=n||t;if(m&&h.has(m))return;if(d.has(t)){h.add(m),d.get(t).then(i,l);return}let g=()=>{r&&r(),h.add(m)},_=document.createElement("script"),b=new Promise((e,t)=>{_.addEventListener("load",function(t){e(),i&&i.call(this,t),g()}),_.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});for(let[n,i]of(o?(_.innerHTML=o.__html||"",g()):s?(_.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",g()):t&&(_.src=t,d.set(t,b)),Object.entries(e))){if(void 0===i||f.includes(n))continue;let e=c.DOMAttributeNames[n]||n.toLowerCase();_.setAttribute(e,i)}"worker"===a&&_.setAttribute("type","text/partytown"),_.setAttribute("data-nscript",a),u&&p(u),document.body.appendChild(_)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>m(e))}):m(e)}function _(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function b(e){let{id:t,src:n="",onLoad:i=()=>{},onReady:r=null,strategy:c="afterInteractive",onError:d,stylesheets:f,...p}=e,{updateScripts:g,scripts:_,getIsSsr:b,appDir:v,nonce:y}=(0,a.useContext)(l.HeadManagerContext),w=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=t||n;w.current||(r&&e&&h.has(e)&&r(),w.current=!0)},[r,t,n]);let E=(0,a.useRef)(!1);if((0,a.useEffect)(()=>{!E.current&&("afterInteractive"===c?m(e):"lazyOnload"===c&&("complete"===document.readyState?(0,u.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>m(e))})),E.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(g?(_[c]=(_[c]||[]).concat([{id:t,src:n,onLoad:i,onReady:r,onError:d,...p}]),g(_)):b&&b()?h.add(t||n):b&&!b()&&m(e)),v){if(f&&f.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)return n?(s.default.preload(n,p.integrity?{as:"script",integrity:p.integrity}:{as:"script"}),(0,o.jsx)("script",{nonce:y,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...p,id:t}])+")"}})):(p.dangerouslySetInnerHTML&&(p.children=p.dangerouslySetInnerHTML.__html,delete p.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:y,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...p,id:t}])+")"}}));"afterInteractive"===c&&n&&s.default.preload(n,p.integrity?{as:"script",integrity:p.integrity}:{as:"script"})}return null}Object.defineProperty(b,"__nextScript",{value:!0});let v=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19721:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}});let i=n(99775);function r(e){let{reason:t,children:n}=e;if("undefined"==typeof window)throw new i.BailoutToCSRError(t);return n}},31680:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.default)(function(){for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];var r=null;return t.forEach(function(e){if(null==r){var t=e.apply(void 0,n);null!=t&&(r=t)}}),r})};var i,r=(i=n(64704))&&i.__esModule?i:{default:i};e.exports=t.default},64704:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(t,n,i,r,o,s){var a=r||"<<anonymous>>",l=s||i;if(null==n[i])return t?Error("Required "+o+" `"+l+"` was not specified in `"+a+"`."):null;for(var c=arguments.length,u=Array(c>6?c-6:0),d=6;d<c;d++)u[d-6]=arguments[d];return e.apply(void 0,[n,i,a,o,l].concat(u))}var n=t.bind(null,!1);return n.isRequired=t.bind(null,!0),n},e.exports=t.default},92201:function(e,t,n){"use strict";n.d(t,{Z:function(){return D}});var i=n(16480),r=n.n(i);n(31680);var o=n(2265),s=n(84127),a=n(96623),l=n(1564);let c=o.createContext(null);c.displayName="NavContext";var u=n(5450);let d=o.createContext(null);var h=n(24209),f=n(45832),p=n(57437);let m=["as","disabled"];function g(e){let{tagName:t,disabled:n,href:i,target:r,rel:o,role:s,onClick:a,tabIndex:l=0,type:c}=e;t||(t=null!=i||null!=r||null!=o?"a":"button");let u={tagName:t};if("button"===t)return[{type:c||"button",disabled:n},u];let d=e=>{var r;if(!n&&("a"!==t||(r=i)&&"#"!==r.trim())||e.preventDefault(),n){e.stopPropagation();return}null==a||a(e)};return"a"===t&&(i||(i="#"),n&&(i=void 0)),[{role:null!=s?s:"button",disabled:void 0,tabIndex:n?void 0:l,href:i,target:"a"===t?r:void 0,"aria-disabled":n||void 0,rel:"a"===t?o:void 0,onClick:d,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),d(e))}},u]}let _=o.forwardRef((e,t)=>{let{as:n,disabled:i}=e,r=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,m),[o,{tagName:s}]=g(Object.assign({tagName:n,disabled:i},r));return(0,p.jsx)(s,Object.assign({},r,o,{ref:t}))});_.displayName="Button";let b=["as","active","eventKey"];function v(e){let{key:t,onClick:n,active:i,id:r,role:s,disabled:a}=e,l=(0,o.useContext)(u.Z),p=(0,o.useContext)(c),m=(0,o.useContext)(d),g=i,_={role:s};if(p){s||"tablist"!==p.role||(_.role="tab");let e=p.getControllerId(null!=t?t:null),n=p.getControlledId(null!=t?t:null);_[(0,h.PB)("event-key")]=t,_.id=e||r,((g=null==i&&null!=t?p.activeKey===t:i)||!(null!=m&&m.unmountOnExit)&&!(null!=m&&m.mountOnEnter))&&(_["aria-controls"]=n)}return"tab"===_.role&&(_["aria-selected"]=g,g||(_.tabIndex=-1),a&&(_.tabIndex=-1,_["aria-disabled"]=!0)),_.onClick=(0,f.Z)(e=>{a||(null==n||n(e),null!=t&&l&&!e.isPropagationStopped()&&l(t,e))}),[_,{isActive:g}]}let y=o.forwardRef((e,t)=>{let{as:n=_,active:i,eventKey:r}=e,o=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,b),[s,a]=v(Object.assign({key:(0,u.h)(r,o.href),active:i},o));return s[(0,h.PB)("active")]=a.isActive,(0,p.jsx)(n,Object.assign({},o,s,{ref:t}))});y.displayName="NavItem";let w=["as","onSelect","activeKey","role","onKeyDown"],E=()=>{},x=(0,h.PB)("event-key"),C=o.forwardRef((e,t)=>{let n,i,{as:r="div",onSelect:s,activeKey:f,role:m,onKeyDown:g}=e,_=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,w),b=function(){let[,e]=(0,o.useReducer)(e=>!e,!1);return e}(),v=(0,o.useRef)(!1),y=(0,o.useContext)(u.Z),C=(0,o.useContext)(d);C&&(m=m||"tablist",f=C.activeKey,n=C.getControlledId,i=C.getControllerId);let O=(0,o.useRef)(null),A=e=>{let t=O.current;if(!t)return null;let n=(0,a.Z)(t,"[".concat(x,"]:not([aria-disabled=true])")),i=t.querySelector("[aria-selected=true]");if(!i||i!==document.activeElement)return null;let r=n.indexOf(i);if(-1===r)return null;let o=r+e;return o>=n.length&&(o=0),o<0&&(o=n.length-1),n[o]},k=(e,t)=>{null!=e&&(null==s||s(e,t),null==y||y(e,t))};(0,o.useEffect)(()=>{if(O.current&&v.current){let e=O.current.querySelector("[".concat(x,"][aria-selected=true]"));null==e||e.focus()}v.current=!1});let T=(0,l.Z)(t,O);return(0,p.jsx)(u.Z.Provider,{value:k,children:(0,p.jsx)(c.Provider,{value:{role:m,activeKey:(0,u.h)(f),getControlledId:n||E,getControllerId:i||E},children:(0,p.jsx)(r,Object.assign({},_,{onKeyDown:e=>{let t;if(null==g||g(e),C){switch(e.key){case"ArrowLeft":case"ArrowUp":t=A(-1);break;case"ArrowRight":case"ArrowDown":t=A(1);break;default:return}t&&(e.preventDefault(),k(t.dataset[(0,h.$F)("EventKey")]||null,e),v.current=!0,b())}},ref:T,role:m}))})})});C.displayName="Nav";var O=Object.assign(C,{Item:y}),A=n(12865),k=n(97061);let T=o.createContext(null);T.displayName="CardHeaderContext";let S=o.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:o="div",...s}=e;return i=(0,A.vE)(i,"nav-item"),(0,p.jsx)(o,{ref:t,className:r()(n,i),...s})});S.displayName="NavItem",n(43756),n(72225),n(17481),n(27531),n(93106),new WeakMap;let N=["onKeyDown"],j=o.forwardRef((e,t)=>{var n;let{onKeyDown:i}=e,r=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,N),[o]=g(Object.assign({tagName:"a"},r)),s=(0,f.Z)(e=>{o.onKeyDown(e),null==i||i(e)});return(n=r.href)&&"#"!==n.trim()&&"button"!==r.role?(0,p.jsx)("a",Object.assign({ref:t},r,{onKeyDown:i})):(0,p.jsx)("a",Object.assign({ref:t},r,o,{onKeyDown:s}))});j.displayName="Anchor";let L=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,as:o=j,active:s,eventKey:a,disabled:l=!1,...c}=e;n=(0,A.vE)(n,"nav-link");let[d,h]=v({key:(0,u.h)(a,c.href),active:s,disabled:l,...c});return(0,p.jsx)(o,{...c,...d,ref:t,disabled:l,className:r()(i,n,l&&"disabled",h.isActive&&"active")})});L.displayName="NavLink";let I=o.forwardRef((e,t)=>{let n,i;let{as:a="div",bsPrefix:l,variant:c,fill:u=!1,justify:d=!1,navbar:h,navbarScroll:f,className:m,activeKey:g,..._}=(0,s.Ch)(e,{activeKey:"onSelect"}),b=(0,A.vE)(l,"nav"),v=!1,y=(0,o.useContext)(k.Z),w=(0,o.useContext)(T);return y?(n=y.bsPrefix,v=null==h||h):w&&({cardHeaderBsPrefix:i}=w),(0,p.jsx)(O,{as:a,ref:t,activeKey:g,className:r()(m,{[b]:!v,["".concat(n,"-nav")]:v,["".concat(n,"-nav-scroll")]:v&&f,["".concat(i,"-").concat(c)]:!!i,["".concat(b,"-").concat(c)]:!!c,["".concat(b,"-fill")]:u,["".concat(b,"-justified")]:d}),..._})});I.displayName="Nav";var D=Object.assign(I,{Item:S,Link:L})},7576:function(e,t,n){"use strict";n.d(t,{Z:function(){return W}});var i=n(16480),r=n.n(i),o=n(2265),s=n(5450),a=n(84127),l=n(12865),c=n(57437);let u=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,as:o,...s}=e;n=(0,l.vE)(n,"navbar-brand");let a=o||(s.href?"a":"span");return(0,c.jsx)(a,{...s,ref:t,className:r()(i,n)})});u.displayName="NavbarBrand";var d=n(14728),h=n(97061);let f=o.forwardRef((e,t)=>{let{children:n,bsPrefix:i,...r}=e;i=(0,l.vE)(i,"navbar-collapse");let s=(0,o.useContext)(h.Z);return(0,c.jsx)(d.Z,{in:!!(s&&s.expanded),...r,children:(0,c.jsx)("div",{ref:t,className:i,children:n})})});f.displayName="NavbarCollapse";var p=n(45832);let m=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,children:s,label:a="Toggle navigation",as:u="button",onClick:d,...f}=e;n=(0,l.vE)(n,"navbar-toggler");let{onToggle:m,expanded:g}=(0,o.useContext)(h.Z)||{},_=(0,p.Z)(e=>{d&&d(e),m&&m()});return"button"===u&&(f.type="button"),(0,c.jsx)(u,{...f,ref:t,onClick:_,"aria-label":a,className:r()(i,n,!g&&"collapsed"),children:s||(0,c.jsx)("span",{className:"".concat(n,"-icon")})})});m.displayName="NavbarToggle";var g=n(93106);let _=new WeakMap,b=(e,t)=>{if(!e||!t)return;let n=_.get(t)||new Map;_.set(t,n);let i=n.get(e);return i||((i=t.matchMedia(e)).refCount=0,n.set(i.media,i)),i},v=function(e){let t=Object.keys(e);function n(e,t){return e===t?t:e?"".concat(e," and ").concat(t):t}return function(i,r,s){let a;return"object"==typeof i?(a=i,s=r,r=!0):a={[i]:r=r||!0},function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"==typeof window?void 0:window,n=b(e,t),[i,r]=(0,o.useState)(()=>!!n&&n.matches);return(0,g.Z)(()=>{let n=b(e,t);if(!n)return r(!1);let i=_.get(t),o=()=>{r(n.matches)};return n.refCount++,n.addListener(o),o(),()=>{n.removeListener(o),n.refCount--,n.refCount<=0&&(null==i||i.delete(n.media)),n=void 0}},[e]),i}((0,o.useMemo)(()=>Object.entries(a).reduce((i,r)=>{let[o,s]=r;if("up"===s||!0===s){let t;i=n(i,("number"==typeof(t=e[o])&&(t="".concat(t,"px")),"(min-width: ".concat(t,")")))}if("down"===s||!0===s){let r;i=n(i,(r="number"==typeof(r=e[t[Math.min(t.indexOf(o)+1,t.length-1)]])?"".concat(r-.2,"px"):"calc(".concat(r," - 0.2px)"),"(max-width: ".concat(r,")")))}return i},""),[JSON.stringify(a)]),s)}}({xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400});var y=n(70133),w=n(83534);let E=o.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:o="div",...s}=e;return i=(0,l.vE)(i,"offcanvas-body"),(0,c.jsx)(o,{ref:t,className:r()(n,i),...s})});E.displayName="OffcanvasBody";var x=n(73968),C=n(3179),O=n(12703);let A={[x.d0]:"show",[x.cn]:"show"},k=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,children:s,in:a=!1,mountOnEnter:u=!1,unmountOnExit:d=!1,appear:h=!1,...f}=e;return n=(0,l.vE)(n,"offcanvas"),(0,c.jsx)(O.Z,{ref:t,addEndListener:C.Z,in:a,mountOnEnter:u,unmountOnExit:d,appear:h,...f,childRef:s.ref,children:(e,t)=>o.cloneElement(s,{...t,className:r()(i,s.props.className,(e===x.d0||e===x.Ix)&&"".concat(n,"-toggling"),A[e])})})});k.displayName="OffcanvasToggling";var T=n(14272),S=n(94241);let N=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,closeLabel:o="Close",closeButton:s=!1,...a}=e;return n=(0,l.vE)(n,"offcanvas-header"),(0,c.jsx)(S.Z,{ref:t,...a,className:r()(i,n),closeLabel:o,closeButton:s})});N.displayName="OffcanvasHeader";let j=(0,n(89764).Z)("h5"),L=o.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:o=j,...s}=e;return i=(0,l.vE)(i,"offcanvas-title"),(0,c.jsx)(o,{ref:t,className:r()(n,i),...s})});L.displayName="OffcanvasTitle";var I=n(46579);function D(e){return(0,c.jsx)(k,{...e})}function P(e){return(0,c.jsx)(w.Z,{...e})}let M=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,children:s,"aria-labelledby":a,placement:u="start",responsive:d,show:f=!1,backdrop:m=!0,keyboard:g=!0,scroll:_=!1,onEscapeKeyDown:b,onShow:w,onHide:E,container:x,autoFocus:C=!0,enforceFocus:O=!0,restoreFocus:A=!0,restoreFocusOptions:k,onEntered:S,onExit:N,onExiting:j,onEnter:L,onEntering:M,onExited:B,backdropClassName:R,manager:H,renderStaticNode:F=!1,...W}=e,q=(0,o.useRef)();n=(0,l.vE)(n,"offcanvas");let{onToggle:z}=(0,o.useContext)(h.Z)||{},[V,K]=(0,o.useState)(!1),Z=v(d||"xs","up");(0,o.useEffect)(()=>{K(d?f&&!Z:f)},[f,d,Z]);let U=(0,p.Z)(()=>{null==z||z(),null==E||E()}),Y=(0,o.useMemo)(()=>({onHide:U}),[U]),X=(0,o.useCallback)(e=>(0,c.jsx)("div",{...e,className:r()("".concat(n,"-backdrop"),R)}),[R,n]),Q=e=>(0,c.jsx)("div",{...e,...W,className:r()(i,d?"".concat(n,"-").concat(d):n,"".concat(n,"-").concat(u)),"aria-labelledby":a,children:s});return(0,c.jsxs)(c.Fragment,{children:[!V&&(d||F)&&Q({}),(0,c.jsx)(T.Z.Provider,{value:Y,children:(0,c.jsx)(y.Z,{show:V,ref:t,backdrop:m,container:x,keyboard:g,autoFocus:C,enforceFocus:O&&!_,restoreFocus:A,restoreFocusOptions:k,onEscapeKeyDown:b,onShow:w,onHide:U,onEnter:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];e&&(e.style.visibility="visible"),null==L||L(e,...n)},onEntering:M,onEntered:S,onExit:N,onExiting:j,onExited:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];e&&(e.style.visibility=""),null==B||B(...n)},manager:H||(_?(q.current||(q.current=new I.Z({handleContainerOverflow:!1})),q.current):(0,I.t)()),transition:D,backdropTransition:P,renderBackdrop:X,renderDialog:Q})})]})});M.displayName="Offcanvas";var B=Object.assign(M,{Body:E,Header:N,Title:L});let R=o.forwardRef((e,t)=>{let n=(0,o.useContext)(h.Z);return(0,c.jsx)(B,{ref:t,show:!!(null!=n&&n.expanded),...e,renderStaticNode:!0})});R.displayName="NavbarOffcanvas";let H=o.forwardRef((e,t)=>{let{className:n,bsPrefix:i,as:o="span",...s}=e;return i=(0,l.vE)(i,"navbar-text"),(0,c.jsx)(o,{ref:t,className:r()(n,i),...s})});H.displayName="NavbarText";let F=o.forwardRef((e,t)=>{let{bsPrefix:n,expand:i=!0,variant:u="light",bg:d,fixed:f,sticky:p,className:m,as:g="nav",expanded:_,onToggle:b,onSelect:v,collapseOnSelect:y=!1,...w}=(0,a.Ch)(e,{expanded:"onToggle"}),E=(0,l.vE)(n,"navbar"),x=(0,o.useCallback)(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];null==v||v(...t),y&&_&&(null==b||b(!1))},[v,y,_,b]);void 0===w.role&&"nav"!==g&&(w.role="navigation");let C="".concat(E,"-expand");"string"==typeof i&&(C="".concat(C,"-").concat(i));let O=(0,o.useMemo)(()=>({onToggle:()=>null==b?void 0:b(!_),bsPrefix:E,expanded:!!_,expand:i}),[E,_,i,b]);return(0,c.jsx)(h.Z.Provider,{value:O,children:(0,c.jsx)(s.Z.Provider,{value:x,children:(0,c.jsx)(g,{ref:t,...w,className:r()(m,E,i&&C,u&&"".concat(E,"-").concat(u),d&&"bg-".concat(d),p&&"sticky-".concat(p),f&&"fixed-".concat(f))})})})});F.displayName="Navbar";var W=Object.assign(F,{Brand:u,Collapse:f,Offcanvas:R,Text:H,Toggle:m})},97061:function(e,t,n){"use strict";let i=n(2265).createContext(null);i.displayName="NavbarContext",t.Z=i},55914:function(e,t,n){"use strict";var i=n(16480),r=n.n(i),o=n(2265),s=n(12865),a=n(57437);let l=o.forwardRef((e,t)=>{let{bsPrefix:n,className:i,as:o="div",...l}=e,c=(0,s.vE)(n,"row"),u=(0,s.pi)(),d=(0,s.zG)(),h="".concat(c,"-cols"),f=[];return u.forEach(e=>{let t;let n=l[e];delete l[e],null!=n&&"object"==typeof n?{cols:t}=n:t=n,null!=t&&f.push("".concat(h).concat(e!==d?"-".concat(e):"","-").concat(t))}),(0,a.jsx)(o,{ref:t,...l,className:r()(i,c,...f)})});l.displayName="Row",t.Z=l},91199:function(e,t,n){"use strict";var i,r,o=n(94825),s=n.n(o),a=n(2265),l=function(e){var t=e.condition,n=e.wrapper,i=e.children;return t?n(i):i};function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function u(e,t){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var d={TOP:"top",BOTTOM:"bottom"};(i=r||(r={})).STRICT="strict",i.LAX="lax",i.NONE="none";var h={HIDDEN:"hidden",BY_COOKIE_VALUE:"byCookieValue"},f="CookieConsent",p=["children"],m={disableStyles:!1,hideOnAccept:!0,hideOnDecline:!0,location:d.BOTTOM,visible:h.BY_COOKIE_VALUE,onAccept:function(e){},onDecline:function(){},cookieName:f,cookieValue:"true",declineCookieValue:"false",setDeclineCookie:!0,buttonText:"I understand",declineButtonText:"I decline",debug:!1,expires:365,containerClasses:"CookieConsent",contentClasses:"",buttonClasses:"",buttonWrapperClasses:"",declineButtonClasses:"",buttonId:"rcc-confirm-button",declineButtonId:"rcc-decline-button",extraCookieOptions:{},disableButtonStyles:!1,enableDeclineButton:!1,flipButtons:!1,sameSite:r.LAX,ButtonComponent:function(e){var t=e.children,n=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,p);return a.createElement("button",Object.assign({},n),t)},overlay:!1,overlayClasses:"",onOverlayClick:function(){},acceptOnOverlayClick:!1,ariaAcceptLabel:"Accept cookies",ariaDeclineLabel:"Decline cookies",acceptOnScroll:!1,acceptOnScrollPercentage:25,customContentAttributes:{},customContainerAttributes:{},customButtonProps:{},customDeclineButtonProps:{},customButtonWrapperAttributes:{},style:{},buttonStyle:{},declineButtonStyle:{},contentStyle:{},overlayStyle:{}},g={visible:!1,style:{alignItems:"baseline",background:"#353535",color:"white",display:"flex",flexWrap:"wrap",justifyContent:"space-between",left:"0",position:"fixed",width:"100%",zIndex:"999"},buttonStyle:{background:"#ffd42d",border:"0",borderRadius:"0px",boxShadow:"none",color:"black",cursor:"pointer",flex:"0 0 auto",padding:"5px 10px",margin:"15px"},declineButtonStyle:{background:"#c12a2a",border:"0",borderRadius:"0px",boxShadow:"none",color:"#e5e5e5",cursor:"pointer",flex:"0 0 auto",padding:"5px 10px",margin:"15px"},contentStyle:{flex:"1 0 300px",margin:"15px"},overlayStyle:{position:"fixed",left:0,top:0,width:"100%",height:"100%",zIndex:"999",backgroundColor:"rgba(0,0,0,0.3)"}},_=function(e){void 0===e&&(e=f);var t=s().get(e);return void 0===t?s().get(b(e)):t},b=function(e){return e+"-legacy"},v=function(e){function t(){var t;return t=e.apply(this,arguments)||this,t.state=g,t.handleScroll=function(){var e=c({},m,t.props).acceptOnScrollPercentage,n=document.documentElement,i=document.body,r="scrollTop",o="scrollHeight";(n[r]||i[r])/((n[o]||i[o])-n.clientHeight)*100>e&&t.accept(!0)},t.removeScrollListener=function(){t.props.acceptOnScroll&&window.removeEventListener("scroll",t.handleScroll)},t}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,u(t,e);var n=t.prototype;return n.componentDidMount=function(){var e=this.props.debug;(void 0===this.getCookieValue()||e)&&(this.setState({visible:!0}),this.props.acceptOnScroll&&window.addEventListener("scroll",this.handleScroll,{passive:!0}))},n.componentWillUnmount=function(){this.removeScrollListener()},n.accept=function(e){void 0===e&&(e=!1);var t,n=c({},m,this.props),i=n.cookieName,r=n.cookieValue,o=n.hideOnAccept,s=n.onAccept;this.setCookie(i,r),s(null!=(t=e)&&t),o&&(this.setState({visible:!1}),this.removeScrollListener())},n.overlayClick=function(){var e=c({},m,this.props),t=e.acceptOnOverlayClick,n=e.onOverlayClick;t&&this.accept(),n()},n.decline=function(){var e=c({},m,this.props),t=e.cookieName,n=e.declineCookieValue,i=e.hideOnDecline,r=e.onDecline;e.setDeclineCookie&&this.setCookie(t,n),r(),i&&this.setState({visible:!1})},n.setCookie=function(e,t){var n=this.props,i=n.extraCookieOptions,o=n.expires,a=n.sameSite,l=this.props.cookieSecurity;void 0===l&&(l=!window.location||"https:"===window.location.protocol);var u=c({expires:o},i,{sameSite:a,secure:l});a===r.NONE&&s().set(b(e),t,u),s().set(e,t,u)},n.getCookieValue=function(){return _(this.props.cookieName)},n.render=function(){var e=this;switch(this.props.visible){case h.HIDDEN:return null;case h.BY_COOKIE_VALUE:if(!this.state.visible)return null}var t=this.props,n=t.location,i=t.style,r=t.buttonStyle,o=t.declineButtonStyle,s=t.contentStyle,u=t.disableStyles,f=t.buttonText,p=t.declineButtonText,m=t.containerClasses,g=t.contentClasses,_=t.buttonClasses,b=t.buttonWrapperClasses,v=t.declineButtonClasses,y=t.buttonId,w=t.declineButtonId,E=t.disableButtonStyles,x=t.enableDeclineButton,C=t.flipButtons,O=t.ButtonComponent,A=t.overlay,k=t.overlayClasses,T=t.overlayStyle,S=t.ariaAcceptLabel,N=t.ariaDeclineLabel,j=t.customContainerAttributes,L=t.customContentAttributes,I=t.customButtonProps,D=t.customDeclineButtonProps,P=t.customButtonWrapperAttributes,M={},B={},R={},H={},F={};switch(u?(M=Object.assign({},i),B=Object.assign({},r),R=Object.assign({},o),H=Object.assign({},s),F=Object.assign({},T)):(M=Object.assign({},c({},this.state.style,i)),H=Object.assign({},c({},this.state.contentStyle,s)),F=Object.assign({},c({},this.state.overlayStyle,T)),E?(B=Object.assign({},r),R=Object.assign({},o)):(B=Object.assign({},c({},this.state.buttonStyle,r)),R=Object.assign({},c({},this.state.declineButtonStyle,o)))),n){case d.TOP:M.top="0";break;case d.BOTTOM:M.bottom="0"}var W=[];return x&&W.push(a.createElement(O,Object.assign({key:"declineButton",style:R,className:v,id:w,"aria-label":N,onClick:function(){e.decline()}},D),p)),W.push(a.createElement(O,Object.assign({key:"acceptButton",style:B,className:_,id:y,"aria-label":S,onClick:function(){e.accept()}},I),f)),C&&W.reverse(),a.createElement(l,{condition:A,wrapper:function(t){return a.createElement("div",{style:F,className:k,onClick:function(){e.overlayClick()}},t)}},a.createElement("div",Object.assign({className:""+m,style:M},j),a.createElement("div",Object.assign({style:H,className:g},L),this.props.children),a.createElement("div",Object.assign({className:""+b},P),W.map(function(e){return e}))))},t}(a.Component);v.defaultProps=m,t.ZP=v},4621:function(){},40969:function(e){e.exports={style:{fontFamily:"'__Poppins_9b9fd1', '__Poppins_Fallback_9b9fd1'",fontStyle:"normal"},className:"__className_9b9fd1",variable:"__variable_9b9fd1"}}}]);