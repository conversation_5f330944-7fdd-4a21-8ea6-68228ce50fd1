"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9089],{47907:function(t,n,e){var r=e(15313);e.o(r,"usePathname")&&e.d(n,{usePathname:function(){return r.usePathname}}),e.o(r,"useRouter")&&e.d(n,{useRouter:function(){return r.useRouter}}),e.o(r,"useSearchParams")&&e.d(n,{useSearchParams:function(){return r.useSearchParams}})},64561:function(t,n,e){e.d(n,{Z:function(){return O}});var r,i=e(16480),o=e.n(i),u=e(59390),a=e(97550),c=e(69275),f=e(44990);function l(t){if((!r&&0!==r||t)&&a.Z){var n=document.createElement("div");n.style.position="absolute",n.style.top="-9999px",n.style.width="50px",n.style.height="50px",n.style.overflow="scroll",document.body.appendChild(n),r=n.offsetWidth-n.clientWidth,document.body.removeChild(n)}return r}var s=e(43756),h=e(45832),d=e(1564),p=e(40343),g=e(82562),v=e(2265),y=e(70133),b=e(46579),m=e(83534),_=e(12865),x=e(57437);let w=v.forwardRef((t,n)=>{let{className:e,bsPrefix:r,as:i="div",...u}=t;return r=(0,_.vE)(r,"modal-body"),(0,x.jsx)(i,{ref:n,className:o()(e,r),...u})});w.displayName="ModalBody";var M=e(14272);let T=v.forwardRef((t,n)=>{let{bsPrefix:e,className:r,contentClassName:i,centered:u,size:a,fullscreen:c,children:f,scrollable:l,...s}=t;e=(0,_.vE)(e,"modal");let h="".concat(e,"-dialog"),d="string"==typeof c?"".concat(e,"-fullscreen-").concat(c):"".concat(e,"-fullscreen");return(0,x.jsx)("div",{...s,ref:n,className:o()(h,r,a&&"".concat(e,"-").concat(a),u&&"".concat(h,"-centered"),l&&"".concat(h,"-scrollable"),c&&d),children:(0,x.jsx)("div",{className:o()("".concat(e,"-content"),i),children:f})})});T.displayName="ModalDialog";let A=v.forwardRef((t,n)=>{let{className:e,bsPrefix:r,as:i="div",...u}=t;return r=(0,_.vE)(r,"modal-footer"),(0,x.jsx)(i,{ref:n,className:o()(e,r),...u})});A.displayName="ModalFooter";var k=e(94241);let S=v.forwardRef((t,n)=>{let{bsPrefix:e,className:r,closeLabel:i="Close",closeButton:u=!1,...a}=t;return e=(0,_.vE)(e,"modal-header"),(0,x.jsx)(k.Z,{ref:n,...a,className:o()(r,e),closeLabel:i,closeButton:u})});S.displayName="ModalHeader";let N=(0,e(89764).Z)("h4"),E=v.forwardRef((t,n)=>{let{className:e,bsPrefix:r,as:i=N,...u}=t;return r=(0,_.vE)(r,"modal-title"),(0,x.jsx)(i,{ref:n,className:o()(e,r),...u})});function C(t){return(0,x.jsx)(m.Z,{...t,timeout:null})}function P(t){return(0,x.jsx)(m.Z,{...t,timeout:null})}E.displayName="ModalTitle";let R=v.forwardRef((t,n)=>{let{bsPrefix:e,className:r,style:i,dialogClassName:m,contentClassName:w,children:A,dialogAs:k=T,"data-bs-theme":S,"aria-labelledby":N,"aria-describedby":E,"aria-label":R,show:O=!1,animation:D=!0,backdrop:z=!0,keyboard:L=!0,onEscapeKeyDown:j,onShow:B,onHide:V,container:I,autoFocus:F=!0,enforceFocus:G=!0,restoreFocus:q=!0,restoreFocusOptions:U,onEntered:Y,onExit:H,onExiting:X,onEnter:Z,onEntering:W,onExited:J,backdropClassName:K,manager:Q,...$}=t,[tt,tn]=(0,v.useState)({}),[te,tr]=(0,v.useState)(!1),ti=(0,v.useRef)(!1),to=(0,v.useRef)(!1),tu=(0,v.useRef)(null),[ta,tc]=(0,s.Z)(),tf=(0,d.Z)(n,tc),tl=(0,h.Z)(V),ts=(0,_.SC)();e=(0,_.vE)(e,"modal");let th=(0,v.useMemo)(()=>({onHide:tl}),[tl]);function td(){return Q||(0,b.t)({isRTL:ts})}function tp(t){if(!a.Z)return;let n=td().getScrollbarWidth()>0,e=t.scrollHeight>(0,c.Z)(t).documentElement.clientHeight;tn({paddingRight:n&&!e?l():void 0,paddingLeft:!n&&e?l():void 0})}let tg=(0,h.Z)(()=>{ta&&tp(ta.dialog)});(0,p.Z)(()=>{(0,f.Z)(window,"resize",tg),null==tu.current||tu.current()});let tv=()=>{ti.current=!0},ty=t=>{ti.current&&ta&&t.target===ta.dialog&&(to.current=!0),ti.current=!1},tb=()=>{tr(!0),tu.current=(0,g.Z)(ta.dialog,()=>{tr(!1)})},tm=t=>{t.target===t.currentTarget&&tb()},t_=t=>{if("static"===z){tm(t);return}if(to.current||t.target!==t.currentTarget){to.current=!1;return}null==V||V()},tx=(0,v.useCallback)(t=>(0,x.jsx)("div",{...t,className:o()("".concat(e,"-backdrop"),K,!D&&"show")}),[D,K,e]),tw={...i,...tt};return tw.display="block",(0,x.jsx)(M.Z.Provider,{value:th,children:(0,x.jsx)(y.Z,{show:O,ref:tf,backdrop:z,container:I,keyboard:!0,autoFocus:F,enforceFocus:G,restoreFocus:q,restoreFocusOptions:U,onEscapeKeyDown:t=>{L?null==j||j(t):(t.preventDefault(),"static"===z&&tb())},onShow:B,onHide:V,onEnter:(t,n)=>{t&&tp(t),null==Z||Z(t,n)},onEntering:(t,n)=>{null==W||W(t,n),(0,u.ZP)(window,"resize",tg)},onEntered:Y,onExit:t=>{null==tu.current||tu.current(),null==H||H(t)},onExiting:X,onExited:t=>{t&&(t.style.display=""),null==J||J(t),(0,f.Z)(window,"resize",tg)},manager:td(),transition:D?C:void 0,backdropTransition:D?P:void 0,renderBackdrop:tx,renderDialog:t=>(0,x.jsx)("div",{role:"dialog",...t,style:tw,className:o()(r,e,te&&"".concat(e,"-static"),!D&&"show"),onClick:z?t_:void 0,onMouseUp:ty,"data-bs-theme":S,"aria-label":R,"aria-labelledby":N,"aria-describedby":E,children:(0,x.jsx)(k,{...$,onMouseDown:tv,className:m,contentClassName:w,children:A})})})})});R.displayName="Modal";var O=Object.assign(R,{Body:w,Header:S,Title:E,Footer:A,Dialog:T,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},37224:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.CONSTANTS=void 0,n.CONSTANTS={arcTooltipClassname:"gauge-component-arc-tooltip",tickLineClassname:"tick-line",tickValueClassname:"tick-value",valueLabelClassname:"value-text",debugTicksRadius:!1,debugSingleGauge:!1,rangeBetweenCenteredTickValueLabel:[.35,.65]},n.default=n.CONSTANTS},34236:function(t,n,e){var r,i=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},o=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},u=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.validateArcs=n.clearOuterArcs=n.clearArcs=n.redrawArcs=n.getCoordByValue=n.createGradientElement=n.getColors=n.applyGradientColors=n.getArcDataByPercentage=n.getArcDataByValue=n.applyColors=n.setupTooltip=n.setupArcs=n.drawArc=n.setArcData=n.hideTooltip=void 0;var a=u(e(68040)),c=e(51130),f=u(e(34236)),l=(r=e(37224))&&r.__esModule?r:{default:r},s=e(20568),h=e(66482),d=e(19524),p=function(t,n,e){void 0!=n.data.tooltip&&(n.data.tooltip.text!=e.tooltip.current.text()&&(e.tooltip.current.html(n.data.tooltip.text).style("position","absolute").style("display","block").style("opacity",1),g(n.data.tooltip,n.data.color,e)),e.tooltip.current.style("left",t.pageX+15+"px").style("top",t.pageY-10+"px")),void 0!=n.data.onMouseMove&&n.data.onMouseMove(t)},g=function(t,n,e){Object.entries(s.defaultTooltipStyle).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)}),e.tooltip.current.style("background-color",n),void 0!=t.style&&Object.entries(t.style).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)})},v=function(t,e,r,i){i.cancel(),(0,n.hideTooltip)(r),void 0!=e.data.onMouseLeave&&e.data.onMouseLeave(t)};n.hideTooltip=function(t){t.tooltip.current.html(" ").style("display","none")};var y=function(t,n,e){t.target.style.stroke="none"},b=function(t,n){void 0!=n.data.onMouseClick&&n.data.onMouseClick(t)};n.setArcData=function(t){var e,r,i=t.props.arc,o=t.props.minValue,u=t.props.maxValue,c=(null==i?void 0:i.nbSubArcs)||(null===(e=null==i?void 0:i.subArcs)||void 0===e?void 0:e.length)||1,f=(0,n.getColors)(c,t);if((null==i?void 0:i.subArcs)&&!(null==i?void 0:i.nbSubArcs)){var l=0,s=0,h=[],d=[],p=[];null===(r=null==i?void 0:i.subArcs)||void 0===r||r.forEach(function(n,e){var r,c=0,f=0,g=n.limit;if(void 0!=n.length)c=n.length,g=a.getCurrentGaugeValueByPercentage(c+s,t);else if(void 0==n.limit){f=l;var v=void 0,y=null===(r=null==i?void 0:i.subArcs)||void 0===r?void 0:r.slice(e),b=(1-a.calculatePercentage(o,u,l))*100;v||(v=b/Math.max((null==y?void 0:y.length)||1,1)/100),g=l+100*v,c=v}else f=g-l,c=0!==e?a.calculatePercentage(o,u,g)-s:a.calculatePercentage(o,u,f);h.push(c),d.push(g),s=h.reduce(function(t,n){return t+n},0),l=g,void 0!=n.tooltip&&p.push(n.tooltip)});var g=i.subArcs;t.arcData.current=h.map(function(t,n){return{value:t,limit:d[n],color:f[n],showTick:g[n].showTick||!1,tooltip:g[n].tooltip||void 0,onMouseMove:g[n].onMouseMove,onMouseLeave:g[n].onMouseLeave,onMouseClick:g[n].onClick}})}else{var v=u/c;t.arcData.current=Array.from({length:c},function(t,n){return{value:v,limit:(n+1)*v,color:f[n],tooltip:void 0}})}};var m=function(t,e){void 0===e&&(e=void 0);var r,i=void 0!=e?e:a.calculatePercentage(t.props.minValue,t.props.maxValue,t.props.value),o=(0,n.getArcDataByPercentage)(i,t);return[{value:i,color:(null==o?void 0:o.color)||"white"},{value:1-i,color:null===(r=t.props.arc)||void 0===r?void 0:r.emptyColor}]},_=function(t,e){void 0===e&&(e=!1);var r=t.dimensions.current.outerRadius;if(t.props.type==h.GaugeType.Grafana&&e){t.doughnut.current.selectAll(".outerSubArc").remove();var i=(0,c.arc)().outerRadius(r+7).innerRadius(r+2).cornerRadius(0).padAngle(0),o=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(t.arcData.current)).enter().append("g").attr("class","outerSubArc"),u=o.append("path").attr("d",i);(0,n.applyColors)(u,t);var a=(0,d.throttle)(function(n,e){return p(n,e,t)},20);o.on("mouseleave",function(n,e){return v(n,e,t,a)}).on("mouseout",function(n,e){return y(n,e,t)}).on("mousemove",a).on("click",function(t,n){return b(t,n)})}};n.drawArc=function(t,e){void 0===e&&(e=void 0);var r,i,o=t.props.arc,u=o.padding,a=o.cornerRadius,f=t.dimensions.current,l=f.innerRadius,s=f.outerRadius,g={};g=(null===(i=null===(r=t.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient)?[{value:1}]:t.arcData.current,t.props.type==h.GaugeType.Grafana&&(g=m(t,e));var _=t.props.type==h.GaugeType.Grafana?0:u,x=t.props.type==h.GaugeType.Grafana?0:a,w=(0,c.arc)().outerRadius(s).innerRadius(l).cornerRadius(x).padAngle(_),M=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(g)).enter().append("g").attr("class","subArc"),T=M.append("path").attr("d",w);(0,n.applyColors)(T,t);var A=(0,d.throttle)(function(n,e){return p(n,e,t)},20);M.on("mouseleave",function(n,e){return v(n,e,t,A)}).on("mouseout",function(n,e){return y(n,e,t)}).on("mousemove",A).on("click",function(t,n){return b(t,n)})},n.setupArcs=function(t,e){void 0===e&&(e=!1),(0,n.setupTooltip)(t),_(t,e),(0,n.drawArc)(t)},n.setupTooltip=function(t){0!=document.getElementsByClassName(l.default.arcTooltipClassname).length||(0,c.select)("body").append("div").attr("class",l.default.arcTooltipClassname),t.tooltip.current=(0,c.select)(".".concat(l.default.arcTooltipClassname)),t.tooltip.current.on("mouseleave",function(){return f.hideTooltip(t)}).on("mouseout",function(){return f.hideTooltip(t)})},n.applyColors=function(t,e){var r,i;if(null===(i=null===(r=e.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient){var o="subArc-linear-gradient-".concat(Math.random()),u=(0,n.createGradientElement)(e.doughnut.current,o);(0,n.applyGradientColors)(u,e),t.style("fill",function(t){return"url(#".concat(o,")")})}else t.style("fill",function(t){return t.data.color})},n.getArcDataByValue=function(t,n){return n.arcData.current.find(function(n){return t<=n.limit})},n.getArcDataByPercentage=function(t,e){return(0,n.getArcDataByValue)(a.getCurrentGaugeValueByPercentage(t,e),e)},n.applyGradientColors=function(t,n){n.arcData.current.forEach(function(e){var r,i,o,u,c=a.normalize(null==e?void 0:e.limit,null!==(i=null===(r=null==n?void 0:n.props)||void 0===r?void 0:r.minValue)&&void 0!==i?i:0,null!==(u=null===(o=null==n?void 0:n.props)||void 0===o?void 0:o.maxValue)&&void 0!==u?u:100);t.append("stop").attr("offset","".concat(c,"%")).style("stop-color",e.color).style("stop-opacity",1)})},n.getColors=function(t,n){var e,r=n.props.arc,i=[];if(r.colorArray)i=r.colorArray;else{var o=null===(e=r.subArcs)||void 0===e?void 0:e.map(function(t){return t.color});i=(null==o?void 0:o.some(function(t){return void 0!=t}))?o:l.default.defaultColors}if(i||(i=["#fff"]),t===(null==i?void 0:i.length))return i;for(var u=(0,c.scaleLinear)().domain([1,t]).range([i[0],i[i.length-1]]).interpolate(c.interpolateHsl),a=[],f=1;f<=t;f++)a.push(u(f));return a},n.createGradientElement=function(t,n){return t.append("defs").append("linearGradient").attr("id",n).attr("x1","0%").attr("x2","100%").attr("y1","0%").attr("y2","0%")},n.getCoordByValue=function(t,n,e,r,i){void 0===e&&(e="inner"),void 0===r&&(r=0),void 0===i&&(i=1);var o,u=({outer:function(){return n.dimensions.current.outerRadius-r+2},inner:function(){return n.dimensions.current.innerRadius*i-r+9},between:function(){var t=n.dimensions.current.outerRadius-n.dimensions.current.innerRadius;return n.dimensions.current.innerRadius+t-5}})[e]();n.props.type===h.GaugeType.Grafana?u+=5:n.props.type===h.GaugeType.Semicircle&&(u+=-2);var c=a.calculatePercentage(n.props.minValue,n.props.maxValue,t),f=((o={})[h.GaugeType.Grafana]={startAngle:a.degToRad(-23),endAngle:a.degToRad(203)},o[h.GaugeType.Semicircle]={startAngle:a.degToRad(.9),endAngle:a.degToRad(179.1)},o[h.GaugeType.Radial]={startAngle:a.degToRad(-39),endAngle:a.degToRad(219)},o)[n.props.type],l=f.startAngle,s=l+c*(f.endAngle-l),d=[0,-(n.dimensions.current.width/500*1)/2],p=[d[0]-u*Math.cos(s),d[1]-u*Math.sin(s)],g=[n.dimensions.current.outerRadius,n.dimensions.current.outerRadius];return{x:g[0]+p[0],y:g[1]+p[1]}},n.redrawArcs=function(t){(0,n.clearArcs)(t),(0,n.setArcData)(t),(0,n.setupArcs)(t)},n.clearArcs=function(t){t.doughnut.current.selectAll(".subArc").remove()},n.clearOuterArcs=function(t){t.doughnut.current.selectAll(".outerSubArc").remove()},n.validateArcs=function(t){x(t)};var x=function(t){for(var n,e=t.props.minValue,r=t.props.maxValue,i=t.props.arc.subArcs,o=void 0,u=0,a=(null===(n=t.props.arc)||void 0===n?void 0:n.subArcs)||[];u<a.length;u++){var c=a[u].limit;if(void 0!==c){if(c<e||c>r)throw Error("The limit of a subArc must be between the minValue and maxValue. The limit of the subArc is ".concat(c));if(void 0!==o&&c<=o)throw Error("The limit of a subArc must be greater than the limit of the previous subArc. The limit of the subArc is ".concat(c,'. If you\'re trying to specify length in percent of the arc, use property "length". refer to: https://github.com/antoniolago/react-gauge-component'));o=c}}if(i.length>0){var f=i[i.length-1];f.limit<r&&(f.limit=r)}}},30987:function(t,n,e){var r=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},i=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},o=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearChart=n.centerGraph=n.calculateRadius=n.updateDimensions=n.renderChart=n.calculateAngles=n.initChart=void 0;var u=e(66482),a=o(e(34236)),c=o(e(94312)),f=o(e(7213));n.initChart=function(t,e){var r,i,o,u,a=t.dimensions.current.angles;if((null===(i=null===(r=t.resizeObserver)||void 0===r?void 0:r.current)||void 0===i?void 0:i.disconnect)&&(null===(u=null===(o=t.resizeObserver)||void 0===o?void 0:o.current)||void 0===u||u.disconnect()),JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value)&&!e){(0,n.renderChart)(t,!1);return}t.container.current.select("svg").remove(),t.svg.current=t.container.current.append("svg"),t.g.current=t.svg.current.append("g"),t.doughnut.current=t.g.current.append("g").attr("class","doughnut"),(0,n.calculateAngles)(t),t.pieChart.current.value(function(t){return t.value}).startAngle(a.startAngle).endAngle(a.endAngle).sort(null),f.addPointerElement(t),(0,n.renderChart)(t,!0)},n.calculateAngles=function(t){var n=t.dimensions.current.angles;t.props.type==u.GaugeType.Semicircle?(n.startAngle=-Math.PI/2+.02,n.endAngle=Math.PI/2-.02):t.props.type==u.GaugeType.Radial?(n.startAngle=-Math.PI/1.37,n.endAngle=Math.PI/1.37):t.props.type==u.GaugeType.Grafana&&(n.startAngle=-Math.PI/1.6,n.endAngle=Math.PI/1.6)},n.renderChart=function(t,e){void 0===e&&(e=!1);var r,i,o,l,s,h,d=t.dimensions,p=t.props.arc,g=t.props.labels;if(e){(0,n.updateDimensions)(t),t.g.current.attr("transform","translate("+d.current.margin.left+", 35)"),(0,n.calculateRadius)(t),t.doughnut.current.attr("transform","translate("+d.current.outerRadius+", "+d.current.outerRadius+")"),t.doughnut.current.on("mouseleave",function(){return a.hideTooltip(t)}).on("mouseout",function(){return a.hideTooltip(t)});var v=p.width;d.current.innerRadius=d.current.outerRadius*(1-v),(0,n.clearChart)(t),a.setArcData(t),a.setupArcs(t,e),c.setupLabels(t),(null===(o=null===(i=t.props)||void 0===i?void 0:i.pointer)||void 0===o?void 0:o.hide)||f.drawPointer(t,e);var y=((r={})[u.GaugeType.Semicircle]=50,r[u.GaugeType.Radial]=55,r[u.GaugeType.Grafana]=55,r),b=t.doughnut.current.node().getBoundingClientRect().height,m=t.container.current.node().getBoundingClientRect().width,_=t.props.type;t.svg.current.attr("width",m).attr("height",b+y[_])}else{var x=JSON.stringify(t.prevProps.current.arc)!==JSON.stringify(t.props.arc),w=JSON.stringify(t.prevProps.current.pointer)!==JSON.stringify(t.props.pointer),M=JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value),T=JSON.stringify(null===(l=t.prevProps.current.labels)||void 0===l?void 0:l.tickLabels)!==JSON.stringify(g.tickLabels);x&&(a.clearArcs(t),a.setArcData(t),a.setupArcs(t,e)),(w||M&&!(null===(h=null===(s=t.props)||void 0===s?void 0:s.pointer)||void 0===h?void 0:h.hide))&&f.drawPointer(t),(x||T)&&(c.clearTicks(t),c.setupTicks(t)),M&&(c.clearValueLabel(t),c.setupValueLabel(t))}},n.updateDimensions=function(t){var n=t.props.marginInPercent,e=t.dimensions,r=t.container.current.node().getBoundingClientRect(),i=r.width,o=r.height;0==e.current.fixedHeight&&(e.current.fixedHeight=o+200);var u="number"==typeof n,a=u?n:n.left,c=u?n:n.right,f=u?n:n.top,l=u?n:n.bottom;e.current.margin.left=i*a,e.current.margin.right=i*c,e.current.width=i-e.current.margin.left-e.current.margin.right,e.current.margin.top=e.current.fixedHeight*f,e.current.margin.bottom=e.current.fixedHeight*l,e.current.height=e.current.width/2-e.current.margin.top-e.current.margin.bottom},n.calculateRadius=function(t){var e=t.dimensions;e.current.width<2*e.current.height?e.current.outerRadius=(e.current.width-e.current.margin.left-e.current.margin.right)/2:e.current.outerRadius=e.current.height-e.current.margin.top-e.current.margin.bottom+35,(0,n.centerGraph)(t)},n.centerGraph=function(t){var n=t.dimensions;n.current.margin.left=n.current.width/2-n.current.outerRadius+n.current.margin.right,t.g.current.attr("transform","translate("+n.current.margin.left+", "+n.current.margin.top+")")},n.clearChart=function(t){c.clearTicks(t),c.clearValueLabel(t),f.clearPointerElement(t),a.clearArcs(t)}},94312:function(t,n,e){var r,i=function(){return(i=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},o=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},u=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},a=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&o(n,t,e);return u(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.calculateAnchorAndAngleByValue=n.clearTicks=n.clearValueLabel=n.addValueText=n.addText=n.getLabelCoordsByValue=n.addTick=n.addTickValue=n.addTickLine=n.mapTick=n.addArcTicks=n.setupTicks=n.setupValueLabel=n.setupLabels=void 0;var c=a(e(68040)),f=(r=e(37224))&&r.__esModule?r:{default:r},l=e(29475),s=a(e(51130)),h=e(66482),d=e(34236);n.setupLabels=function(t){(0,n.setupValueLabel)(t),(0,n.setupTicks)(t)},n.setupValueLabel=function(t){var e,r=t.props.labels;(null===(e=null==r?void 0:r.valueLabel)||void 0===e?void 0:e.hide)||(0,n.addValueText)(t)},n.setupTicks=function(t){var e,r,i,o,u,a,c,l,s,h=t.props.labels,d=t.props.minValue,p=t.props.maxValue;if(f.default.debugTicksRadius)for(var g=0;g<p;g++){var v=(0,n.mapTick)(g,t);(0,n.addTick)(v,t)}else if(!(null===(e=h.tickLabels)||void 0===e?void 0:e.hideMinMax)){if(!(null===(i=null===(r=h.tickLabels)||void 0===r?void 0:r.ticks)||void 0===i?void 0:i.some(function(t){return t.value==d}))){var y=(0,n.mapTick)(d,t);(0,n.addTick)(y,t)}if(!(null===(u=null===(o=h.tickLabels)||void 0===o?void 0:o.ticks)||void 0===u?void 0:u.some(function(t){return t.value==p}))){var b=(0,n.mapTick)(p,t);(0,n.addTick)(b,t)}}(null===(c=null===(a=h.tickLabels)||void 0===a?void 0:a.ticks)||void 0===c?void 0:c.length)>0&&(null===(s=null===(l=h.tickLabels)||void 0===l?void 0:l.ticks)||void 0===s||s.forEach(function(e){(0,n.addTick)(e,t)})),(0,n.addArcTicks)(t)},n.addArcTicks=function(t){var e;null===(e=t.arcData.current)||void 0===e||e.map(function(t){if(t.showTick)return t.limit}).forEach(function(e){e&&(0,n.addTick)((0,n.mapTick)(e,t),t)})},n.mapTick=function(t,n){var e=n.props.labels.tickLabels;return{value:t,valueConfig:null==e?void 0:e.defaultTickValueConfig,lineConfig:null==e?void 0:e.defaultTickLineConfig}},n.addTickLine=function(t,e){var r,i,o,u,a,c,h,d,p,g,v,y,b,m,_,x,w,M,T,A,k,S=e.props.labels,N=(0,n.calculateAnchorAndAngleByValue)(null==t?void 0:t.value,e),E=(N.tickAnchor,N.angle),C=(null===(r=t.lineConfig)||void 0===r?void 0:r.distanceFromArc)||(null===(o=null===(i=null==S?void 0:S.tickLabels)||void 0===i?void 0:i.defaultTickLineConfig)||void 0===o?void 0:o.distanceFromArc)||0;(null===(a=null===(u=e.props.labels)||void 0===u?void 0:u.tickLabels)||void 0===a?void 0:a.type)=="outer"&&(C=-C);var P=(0,n.getLabelCoordsByValue)(null==t?void 0:t.value,e,C),R=(null===(c=t.lineConfig)||void 0===c?void 0:c.color)||(null===(d=null===(h=null==S?void 0:S.tickLabels)||void 0===h?void 0:h.defaultTickLineConfig)||void 0===d?void 0:d.color)||(null===(p=l.defaultTickLabels.defaultTickLineConfig)||void 0===p?void 0:p.color),O=(null===(g=t.lineConfig)||void 0===g?void 0:g.width)||(null===(y=null===(v=null==S?void 0:S.tickLabels)||void 0===v?void 0:v.defaultTickLineConfig)||void 0===y?void 0:y.width)||(null===(b=l.defaultTickLabels.defaultTickLineConfig)||void 0===b?void 0:b.width),D=(null===(m=t.lineConfig)||void 0===m?void 0:m.length)||(null===(x=null===(_=null==S?void 0:S.tickLabels)||void 0===_?void 0:_.defaultTickLineConfig)||void 0===x?void 0:x.length)||(null===(w=l.defaultTickLabels.defaultTickLineConfig)||void 0===w?void 0:w.length);(null===(M=null==S?void 0:S.tickLabels)||void 0===M?void 0:M.type)=="inner"?(T=P.x+D*Math.cos(E*Math.PI/180),A=P.y+D*Math.sin(E*Math.PI/180)):(T=P.x-D*Math.cos(E*Math.PI/180),A=P.y-D*Math.sin(E*Math.PI/180));var z=s.line();k=[[P.x,P.y],[T,A]],e.g.current.append("path").datum(k).attr("class",f.default.tickLineClassname).attr("d",z).attr("stroke",R).attr("stroke-width",O).attr("fill","none")},n.addTickValue=function(t,e){var r,o,u,a,l,s,h,d,p,v,y,b,m,_,x,w,M,T=e.props.labels,A=e.props.arc.width,k=null==t?void 0:t.value,S=(0,n.calculateAnchorAndAngleByValue)(k,e).tickAnchor,N=27-10*A,E=(null===(r=null==T?void 0:T.tickLabels)||void 0===r?void 0:r.type)=="inner";E?N-=10:N=10*A-10;var C=(null===(o=t.lineConfig)||void 0===o?void 0:o.distanceFromArc)||(null===(a=null===(u=null==T?void 0:T.tickLabels)||void 0===u?void 0:u.defaultTickLineConfig)||void 0===a?void 0:a.distanceFromArc)||0,P=(null===(l=t.lineConfig)||void 0===l?void 0:l.length)||(null===(h=null===(s=null==T?void 0:T.tickLabels)||void 0===s?void 0:s.defaultTickLineConfig)||void 0===h?void 0:h.length)||0;g(t,e)||(E?N+=C+P:(N-=C,N-=P));var R=(0,n.getLabelCoordsByValue)(k,e,N),O=(null===(d=t.valueConfig)||void 0===d?void 0:d.style)||(null===(v=null===(p=null==T?void 0:T.tickLabels)||void 0===p?void 0:p.defaultTickValueConfig)||void 0===v?void 0:v.style)||{};O=i({},O);var D="",z=(null===(y=t.valueConfig)||void 0===y?void 0:y.maxDecimalDigits)||(null===(m=null===(b=null==T?void 0:T.tickLabels)||void 0===b?void 0:b.defaultTickValueConfig)||void 0===m?void 0:m.maxDecimalDigits);D=(null===(_=t.valueConfig)||void 0===_?void 0:_.formatTextValue)?t.valueConfig.formatTextValue(c.floatingNumber(k,z)):(null===(w=null===(x=null==T?void 0:T.tickLabels)||void 0===x?void 0:x.defaultTickValueConfig)||void 0===w?void 0:w.formatTextValue)?T.tickLabels.defaultTickValueConfig.formatTextValue(c.floatingNumber(k,z)):0===e.props.minValue&&100===e.props.maxValue?c.floatingNumber(k,z).toString()+"%":c.floatingNumber(k,z).toString(),(null===(M=null==T?void 0:T.tickLabels)||void 0===M?void 0:M.type)=="inner"?("end"===S&&(R.x+=10),"start"===S&&(R.x-=10)):"middle"===S&&(R.y+=2),"middle"===S?R.y+=0:R.y+=3,O.textAnchor=S,(0,n.addText)(D,R.x,R.y,e,O,f.default.tickValueClassname)},n.addTick=function(t,e){e.props.labels;var r=g(t,e),i=v(t,e);r||(0,n.addTickLine)(t,e),f.default.debugTicksRadius||i||(0,n.addTickValue)(t,e)},n.getLabelCoordsByValue=function(t,n,e){void 0===e&&(e=0);var r,i=n.props.labels,o=n.props.minValue,u=n.props.maxValue,a=null===(r=i.tickLabels)||void 0===r?void 0:r.type,f=(0,d.getCoordByValue)(t,n,a,e,.93),l=f.x,s=f.y;return c.calculatePercentage(o,u,t),n.props.type==h.GaugeType.Radial&&(s+=3),{x:l,y:s}},n.addText=function(t,n,e,r,i,o,u){void 0===u&&(u=0);var a=r.g.current.append("g").attr("class",o).attr("transform","translate(".concat(n,", ").concat(e,")")).append("text").text(t);p(a,i),a.attr("transform","rotate(".concat(u,")"))};var p=function(t,n){Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(c.camelCaseToKebabCase(e),r)}),void 0!=n&&Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(c.camelCaseToKebabCase(e),r)})};n.addValueText=function(t){var e,r,o,u=t.props.labels,a=t.props.value,l=null==u?void 0:u.valueLabel,s="",p=null===(e=null==u?void 0:u.valueLabel)||void 0===e?void 0:e.maxDecimalDigits,g=c.floatingNumber(a,p),v=(null==(s=l.formatTextValue?l.formatTextValue(g):0===t.props.minValue&&100===t.props.maxValue?g.toString()+"%":g.toString())?void 0:s.length)||0,y=v>4?4/v*1.5:1,b=null===(r=null==l?void 0:l.style)||void 0===r?void 0:r.fontSize,m=i({},l.style),_=t.dimensions.current.outerRadius,x=0;m.textAnchor="middle",t.props.type==h.GaugeType.Semicircle?x=t.dimensions.current.outerRadius/1.5+20:t.props.type==h.GaugeType.Radial?x=1.45*t.dimensions.current.outerRadius+20:t.props.type==h.GaugeType.Grafana&&(x=1*t.dimensions.current.outerRadius+20);var w=(t.props.type,h.GaugeType.Radial,.003);y=t.dimensions.current.width*w*y;var M=parseInt(b,10)*y;m.fontSize=M+"px",l.matchColorWithArc&&(m.fill=(null===(o=(0,d.getArcDataByValue)(a,t))||void 0===o?void 0:o.color)||"white"),(0,n.addText)(s,_,x,t,m,f.default.valueLabelClassname)},n.clearValueLabel=function(t){return t.g.current.selectAll(".".concat(f.default.valueLabelClassname)).remove()},n.clearTicks=function(t){t.g.current.selectAll(".".concat(f.default.tickLineClassname)).remove(),t.g.current.selectAll(".".concat(f.default.tickValueClassname)).remove()},n.calculateAnchorAndAngleByValue=function(t,n){var e,r,i=n.props.labels,o=n.props.minValue,u=n.props.maxValue,a=c.calculatePercentage(o,u,t),l=((e={})[h.GaugeType.Grafana]={startAngle:-20,endAngle:220},e[h.GaugeType.Semicircle]={startAngle:0,endAngle:180},e[h.GaugeType.Radial]={startAngle:-42,endAngle:266},e)[n.props.type],s=l.startAngle,d=l.endAngle,p=a>f.default.rangeBetweenCenteredTickValueLabel[0]&&a<f.default.rangeBetweenCenteredTickValueLabel[1],g=(null===(r=null==i?void 0:i.tickLabels)||void 0===r?void 0:r.type)=="inner";return{tickAnchor:p?"middle":a<.5?g?"start":"end":g?"end":"start",angle:s+100*a*d/100}};var g=function(t,n){var e,r,i,o,u=n.props.labels,a=null===(e=l.defaultTickLabels.defaultTickLineConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==u?void 0:u.tickLabels)||void 0===r?void 0:r.defaultTickLineConfig)||void 0===i?void 0:i.hide;void 0!=c&&(a=c);var f=null===(o=t.lineConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(a=f),a},v=function(t,n){var e,r,i,o,u=n.props.labels,a=null===(e=l.defaultTickLabels.defaultTickValueConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==u?void 0:u.tickLabels)||void 0===r?void 0:r.defaultTickValueConfig)||void 0===i?void 0:i.hide;void 0!=c&&(a=c);var f=null===(o=t.valueConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(a=f),a}},7213:function(t,n,e){var r=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},i=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},o=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearPointerElement=n.addPointerElement=n.translatePointer=n.drawPointer=void 0;var u=e(51130),a=e(70749),c=e(34236),f=o(e(68040)),l=o(e(34236)),s=e(66482);n.drawPointer=function(t,n){void 0===n&&(n=!1),t.pointer.current.context=h(t);var e,r=t.pointer.current.context,i=r.prevPercent,o=r.currentPercent,a=r.prevProgress,c=t.props.pointer,f=(null===(e=t.prevProps)||void 0===e?void 0:e.current.value)==void 0;(f||n)&&t.props.type!=s.GaugeType.Grafana&&d(t),(!n||f)&&c.animate?t.doughnut.current.transition().delay(c.animationDelay).ease(c.elastic?u.easeElastic:u.easeExpOut).duration(c.animationDuration).tween("progress",function(){var n=(0,u.interpolateNumber)(i,o);return function(e){var r=n(e);v(r,a,t)&&(t.props.type==s.GaugeType.Grafana?(l.clearArcs(t),l.drawArc(t,r)):p(r,t)),t.pointer.current.context.prevProgress=r}}):p(o,t)};var h=function(t){var n,e=t.props.value,r=t.props.pointer,i=r.length,o=t.props.minValue,u=t.props.maxValue;t.pointer.current.context.pointerPath;var c=b(t),l=r.type==a.PointerType.Needle?i:.2,s=[a.PointerType.Needle,a.PointerType.Arrow];return{centerPoint:[0,-c/2],pointerRadius:b(t),pathLength:t.dimensions.current.outerRadius*l,currentPercent:f.calculatePercentage(o,u,e),prevPercent:f.calculatePercentage(o,u,(null===(n=t.prevProps)||void 0===n?void 0:n.current.value)||o),prevProgress:0,pathStr:"",shouldDrawPath:s.includes(r.type),prevColor:""}},d=function(t){var n=t.props.value,e=t.props.pointer,r=t.pointer.current.context,i=r.shouldDrawPath,o=r.centerPoint,u=r.pointerRadius,c=(r.pathStr,r.currentPercent),f=r.prevPercent;i&&(t.pointer.current.context.pathStr=y(t,f||c),t.pointer.current.path=t.pointer.current.element.append("path").attr("d",t.pointer.current.context.pathStr).attr("fill",e.color)),e.type==a.PointerType.Needle?t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",u).attr("fill",e.color):e.type==a.PointerType.Blob&&t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",u).attr("fill",e.baseColor).attr("stroke",e.color).attr("stroke-width",e.strokeWidth*u/10),g(u,n,t)},p=function(t,n){var e,r=n.props.pointer,i=n.pointer.current.context,o=i.pointerRadius,u=i.shouldDrawPath,c=i.prevColor;if(g(o,t,n),u&&n.props.type!=s.GaugeType.Grafana&&n.pointer.current.path.attr("d",y(n,t)),r.type==a.PointerType.Blob){var f=null===(e=l.getArcDataByPercentage(t,n))||void 0===e?void 0:e.color;f!=c&&n.pointer.current.element.select("circle").attr("stroke",f);var h=r.strokeWidth*o/10;n.pointer.current.element.select("circle").attr("stroke-width",h),n.pointer.current.context.prevColor=f}},g=function(t,e,r){var i,o=r.props.pointer.type,u=r.dimensions,l=f.getCurrentGaugeValueByPercentage(e,r);return((i={})[a.PointerType.Needle]=function(){(0,n.translatePointer)(u.current.outerRadius,u.current.outerRadius,r)},i[a.PointerType.Arrow]=function(){var e=(0,c.getCoordByValue)(l,r,"inner",0,.7),i=e.x,o=e.y;i-=1,o+=t-3,(0,n.translatePointer)(i,o,r)},i[a.PointerType.Blob]=function(){var e=(0,c.getCoordByValue)(l,r,"between",0,.75),i=e.x,o=e.y;i-=1,o+=t,(0,n.translatePointer)(i,o,r)},i)[o]()},v=function(t,n,e){return!(1e-4>Math.abs(t-n))&&t!=n&&!(t>1||t<0)},y=function(t,n){var e=t.pointer.current.context,r=e.centerPoint,i=e.pointerRadius,o=e.pathLength,u=f.degToRad(t.props.type==s.GaugeType.Semicircle?0:-42),a=u+n*(f.degToRad(t.props.type==s.GaugeType.Semicircle?180:223)-u),c=[r[0]-o*Math.cos(a),r[1]-o*Math.sin(a)],l=a-Math.PI/2,h=[r[0]-i*Math.cos(l),r[1]-i*Math.sin(l)],d=a+Math.PI/2,p=[r[0]-i*Math.cos(d),r[1]-i*Math.sin(d)];return"M ".concat(h[0]," ").concat(h[1]," L ").concat(c[0]," ").concat(c[1]," L ").concat(p[0]," ").concat(p[1])},b=function(t){var n=t.props.pointer.width;return t.dimensions.current.width/500*n};n.translatePointer=function(t,n,e){return e.pointer.current.element.attr("transform","translate("+t+", "+n+")")},n.addPointerElement=function(t){return t.pointer.current.element=t.g.current.append("g").attr("class","pointer")},n.clearPointerElement=function(t){return t.pointer.current.element.selectAll("*").remove()}},68040:function(t,n){var e=function(){return(e=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};Object.defineProperty(n,"__esModule",{value:!0}),n.camelCaseToKebabCase=n.getCurrentGaugeValueByPercentage=n.getCurrentGaugePercentageByValue=n.degToRad=n.normalize=n.floatingNumber=n.percentToRad=n.mergeObjects=n.isEmptyObject=n.calculatePercentage=void 0,n.calculatePercentage=function(t,n,e){return e<t?0:e>n?1:(e-t)/(n-t)},n.isEmptyObject=function(t){return 0===Object.keys(t).length&&t.constructor===Object},n.mergeObjects=function(t,r){var i=e({},t);return Object.keys(r).forEach(function(e){var o=t[e],u=r[e];Array.isArray(o)&&Array.isArray(u)?i[e]=u:"object"==typeof o&&"object"==typeof u?i[e]=(0,n.mergeObjects)(o,u):void 0!==u&&(i[e]=u)}),i},n.percentToRad=function(t,n){return Math.PI/n*t},n.floatingNumber=function(t,n){return void 0===n&&(n=2),Math.round(t*Math.pow(10,n))/Math.pow(10,n)},n.normalize=function(t,n,e){return(t-n)/(e-n)*100},n.degToRad=function(t){return Math.PI/180*t},n.getCurrentGaugePercentageByValue=function(t,e){return(0,n.calculatePercentage)(e.minValue,e.maxValue,t)},n.getCurrentGaugeValueByPercentage=function(t,n){var e=n.props.minValue;return e+t*(n.props.maxValue-e)},n.camelCaseToKebabCase=function(t){return t.replace(/[A-Z]/g,function(t){return"-".concat(t.toLowerCase())})}},49460:function(t,n,e){var r=function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]},o=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n},u=function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.GaugeComponent=void 0;var a=u(e(2265)),c=e(51130),f=e(66482),l=u(e(30987)),s=u(e(34236)),h=e(68040),d=e(61682),p=e(70749),g=e(95600),v=function(t){var n=(0,a.useRef)({}),e=(0,a.useRef)({}),i=(0,a.useRef)({}),o=(0,a.useRef)({}),u=(0,a.useRef)(!0),v=(0,a.useRef)(0),y=(0,a.useRef)(r({},p.defaultPointerRef)),b=(0,a.useRef)({}),m=(0,a.useRef)([]),_=(0,a.useRef)((0,c.pie)()),x=(0,a.useRef)(r({},d.defaultDimensions)),w=(0,a.useRef)(t),M=(0,a.useRef)({}),T=(0,a.useRef)({}),A=(0,a.useRef)(null),k={props:w.current,resizeObserver:T,prevProps:M,svg:n,g:i,dimensions:x,doughnut:o,isFirstRun:u,currentProgress:v,pointer:y,container:b,arcData:m,pieChart:_,tooltip:e},S=function(){var n,e,i=r({},f.defaultGaugeProps);k.props=w.current=(0,h.mergeObjects)(i,t),(null===(n=k.props.arc)||void 0===n?void 0:n.width)==(null===(e=f.defaultGaugeProps.arc)||void 0===e?void 0:e.width)&&(w.current.arc.width=(0,g.getArcWidthByType)(k.props.type)),k.props.marginInPercent==f.defaultGaugeProps.marginInPercent&&(w.current.marginInPercent=(0,f.getGaugeMarginByType)(k.props.type)),s.validateArcs(k)},N=function(){var t=JSON.stringify(M.current.arc)!==JSON.stringify(w.current.arc),n=JSON.stringify(M.current.pointer)!==JSON.stringify(w.current.pointer),e=JSON.stringify(M.current.value)!==JSON.stringify(w.current.value),r=JSON.stringify(M.current.minValue)!==JSON.stringify(w.current.minValue),i=JSON.stringify(M.current.maxValue)!==JSON.stringify(w.current.maxValue);return t||n||e||r||i};(0,a.useLayoutEffect)(function(){S(),u.current=(0,h.isEmptyObject)(b.current),u.current&&(b.current=(0,c.select)(A.current)),N()&&l.initChart(k,u.current),k.prevProps.current=w.current},[t]),(0,a.useEffect)(function(){var t=function(){return l.renderChart(k,!0)};return window.addEventListener("resize",t),function(){return window.removeEventListener("resize",t)}},[t]),(0,a.useEffect)(function(){var t=A.current;if(t){var n=new ResizeObserver(function(){l.renderChart(k,!0)});return k.resizeObserver.current=n,t.parentNode&&n.observe(t.parentNode),function(){var t;k.resizeObserver&&(null===(t=k.resizeObserver.current)||void 0===t||t.disconnect(),delete k.resizeObserver.current)}}},[]);var E=t.id,C=t.style,P=t.className;return t.type,a.default.createElement("div",{id:E,className:"".concat(k.props.type,"-gauge").concat(P?" "+P:""),style:C,ref:function(t){return A.current=t}})};n.GaugeComponent=v,n.default=v},95600:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultArc=n.getArcWidthByType=n.defaultSubArcs=void 0;var r=e(66482);n.defaultSubArcs=[{limit:33,color:"#5BE12C"},{limit:66,color:"#F5CD19"},{color:"#EA4228"}],n.getArcWidthByType=function(t){var n,e=((n={})[r.GaugeType.Grafana]=.25,n[r.GaugeType.Semicircle]=.15,n[r.GaugeType.Radial]=.2,n);return t||(t=r.defaultGaugeProps.type),e[t]},n.defaultArc={padding:.05,width:.25,cornerRadius:7,nbSubArcs:void 0,emptyColor:"#5C5C5C",colorArray:void 0,subArcs:n.defaultSubArcs,gradient:!1}},61682:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultDimensions=n.defaultAngles=n.defaultMargins=void 0,n.defaultMargins={top:0,right:0,bottom:0,left:0},n.defaultAngles={startAngle:0,endAngle:0,startAngleDeg:0,endAngleDeg:0},n.defaultDimensions={width:0,height:0,margin:n.defaultMargins,outerRadius:0,innerRadius:0,angles:n.defaultAngles,fixedHeight:0}},66482:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.getGaugeMarginByType=n.defaultGaugeProps=n.GaugeType=void 0;var r,i,o=e(95600),u=e(97525),a=e(70749);(r=i||(n.GaugeType=i={})).Semicircle="semicircle",r.Radial="radial",r.Grafana="grafana",n.defaultGaugeProps={id:"",className:"gauge-component-class",style:{width:"100%"},marginInPercent:.07,value:33,minValue:0,maxValue:100,arc:o.defaultArc,labels:u.defaultLabels,pointer:a.defaultPointer,type:i.Grafana},n.getGaugeMarginByType=function(t){var n;return((n={})[i.Grafana]={top:.12,bottom:0,left:.07,right:.07},n[i.Semicircle]={top:.08,bottom:0,left:.08,right:.08},n[i.Radial]={top:.07,bottom:0,left:.07,right:.07},n)[t]}},97525:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultLabels=n.defaultValueLabel=void 0;var r=e(29475);n.defaultValueLabel={formatTextValue:void 0,matchColorWithArc:!1,maxDecimalDigits:2,style:{fontSize:"35px",fill:"#fff",textShadow:"black 1px 0.5px 0px, black 0px 0px 0.03em, black 0px 0px 0.01em"},hide:!1},n.defaultLabels={valueLabel:n.defaultValueLabel,tickLabels:r.defaultTickLabels}},70749:function(t,n){var e,r;Object.defineProperty(n,"__esModule",{value:!0}),n.defaultPointer=n.defaultPointerRef=n.defaultPointerContext=n.PointerType=void 0,(r=e||(n.PointerType=e={})).Needle="needle",r.Blob="blob",r.Arrow="arrow",n.defaultPointerContext={centerPoint:[0,0],pointerRadius:0,pathLength:0,currentPercent:0,prevPercent:0,prevProgress:0,pathStr:"",shouldDrawPath:!1,prevColor:""},n.defaultPointerRef={element:void 0,path:void 0,context:n.defaultPointerContext},n.defaultPointer={type:e.Needle,color:"#5A5A5A",baseColor:"white",length:.7,width:20,animate:!0,elastic:!1,hide:!1,animationDuration:3e3,animationDelay:100,strokeWidth:8}},29475:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTickLabels=void 0,n.defaultTickLabels={type:"outer",hideMinMax:!1,ticks:[],defaultTickValueConfig:{formatTextValue:void 0,maxDecimalDigits:2,style:{fontSize:"10px",fill:"rgb(173 172 171)"},hide:!1},defaultTickLineConfig:{color:"rgb(173 172 171)",length:7,width:1,distanceFromArc:3,hide:!1}}},20568:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTooltipStyle=void 0,n.defaultTooltipStyle={borderColor:"#5A5A5A",borderStyle:"solid",borderWidth:"1px",borderRadius:"5px",color:"white",padding:"5px",fontSize:"15px",textShadow:"1px 1px 2px black, 0 0 1em black, 0 0 0.2em black"}},62884:function(t,n,e){var r,i=(r=e(49460))&&r.__esModule?r:{default:r};i.default,n.ZP=i.default},51130:function(t,n,e){let r,i,o,u;function a(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function c(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function f(t){let n,e,r;function i(t,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>e(t[n],r)?i=n+1:o=n}while(i<o)}return i}return 2!==t.length?(n=a,e=(n,e)=>a(t(n),e),r=(n,e)=>t(n)-e):(n=t===a||t===c?t:l,e=t,r=t),{left:i,center:function(t,n){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length,u=i(t,n,e,o-1);return u>e&&r(t[u-1],n)>-r(t[u],n)?u-1:u},right:function(t,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(i<o){if(0!==n(r,r))return o;do{let n=i+o>>>1;0>=e(t[n],r)?i=n+1:o=n}while(i<o)}return i}}}function l(){return 0}function s(t){return null===t?NaN:+t}e.r(n),e.d(n,{Adder:function(){return B},Delaunay:function(){return oK},FormatSpecifier:function(){return aI},InternMap:function(){return F},InternSet:function(){return G},Node:function(){return hn},Path:function(){return iG},Voronoi:function(){return oH},ZoomTransform:function(){return m8},active:function(){return rG},arc:function(){return bo},area:function(){return bd},areaRadial:function(){return bw},ascending:function(){return a},autoType:function(){return uy},axisBottom:function(){return nl},axisLeft:function(){return ns},axisRight:function(){return nf},axisTop:function(){return nc},bin:function(){return tx},bisect:function(){return x},bisectCenter:function(){return g},bisectLeft:function(){return p},bisectRight:function(){return d},bisector:function(){return f},blob:function(){return u5},blur:function(){return w},blur2:function(){return M},blurImage:function(){return T},brush:function(){return ic},brushSelection:function(){return io},brushX:function(){return iu},brushY:function(){return ia},buffer:function(){return u8},chord:function(){return im},chordDirected:function(){return ix},chordTranspose:function(){return i_},cluster:function(){return s3},color:function(){return em},contourDensity:function(){return oN},contours:function(){return o_},count:function(){return E},create:function(){return yU},creator:function(){return nZ},cross:function(){return O},csv:function(){return ae},csvFormat:function(){return ui},csvFormatBody:function(){return uo},csvFormatRow:function(){return ua},csvFormatRows:function(){return uu},csvFormatValue:function(){return uc},csvParse:function(){return ue},csvParseRows:function(){return ur},cubehelix:function(){return od},cumsum:function(){return D},curveBasis:function(){return mn},curveBasisClosed:function(){return mr},curveBasisOpen:function(){return mo},curveBumpX:function(){return bk},curveBumpY:function(){return bS},curveBundle:function(){return ma},curveCardinal:function(){return ml},curveCardinalClosed:function(){return mh},curveCardinalOpen:function(){return mp},curveCatmullRom:function(){return my},curveCatmullRomClosed:function(){return mm},curveCatmullRomOpen:function(){return mx},curveLinear:function(){return bf},curveLinearClosed:function(){return mM},curveMonotoneX:function(){return mC},curveMonotoneY:function(){return mP},curveNatural:function(){return mD},curveStep:function(){return mL},curveStepAfter:function(){return mB},curveStepBefore:function(){return mj},descending:function(){return c},deviation:function(){return L},difference:function(){return t5},disjoint:function(){return t4},dispatch:function(){return nv},drag:function(){return o3},dragDisable:function(){return en},dragEnable:function(){return ee},dsv:function(){return an},dsvFormat:function(){return ut},easeBack:function(){return u$},easeBackIn:function(){return uK},easeBackInOut:function(){return u$},easeBackOut:function(){return uQ},easeBounce:function(){return uW},easeBounceIn:function(){return uZ},easeBounceInOut:function(){return uJ},easeBounceOut:function(){return uW},easeCircle:function(){return uj},easeCircleIn:function(){return uz},easeCircleInOut:function(){return uj},easeCircleOut:function(){return uL},easeCubic:function(){return rV},easeCubicIn:function(){return rj},easeCubicInOut:function(){return rV},easeCubicOut:function(){return rB},easeElastic:function(){return u2},easeElasticIn:function(){return u1},easeElasticInOut:function(){return u6},easeElasticOut:function(){return u2},easeExp:function(){return uD},easeExpIn:function(){return uR},easeExpInOut:function(){return uD},easeExpOut:function(){return uO},easeLinear:function(){return um},easePoly:function(){return uA},easePolyIn:function(){return uM},easePolyInOut:function(){return uA},easePolyOut:function(){return uT},easeQuad:function(){return uw},easeQuadIn:function(){return u_},easeQuadInOut:function(){return uw},easeQuadOut:function(){return ux},easeSin:function(){return uC},easeSinIn:function(){return uN},easeSinInOut:function(){return uC},easeSinOut:function(){return uE},every:function(){return t$},extent:function(){return j},fcumsum:function(){return I},filter:function(){return t1},flatGroup:function(){return K},flatRollup:function(){return Q},forceCenter:function(){return as},forceCollide:function(){return aT},forceLink:function(){return aS},forceManyBody:function(){return aR},forceRadial:function(){return aO},forceSimulation:function(){return aP},forceX:function(){return aD},forceY:function(){return az},format:function(){return cb},formatDefaultLocale:function(){return aX},formatLocale:function(){return aH},formatPrefix:function(){return cm},formatSpecifier:function(){return aV},fsum:function(){return V},geoAlbers:function(){return sM},geoAlbersUsa:function(){return sT},geoArea:function(){return cR},geoAzimuthalEqualArea:function(){return sN},geoAzimuthalEqualAreaRaw:function(){return sS},geoAzimuthalEquidistant:function(){return sC},geoAzimuthalEquidistantRaw:function(){return sE},geoBounds:function(){return cK},geoCentroid:function(){return c9},geoCircle:function(){return fl},geoClipAntimeridian:function(){return fx},geoClipCircle:function(){return fw},geoClipExtent:function(){return fT},geoClipRectangle:function(){return fM},geoConicConformal:function(){return sL},geoConicConformalRaw:function(){return sz},geoConicEqualArea:function(){return sw},geoConicEqualAreaRaw:function(){return sx},geoConicEquidistant:function(){return sI},geoConicEquidistantRaw:function(){return sV},geoContains:function(){return fI},geoDistance:function(){return fR},geoEqualEarth:function(){return sq},geoEqualEarthRaw:function(){return sG},geoEquirectangular:function(){return sB},geoEquirectangularRaw:function(){return sj},geoGnomonic:function(){return sY},geoGnomonicRaw:function(){return sU},geoGraticule:function(){return fq},geoGraticule10:function(){return fU},geoIdentity:function(){return sH},geoInterpolate:function(){return fY},geoLength:function(){return fE},geoMercator:function(){return sR},geoMercatorRaw:function(){return sP},geoNaturalEarth1:function(){return sZ},geoNaturalEarth1Raw:function(){return sX},geoOrthographic:function(){return sJ},geoOrthographicRaw:function(){return sW},geoPath:function(){return so},geoProjection:function(){return sb},geoProjectionMutator:function(){return sm},geoRotation:function(){return fa},geoStereographic:function(){return sQ},geoStereographicRaw:function(){return sK},geoStream:function(){return cp},geoTransform:function(){return su},geoTransverseMercator:function(){return s0},geoTransverseMercatorRaw:function(){return s$},gray:function(){return ot},greatest:function(){return tN},greatestIndex:function(){return tY},group:function(){return Z},groupSort:function(){return tf},groups:function(){return W},hcl:function(){return of},hierarchy:function(){return s4},histogram:function(){return tx},hsl:function(){return eR},html:function(){return af},image:function(){return ai},index:function(){return tn},indexes:function(){return te},interpolate:function(){return e3},interpolateArray:function(){return eJ},interpolateBasis:function(){return eB},interpolateBasisClosed:function(){return eV},interpolateBlues:function(){return yy},interpolateBrBG:function(){return vV},interpolateBuGn:function(){return v6},interpolateBuPu:function(){return v5},interpolateCividis:function(){return yN},interpolateCool:function(){return yP},interpolateCubehelix:function(){return h7},interpolateCubehelixDefault:function(){return yE},interpolateCubehelixLong:function(){return h9},interpolateDate:function(){return eQ},interpolateDiscrete:function(){return hW},interpolateGnBu:function(){return v8},interpolateGreens:function(){return ym},interpolateGreys:function(){return yx},interpolateHcl:function(){return h5},interpolateHclLong:function(){return h4},interpolateHsl:function(){return h1},interpolateHslLong:function(){return h2},interpolateHue:function(){return hJ},interpolateInferno:function(){return yG},interpolateLab:function(){return h6},interpolateMagma:function(){return yF},interpolateNumber:function(){return e$},interpolateNumberArray:function(){return eZ},interpolateObject:function(){return e0},interpolateOrRd:function(){return v9},interpolateOranges:function(){return yS},interpolatePRGn:function(){return vF},interpolatePiYG:function(){return vq},interpolatePlasma:function(){return yq},interpolatePuBu:function(){return yr},interpolatePuBuGn:function(){return yn},interpolatePuOr:function(){return vY},interpolatePuRd:function(){return yo},interpolatePurples:function(){return yM},interpolateRainbow:function(){return yO},interpolateRdBu:function(){return vX},interpolateRdGy:function(){return vW},interpolateRdPu:function(){return ya},interpolateRdYlBu:function(){return vK},interpolateRdYlGn:function(){return v$},interpolateReds:function(){return yA},interpolateRgb:function(){return eU},interpolateRgbBasis:function(){return eH},interpolateRgbBasisClosed:function(){return eX},interpolateRound:function(){return hK},interpolateSinebow:function(){return yj},interpolateSpectral:function(){return v1},interpolateString:function(){return e6},interpolateTransformCss:function(){return rS},interpolateTransformSvg:function(){return rN},interpolateTurbo:function(){return yB},interpolateViridis:function(){return yI},interpolateWarm:function(){return yC},interpolateYlGn:function(){return ys},interpolateYlGnBu:function(){return yf},interpolateYlOrBr:function(){return yd},interpolateYlOrRd:function(){return yg},interpolateZoom:function(){return h$},interrupt:function(){return rw},intersection:function(){return t8},interval:function(){return m3},isoFormat:function(){return m2},isoParse:function(){return m6},json:function(){return au},lab:function(){return on},lch:function(){return oc},least:function(){return tq},leastIndex:function(){return tU},line:function(){return bh},lineRadial:function(){return bx},link:function(){return bP},linkHorizontal:function(){return bR},linkRadial:function(){return bD},linkVertical:function(){return bO},local:function(){return yH},map:function(){return t2},matcher:function(){return nw},max:function(){return tw},maxIndex:function(){return tM},mean:function(){return tD},median:function(){return tz},medianIndex:function(){return tL},merge:function(){return tj},min:function(){return tT},minIndex:function(){return tA},mode:function(){return tB},namespace:function(){return nL},namespaces:function(){return nz},nice:function(){return tm},now:function(){return ra},pack:function(){return hx},packEnclose:function(){return ha},packSiblings:function(){return hm},pairs:function(){return tV},partition:function(){return hS},path:function(){return iq},pathRound:function(){return iU},permute:function(){return to},pie:function(){return bv},piecewise:function(){return dt},pointRadial:function(){return bM},pointer:function(){return e4},pointers:function(){return yZ},polygonArea:function(){return de},polygonCentroid:function(){return dr},polygonContains:function(){return dc},polygonHull:function(){return da},polygonLength:function(){return df},precisionFixed:function(){return aZ},precisionPrefix:function(){return aW},precisionRound:function(){return aJ},quadtree:function(){return av},quantile:function(){return tE},quantileIndex:function(){return tP},quantileSorted:function(){return tC},quantize:function(){return dn},quickselect:function(){return tk},radialArea:function(){return bw},radialLine:function(){return bx},randomBates:function(){return dv},randomBernoulli:function(){return dm},randomBeta:function(){return dw},randomBinomial:function(){return dM},randomCauchy:function(){return dA},randomExponential:function(){return dy},randomGamma:function(){return dx},randomGeometric:function(){return d_},randomInt:function(){return dh},randomIrwinHall:function(){return dg},randomLcg:function(){return dE},randomLogNormal:function(){return dp},randomLogistic:function(){return dk},randomNormal:function(){return dd},randomPareto:function(){return db},randomPoisson:function(){return dS},randomUniform:function(){return ds},randomWeibull:function(){return dT},range:function(){return tF},rank:function(){return tG},reduce:function(){return t6},reverse:function(){return t3},rgb:function(){return eM},ribbon:function(){return i1},ribbonArrow:function(){return i2},rollup:function(){return $},rollups:function(){return tt},scaleBand:function(){return dD},scaleDiverging:function(){return function t(){var n=dH(vw()(dB));return n.copy=function(){return vm(n,t())},dP.apply(n,arguments)}},scaleDivergingLog:function(){return function t(){var n=d0(vw()).domain([.1,1,10]);return n.copy=function(){return vm(n,t()).base(n.base())},dP.apply(n,arguments)}},scaleDivergingPow:function(){return vM},scaleDivergingSqrt:function(){return vT},scaleDivergingSymlog:function(){return function t(){var n=d6(vw());return n.copy=function(){return vm(n,t()).constant(n.constant())},dP.apply(n,arguments)}},scaleIdentity:function(){return function t(n){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(n=Array.from(t,dL),r):n.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return t(n).unknown(e)},n=arguments.length?Array.from(n,dL):[0,1],dH(r)}},scaleImplicit:function(){return dR},scaleLinear:function(){return function t(){var n=dU();return n.copy=function(){return dG(n,t())},dC.apply(n,arguments),dH(n)}},scaleLog:function(){return function t(){let n=d0(dq()).domain([1,10]);return n.copy=()=>dG(n,t()).base(n.base()),dC.apply(n,arguments),n}},scaleOrdinal:function(){return dO},scalePoint:function(){return dz},scalePow:function(){return d7},scaleQuantile:function(){return function t(){var n,e=[],r=[],i=[];function o(){var t=0,n=Math.max(1,r.length);for(i=Array(n-1);++t<n;)i[t-1]=tC(e,t/n);return u}function u(t){return null==t||isNaN(t=+t)?n:r[x(i,t)]}return u.invertExtent=function(t){var n=r.indexOf(t);return n<0?[NaN,NaN]:[n>0?i[n-1]:e[0],n<i.length?i[n]:e[e.length-1]]},u.domain=function(t){if(!arguments.length)return e.slice();for(let n of(e=[],t))null==n||isNaN(n=+n)||e.push(n);return e.sort(a),o()},u.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},u.unknown=function(t){return arguments.length?(n=t,u):n},u.quantiles=function(){return i.slice()},u.copy=function(){return t().domain(e).range(r).unknown(n)},dC.apply(u,arguments)}},scaleQuantize:function(){return function t(){var n,e=0,r=1,i=1,o=[.5],u=[0,1];function a(t){return null!=t&&t<=t?u[x(o,t,0,i)]:n}function c(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*r-(t-i)*e)/(i+1);return a}return a.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},a.range=function(t){return arguments.length?(i=(u=Array.from(t)).length-1,c()):u.slice()},a.invertExtent=function(t){var n=u.indexOf(t);return n<0?[NaN,NaN]:n<1?[e,o[0]]:n>=i?[o[i-1],r]:[o[n-1],o[n]]},a.unknown=function(t){return arguments.length&&(n=t),a},a.thresholds=function(){return o.slice()},a.copy=function(){return t().domain([e,r]).range(u).unknown(n)},dC.apply(dH(a),arguments)}},scaleRadial:function(){return function t(){var n,e=dU(),r=[0,1],i=!1;function o(t){var r,o=Math.sign(r=e(t))*Math.sqrt(Math.abs(r));return isNaN(o)?n:i?Math.round(o):o}return o.invert=function(t){return e.invert(pt(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((r=Array.from(t,dL)).map(pt)),o):r.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t(e.domain(),r).round(i).clamp(e.clamp()).unknown(n)},dC.apply(o,arguments),dH(o)}},scaleSequential:function(){return function t(){var n=dH(vb()(dB));return n.copy=function(){return vm(n,t())},dP.apply(n,arguments)}},scaleSequentialLog:function(){return function t(){var n=d0(vb()).domain([1,10]);return n.copy=function(){return vm(n,t()).base(n.base())},dP.apply(n,arguments)}},scaleSequentialPow:function(){return v_},scaleSequentialQuantile:function(){return function t(){var n=[],e=dB;function r(t){if(null!=t&&!isNaN(t=+t))return e((x(n,t,1)-1)/(n.length-1))}return r.domain=function(t){if(!arguments.length)return n.slice();for(let e of(n=[],t))null==e||isNaN(e=+e)||n.push(e);return n.sort(a),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return n.map((t,r)=>e(r/(n.length-1)))},r.quantiles=function(t){return Array.from({length:t+1},(e,r)=>tE(n,r/t))},r.copy=function(){return t(e).domain(n)},dP.apply(r,arguments)}},scaleSequentialSqrt:function(){return vx},scaleSequentialSymlog:function(){return function t(){var n=d6(vb());return n.copy=function(){return vm(n,t()).constant(n.constant())},dP.apply(n,arguments)}},scaleSqrt:function(){return d9},scaleSymlog:function(){return function t(){var n=d6(dq());return n.copy=function(){return dG(n,t()).constant(n.constant())},dC.apply(n,arguments)}},scaleThreshold:function(){return function t(){var n,e=[.5],r=[0,1],i=1;function o(t){return null!=t&&t<=t?r[x(e,t,0,i)]:n}return o.domain=function(t){return arguments.length?(i=Math.min((e=Array.from(t)).length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),i=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t().domain(e).range(r).unknown(n)},dC.apply(o,arguments)}},scaleTime:function(){return vv},scaleUtc:function(){return vy},scan:function(){return tH},schemeAccent:function(){return vS},schemeBlues:function(){return yv},schemeBrBG:function(){return vB},schemeBuGn:function(){return v2},schemeBuPu:function(){return v3},schemeCategory10:function(){return vk},schemeDark2:function(){return vN},schemeGnBu:function(){return v4},schemeGreens:function(){return yb},schemeGreys:function(){return y_},schemeObservable10:function(){return vE},schemeOrRd:function(){return v7},schemeOranges:function(){return yk},schemePRGn:function(){return vI},schemePaired:function(){return vC},schemePastel1:function(){return vP},schemePastel2:function(){return vR},schemePiYG:function(){return vG},schemePuBu:function(){return ye},schemePuBuGn:function(){return yt},schemePuOr:function(){return vU},schemePuRd:function(){return yi},schemePurples:function(){return yw},schemeRdBu:function(){return vH},schemeRdGy:function(){return vZ},schemeRdPu:function(){return yu},schemeRdYlBu:function(){return vJ},schemeRdYlGn:function(){return vQ},schemeReds:function(){return yT},schemeSet1:function(){return vO},schemeSet2:function(){return vD},schemeSet3:function(){return vz},schemeSpectral:function(){return v0},schemeTableau10:function(){return vL},schemeYlGn:function(){return yl},schemeYlGnBu:function(){return yc},schemeYlOrBr:function(){return yh},schemeYlOrRd:function(){return yp},select:function(){return n4},selectAll:function(){return yW},selection:function(){return n5},selector:function(){return nb},selectorAll:function(){return nx},shuffle:function(){return tX},shuffler:function(){return tZ},some:function(){return t0},sort:function(){return tu},stack:function(){return mq},stackOffsetDiverging:function(){return mY},stackOffsetExpand:function(){return mU},stackOffsetNone:function(){return mV},stackOffsetSilhouette:function(){return mH},stackOffsetWiggle:function(){return mX},stackOrderAppearance:function(){return mZ},stackOrderAscending:function(){return mJ},stackOrderDescending:function(){return mQ},stackOrderInsideOut:function(){return m$},stackOrderNone:function(){return mI},stackOrderReverse:function(){return m0},stratify:function(){return hO},style:function(){return nB},subset:function(){return nn},sum:function(){return tW},superset:function(){return t9},svg:function(){return al},symbol:function(){return b8},symbolAsterisk:function(){return bL},symbolCircle:function(){return bj},symbolCross:function(){return bB},symbolDiamond:function(){return bF},symbolDiamond2:function(){return bG},symbolPlus:function(){return bq},symbolSquare:function(){return bU},symbolSquare2:function(){return bY},symbolStar:function(){return bW},symbolTimes:function(){return b3},symbolTriangle:function(){return bK},symbolTriangle2:function(){return b$},symbolWye:function(){return b6},symbolX:function(){return b3},symbols:function(){return b5},symbolsFill:function(){return b5},symbolsStroke:function(){return b4},text:function(){return u9},thresholdFreedmanDiaconis:function(){return tR},thresholdScott:function(){return tO},thresholdSturges:function(){return t_},tickFormat:function(){return dY},tickIncrement:function(){return ty},tickStep:function(){return tb},ticks:function(){return tv},timeDay:function(){return pv},timeDays:function(){return py},timeFormat:function(){return y},timeFormatDefaultLocale:function(){return vh},timeFormatLocale:function(){return gi},timeFriday:function(){return pN},timeFridays:function(){return pz},timeHour:function(){return ph},timeHours:function(){return pd},timeInterval:function(){return pr},timeMillisecond:function(){return pi},timeMilliseconds:function(){return po},timeMinute:function(){return pc},timeMinutes:function(){return pf},timeMonday:function(){return pT},timeMondays:function(){return pP},timeMonth:function(){return pQ},timeMonths:function(){return p$},timeParse:function(){return b},timeSaturday:function(){return pE},timeSaturdays:function(){return pL},timeSecond:function(){return pu},timeSeconds:function(){return pa},timeSunday:function(){return pM},timeSundays:function(){return pC},timeThursday:function(){return pS},timeThursdays:function(){return pD},timeTickInterval:function(){return gt},timeTicks:function(){return p9},timeTuesday:function(){return pA},timeTuesdays:function(){return pR},timeWednesday:function(){return pk},timeWednesdays:function(){return pO},timeWeek:function(){return pM},timeWeeks:function(){return pC},timeYear:function(){return p2},timeYears:function(){return p6},timeout:function(){return rg},timer:function(){return rl},timerFlush:function(){return rs},transition:function(){return rz},transpose:function(){return tJ},tree:function(){return hI},treemap:function(){return hY},treemapBinary:function(){return hH},treemapDice:function(){return hk},treemapResquarify:function(){return hZ},treemapSlice:function(){return hF},treemapSliceDice:function(){return hX},treemapSquarify:function(){return hU},tsv:function(){return ar},tsvFormat:function(){return uh},tsvFormatBody:function(){return ud},tsvFormatRow:function(){return ug},tsvFormatRows:function(){return up},tsvFormatValue:function(){return uv},tsvParse:function(){return ul},tsvParseRows:function(){return us},union:function(){return ne},unixDay:function(){return p_},unixDays:function(){return px},utcDay:function(){return pb},utcDays:function(){return pm},utcFormat:function(){return m},utcFriday:function(){return pq},utcFridays:function(){return pJ},utcHour:function(){return pp},utcHours:function(){return pg},utcMillisecond:function(){return pi},utcMilliseconds:function(){return po},utcMinute:function(){return pl},utcMinutes:function(){return ps},utcMonday:function(){return pV},utcMondays:function(){return pH},utcMonth:function(){return p0},utcMonths:function(){return p1},utcParse:function(){return _},utcSaturday:function(){return pU},utcSaturdays:function(){return pK},utcSecond:function(){return pu},utcSeconds:function(){return pa},utcSunday:function(){return pB},utcSundays:function(){return pY},utcThursday:function(){return pG},utcThursdays:function(){return pW},utcTickInterval:function(){return p7},utcTicks:function(){return p8},utcTuesday:function(){return pI},utcTuesdays:function(){return pX},utcWednesday:function(){return pF},utcWednesdays:function(){return pZ},utcWeek:function(){return pB},utcWeeks:function(){return pY},utcYear:function(){return p3},utcYears:function(){return p5},variance:function(){return z},window:function(){return nj},xml:function(){return ac},zip:function(){return tQ},zoom:function(){return _c},zoomIdentity:function(){return m7},zoomTransform:function(){return m9}});let h=f(a),d=h.right,p=h.left,g=f(s).center;var v,y,b,m,_,x=d;function w(t,n){if(!((n=+n)>=0))throw RangeError("invalid r");let e=t.length;if(!((e=Math.floor(e))>=0))throw RangeError("invalid length");if(!e||!n)return t;let r=N(n),i=t.slice();return r(t,i,0,e,1),r(i,t,0,e,1),r(t,i,0,e,1),t}let M=A(N),T=A(function(t){let n=N(t);return(t,e,r,i,o)=>{n(t,e,(r<<=2)+0,(i<<=2)+0,o<<=2),n(t,e,r+1,i+1,o),n(t,e,r+2,i+2,o),n(t,e,r+3,i+3,o)}});function A(t){return function(n,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;if(!((e=+e)>=0))throw RangeError("invalid rx");if(!((r=+r)>=0))throw RangeError("invalid ry");let{data:i,width:o,height:u}=n;if(!((o=Math.floor(o))>=0))throw RangeError("invalid width");if(!((u=Math.floor(void 0!==u?u:i.length/o))>=0))throw RangeError("invalid height");if(!o||!u||!e&&!r)return n;let a=e&&t(e),c=r&&t(r),f=i.slice();return a&&c?(k(a,f,i,o,u),k(a,i,f,o,u),k(a,f,i,o,u),S(c,i,f,o,u),S(c,f,i,o,u),S(c,i,f,o,u)):a?(k(a,i,f,o,u),k(a,f,i,o,u),k(a,i,f,o,u)):c&&(S(c,i,f,o,u),S(c,f,i,o,u),S(c,i,f,o,u)),n}}function k(t,n,e,r,i){for(let o=0,u=r*i;o<u;)t(n,e,o,o+=r,1)}function S(t,n,e,r,i){for(let o=0,u=r*i;o<r;++o)t(n,e,o,o+u,r)}function N(t){let n=Math.floor(t);if(n===t)return function(t){let n=2*t+1;return(e,r,i,o,u)=>{if(!((o-=u)>=i))return;let a=t*r[i],c=u*t;for(let t=i,n=i+c;t<n;t+=u)a+=r[Math.min(o,t)];for(let t=i,f=o;t<=f;t+=u)a+=r[Math.min(o,t+c)],e[t]=a/n,a-=r[Math.max(i,t-c)]}}(t);let e=t-n,r=2*t+1;return(t,i,o,u,a)=>{if(!((u-=a)>=o))return;let c=n*i[o],f=a*n,l=f+a;for(let t=o,n=o+f;t<n;t+=a)c+=i[Math.min(u,t)];for(let n=o,s=u;n<=s;n+=a)c+=i[Math.min(u,n+f)],t[n]=(c+e*(i[Math.max(o,n-l)]+i[Math.min(u,n+l)]))/r,c-=i[Math.max(o,n-f)]}}function E(t,n){let e=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&++e;else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(i=+i)>=i&&++e}return e}function C(t){return 0|t.length}function P(t){return!(t>0)}function R(t){return"object"!=typeof t||"length"in t?t:Array.from(t)}function O(){for(var t,n=arguments.length,e=Array(n),r=0;r<n;r++)e[r]=arguments[r];let i="function"==typeof e[e.length-1]&&(t=e.pop(),n=>t(...n)),o=(e=e.map(R)).map(C),u=e.length-1,a=Array(u+1).fill(0),c=[];if(u<0||o.some(P))return c;for(;;){c.push(a.map((t,n)=>e[n][t]));let t=u;for(;++a[t]===o[t];){if(0===t)return i?c.map(i):c;a[t--]=0}}}function D(t,n){var e=0,r=0;return Float64Array.from(t,void 0===n?t=>e+=+t||0:i=>e+=+n(i,r++,t)||0)}function z(t,n){let e,r=0,i=0,o=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(e=n-i,i+=e/++r,o+=e*(n-i));else{let u=-1;for(let a of t)null!=(a=n(a,++u,t))&&(a=+a)>=a&&(e=a-i,i+=e/++r,o+=e*(a-i))}if(r>1)return o/(r-1)}function L(t,n){let e=z(t,n);return e?Math.sqrt(e):e}function j(t,n){let e,r;if(void 0===n)for(let n of t)null!=n&&(void 0===e?n>=n&&(e=r=n):(e>n&&(e=n),r<n&&(r=n)));else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(void 0===e?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}class B{add(t){let n=this._partials,e=0;for(let r=0;r<this._n&&r<32;r++){let i=n[r],o=t+i,u=Math.abs(t)<Math.abs(i)?t-(o-i):i-(o-t);u&&(n[e++]=u),t=o}return n[e]=t,this._n=e+1,this}valueOf(){let t=this._partials,n=this._n,e,r,i,o=0;if(n>0){for(o=t[--n];n>0&&(o=(e=o)+(r=t[--n]),!(i=r-(o-e))););n>0&&(i<0&&t[n-1]<0||i>0&&t[n-1]>0)&&(e=o+(r=2*i),r==e-o&&(o=e))}return o}constructor(){this._partials=new Float64Array(32),this._n=0}}function V(t,n){let e=new B;if(void 0===n)for(let n of t)(n=+n)&&e.add(n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&e.add(i)}return+e}function I(t,n){let e=new B,r=-1;return Float64Array.from(t,void 0===n?t=>e.add(+t||0):i=>e.add(+n(i,++r,t)||0))}class F extends Map{get(t){return super.get(q(this,t))}has(t){return super.has(q(this,t))}set(t,n){return super.set(U(this,t),n)}delete(t){return super.delete(Y(this,t))}constructor(t,n=H){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(let[n,e]of t)this.set(n,e)}}class G extends Set{has(t){return super.has(q(this,t))}add(t){return super.add(U(this,t))}delete(t){return super.delete(Y(this,t))}constructor(t,n=H){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(let n of t)this.add(n)}}function q(t,n){let{_intern:e,_key:r}=t,i=r(n);return e.has(i)?e.get(i):n}function U(t,n){let{_intern:e,_key:r}=t,i=r(n);return e.has(i)?e.get(i):(e.set(i,n),n)}function Y(t,n){let{_intern:e,_key:r}=t,i=r(n);return e.has(i)&&(n=e.get(i),e.delete(i)),n}function H(t){return null!==t&&"object"==typeof t?t.valueOf():t}function X(t){return t}function Z(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return ti(t,X,X,e)}function W(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return ti(t,Array.from,X,e)}function J(t,n){for(let e=1,r=n.length;e<r;++e)t=t.flatMap(t=>t.pop().map(n=>{let[e,r]=n;return[...t,e,r]}));return t}function K(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return J(W(t,...e),e)}function Q(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),i=2;i<e;i++)r[i-2]=arguments[i];return J(tt(t,n,...r),r)}function $(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),i=2;i<e;i++)r[i-2]=arguments[i];return ti(t,X,n,r)}function tt(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),i=2;i<e;i++)r[i-2]=arguments[i];return ti(t,Array.from,n,r)}function tn(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return ti(t,X,tr,e)}function te(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return ti(t,Array.from,tr,e)}function tr(t){if(1!==t.length)throw Error("duplicate key");return t[0]}function ti(t,n,e,r){return function t(i,o){if(o>=r.length)return e(i);let u=new F,a=r[o++],c=-1;for(let t of i){let n=a(t,++c,i),e=u.get(n);e?e.push(t):u.set(n,[t])}for(let[n,e]of u)u.set(n,t(e,o));return n(u)}(t,0)}function to(t,n){return Array.from(n,n=>t[n])}function tu(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");t=Array.from(t);let[i]=e;if(i&&2!==i.length||e.length>1){let n=Uint32Array.from(t,(t,n)=>n);return e.length>1?(e=e.map(n=>t.map(n)),n.sort((t,n)=>{for(let r of e){let e=tc(r[t],r[n]);if(e)return e}})):(i=t.map(i),n.sort((t,n)=>tc(i[t],i[n]))),to(t,n)}return t.sort(ta(i))}function ta(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;if(t===a)return tc;if("function"!=typeof t)throw TypeError("compare is not a function");return(n,e)=>{let r=t(n,e);return r||0===r?r:(0===t(e,e))-(0===t(n,n))}}function tc(t,n){return(null==t||!(t>=t))-(null==n||!(n>=n))||(t<n?-1:t>n?1:0)}function tf(t,n,e){return(2!==n.length?tu($(t,n,e),(t,n)=>{let[e,r]=t,[i,o]=n;return a(r,o)||a(e,i)}):tu(Z(t,e),(t,e)=>{let[r,i]=t,[o,u]=e;return n(i,u)||a(r,o)})).map(t=>{let[n]=t;return n})}var tl=Array.prototype,ts=tl.slice;tl.map;let th=Math.sqrt(50),td=Math.sqrt(10),tp=Math.sqrt(2);function tg(t,n,e){let r,i,o;let u=(n-t)/Math.max(0,e),a=Math.floor(Math.log10(u)),c=u/Math.pow(10,a),f=c>=th?10:c>=td?5:c>=tp?2:1;return(a<0?(r=Math.round(t*(o=Math.pow(10,-a)/f)),i=Math.round(n*o),r/o<t&&++r,i/o>n&&--i,o=-o):(r=Math.round(t/(o=Math.pow(10,a)*f)),i=Math.round(n/o),r*o<t&&++r,i*o>n&&--i),i<r&&.5<=e&&e<2)?tg(t,n,2*e):[r,i,o]}function tv(t,n,e){if(n=+n,t=+t,!((e=+e)>0))return[];if(t===n)return[t];let r=n<t,[i,o,u]=r?tg(n,t,e):tg(t,n,e);if(!(o>=i))return[];let a=o-i+1,c=Array(a);if(r){if(u<0)for(let t=0;t<a;++t)c[t]=-((o-t)/u);else for(let t=0;t<a;++t)c[t]=(o-t)*u}else if(u<0)for(let t=0;t<a;++t)c[t]=-((i+t)/u);else for(let t=0;t<a;++t)c[t]=(i+t)*u;return c}function ty(t,n,e){return tg(t=+t,n=+n,e=+e)[2]}function tb(t,n,e){n=+n,t=+t,e=+e;let r=n<t,i=r?ty(n,t,e):ty(t,n,e);return(r?-1:1)*(i<0?-(1/i):i)}function tm(t,n,e){let r;for(;;){let i=ty(t,n,e);if(i===r||0===i||!isFinite(i))return[t,n];i>0?(t=Math.floor(t/i)*i,n=Math.ceil(n/i)*i):i<0&&(t=Math.ceil(t*i)/i,n=Math.floor(n*i)/i),r=i}}function t_(t){return Math.max(1,Math.ceil(Math.log(E(t))/Math.LN2)+1)}function tx(){var t=X,n=j,e=t_;function r(r){Array.isArray(r)||(r=Array.from(r));var i,o,u,a=r.length,c=Array(a);for(i=0;i<a;++i)c[i]=t(r[i],i,r);var f=n(c),l=f[0],s=f[1],h=e(c,l,s);if(!Array.isArray(h)){let t=s,e=+h;if(n===j&&([l,s]=tm(l,s,e)),(h=tv(l,s,e))[0]<=l&&(u=ty(l,s,e)),h[h.length-1]>=s){if(t>=s&&n===j){let t=ty(l,s,e);isFinite(t)&&(t>0?s=(Math.floor(s/t)+1)*t:t<0&&(s=-((Math.ceil(-(s*t))+1)/t)))}else h.pop()}}for(var d=h.length,p=0,g=d;h[p]<=l;)++p;for(;h[g-1]>s;)--g;(p||g<d)&&(h=h.slice(p,g),d=g-p);var v,y=Array(d+1);for(i=0;i<=d;++i)(v=y[i]=[]).x0=i>0?h[i-1]:l,v.x1=i<d?h[i]:s;if(isFinite(u)){if(u>0)for(i=0;i<a;++i)null!=(o=c[i])&&l<=o&&o<=s&&y[Math.min(d,Math.floor((o-l)/u))].push(r[i]);else if(u<0){for(i=0;i<a;++i)if(null!=(o=c[i])&&l<=o&&o<=s){let t=Math.floor((l-o)*u);y[Math.min(d,t+(h[t]<=o))].push(r[i])}}}else for(i=0;i<a;++i)null!=(o=c[i])&&l<=o&&o<=s&&y[x(h,o,0,d)].push(r[i]);return y}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:()=>n,r):t},r.domain=function(t){var e;return arguments.length?(n="function"==typeof t?t:(e=[t[0],t[1]],()=>e),r):n},r.thresholds=function(t){var n;return arguments.length?(e="function"==typeof t?t:(n=Array.isArray(t)?ts.call(t):t,()=>n),r):e},r}function tw(t,n){let e;if(void 0===n)for(let n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e<i||void 0===e&&i>=i)&&(e=i)}return e}function tM(t,n){let e;let r=-1,i=-1;if(void 0===n)for(let n of t)++i,null!=n&&(e<n||void 0===e&&n>=n)&&(e=n,r=i);else for(let o of t)null!=(o=n(o,++i,t))&&(e<o||void 0===e&&o>=o)&&(e=o,r=i);return r}function tT(t,n){let e;if(void 0===n)for(let n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e>i||void 0===e&&i>=i)&&(e=i)}return e}function tA(t,n){let e;let r=-1,i=-1;if(void 0===n)for(let n of t)++i,null!=n&&(e>n||void 0===e&&n>=n)&&(e=n,r=i);else for(let o of t)null!=(o=n(o,++i,t))&&(e>o||void 0===e&&o>=o)&&(e=o,r=i);return r}function tk(t,n){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,i=arguments.length>4?arguments[4]:void 0;if(n=Math.floor(n),e=Math.floor(Math.max(0,e)),r=Math.floor(Math.min(t.length-1,r)),!(e<=n&&n<=r))return t;for(i=void 0===i?tc:ta(i);r>e;){if(r-e>600){let o=r-e+1,u=n-e+1,a=Math.log(o),c=.5*Math.exp(2*a/3),f=.5*Math.sqrt(a*c*(o-c)/o)*(u-o/2<0?-1:1),l=Math.max(e,Math.floor(n-u*c/o+f)),s=Math.min(r,Math.floor(n+(o-u)*c/o+f));tk(t,n,l,s,i)}let o=t[n],u=e,a=r;for(tS(t,e,n),i(t[r],o)>0&&tS(t,e,r);u<a;){for(tS(t,u,a),++u,--a;0>i(t[u],o);)++u;for(;i(t[a],o)>0;)--a}0===i(t[e],o)?tS(t,e,a):tS(t,++a,r),a<=n&&(e=a+1),n<=a&&(r=a-1)}return t}function tS(t,n,e){let r=t[n];t[n]=t[e],t[e]=r}function tN(t){let n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=!1;if(1===e.length){let i;for(let o of t){let t=e(o);(r?a(t,i)>0:0===a(t,t))&&(n=o,i=t,r=!0)}}else for(let i of t)(r?e(i,n)>0:0===e(i,i))&&(n=i,r=!0);return n}function tE(t,n,e){if(!(!(r=(t=Float64Array.from(function*(t,n){if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(yield n);else{let e=-1;for(let r of t)null!=(r=n(r,++e,t))&&(r=+r)>=r&&(yield r)}}(t,e))).length)||isNaN(n=+n))){if(n<=0||r<2)return tT(t);if(n>=1)return tw(t);var r,i=(r-1)*n,o=Math.floor(i),u=tw(tk(t,o).subarray(0,o+1));return u+(tT(t.subarray(o+1))-u)*(i-o)}}function tC(t,n){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(!(!(r=t.length)||isNaN(n=+n))){if(n<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),u=+e(t[o],o,t);return u+(+e(t[o+1],o+1,t)-u)*(i-o)}}function tP(t,n){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s;if(!isNaN(n=+n)){if(r=Float64Array.from(t,(n,r)=>s(e(t[r],r,t))),n<=0)return tA(r);if(n>=1)return tM(r);var r,i=Uint32Array.from(t,(t,n)=>n),o=r.length-1,u=Math.floor(o*n);return tk(i,u,0,o,(t,n)=>tc(r[t],r[n])),(u=tN(i.subarray(0,u+1),t=>r[t]))>=0?u:-1}}function tR(t,n,e){let r=E(t),i=tE(t,.75)-tE(t,.25);return r&&i?Math.ceil((e-n)/(2*i*Math.pow(r,-1/3))):1}function tO(t,n,e){let r=E(t),i=L(t);return r&&i?Math.ceil((e-n)*Math.cbrt(r)/(3.49*i)):1}function tD(t,n){let e=0,r=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(++e,r+=n);else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}function tz(t,n){return tE(t,.5,n)}function tL(t,n){return tP(t,.5,n)}function tj(t){return Array.from(function*(t){for(let n of t)yield*n}(t))}function tB(t,n){let e;let r=new F;if(void 0===n)for(let n of t)null!=n&&n>=n&&r.set(n,(r.get(n)||0)+1);else{let e=-1;for(let i of t)null!=(i=n(i,++e,t))&&i>=i&&r.set(i,(r.get(i)||0)+1)}let i=0;for(let[t,n]of r)n>i&&(i=n,e=t);return e}function tV(t){let n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:tI,r=[],i=!1;for(let o of t)i&&r.push(e(n,o)),n=o,i=!0;return r}function tI(t,n){return[t,n]}function tF(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=Array(i);++r<i;)o[r]=t+r*e;return o}function tG(t){let n,e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");let i=Array.from(t),o=new Float64Array(i.length);2!==r.length&&(i=i.map(r),r=a);let u=(t,n)=>r(i[t],i[n]);return(t=Uint32Array.from(i,(t,n)=>n)).sort(r===a?(t,n)=>tc(i[t],i[n]):ta(u)),t.forEach((t,r)=>{let i=u(t,void 0===n?t:n);i>=0?((void 0===n||i>0)&&(n=t,e=r),o[t]=e):o[t]=NaN}),o}function tq(t){let n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=!1;if(1===e.length){let i;for(let o of t){let t=e(o);(r?0>a(t,i):0===a(t,t))&&(n=o,i=t,r=!0)}}else for(let i of t)(r?0>e(i,n):0===e(i,i))&&(n=i,r=!0);return n}function tU(t){let n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;if(1===e.length)return tA(t,e);let r=-1,i=-1;for(let o of t)++i,(r<0?0===e(o,o):0>e(o,n))&&(n=o,r=i);return r}function tY(t){let n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;if(1===e.length)return tM(t,e);let r=-1,i=-1;for(let o of t)++i,(r<0?0===e(o,o):e(o,n)>0)&&(n=o,r=i);return r}function tH(t,n){let e=tU(t,n);return e<0?void 0:e}var tX=tZ(Math.random);function tZ(t){return function(n){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n.length,i=r-(e=+e);for(;i;){let r=t()*i--|0,o=n[i+e];n[i+e]=n[r+e],n[r+e]=o}return n}}function tW(t,n){let e=0;if(void 0===n)for(let n of t)(n=+n)&&(e+=n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&(e+=i)}return e}function tJ(t){if(!(i=t.length))return[];for(var n=-1,e=tT(t,tK),r=Array(e);++n<e;)for(var i,o=-1,u=r[n]=Array(i);++o<i;)u[o]=t[o][n];return r}function tK(t){return t.length}function tQ(){return tJ(arguments)}function t$(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(!n(r,++e,t))return!1;return!0}function t0(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(n(r,++e,t))return!0;return!1}function t1(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=[],r=-1;for(let i of t)n(i,++r,t)&&e.push(i);return e}function t2(t,n){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");if("function"!=typeof n)throw TypeError("mapper is not a function");return Array.from(t,(e,r)=>n(e,r,t))}function t6(t,n,e){if("function"!=typeof n)throw TypeError("reducer is not a function");let r=t[Symbol.iterator](),i,o,u=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++u}for(;{done:i,value:o}=r.next(),!i;)e=n(e,o,++u,t);return e}function t3(t){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");return Array.from(t).reverse()}function t5(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];for(let n of(t=new G(t),e))for(let e of n)t.delete(e);return t}function t4(t,n){let e=n[Symbol.iterator](),r=new G;for(let n of t){let t,i;if(r.has(n))return!1;for(;({value:t,done:i}=e.next())&&!i;){if(Object.is(n,t))return!1;r.add(t)}}return!0}function t8(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];t=new G(t),e=e.map(t7);t:for(let n of t)for(let r of e)if(!r.has(n)){t.delete(n);continue t}return t}function t7(t){return t instanceof G?t:new G(t)}function t9(t,n){let e=t[Symbol.iterator](),r=new Set;for(let t of n){let n,i;let o=nt(t);if(!r.has(o))for(;{value:n,done:i}=e.next();){if(i)return!1;let t=nt(n);if(r.add(t),Object.is(o,t))break}}return!0}function nt(t){return null!==t&&"object"==typeof t?t.valueOf():t}function nn(t,n){return t9(n,t)}function ne(){for(var t=arguments.length,n=Array(t),e=0;e<t;e++)n[e]=arguments[e];let r=new G;for(let t of n)for(let n of t)r.add(n);return r}function nr(t){return t}function ni(t){return"translate("+t+",0)"}function no(t){return"translate(0,"+t+")"}function nu(){return!this.__axis}function na(t,n){var e=[],r=null,i=null,o=6,u=6,a=3,c="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,f=1===t||4===t?-1:1,l=4===t||2===t?"x":"y",s=1===t||3===t?ni:no;function h(h){var d=null==r?n.ticks?n.ticks.apply(n,e):n.domain():r,p=null==i?n.tickFormat?n.tickFormat.apply(n,e):nr:i,g=Math.max(o,0)+a,v=n.range(),y=+v[0]+c,b=+v[v.length-1]+c,m=(n.bandwidth?function(t,n){return n=Math.max(0,t.bandwidth()-2*n)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}:function(t){return n=>+t(n)})(n.copy(),c),_=h.selection?h.selection():h,x=_.selectAll(".domain").data([null]),w=_.selectAll(".tick").data(d,n).order(),M=w.exit(),T=w.enter().append("g").attr("class","tick"),A=w.select("line"),k=w.select("text");x=x.merge(x.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),w=w.merge(T),A=A.merge(T.append("line").attr("stroke","currentColor").attr(l+"2",f*o)),k=k.merge(T.append("text").attr("fill","currentColor").attr(l,f*g).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),h!==_&&(x=x.transition(h),w=w.transition(h),A=A.transition(h),k=k.transition(h),M=M.transition(h).attr("opacity",1e-6).attr("transform",function(t){return isFinite(t=m(t))?s(t+c):this.getAttribute("transform")}),T.attr("opacity",1e-6).attr("transform",function(t){var n=this.parentNode.__axis;return s((n&&isFinite(n=n(t))?n:m(t))+c)})),M.remove(),x.attr("d",4===t||2===t?u?"M"+f*u+","+y+"H"+c+"V"+b+"H"+f*u:"M"+c+","+y+"V"+b:u?"M"+y+","+f*u+"V"+c+"H"+b+"V"+f*u:"M"+y+","+c+"H"+b),w.attr("opacity",1).attr("transform",function(t){return s(m(t)+c)}),A.attr(l+"2",f*o),k.attr(l,f*g).text(p),_.filter(nu).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),_.each(function(){this.__axis=m})}return h.scale=function(t){return arguments.length?(n=t,h):n},h.ticks=function(){return e=Array.from(arguments),h},h.tickArguments=function(t){return arguments.length?(e=null==t?[]:Array.from(t),h):e.slice()},h.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),h):r&&r.slice()},h.tickFormat=function(t){return arguments.length?(i=t,h):i},h.tickSize=function(t){return arguments.length?(o=u=+t,h):o},h.tickSizeInner=function(t){return arguments.length?(o=+t,h):o},h.tickSizeOuter=function(t){return arguments.length?(u=+t,h):u},h.tickPadding=function(t){return arguments.length?(a=+t,h):a},h.offset=function(t){return arguments.length?(c=+t,h):c},h}function nc(t){return na(1,t)}function nf(t){return na(2,t)}function nl(t){return na(3,t)}function ns(t){return na(4,t)}var nh={value:()=>{}};function nd(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new np(r)}function np(t){this._=t}function ng(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=nh,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}np.prototype=nd.prototype={constructor:np,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<u;)if(e=(t=i[o]).type)r[e]=ng(r[e],t.name,n);else if(null==n)for(e in r)r[e]=ng(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new np(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var nv=nd;function ny(){}function nb(t){return null==t?ny:function(){return this.querySelector(t)}}function nm(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function n_(){return[]}function nx(t){return null==t?n_:function(){return this.querySelectorAll(t)}}function nw(t){return function(){return this.matches(t)}}function nM(t){return function(n){return n.matches(t)}}var nT=Array.prototype.find;function nA(){return this.firstElementChild}var nk=Array.prototype.filter;function nS(){return Array.from(this.children)}function nN(t){return Array(t.length)}function nE(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function nC(t,n,e,r,i,o){for(var u,a=0,c=n.length,f=o.length;a<f;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new nE(t,o[a]);for(;a<c;++a)(u=n[a])&&(i[a]=u)}function nP(t,n,e,r,i,o,u){var a,c,f,l=new Map,s=n.length,h=o.length,d=Array(s);for(a=0;a<s;++a)(c=n[a])&&(d[a]=f=u.call(c,c.__data__,a,n)+"",l.has(f)?i[a]=c:l.set(f,c));for(a=0;a<h;++a)f=u.call(t,o[a],a,o)+"",(c=l.get(f))?(r[a]=c,c.__data__=o[a],l.delete(f)):e[a]=new nE(t,o[a]);for(a=0;a<s;++a)(c=n[a])&&l.get(d[a])===c&&(i[a]=c)}function nR(t){return t.__data__}function nO(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}nE.prototype={constructor:nE,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var nD="http://www.w3.org/1999/xhtml",nz={svg:"http://www.w3.org/2000/svg",xhtml:nD,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function nL(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),nz.hasOwnProperty(n)?{space:nz[n],local:t}:t}function nj(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function nB(t,n){return t.style.getPropertyValue(n)||nj(t).getComputedStyle(t,null).getPropertyValue(n)}function nV(t){return t.trim().split(/^|\s+/)}function nI(t){return t.classList||new nF(t)}function nF(t){this._node=t,this._names=nV(t.getAttribute("class")||"")}function nG(t,n){for(var e=nI(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function nq(t,n){for(var e=nI(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function nU(){this.textContent=""}function nY(){this.innerHTML=""}function nH(){this.nextSibling&&this.parentNode.appendChild(this)}function nX(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function nZ(t){var n=nL(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===nD&&n.documentElement.namespaceURI===nD?n.createElement(t):n.createElementNS(e,t)}})(n)}function nW(){return null}function nJ(){var t=this.parentNode;t&&t.removeChild(this)}function nK(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function nQ(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function n$(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function n0(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var u=0,a=i.length;u<a;++u)if((r=i[u]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function n1(t,n,e){var r=nj(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}nF.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var n2=[null];function n6(t,n){this._groups=t,this._parents=n}function n3(){return new n6([[document.documentElement]],n2)}n6.prototype=n3.prototype={constructor:n6,select:function(t){"function"!=typeof t&&(t=nb(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u,a=n[i],c=a.length,f=r[i]=Array(c),l=0;l<c;++l)(o=a[l])&&(u=t.call(o,o.__data__,l,a))&&("__data__"in o&&(u.__data__=o.__data__),f[l]=u);return new n6(r,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){return nm(n.apply(this,arguments))}}else t=nx(t);for(var e=this._groups,r=e.length,i=[],o=[],u=0;u<r;++u)for(var a,c=e[u],f=c.length,l=0;l<f;++l)(a=c[l])&&(i.push(t.call(a,a.__data__,l,c)),o.push(a));return new n6(i,o)},selectChild:function(t){var n;return this.select(null==t?nA:(n="function"==typeof t?t:nM(t),function(){return nT.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?nS:(n="function"==typeof t?t:nM(t),function(){return nk.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=nw(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new n6(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,nR);var e=n?nP:nC,r=this._parents,i=this._groups;"function"!=typeof t&&(b=t,t=function(){return b});for(var o=i.length,u=Array(o),a=Array(o),c=Array(o),f=0;f<o;++f){var l=r[f],s=i[f],h=s.length,d="object"==typeof(y=t.call(l,l&&l.__data__,f,r))&&"length"in y?y:Array.from(y),p=d.length,g=a[f]=Array(p),v=u[f]=Array(p);e(l,s,g,v,c[f]=Array(h),d,n);for(var y,b,m,_,x=0,w=0;x<p;++x)if(m=g[x]){for(x>=w&&(w=x+1);!(_=v[w])&&++w<p;);m._next=_||null}}return(u=new n6(u,r))._enter=a,u._exit=c,u},enter:function(){return new n6(this._enter||this._groups.map(nN),this._parents)},exit:function(){return new n6(this._exit||this._groups.map(nN),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=Array(i),c=0;c<u;++c)for(var f,l=e[c],s=r[c],h=l.length,d=a[c]=Array(h),p=0;p<h;++p)(f=l[p]||s[p])&&(d[p]=f);for(;c<i;++c)a[c]=e[c];return new n6(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=nO);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var u,a=e[o],c=a.length,f=i[o]=Array(c),l=0;l<c;++l)(u=a[l])&&(f[l]=u);f.sort(n)}return new n6(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=nL(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):nB(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=nV(t+"");if(arguments.length<2){for(var r=nI(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?nG:nq)(this,t)}}:n?function(t){return function(){nG(this,t)}}:function(t){return function(){nq(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?nU:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?nY:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(nH)},lower:function(){return this.each(nX)},append:function(t){var n="function"==typeof t?t:nZ(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:nZ(t),r=null==n?nW:"function"==typeof n?n:nb(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(nJ)},clone:function(t){return this.select(t?nQ:nK)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),u=o.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var c,f=0,l=a.length;f<l;++f)for(r=0,c=a[f];r<u;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value}return}for(r=0,a=n?n0:n$;r<u;++r)this.each(a(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return n1(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return n1(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var n5=n3;function n4(t){return"string"==typeof t?new n6([[document.querySelector(t)]],[document.documentElement]):new n6([[t]],n2)}let n8={passive:!1},n7={capture:!0,passive:!1};function n9(t){t.stopImmediatePropagation()}function et(t){t.preventDefault(),t.stopImmediatePropagation()}function en(t){var n=t.document.documentElement,e=n4(t).on("dragstart.drag",et,n7);"onselectstart"in n?e.on("selectstart.drag",et,n7):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function ee(t,n){var e=t.document.documentElement,r=n4(t).on("dragstart.drag",null);n&&(r.on("click.drag",et,n7),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}function er(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function ei(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function eo(){}var eu="\\s*([+-]?\\d+)\\s*",ea="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ec="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ef=/^#([0-9a-f]{3,8})$/,el=new RegExp("^rgb\\(".concat(eu,",").concat(eu,",").concat(eu,"\\)$")),es=new RegExp("^rgb\\(".concat(ec,",").concat(ec,",").concat(ec,"\\)$")),eh=new RegExp("^rgba\\(".concat(eu,",").concat(eu,",").concat(eu,",").concat(ea,"\\)$")),ed=new RegExp("^rgba\\(".concat(ec,",").concat(ec,",").concat(ec,",").concat(ea,"\\)$")),ep=new RegExp("^hsl\\(".concat(ea,",").concat(ec,",").concat(ec,"\\)$")),eg=new RegExp("^hsla\\(".concat(ea,",").concat(ec,",").concat(ec,",").concat(ea,"\\)$")),ev={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ey(){return this.rgb().formatHex()}function eb(){return this.rgb().formatRgb()}function em(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=ef.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?e_(n):3===e?new eT(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?ex(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?ex(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=el.exec(t))?new eT(n[1],n[2],n[3],1):(n=es.exec(t))?new eT(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=eh.exec(t))?ex(n[1],n[2],n[3],n[4]):(n=ed.exec(t))?ex(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=ep.exec(t))?eC(n[1],n[2]/100,n[3]/100,1):(n=eg.exec(t))?eC(n[1],n[2]/100,n[3]/100,n[4]):ev.hasOwnProperty(t)?e_(ev[t]):"transparent"===t?new eT(NaN,NaN,NaN,0):null}function e_(t){return new eT(t>>16&255,t>>8&255,255&t,1)}function ex(t,n,e,r){return r<=0&&(t=n=e=NaN),new eT(t,n,e,r)}function ew(t){return(t instanceof eo||(t=em(t)),t)?new eT((t=t.rgb()).r,t.g,t.b,t.opacity):new eT}function eM(t,n,e,r){return 1==arguments.length?ew(t):new eT(t,n,e,null==r?1:r)}function eT(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function eA(){return"#".concat(eE(this.r)).concat(eE(this.g)).concat(eE(this.b))}function ek(){let t=eS(this.opacity);return"".concat(1===t?"rgb(":"rgba(").concat(eN(this.r),", ").concat(eN(this.g),", ").concat(eN(this.b)).concat(1===t?")":", ".concat(t,")"))}function eS(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function eN(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function eE(t){return((t=eN(t))<16?"0":"")+t.toString(16)}function eC(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new eO(t,n,e,r)}function eP(t){if(t instanceof eO)return new eO(t.h,t.s,t.l,t.opacity);if(t instanceof eo||(t=em(t)),!t)return new eO;if(t instanceof eO)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,c=(o+i)/2;return a?(u=n===o?(e-r)/a+(e<r)*6:e===o?(r-n)/a+2:(n-e)/a+4,a/=c<.5?o+i:2-o-i,u*=60):a=c>0&&c<1?0:u,new eO(u,a,c,t.opacity)}function eR(t,n,e,r){return 1==arguments.length?eP(t):new eO(t,n,e,null==r?1:r)}function eO(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function eD(t){return(t=(t||0)%360)<0?t+360:t}function ez(t){return Math.max(0,Math.min(1,t||0))}function eL(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function ej(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}function eB(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return ej((e-r/n)*n,u,i,o,a)}}function eV(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return ej((e-r/n)*n,i,o,u,a)}}er(eo,em,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ey,formatHex:ey,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return eP(this).formatHsl()},formatRgb:eb,toString:eb}),er(eT,eM,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new eT(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new eT(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new eT(eN(this.r),eN(this.g),eN(this.b),eS(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:eA,formatHex:eA,formatHex8:function(){return"#".concat(eE(this.r)).concat(eE(this.g)).concat(eE(this.b)).concat(eE((isNaN(this.opacity)?1:this.opacity)*255))},formatRgb:ek,toString:ek})),er(eO,eR,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new eO(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new eO(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new eT(eL(t>=240?t-240:t+120,i,r),eL(t,i,r),eL(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new eO(eD(this.h),ez(this.s),ez(this.l),eS(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=eS(this.opacity);return"".concat(1===t?"hsl(":"hsla(").concat(eD(this.h),", ").concat(100*ez(this.s),"%, ").concat(100*ez(this.l),"%").concat(1===t?")":", ".concat(t,")"))}}));var eI=t=>()=>t;function eF(t,n){return function(e){return t+e*n}}function eG(t,n){var e=n-t;return e?eF(t,e>180||e<-180?e-360*Math.round(e/360):e):eI(isNaN(t)?n:t)}function eq(t,n){var e=n-t;return e?eF(t,e):eI(isNaN(t)?n:t)}var eU=function t(n){var e,r=1==(e=+(e=n))?eq:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):eI(isNaN(t)?n:t)};function i(t,n){var e=r((t=eM(t)).r,(n=eM(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),u=eq(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return i.gamma=t,i}(1);function eY(t){return function(n){var e,r,i=n.length,o=Array(i),u=Array(i),a=Array(i);for(e=0;e<i;++e)r=eM(n[e]),o[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return o=t(o),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=a(t),r+""}}}var eH=eY(eB),eX=eY(eV);function eZ(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}}function eW(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function eJ(t,n){return(eW(n)?eZ:eK)(t,n)}function eK(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=Array(i),u=Array(r);for(e=0;e<i;++e)o[e]=e3(t[e],n[e]);for(;e<r;++e)u[e]=n[e];return function(t){for(e=0;e<i;++e)u[e]=o[e](t);return u}}function eQ(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function e$(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function e0(t,n){var e,r={},i={};for(e in(null===t||"object"!=typeof t)&&(t={}),(null===n||"object"!=typeof n)&&(n={}),n)e in t?r[e]=e3(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var e1=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,e2=RegExp(e1.source,"g");function e6(t,n){var e,r,i,o,u,a=e1.lastIndex=e2.lastIndex=0,c=-1,f=[],l=[];for(t+="",n+="";(i=e1.exec(t))&&(o=e2.exec(n));)(u=o.index)>a&&(u=n.slice(a,u),f[c]?f[c]+=u:f[++c]=u),(i=i[0])===(o=o[0])?f[c]?f[c]+=o:f[++c]=o:(f[++c]=null,l.push({i:c,x:e$(i,o)})),a=e2.lastIndex;return a<n.length&&(u=n.slice(a),f[c]?f[c]+=u:f[++c]=u),f.length<2?l[0]?(e=l[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=l.length,function(t){for(var e,r=0;r<n;++r)f[(e=l[r]).i]=e.x(t);return f.join("")})}function e3(t,n){var e,r=typeof n;return null==n||"boolean"===r?eI(n):("number"===r?e$:"string"===r?(e=em(n))?(n=e,eU):e6:n instanceof em?eU:n instanceof Date?eQ:eW(n)?eZ:Array.isArray(n)?eK:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?e0:e$)(t,n)}function e5(t){let n;for(;n=t.sourceEvent;)t=n;return t}function e4(t,n){if(t=e5(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}var e8,e7,e9=0,rt=0,rn=0,re=0,rr=0,ri=0,ro="object"==typeof performance&&performance.now?performance:Date,ru="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function ra(){return rr||(ru(rc),rr=ro.now()+ri)}function rc(){rr=0}function rf(){this._call=this._time=this._next=null}function rl(t,n,e){var r=new rf;return r.restart(t,n,e),r}function rs(){ra(),++e9;for(var t,n=e8;n;)(t=rr-n._time)>=0&&n._call.call(void 0,t),n=n._next;--e9}function rh(){rr=(re=ro.now())+ri,e9=rt=0;try{rs()}finally{e9=0,function(){for(var t,n,e=e8,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:e8=n);e7=t,rp(r)}(),rr=0}}function rd(){var t=ro.now(),n=t-re;n>1e3&&(ri-=n,re=t)}function rp(t){!e9&&(rt&&(rt=clearTimeout(rt)),t-rr>24?(t<1/0&&(rt=setTimeout(rh,t-ro.now()-ri)),rn&&(rn=clearInterval(rn))):(rn||(re=ro.now(),rn=setInterval(rd,1e3)),e9=1,ru(rh)))}function rg(t,n,e){var r=new rf;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}rf.prototype=rl.prototype={constructor:rf,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?ra():+e)+(null==n?0:+n),this._next||e7===this||(e7?e7._next=this:e8=this,e7=this),this._call=t,this._time=e,rp()},stop:function(){this._call&&(this._call=null,this._time=1/0,rp())}};var rv=nv("start","end","cancel","interrupt"),ry=[];function rb(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(c){var f,l,s,h;if(1!==e.state)return a();for(f in i)if((h=i[f]).name===e.name){if(3===h.state)return rg(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[f]):+f<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[f])}if(rg(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(c))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(f=0,e.state=3,r=Array(s=e.tween.length),l=-1;f<s;++f)(h=e.tween[f].value.call(t,t.__data__,e.index,e.group))&&(r[++l]=h);r.length=l+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(a),e.state=5,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),a())}function a(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=rl(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:rv,tween:ry,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function rm(t,n){var e=rx(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function r_(t,n){var e=rx(t,n);if(e.state>3)throw Error("too late; already running");return e}function rx(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function rw(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){u=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}u&&delete t.__transition}}var rM=180/Math.PI,rT={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function rA(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*rM,skewX:Math.atan(c)*rM,scaleX:u,scaleY:a}}function rk(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a,c,f,l,s=[],h=[];return o=t(o),u=t(u),!function(t,r,i,o,u,a){if(t!==i||r!==o){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:e$(t,i)},{i:c-2,x:e$(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,s,h),(a=o.rotate)!==(c=u.rotate)?(a-c>180?c+=360:c-a>180&&(a+=360),h.push({i:s.push(i(s)+"rotate(",null,r)-2,x:e$(a,c)})):c&&s.push(i(s)+"rotate("+c+r),(f=o.skewX)!==(l=u.skewX)?h.push({i:s.push(i(s)+"skewX(",null,r)-2,x:e$(f,l)}):l&&s.push(i(s)+"skewX("+l+r),!function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:e$(t,e)},{i:a-2,x:e$(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,s,h),o=u=null,function(t){for(var n,e=-1,r=h.length;++e<r;)s[(n=h[e]).i]=n.x(t);return s.join("")}}}var rS=rk(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?rT:rA(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),rN=rk(function(t){return null==t?rT:(cg||(cg=document.createElementNS("http://www.w3.org/2000/svg","g")),cg.setAttribute("transform",t),t=cg.transform.baseVal.consolidate())?rA((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):rT},", ",")",")");function rE(t,n,e){var r=t._id;return t.each(function(){var t=r_(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return rx(t,r).value[n]}}function rC(t,n){var e;return("number"==typeof n?e$:n instanceof em?eU:(e=em(n))?(n=e,eU):e6)(t,n)}var rP=n5.prototype.constructor;function rR(t){return function(){this.style.removeProperty(t)}}var rO=0;function rD(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function rz(t){return n5().transition(t)}var rL=n5.prototype;function rj(t){return t*t*t}function rB(t){return--t*t*t+1}function rV(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}rD.prototype=rz.prototype={constructor:rD,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=nb(t));for(var r=this._groups,i=r.length,o=Array(i),u=0;u<i;++u)for(var a,c,f=r[u],l=f.length,s=o[u]=Array(l),h=0;h<l;++h)(a=f[h])&&(c=t.call(a,a.__data__,h,f))&&("__data__"in a&&(c.__data__=a.__data__),s[h]=c,rb(s[h],n,e,h,s,rx(a,e)));return new rD(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=nx(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var c,f=r[a],l=f.length,s=0;s<l;++s)if(c=f[s]){for(var h,d=t.call(c,c.__data__,s,f),p=rx(c,e),g=0,v=d.length;g<v;++g)(h=d[g])&&rb(h,n,e,g,d,p);o.push(d),u.push(c)}return new rD(o,u,n,e)},selectChild:rL.selectChild,selectChildren:rL.selectChildren,filter:function(t){"function"!=typeof t&&(t=nw(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new rD(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=Array(r),a=0;a<o;++a)for(var c,f=n[a],l=e[a],s=f.length,h=u[a]=Array(s),d=0;d<s;++d)(c=f[d]||l[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new rD(u,this._parents,this._name,this._id)},selection:function(){return new rP(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++rO,r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)if(u=a[f]){var l=rx(u,n);rb(u,t,e,f,a,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new rD(r,this._parents,t,e)},call:rL.call,nodes:rL.nodes,node:rL.node,size:rL.size,empty:rL.empty,each:rL.each,on:function(t,n){var e,r,i,o=this._id;return arguments.length<2?rx(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?rm:r_,function(){var u=i(this,o),a=u.on;a!==e&&(r=(e=a).copy()).on(t,n),u.on=r}))},attr:function(t,n){var e=nL(t),r="transform"===e?rN:rC;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);return null==c?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c))}}:function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);return null==c?void this.removeAttribute(t):(u=this.getAttribute(t))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c))}})(e,r,rE(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=nL(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,u,a,c,f,l,s,h,d,p,g,v,y,b,m,_,x,w,M,T="transform"==(t+="")?rS:rC;return null==n?this.styleTween(t,(r=t,function(){var t=nB(this,r),n=(this.style.removeProperty(r),nB(this,r));return t===n?null:t===i&&n===o?u:u=T(i=t,o=n)})).on("end.style."+t,rR(t)):"function"==typeof n?this.styleTween(t,(a=t,c=rE(this,"style."+t,n),function(){var t=nB(this,a),n=c(this),e=n+"";return null==n&&(this.style.removeProperty(a),e=n=nB(this,a)),t===e?null:t===f&&e===l?s:(l=e,s=T(f=t,n))})).each((h=this._id,m="end."+(b="style."+(d=t)),function(){var t=r_(this,h),n=t.on,e=null==t.value[b]?y||(y=rR(d)):void 0;(n!==p||v!==e)&&(g=(p=n).copy()).on(m,v=e),t.on=g})):this.styleTween(t,(_=t,M=n+"",function(){var t=nB(this,_);return t===M?null:t===x?w:w=T(x=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=rE(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=rx(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=r_(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=r_(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,f=i.length;c<f;++c)if(i[c].name===n){i[c]=a;break}c===f&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){rm(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){rm(this,t).delay=n}})(n,t)):rx(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){r_(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){r_(this,t).duration=n}})(n,t)):rx(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){r_(this,t).ease=n}}(n,t)):rx(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();r_(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},c={value:function(){0==--i&&o()}};e.each(function(){var e=r_(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),e.on=n}),0===i&&o()})},[Symbol.iterator]:rL[Symbol.iterator]};var rI={time:null,delay:0,duration:250,ease:rV};n5.prototype.interrupt=function(t){return this.each(function(){rw(this,t)})},n5.prototype.transition=function(t){var n,e;t instanceof rD?(n=t._id,t=t._name):(n=++rO,(e=rI).time=ra(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)(u=a[f])&&rb(u,t,n,f,a,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error("transition ".concat(n," not found"));return e}(u,n));return new rD(r,this._parents,t,n)};var rF=[null];function rG(t,n){var e,r,i=t.__transition;if(i){for(r in n=null==n?null:n+"",i)if((e=i[r]).state>1&&e.name===n)return new rD([[t]],rF,n,+r)}return null}var rq=t=>()=>t;function rU(t,n){let{sourceEvent:e,target:r,selection:i,mode:o,dispatch:u}=n;Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},selection:{value:i,enumerable:!0,configurable:!0},mode:{value:o,enumerable:!0,configurable:!0},_:{value:u}})}function rY(t){t.preventDefault(),t.stopImmediatePropagation()}var rH={name:"drag"},rX={name:"space"},rZ={name:"handle"},rW={name:"center"};let{abs:rJ,max:rK,min:rQ}=Math;function r$(t){return[+t[0],+t[1]]}function r0(t){return[r$(t[0]),r$(t[1])]}var r1={name:"x",handles:["w","e"].map(r9),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},r2={name:"y",handles:["n","s"].map(r9),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},r6={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(r9),input:function(t){return null==t?null:r0(t)},output:function(t){return t}},r3={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},r5={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},r4={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},r8={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},r7={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function r9(t){return{type:t}}function it(t){return!t.ctrlKey&&!t.button}function ie(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function ir(){return navigator.maxTouchPoints||"ontouchstart"in this}function ii(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function io(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function iu(){return il(r1)}function ia(){return il(r2)}function ic(){return il(r6)}function il(t){var n,e=ie,r=it,i=ir,o=!0,u=nv("start","brush","end"),a=6;function c(n){var e=n.property("__brush",g).selectAll(".overlay").data([r9("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",r3.overlay).merge(e).each(function(){var t=ii(this).extent;n4(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),n.selectAll(".selection").data([r9("selection")]).enter().append("rect").attr("class","selection").attr("cursor",r3.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=n.selectAll(".handle").data(t.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return r3[t.type]}),n.each(f).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",h).filter(i).on("touchstart.brush",h).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",p).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(){var t=n4(this),n=ii(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-a/2:n[0][0]-a/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-a/2:n[0][1]-a/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+a:a}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+a:a})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function l(t,n,e){var r=t.__brush.emitter;return!r||e&&r.clean?new s(t,n,e):r}function s(t,n,e){this.that=t,this.args=n,this.state=t.__brush,this.active=0,this.clean=e}function h(e){if((!n||e.touches)&&r.apply(this,arguments)){var i,u,a,c,s,h,d,p,g,v,y,b=this,m=e.target.__data__.type,_=(o&&e.metaKey?m="overlay":m)==="selection"?rH:o&&e.altKey?rW:rZ,x=t===r2?null:r8[m],w=t===r1?null:r7[m],M=ii(b),T=M.extent,A=M.selection,k=T[0][0],S=T[0][1],N=T[1][0],E=T[1][1],C=0,P=0,R=x&&w&&o&&e.shiftKey,O=Array.from(e.touches||[e],t=>{let n=t.identifier;return(t=e4(t,b)).point0=t.slice(),t.identifier=n,t});rw(b);var D=l(b,arguments,!0).beforestart();if("overlay"===m){A&&(g=!0);let n=[O[0],O[1]||O[0]];M.selection=A=[[i=t===r2?k:rQ(n[0][0],n[1][0]),a=t===r1?S:rQ(n[0][1],n[1][1])],[s=t===r2?N:rK(n[0][0],n[1][0]),d=t===r1?E:rK(n[0][1],n[1][1])]],O.length>1&&V(e)}else i=A[0][0],a=A[0][1],s=A[1][0],d=A[1][1];u=i,c=a,h=s,p=d;var z=n4(b).attr("pointer-events","none"),L=z.selectAll(".overlay").attr("cursor",r3[m]);if(e.touches)D.moved=B,D.ended=I;else{var j=n4(e.view).on("mousemove.brush",B,!0).on("mouseup.brush",I,!0);o&&j.on("keydown.brush",function(t){switch(t.keyCode){case 16:R=x&&w;break;case 18:_===rZ&&(x&&(s=h-C*x,i=u+C*x),w&&(d=p-P*w,a=c+P*w),_=rW,V(t));break;case 32:(_===rZ||_===rW)&&(x<0?s=h-C:x>0&&(i=u-C),w<0?d=p-P:w>0&&(a=c-P),_=rX,L.attr("cursor",r3.selection),V(t));break;default:return}rY(t)},!0).on("keyup.brush",function(t){switch(t.keyCode){case 16:R&&(v=y=R=!1,V(t));break;case 18:_===rW&&(x<0?s=h:x>0&&(i=u),w<0?d=p:w>0&&(a=c),_=rZ,V(t));break;case 32:_===rX&&(t.altKey?(x&&(s=h-C*x,i=u+C*x),w&&(d=p-P*w,a=c+P*w),_=rW):(x<0?s=h:x>0&&(i=u),w<0?d=p:w>0&&(a=c),_=rZ),L.attr("cursor",r3[m]),V(t));break;default:return}rY(t)},!0),en(e.view)}f.call(b),D.start(e,_.name)}function B(t){for(let n of t.changedTouches||[t])for(let t of O)t.identifier===n.identifier&&(t.cur=e4(n,b));if(R&&!v&&!y&&1===O.length){let t=O[0];rJ(t.cur[0]-t[0])>rJ(t.cur[1]-t[1])?y=!0:v=!0}for(let t of O)t.cur&&(t[0]=t.cur[0],t[1]=t.cur[1]);g=!0,rY(t),V(t)}function V(t){var n;let e=O[0],r=e.point0;switch(C=e[0]-r[0],P=e[1]-r[1],_){case rX:case rH:x&&(C=rK(k-i,rQ(N-s,C)),u=i+C,h=s+C),w&&(P=rK(S-a,rQ(E-d,P)),c=a+P,p=d+P);break;case rZ:O[1]?(x&&(u=rK(k,rQ(N,O[0][0])),h=rK(k,rQ(N,O[1][0])),x=1),w&&(c=rK(S,rQ(E,O[0][1])),p=rK(S,rQ(E,O[1][1])),w=1)):(x<0?(C=rK(k-i,rQ(N-i,C)),u=i+C,h=s):x>0&&(C=rK(k-s,rQ(N-s,C)),u=i,h=s+C),w<0?(P=rK(S-a,rQ(E-a,P)),c=a+P,p=d):w>0&&(P=rK(S-d,rQ(E-d,P)),c=a,p=d+P));break;case rW:x&&(u=rK(k,rQ(N,i-C*x)),h=rK(k,rQ(N,s+C*x))),w&&(c=rK(S,rQ(E,a-P*w)),p=rK(S,rQ(E,d+P*w)))}h<u&&(x*=-1,n=i,i=s,s=n,n=u,u=h,h=n,m in r5&&L.attr("cursor",r3[m=r5[m]])),p<c&&(w*=-1,n=a,a=d,d=n,n=c,c=p,p=n,m in r4&&L.attr("cursor",r3[m=r4[m]])),M.selection&&(A=M.selection),v&&(u=A[0][0],h=A[1][0]),y&&(c=A[0][1],p=A[1][1]),(A[0][0]!==u||A[0][1]!==c||A[1][0]!==h||A[1][1]!==p)&&(M.selection=[[u,c],[h,p]],f.call(b),D.brush(t,_.name))}function I(t){var e;if(!function(t){t.stopImmediatePropagation()}(t),t.touches){if(t.touches.length)return;n&&clearTimeout(n),n=setTimeout(function(){n=null},500)}else ee(t.view,g),j.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);z.attr("pointer-events","all"),L.attr("cursor",r3.overlay),M.selection&&(A=M.selection),((e=A)[0][0]===e[1][0]||e[0][1]===e[1][1])&&(M.selection=null,f.call(b)),D.end(t,_.name)}}function d(t){l(this,arguments).moved(t)}function p(t){l(this,arguments).ended(t)}function g(){var n=this.__brush||{selection:null};return n.extent=r0(e.apply(this,arguments)),n.dim=t,n}return c.move=function(n,e,r){n.tween?n.on("start.brush",function(t){l(this,arguments).beforestart().start(t)}).on("interrupt.brush end.brush",function(t){l(this,arguments).end(t)}).tween("brush",function(){var n=this,r=n.__brush,i=l(n,arguments),o=r.selection,u=t.input("function"==typeof e?e.apply(this,arguments):e,r.extent),a=e3(o,u);function c(t){r.selection=1===t&&null===u?null:a(t),f.call(n),i.brush()}return null!==o&&null!==u?c:c(1)}):n.each(function(){var n=arguments,i=this.__brush,o=t.input("function"==typeof e?e.apply(this,n):e,i.extent),u=l(this,n).beforestart();rw(this),i.selection=null===o?null:o,f.call(this),u.start(r).brush(r).end(r)})},c.clear=function(t,n){c.move(t,null,n)},s.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(t,n){return this.starting?(this.starting=!1,this.emit("start",t,n)):this.emit("brush",t),this},brush:function(t,n){return this.emit("brush",t,n),this},end:function(t,n){return 0==--this.active&&(delete this.state.emitter,this.emit("end",t,n)),this},emit:function(n,e,r){var i=n4(this.that).datum();u.call(n,this.that,new rU(n,{sourceEvent:e,target:c,selection:t.output(this.state.selection),mode:r,dispatch:u}),i)}},c.extent=function(t){return arguments.length?(e="function"==typeof t?t:rq(r0(t)),c):e},c.filter=function(t){return arguments.length?(r="function"==typeof t?t:rq(!!t),c):r},c.touchable=function(t){return arguments.length?(i="function"==typeof t?t:rq(!!t),c):i},c.handleSize=function(t){return arguments.length?(a=+t,c):a},c.keyModifiers=function(t){return arguments.length?(o=!!t,c):o},c.on=function(){var t=u.on.apply(u,arguments);return t===u?c:t},c}var is=Math.abs,ih=Math.cos,id=Math.sin,ip=Math.PI,ig=ip/2,iv=2*ip,iy=Math.max;function ib(t,n){return Array.from({length:n-t},(n,e)=>t+e)}function im(){return iw(!1,!1)}function i_(){return iw(!1,!0)}function ix(){return iw(!0,!1)}function iw(t,n){var e=0,r=null,i=null,o=null;function u(u){var a,c=u.length,f=Array(c),l=ib(0,c),s=Array(c*c),h=Array(c),d=0;u=Float64Array.from({length:c*c},n?(t,n)=>u[n%c][n/c|0]:(t,n)=>u[n/c|0][n%c]);for(let n=0;n<c;++n){let e=0;for(let r=0;r<c;++r)e+=u[n*c+r]+t*u[r*c+n];d+=f[n]=e}a=(d=iy(0,iv-e*c)/d)?e:iv/c;{let n=0;for(let e of(r&&l.sort((t,n)=>r(f[t],f[n])),l)){let r=n;if(t){let t=ib(~c+1,c).filter(t=>t<0?u[~t*c+e]:u[e*c+t]);for(let r of(i&&t.sort((t,n)=>i(t<0?-u[~t*c+e]:u[e*c+t],n<0?-u[~n*c+e]:u[e*c+n])),t))r<0?(s[~r*c+e]||(s[~r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=u[~r*c+e]*d,value:u[~r*c+e]}:(s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]};h[e]={index:e,startAngle:r,endAngle:n,value:f[e]}}else{let t=ib(0,c).filter(t=>u[e*c+t]||u[t*c+e]);for(let r of(i&&t.sort((t,n)=>i(u[e*c+t],u[e*c+n])),t)){let t;if(e<r?(t=s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]}:((t=s[r*c+e]||(s[r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]},e===r&&(t.source=t.target)),t.source&&t.target&&t.source.value<t.target.value){let n=t.source;t.source=t.target,t.target=n}}h[e]={index:e,startAngle:r,endAngle:n,value:f[e]}}n+=a}}return(s=Object.values(s)).groups=h,o?s.sort(o):s}return u.padAngle=function(t){return arguments.length?(e=iy(0,t),u):e},u.sortGroups=function(t){return arguments.length?(r=t,u):r},u.sortSubgroups=function(t){return arguments.length?(i=t,u):i},u.sortChords=function(t){return arguments.length?(null==t?o=null:(o=function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)})._=t,u):o&&o._},u}function iM(t,n){return n||(n=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(n)}}))}function iT(){let t=iM(["M",",",""]);return iT=function(){return t},t}function iA(){let t=iM(["Z"]);return iA=function(){return t},t}function ik(){let t=iM(["L",",",""]);return ik=function(){return t},t}function iS(){let t=iM(["Q",",",",",",",""]);return iS=function(){return t},t}function iN(){let t=iM(["C",",",",",",",",",",",""]);return iN=function(){return t},t}function iE(){let t=iM(["M",",",""]);return iE=function(){return t},t}function iC(){let t=iM(["L",",",""]);return iC=function(){return t},t}function iP(){let t=iM(["L",",",""]);return iP=function(){return t},t}function iR(){let t=iM(["A",",",",0,0,",",",",",""]);return iR=function(){return t},t}function iO(){let t=iM(["M",",",""]);return iO=function(){return t},t}function iD(){let t=iM(["L",",",""]);return iD=function(){return t},t}function iz(){let t=iM(["A",",",",0,1,",",",",","A",",",",0,1,",",",",",""]);return iz=function(){return t},t}function iL(){let t=iM(["A",",",",0,",",",",",",",""]);return iL=function(){return t},t}function ij(){let t=iM(["M",",","h","v","h","Z"]);return ij=function(){return t},t}let iB=Math.PI,iV=2*iB,iI=iV-1e-6;function iF(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}class iG{moveTo(t,n){this._append(iT(),this._x0=this._x1=+t,this._y0=this._y1=+n)}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append(iA()))}lineTo(t,n){this._append(ik(),this._x1=+t,this._y1=+n)}quadraticCurveTo(t,n,e,r){this._append(iS(),+t,+n,this._x1=+e,this._y1=+r)}bezierCurveTo(t,n,e,r,i,o){this._append(iN(),+t,+n,+e,+r,this._x1=+i,this._y1=+o)}arcTo(t,n,e,r,i){if(t=+t,n=+n,e=+e,r=+r,(i=+i)<0)throw Error("negative radius: ".concat(i));let o=this._x1,u=this._y1,a=e-t,c=r-n,f=o-t,l=u-n,s=f*f+l*l;if(null===this._x1)this._append(iE(),this._x1=t,this._y1=n);else if(s>1e-6){if(Math.abs(l*a-c*f)>1e-6&&i){let h=e-o,d=r-u,p=a*a+c*c,g=Math.sqrt(p),v=Math.sqrt(s),y=i*Math.tan((iB-Math.acos((p+s-(h*h+d*d))/(2*g*v)))/2),b=y/v,m=y/g;Math.abs(b-1)>1e-6&&this._append(iP(),t+b*f,n+b*l),this._append(iR(),i,i,+(l*h>f*d),this._x1=t+m*a,this._y1=n+m*c)}else this._append(iC(),this._x1=t,this._y1=n)}}arc(t,n,e,r,i,o){if(t=+t,n=+n,o=!!o,(e=+e)<0)throw Error("negative radius: ".concat(e));let u=e*Math.cos(r),a=e*Math.sin(r),c=t+u,f=n+a,l=1^o,s=o?r-i:i-r;null===this._x1?this._append(iO(),c,f):(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-f)>1e-6)&&this._append(iD(),c,f),e&&(s<0&&(s=s%iV+iV),s>iI?this._append(iz(),e,e,l,t-u,n-a,e,e,l,this._x1=c,this._y1=f):s>1e-6&&this._append(iL(),e,e,+(s>=iB),l,this._x1=t+e*Math.cos(i),this._y1=n+e*Math.sin(i)))}rect(t,n,e,r){this._append(ij(),this._x0=this._x1=+t,this._y0=this._y1=+n,e=+e,+r,-e)}toString(){return this._}constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?iF:function(t){let n=Math.floor(t);if(!(n>=0))throw Error("invalid digits: ".concat(t));if(n>15)return iF;let e=10**n;return function(t){this._+=t[0];for(let n=1,r=t.length;n<r;++n)this._+=Math.round(arguments[n]*e)/e+t[n]}}(t)}}function iq(){return new iG}function iU(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return new iG(+t)}iq.prototype=iG.prototype;var iY=Array.prototype.slice;function iH(t){return function(){return t}}function iX(t){return t.source}function iZ(t){return t.target}function iW(t){return t.radius}function iJ(t){return t.startAngle}function iK(t){return t.endAngle}function iQ(){return 0}function i$(){return 10}function i0(t){var n=iX,e=iZ,r=iW,i=iW,o=iJ,u=iK,a=iQ,c=null;function f(){var f,l=n.apply(this,arguments),s=e.apply(this,arguments),h=a.apply(this,arguments)/2,d=iY.call(arguments),p=+r.apply(this,(d[0]=l,d)),g=o.apply(this,d)-ig,v=u.apply(this,d)-ig,y=+i.apply(this,(d[0]=s,d)),b=o.apply(this,d)-ig,m=u.apply(this,d)-ig;if(c||(c=f=iq()),h>1e-12&&(is(v-g)>2*h+1e-12?v>g?(g+=h,v-=h):(g-=h,v+=h):g=v=(g+v)/2,is(m-b)>2*h+1e-12?m>b?(b+=h,m-=h):(b-=h,m+=h):b=m=(b+m)/2),c.moveTo(p*ih(g),p*id(g)),c.arc(0,0,p,g,v),g!==b||v!==m){if(t){var _=+t.apply(this,arguments),x=y-_,w=(b+m)/2;c.quadraticCurveTo(0,0,x*ih(b),x*id(b)),c.lineTo(y*ih(w),y*id(w)),c.lineTo(x*ih(m),x*id(m))}else c.quadraticCurveTo(0,0,y*ih(b),y*id(b)),c.arc(0,0,y,b,m)}if(c.quadraticCurveTo(0,0,p*ih(g),p*id(g)),c.closePath(),f)return c=null,f+""||null}return t&&(f.headRadius=function(n){return arguments.length?(t="function"==typeof n?n:iH(+n),f):t}),f.radius=function(t){return arguments.length?(r=i="function"==typeof t?t:iH(+t),f):r},f.sourceRadius=function(t){return arguments.length?(r="function"==typeof t?t:iH(+t),f):r},f.targetRadius=function(t){return arguments.length?(i="function"==typeof t?t:iH(+t),f):i},f.startAngle=function(t){return arguments.length?(o="function"==typeof t?t:iH(+t),f):o},f.endAngle=function(t){return arguments.length?(u="function"==typeof t?t:iH(+t),f):u},f.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:iH(+t),f):a},f.source=function(t){return arguments.length?(n=t,f):n},f.target=function(t){return arguments.length?(e=t,f):e},f.context=function(t){return arguments.length?(c=null==t?null:t,f):c},f}function i1(){return i0()}function i2(){return i0(i$)}let i6=Math.PI/180,i3=180/Math.PI,i5=4/29,i4=6/29,i8=6/29*3*(6/29),i7=6/29*(6/29)*(6/29);function i9(t){if(t instanceof oe)return new oe(t.l,t.a,t.b,t.opacity);if(t instanceof ol)return os(t);t instanceof eT||(t=ew(t));var n,e,r=ou(t.r),i=ou(t.g),o=ou(t.b),u=or((.2225045*r+.7168786*i+.0606169*o)/1);return r===i&&i===o?n=e=u:(n=or((.4360747*r+.3850649*i+.1430804*o)/.96422),e=or((.0139322*r+.0971045*i+.7141733*o)/.82521)),new oe(116*u-16,500*(n-u),200*(u-e),t.opacity)}function ot(t,n){return new oe(t,0,0,null==n?1:n)}function on(t,n,e,r){return 1==arguments.length?i9(t):new oe(t,n,e,null==r?1:r)}function oe(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function or(t){return t>i7?Math.pow(t,1/3):t/i8+i5}function oi(t){return t>i4?t*t*t:i8*(t-i5)}function oo(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function ou(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function oa(t){if(t instanceof ol)return new ol(t.h,t.c,t.l,t.opacity);if(t instanceof oe||(t=i9(t)),0===t.a&&0===t.b)return new ol(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*i3;return new ol(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function oc(t,n,e,r){return 1==arguments.length?oa(t):new ol(e,n,t,null==r?1:r)}function of(t,n,e,r){return 1==arguments.length?oa(t):new ol(t,n,e,null==r?1:r)}function ol(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function os(t){if(isNaN(t.h))return new oe(t.l,0,0,t.opacity);var n=t.h*i6;return new oe(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}er(oe,on,ei(eo,{brighter(t){return new oe(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new oe(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new eT(oo(3.1338561*(n=.96422*oi(n))-1.6168667*(t=1*oi(t))-.4906146*(e=.82521*oi(e))),oo(-.9787684*n+1.9161415*t+.033454*e),oo(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),er(ol,of,ei(eo,{brighter(t){return new ol(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new ol(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return os(this).rgb()}}));var oh=-1.78277*.29227-.1347134789;function od(t,n,e,r){return 1==arguments.length?function(t){if(t instanceof op)return new op(t.h,t.s,t.l,t.opacity);t instanceof eT||(t=ew(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(oh*r+-1.7884503806*n-3.5172982438*e)/(oh+-1.7884503806-3.5172982438),o=r-i,u=-((1.97294*(e-i)- -.29227*o)/.90649),a=Math.sqrt(u*u+o*o)/(1.97294*i*(1-i)),c=a?Math.atan2(u,o)*i3-120:NaN;return new op(c<0?c+360:c,a,i,t.opacity)}(t):new op(t,n,e,null==r?1:r)}function op(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}er(op,od,ei(eo,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new op(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new op(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*i6,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new eT(255*(n+e*(-.14861*r+1.78277*i)),255*(n+e*(-.29227*r+-.90649*i)),255*(n+1.97294*r*e),this.opacity)}}));var og=Array.prototype.slice;function ov(t,n){return t-n}var oy=t=>()=>t;function ob(){}var om=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function o_(){var t=1,n=1,e=t_,r=a;function i(t){var n=e(t);if(Array.isArray(n))n=n.slice().sort(ov);else{let e=j(t,ox);for(n=tv(...tm(e[0],e[1],n),n);n[n.length-1]>=e[1];)n.pop();for(;n[1]<e[0];)n.shift()}return n.map(n=>o(t,n))}function o(e,i){let o=null==i?NaN:+i;if(isNaN(o))throw Error("invalid value: ".concat(i));var a=[],c=[];return function(e,r,i){var o,a,c,f,l,s,h=[],d=[];for(o=a=-1,om[(f=ow(e[0],r))<<1].forEach(p);++o<t-1;)om[(c=f)|(f=ow(e[o+1],r))<<1].forEach(p);for(om[f<<0].forEach(p);++a<n-1;){for(o=-1,om[(f=ow(e[a*t+t],r))<<1|(l=ow(e[a*t],r))<<2].forEach(p);++o<t-1;)c=f,f=ow(e[a*t+t+o+1],r),s=l,om[c|f<<1|(l=ow(e[a*t+o+1],r))<<2|s<<3].forEach(p);om[f|l<<3].forEach(p)}for(o=-1,om[(l=e[a*t]>=r)<<2].forEach(p);++o<t-1;)s=l,om[(l=ow(e[a*t+o+1],r))<<2|s<<3].forEach(p);function p(t){var n,e,r=[t[0][0]+o,t[0][1]+a],c=[t[1][0]+o,t[1][1]+a],f=u(r),l=u(c);(n=d[f])?(e=h[l])?(delete d[n.end],delete h[e.start],n===e?(n.ring.push(c),i(n.ring)):h[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete d[n.end],n.ring.push(c),d[n.end=l]=n):(n=h[l])?(e=d[f])?(delete h[n.start],delete d[e.end],n===e?(n.ring.push(c),i(n.ring)):h[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete h[n.start],n.ring.unshift(r),h[n.start=f]=n):h[f]=d[l]={start:f,end:l,ring:[r,c]}}om[l<<3].forEach(p)}(e,o,function(t){r(t,e,o),function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}(t)>0?a.push([t]):c.push(t)}),c.forEach(function(t){for(var n,e=0,r=a.length;e<r;++e)if(-1!==function(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=function(t,n){for(var e=n[0],r=n[1],i=-1,o=0,u=t.length,a=u-1;o<u;a=o++){var c=t[o],f=c[0],l=c[1],s=t[a],h=s[0],d=s[1];if(function(t,n,e){var r,i,o,u;return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])&&(i=t[r=+(t[0]===n[0])],o=e[r],u=n[r],i<=o&&o<=u||u<=o&&o<=i)}(c,s,n))return 0;l>r!=d>r&&e<(h-f)*(r-l)/(d-l)+f&&(i=-i)}return i}(t,n[r]))return e;return 0}((n=a[e])[0],t)){n.push(t);return}}),{type:"MultiPolygon",value:i,coordinates:a}}function u(n){return 2*n[0]+n[1]*(t+1)*4}function a(e,r,i){e.forEach(function(e){var o=e[0],u=e[1],a=0|o,c=0|u,f=oM(r[c*t+a]);o>0&&o<t&&a===o&&(e[0]=oT(o,oM(r[c*t+a-1]),f,i)),u>0&&u<n&&c===u&&(e[1]=oT(u,oM(r[(c-1)*t+a]),f,i))})}return i.contour=o,i.size=function(e){if(!arguments.length)return[t,n];var r=Math.floor(e[0]),o=Math.floor(e[1]);if(!(r>=0&&o>=0))throw Error("invalid size");return t=r,n=o,i},i.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?oy(og.call(t)):oy(t),i):e},i.smooth=function(t){return arguments.length?(r=t?a:ob,i):r===a},i}function ox(t){return isFinite(t)?t:NaN}function ow(t,n){return null!=t&&+t>=n}function oM(t){return null==t||isNaN(t=+t)?-1/0:t}function oT(t,n,e,r){let i=r-n,o=e-n,u=isFinite(i)||isFinite(o)?i/o:Math.sign(i)/Math.sign(o);return isNaN(u)?t:t+u-.5}function oA(t){return t[0]}function ok(t){return t[1]}function oS(){return 1}function oN(){var t=oA,n=ok,e=oS,r=960,i=500,o=20,u=2,a=60,c=270,f=155,l=oy(20);function s(r){var i=new Float32Array(c*f),l=Math.pow(2,-u),s=-1;for(let o of r){var h=(t(o,++s,r)+a)*l,d=(n(o,s,r)+a)*l,p=+e(o,s,r);if(p&&h>=0&&h<c&&d>=0&&d<f){var g=Math.floor(h),v=Math.floor(d),y=h-g-.5,b=d-v-.5;i[g+v*c]+=(1-y)*(1-b)*p,i[g+1+v*c]+=y*(1-b)*p,i[g+1+(v+1)*c]+=y*b*p,i[g+(v+1)*c]+=(1-y)*b*p}}return M({data:i,width:c,height:f},o*l),i}function h(t){var n=s(t),e=l(n),r=Math.pow(2,2*u);return Array.isArray(e)||(e=tv(Number.MIN_VALUE,tw(n)/r,e)),o_().size([c,f]).thresholds(e.map(t=>t*r))(n).map((t,n)=>(t.value=+e[n],d(t)))}function d(t){return t.coordinates.forEach(p),t}function p(t){t.forEach(g)}function g(t){t.forEach(v)}function v(t){t[0]=t[0]*Math.pow(2,u)-a,t[1]=t[1]*Math.pow(2,u)-a}function y(){return c=r+2*(a=3*o)>>u,f=i+2*a>>u,h}return h.contours=function(t){var n=s(t),e=o_().size([c,f]),r=Math.pow(2,2*u),i=t=>{t=+t;var i=d(e.contour(n,t*r));return i.value=t,i};return Object.defineProperty(i,"max",{get:()=>tw(n)/r}),i},h.x=function(n){return arguments.length?(t="function"==typeof n?n:oy(+n),h):t},h.y=function(t){return arguments.length?(n="function"==typeof t?t:oy(+t),h):n},h.weight=function(t){return arguments.length?(e="function"==typeof t?t:oy(+t),h):e},h.size=function(t){if(!arguments.length)return[r,i];var n=+t[0],e=+t[1];if(!(n>=0&&e>=0))throw Error("invalid size");return r=n,i=e,y()},h.cellSize=function(t){if(!arguments.length)return 1<<u;if(!((t=+t)>=1))throw Error("invalid cell size");return u=Math.floor(Math.log(t)/Math.LN2),y()},h.thresholds=function(t){return arguments.length?(l="function"==typeof t?t:Array.isArray(t)?oy(og.call(t)):oy(t),h):l},h.bandwidth=function(t){if(!arguments.length)return Math.sqrt(o*(o+1));if(!((t=+t)>=0))throw Error("invalid bandwidth");return o=(Math.sqrt(4*t*t+1)-1)/2,y()},h}function oE(t,n,e,r,i){let o,u,a,c;let f=n[0],l=r[0],s=0,h=0;l>f==l>-f?(o=f,f=n[++s]):(o=l,l=r[++h]);let d=0;if(s<t&&h<e)for(l>f==l>-f?(u=f+o,a=o-(u-f),f=n[++s]):(u=l+o,a=o-(u-l),l=r[++h]),o=u,0!==a&&(i[d++]=a);s<t&&h<e;)l>f==l>-f?(c=(u=o+f)-o,a=o-(u-c)+(f-c),f=n[++s]):(c=(u=o+l)-o,a=o-(u-c)+(l-c),l=r[++h]),o=u,0!==a&&(i[d++]=a);for(;s<t;)c=(u=o+f)-o,a=o-(u-c)+(f-c),f=n[++s],o=u,0!==a&&(i[d++]=a);for(;h<e;)c=(u=o+l)-o,a=o-(u-c)+(l-c),l=r[++h],o=u,0!==a&&(i[d++]=a);return(0!==o||0===d)&&(i[d++]=o),d}function oC(t){return new Float64Array(t)}let oP=oC(4),oR=oC(8),oO=oC(12),oD=oC(16),oz=oC(4);function oL(t,n,e,r,i,o){let u=(n-o)*(e-i),a=(t-i)*(r-o),c=u-a,f=Math.abs(u+a);return Math.abs(c)>=33306690738754716e-32*f?c:-function(t,n,e,r,i,o,u){let a,c,f,l,s,h,d,p,g,v,y,b,m,_,x,w,M,T;let A=t-i,k=e-i,S=n-o,N=r-o;_=A*N,d=(h=134217729*A)-(h-A),p=A-d,g=(h=134217729*N)-(h-N),x=p*(v=N-g)-(_-d*g-p*g-d*v),w=S*k,d=(h=134217729*S)-(h-S),p=S-d,g=(h=134217729*k)-(h-k),y=x-(M=p*(v=k-g)-(w-d*g-p*g-d*v)),s=x-y,oP[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,oP[1]=m-(y+s)+(s-w),s=(T=b+y)-b,oP[2]=b-(T-s)+(y-s),oP[3]=T;let E=function(t,n){let e=n[0];for(let t=1;t<4;t++)e+=n[t];return e}(0,oP),C=22204460492503146e-32*u;if(E>=C||-E>=C||(s=t-A,a=t-(A+s)+(s-i),s=e-k,f=e-(k+s)+(s-i),s=n-S,c=n-(S+s)+(s-o),s=r-N,l=r-(N+s)+(s-o),0===a&&0===c&&0===f&&0===l)||(C=11093356479670487e-47*u+33306690738754706e-32*Math.abs(E),(E+=A*l+N*a-(S*f+k*c))>=C||-E>=C))return E;_=a*N,d=(h=134217729*a)-(h-a),p=a-d,g=(h=134217729*N)-(h-N),x=p*(v=N-g)-(_-d*g-p*g-d*v),w=c*k,d=(h=134217729*c)-(h-c),p=c-d,g=(h=134217729*k)-(h-k),y=x-(M=p*(v=k-g)-(w-d*g-p*g-d*v)),s=x-y,oz[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,oz[1]=m-(y+s)+(s-w),s=(T=b+y)-b,oz[2]=b-(T-s)+(y-s),oz[3]=T;let P=oE(4,oP,4,oz,oR);_=A*l,d=(h=134217729*A)-(h-A),p=A-d,g=(h=134217729*l)-(h-l),x=p*(v=l-g)-(_-d*g-p*g-d*v),w=S*f,d=(h=134217729*S)-(h-S),p=S-d,g=(h=134217729*f)-(h-f),y=x-(M=p*(v=f-g)-(w-d*g-p*g-d*v)),s=x-y,oz[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,oz[1]=m-(y+s)+(s-w),s=(T=b+y)-b,oz[2]=b-(T-s)+(y-s),oz[3]=T;let R=oE(P,oR,4,oz,oO);_=a*l,d=(h=134217729*a)-(h-a),p=a-d,g=(h=134217729*l)-(h-l),x=p*(v=l-g)-(_-d*g-p*g-d*v),w=c*f,d=(h=134217729*c)-(h-c),p=c-d,g=(h=134217729*f)-(h-f),y=x-(M=p*(v=f-g)-(w-d*g-p*g-d*v)),s=x-y,oz[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,oz[1]=m-(y+s)+(s-w),s=(T=b+y)-b,oz[2]=b-(T-s)+(y-s),oz[3]=T;let O=oE(R,oO,4,oz,oD);return oD[O-1]}(t,n,e,r,i,o,f)}oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(8),oC(8),oC(8),oC(4),oC(8),oC(8),oC(8),oC(12),oC(192),oC(192),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(8),oC(8),oC(8),oC(8),oC(8),oC(8),oC(8),oC(8),oC(8),oC(4),oC(4),oC(4),oC(8),oC(16),oC(16),oC(16),oC(32),oC(32),oC(48),oC(64),oC(1152),oC(1152),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(4),oC(24),oC(24),oC(24),oC(24),oC(24),oC(24),oC(24),oC(24),oC(24),oC(24),oC(1152),oC(1152),oC(1152),oC(1152),oC(1152),oC(2304),oC(2304),oC(3456),oC(5760),oC(8),oC(8),oC(8),oC(16),oC(24),oC(48),oC(48),oC(96),oC(192),oC(384),oC(384),oC(384),oC(768),oC(96),oC(96),oC(96),oC(1152);let oj=new Uint32Array(512);class oB{static from(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:oG,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:oq,r=t.length,i=new Float64Array(2*r);for(let o=0;o<r;o++){let r=t[o];i[2*o]=n(r),i[2*o+1]=e(r)}return new oB(i)}update(){let t,n,e;let{coords:r,_hullPrev:i,_hullNext:o,_hullTri:u,_hullHash:a}=this,c=r.length>>1,f=1/0,l=1/0,s=-1/0,h=-1/0;for(let t=0;t<c;t++){let n=r[2*t],e=r[2*t+1];n<f&&(f=n),e<l&&(l=e),n>s&&(s=n),e>h&&(h=e),this._ids[t]=t}let d=(f+s)/2,p=(l+h)/2;for(let n=0,e=1/0;n<c;n++){let i=oV(d,p,r[2*n],r[2*n+1]);i<e&&(t=n,e=i)}let g=r[2*t],v=r[2*t+1];for(let e=0,i=1/0;e<c;e++){if(e===t)continue;let o=oV(g,v,r[2*e],r[2*e+1]);o<i&&o>0&&(n=e,i=o)}let y=r[2*n],b=r[2*n+1],m=1/0;for(let i=0;i<c;i++){if(i===t||i===n)continue;let o=function(t,n,e,r,i,o){let u=e-t,a=r-n,c=i-t,f=o-n,l=u*u+a*a,s=c*c+f*f,h=.5/(u*f-a*c),d=(f*l-a*s)*h,p=(u*s-c*l)*h;return d*d+p*p}(g,v,y,b,r[2*i],r[2*i+1]);o<m&&(e=i,m=o)}let _=r[2*e],x=r[2*e+1];if(m===1/0){for(let t=0;t<c;t++)this._dists[t]=r[2*t]-r[0]||r[2*t+1]-r[1];oI(this._ids,this._dists,0,c-1);let t=new Uint32Array(c),n=0;for(let e=0,r=-1/0;e<c;e++){let i=this._ids[e],o=this._dists[i];o>r&&(t[n++]=i,r=o)}this.hull=t.subarray(0,n),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(0>oL(g,v,y,b,_,x)){let t=n,r=y,i=b;n=e,y=_,b=x,e=t,_=r,x=i}let w=function(t,n,e,r,i,o){let u=e-t,a=r-n,c=i-t,f=o-n,l=u*u+a*a,s=c*c+f*f,h=.5/(u*f-a*c);return{x:t+(f*l-a*s)*h,y:n+(u*s-c*l)*h}}(g,v,y,b,_,x);this._cx=w.x,this._cy=w.y;for(let t=0;t<c;t++)this._dists[t]=oV(r[2*t],r[2*t+1],w.x,w.y);oI(this._ids,this._dists,0,c-1),this._hullStart=t;let M=3;o[t]=i[e]=n,o[n]=i[t]=e,o[e]=i[n]=t,u[t]=0,u[n]=1,u[e]=2,a.fill(-1),a[this._hashKey(g,v)]=t,a[this._hashKey(y,b)]=n,a[this._hashKey(_,x)]=e,this.trianglesLen=0,this._addTriangle(t,n,e,-1,-1,-1);for(let c=0,f,l;c<this._ids.length;c++){let s=this._ids[c],h=r[2*s],d=r[2*s+1];if(c>0&&2220446049250313e-31>=Math.abs(h-f)&&2220446049250313e-31>=Math.abs(d-l)||(f=h,l=d,s===t||s===n||s===e))continue;let p=0;for(let t=0,n=this._hashKey(h,d);t<this._hashSize&&(-1===(p=a[(n+t)%this._hashSize])||p===o[p]);t++);let g=p=i[p],v;for(;v=o[g],oL(h,d,r[2*g],r[2*g+1],r[2*v],r[2*v+1])>=0;)if((g=v)===p){g=-1;break}if(-1===g)continue;let y=this._addTriangle(g,s,o[g],-1,-1,u[g]);u[s]=this._legalize(y+2),u[g]=y,M++;let b=o[g];for(;v=o[b],0>oL(h,d,r[2*b],r[2*b+1],r[2*v],r[2*v+1]);)y=this._addTriangle(b,s,v,u[s],-1,u[b]),u[s]=this._legalize(y+2),o[b]=b,M--,b=v;if(g===p)for(;0>oL(h,d,r[2*(v=i[g])],r[2*v+1],r[2*g],r[2*g+1]);)y=this._addTriangle(v,s,g,-1,u[g],u[v]),this._legalize(y+2),u[v]=y,o[g]=g,M--,g=v;this._hullStart=i[s]=g,o[g]=i[b]=s,o[s]=b,a[this._hashKey(h,d)]=s,a[this._hashKey(r[2*g],r[2*g+1])]=g}this.hull=new Uint32Array(M);for(let t=0,n=this._hullStart;t<M;t++)this.hull[t]=n,n=o[n];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,n){return Math.floor(function(t,n){let e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}(t-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(t){let{_triangles:n,_halfedges:e,coords:r}=this,i=0,o=0;for(;;){let u=e[t],a=t-t%3;if(o=a+(t+2)%3,-1===u){if(0===i)break;t=oj[--i];continue}let c=u-u%3,f=a+(t+1)%3,l=c+(u+2)%3,s=n[o],h=n[t],d=n[f],p=n[l];if(function(t,n,e,r,i,o,u,a){let c=t-u,f=n-a,l=e-u,s=r-a,h=i-u,d=o-a,p=l*l+s*s,g=h*h+d*d;return c*(s*g-p*d)-f*(l*g-p*h)+(c*c+f*f)*(l*d-s*h)<0}(r[2*s],r[2*s+1],r[2*h],r[2*h+1],r[2*d],r[2*d+1],r[2*p],r[2*p+1])){n[t]=p,n[u]=s;let r=e[l];if(-1===r){let n=this._hullStart;do{if(this._hullTri[n]===l){this._hullTri[n]=t;break}n=this._hullPrev[n]}while(n!==this._hullStart)}this._link(t,r),this._link(u,e[o]),this._link(o,l);let a=c+(u+1)%3;i<oj.length&&(oj[i++]=a)}else{if(0===i)break;t=oj[--i]}}return o}_link(t,n){this._halfedges[t]=n,-1!==n&&(this._halfedges[n]=t)}_addTriangle(t,n,e,r,i,o){let u=this.trianglesLen;return this._triangles[u]=t,this._triangles[u+1]=n,this._triangles[u+2]=e,this._link(u,r),this._link(u+1,i),this._link(u+2,o),this.trianglesLen+=3,u}constructor(t){let n=t.length>>1;if(n>0&&"number"!=typeof t[0])throw Error("Expected coords to contain numbers.");this.coords=t;let e=Math.max(2*n-5,0);this._triangles=new Uint32Array(3*e),this._halfedges=new Int32Array(3*e),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}}function oV(t,n,e,r){let i=t-e,o=n-r;return i*i+o*o}function oI(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){let r=t[i],o=n[r],u=i-1;for(;u>=e&&n[t[u]]>o;)t[u+1]=t[u--];t[u+1]=r}else{let i=e+r>>1,o=e+1,u=r;oF(t,i,o),n[t[e]]>n[t[r]]&&oF(t,e,r),n[t[o]]>n[t[r]]&&oF(t,o,r),n[t[e]]>n[t[o]]&&oF(t,e,o);let a=t[o],c=n[a];for(;;){do o++;while(n[t[o]]<c);do u--;while(n[t[u]]>c);if(u<o)break;oF(t,o,u)}t[e+1]=t[u],t[u]=a,r-o+1>=u-e?(oI(t,n,o,r),oI(t,n,e,u-1)):(oI(t,n,e,u-1),oI(t,n,o,r))}}function oF(t,n,e){let r=t[n];t[n]=t[e],t[e]=r}function oG(t){return t[0]}function oq(t){return t[1]}class oU{moveTo(t,n){this._+="M".concat(this._x0=this._x1=+t,",").concat(this._y0=this._y1=+n)}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+="L".concat(this._x1=+t,",").concat(this._y1=+n)}arc(t,n,e){t=+t,n=+n;let r=t+(e=+e),i=n;if(e<0)throw Error("negative radius");null===this._x1?this._+="M".concat(r,",").concat(i):(Math.abs(this._x1-r)>1e-6||Math.abs(this._y1-i)>1e-6)&&(this._+="L"+r+","+i),e&&(this._+="A".concat(e,",").concat(e,",0,1,1,").concat(t-e,",").concat(n,"A").concat(e,",").concat(e,",0,1,1,").concat(this._x1=r,",").concat(this._y1=i))}rect(t,n,e,r){this._+="M".concat(this._x0=this._x1=+t,",").concat(this._y0=this._y1=+n,"h").concat(+e,"v").concat(+r,"h").concat(-e,"Z")}value(){return this._||null}constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}}class oY{moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}constructor(){this._=[]}}class oH{update(){return this.delaunay.update(),this._init(),this}_init(){let t,n;let{delaunay:{points:e,hull:r,triangles:i},vectors:o}=this,u=this.circumcenters=this._circumcenters.subarray(0,i.length/3*2);for(let o=0,a=0,c=i.length,f,l;o<c;o+=3,a+=2){let c=2*i[o],s=2*i[o+1],h=2*i[o+2],d=e[c],p=e[c+1],g=e[s],v=e[s+1],y=e[h],b=e[h+1],m=g-d,_=v-p,x=y-d,w=b-p,M=(m*w-_*x)*2;if(1e-9>Math.abs(M)){if(void 0===t){for(let i of(t=n=0,r))t+=e[2*i],n+=e[2*i+1];t/=r.length,n/=r.length}let i=1e9*Math.sign((t-d)*w-(n-p)*x);f=(d+y)/2-i*w,l=(p+b)/2+i*x}else{let t=1/M,n=m*m+_*_,e=x*x+w*w;f=d+(w*n-_*e)*t,l=p+(m*e-x*n)*t}u[a]=f,u[a+1]=l}let a=r[r.length-1],c,f=4*a,l,s=e[2*a],h,d=e[2*a+1];o.fill(0);for(let t=0;t<r.length;++t)a=r[t],c=f,l=s,h=d,f=4*a,s=e[2*a],d=e[2*a+1],o[c+2]=o[f]=h-d,o[c+3]=o[f+1]=s-l}render(t){let n=null==t?t=new oU:void 0,{delaunay:{halfedges:e,inedges:r,hull:i},circumcenters:o,vectors:u}=this;if(i.length<=1)return null;for(let n=0,r=e.length;n<r;++n){let r=e[n];if(r<n)continue;let i=2*Math.floor(n/3),u=2*Math.floor(r/3),a=o[i],c=o[i+1],f=o[u],l=o[u+1];this._renderSegment(a,c,f,l,t)}let a,c=i[i.length-1];for(let n=0;n<i.length;++n){a=c;let e=2*Math.floor(r[c=i[n]]/3),f=o[e],l=o[e+1],s=4*a,h=this._project(f,l,u[s+2],u[s+3]);h&&this._renderSegment(f,l,h[0],h[1],t)}return n&&n.value()}renderBounds(t){let n=null==t?t=new oU:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,n){let e=null==n?n=new oU:void 0,r=this._clip(t);if(null===r||!r.length)return;n.moveTo(r[0],r[1]);let i=r.length;for(;r[0]===r[i-2]&&r[1]===r[i-1]&&i>1;)i-=2;for(let t=2;t<i;t+=2)(r[t]!==r[t-2]||r[t+1]!==r[t-1])&&n.lineTo(r[t],r[t+1]);return n.closePath(),e&&e.value()}*cellPolygons(){let{delaunay:{points:t}}=this;for(let n=0,e=t.length/2;n<e;++n){let t=this.cellPolygon(n);t&&(t.index=n,yield t)}}cellPolygon(t){let n=new oY;return this.renderCell(t,n),n.value()}_renderSegment(t,n,e,r,i){let o;let u=this._regioncode(t,n),a=this._regioncode(e,r);0===u&&0===a?(i.moveTo(t,n),i.lineTo(e,r)):(o=this._clipSegment(t,n,e,r,u,a))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,n,e){return(n=+n)==n&&(e=+e)==e&&this.delaunay._step(t,n,e)===t}*neighbors(t){let n=this._clip(t);if(n)for(let e of this.delaunay.neighbors(t)){let t=this._clip(e);if(t){n:for(let r=0,i=n.length;r<i;r+=2)for(let o=0,u=t.length;o<u;o+=2)if(n[r]===t[o]&&n[r+1]===t[o+1]&&n[(r+2)%i]===t[(o+u-2)%u]&&n[(r+3)%i]===t[(o+u-1)%u]){yield e;break n}}}}_cell(t){let{circumcenters:n,delaunay:{inedges:e,halfedges:r,triangles:i}}=this,o=e[t];if(-1===o)return null;let u=[],a=o;do{let e=Math.floor(a/3);if(u.push(n[2*e],n[2*e+1]),i[a=a%3==2?a-2:a+1]!==t)break;a=r[a]}while(a!==o&&-1!==a);return u}_clip(t){if(0===t&&1===this.delaunay.hull.length)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];let n=this._cell(t);if(null===n)return null;let{vectors:e}=this,r=4*t;return this._simplify(e[r]||e[r+1]?this._clipInfinite(t,n,e[r],e[r+1],e[r+2],e[r+3]):this._clipFinite(t,n))}_clipFinite(t,n){let e=n.length,r=null,i,o,u=n[e-2],a=n[e-1],c,f=this._regioncode(u,a),l,s=0;for(let h=0;h<e;h+=2)if(i=u,o=a,u=n[h],a=n[h+1],c=f,f=this._regioncode(u,a),0===c&&0===f)l=s,s=0,r?r.push(u,a):r=[u,a];else{let n,e,h,d,p;if(0===c){if(null===(n=this._clipSegment(i,o,u,a,c,f)))continue;[e,h,d,p]=n}else{if(null===(n=this._clipSegment(u,a,i,o,f,c)))continue;[d,p,e,h]=n,l=s,s=this._edgecode(e,h),l&&s&&this._edge(t,l,s,r,r.length),r?r.push(e,h):r=[e,h]}l=s,s=this._edgecode(d,p),l&&s&&this._edge(t,l,s,r,r.length),r?r.push(d,p):r=[d,p]}if(r)l=s,s=this._edgecode(r[0],r[1]),l&&s&&this._edge(t,l,s,r,r.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return r}_clipSegment(t,n,e,r,i,o){let u=i<o;for(u&&([t,n,e,r,i,o]=[e,r,t,n,o,i]);;){if(0===i&&0===o)return u?[e,r,t,n]:[t,n,e,r];if(i&o)return null;let a,c,f=i||o;8&f?(a=t+(e-t)*(this.ymax-n)/(r-n),c=this.ymax):4&f?(a=t+(e-t)*(this.ymin-n)/(r-n),c=this.ymin):2&f?(c=n+(r-n)*(this.xmax-t)/(e-t),a=this.xmax):(c=n+(r-n)*(this.xmin-t)/(e-t),a=this.xmin),i?(t=a,n=c,i=this._regioncode(t,n)):(e=a,r=c,o=this._regioncode(e,r))}}_clipInfinite(t,n,e,r,i,o){let u=Array.from(n),a;if((a=this._project(u[0],u[1],e,r))&&u.unshift(a[0],a[1]),(a=this._project(u[u.length-2],u[u.length-1],i,o))&&u.push(a[0],a[1]),u=this._clipFinite(t,u))for(let n=0,e=u.length,r,i=this._edgecode(u[e-2],u[e-1]);n<e;n+=2)r=i,i=this._edgecode(u[n],u[n+1]),r&&i&&(n=this._edge(t,r,i,u,n),e=u.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(u=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return u}_edge(t,n,e,r,i){for(;n!==e;){let e,o;switch(n){case 5:n=4;continue;case 4:n=6,e=this.xmax,o=this.ymin;break;case 6:n=2;continue;case 2:n=10,e=this.xmax,o=this.ymax;break;case 10:n=8;continue;case 8:n=9,e=this.xmin,o=this.ymax;break;case 9:n=1;continue;case 1:n=5,e=this.xmin,o=this.ymin}(r[i]!==e||r[i+1]!==o)&&this.contains(t,e,o)&&(r.splice(i,0,e,o),i+=2)}return i}_project(t,n,e,r){let i=1/0,o,u,a;if(r<0){if(n<=this.ymin)return null;(o=(this.ymin-n)/r)<i&&(a=this.ymin,u=t+(i=o)*e)}else if(r>0){if(n>=this.ymax)return null;(o=(this.ymax-n)/r)<i&&(a=this.ymax,u=t+(i=o)*e)}if(e>0){if(t>=this.xmax)return null;(o=(this.xmax-t)/e)<i&&(u=this.xmax,a=n+(i=o)*r)}else if(e<0){if(t<=this.xmin)return null;(o=(this.xmin-t)/e)<i&&(u=this.xmin,a=n+(i=o)*r)}return[u,a]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let n=0;n<t.length;n+=2){let e=(n+2)%t.length,r=(n+4)%t.length;(t[n]===t[e]&&t[e]===t[r]||t[n+1]===t[e+1]&&t[e+1]===t[r+1])&&(t.splice(e,2),n-=2)}t.length||(t=null)}return t}constructor(t,[n,e,r,i]=[0,0,960,500]){if(!((r=+r)>=(n=+n))||!((i=+i)>=(e=+e)))throw Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=n,this.ymax=i,this.ymin=e,this._init()}}let oX=2*Math.PI,oZ=Math.pow;function oW(t){return t[0]}function oJ(t){return t[1]}class oK{static from(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:oW,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:oJ,r=arguments.length>3?arguments[3]:void 0;return new oK("length"in t?function(t,n,e,r){let i=t.length,o=new Float64Array(2*i);for(let u=0;u<i;++u){let i=t[u];o[2*u]=n.call(r,i,u,t),o[2*u+1]=e.call(r,i,u,t)}return o}(t,n,e,r):Float64Array.from(function*(t,n,e,r){let i=0;for(let o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}(t,n,e,r)))}update(){return this._delaunator.update(),this._init(),this}_init(){let t=this._delaunator,n=this.points;if(t.hull&&t.hull.length>2&&function(t){let{triangles:n,coords:e}=t;for(let t=0;t<n.length;t+=3){let r=2*n[t],i=2*n[t+1],o=2*n[t+2];if((e[o]-e[r])*(e[i+1]-e[r+1])-(e[i]-e[r])*(e[o+1]-e[r+1])>1e-10)return!1}return!0}(t)){this.collinear=Int32Array.from({length:n.length/2},(t,n)=>n).sort((t,e)=>n[2*t]-n[2*e]||n[2*t+1]-n[2*e+1]);let t=this.collinear[0],i=this.collinear[this.collinear.length-1],o=[n[2*t],n[2*t+1],n[2*i],n[2*i+1]],u=1e-8*Math.hypot(o[3]-o[1],o[2]-o[0]);for(let t=0,i=n.length/2;t<i;++t){var e,r;let i=[(e=n[2*t])+Math.sin(e+(r=n[2*t+1]))*u,r+Math.cos(e-r)*u];n[2*t]=i[0],n[2*t+1]=i[1]}this._delaunator=new oB(n)}else delete this.collinear;let i=this.halfedges=this._delaunator.halfedges,o=this.hull=this._delaunator.hull,u=this.triangles=this._delaunator.triangles,a=this.inedges.fill(-1),c=this._hullIndex.fill(-1);for(let t=0,n=i.length;t<n;++t){let n=u[t%3==2?t-2:t+1];(-1===i[t]||-1===a[n])&&(a[n]=t)}for(let t=0,n=o.length;t<n;++t)c[o[t]]=t;o.length<=2&&o.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=o[0],a[o[0]]=1,2===o.length&&(a[o[1]]=0,this.triangles[1]=o[1],this.triangles[2]=o[1]))}voronoi(t){return new oH(this,t)}*neighbors(t){let{inedges:n,hull:e,_hullIndex:r,halfedges:i,triangles:o,collinear:u}=this;if(u){let n=u.indexOf(t);n>0&&(yield u[n-1]),n<u.length-1&&(yield u[n+1]);return}let a=n[t];if(-1===a)return;let c=a,f=-1;do{if(yield f=o[c],o[c=c%3==2?c-2:c+1]!==t)return;if(-1===(c=i[c])){let n=e[(r[t]+1)%e.length];n!==f&&(yield n);return}}while(c!==a)}find(t,n){let e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if((t=+t)!=t||(n=+n)!=n)return -1;let i=r;for(;(e=this._step(r,t,n))>=0&&e!==r&&e!==i;)r=e;return e}_step(t,n,e){let{inedges:r,hull:i,_hullIndex:o,halfedges:u,triangles:a,points:c}=this;if(-1===r[t]||!c.length)return(t+1)%(c.length>>1);let f=t,l=oZ(n-c[2*t],2)+oZ(e-c[2*t+1],2),s=r[t],h=s;do{let r=a[h],s=oZ(n-c[2*r],2)+oZ(e-c[2*r+1],2);if(s<l&&(l=s,f=r),a[h=h%3==2?h-2:h+1]!==t)break;if(-1===(h=u[h])){if((h=i[(o[t]+1)%i.length])!==r&&oZ(n-c[2*h],2)+oZ(e-c[2*h+1],2)<l)return h;break}}while(h!==s);return f}render(t){let n=null==t?t=new oU:void 0,{points:e,halfedges:r,triangles:i}=this;for(let n=0,o=r.length;n<o;++n){let o=r[n];if(o<n)continue;let u=2*i[n],a=2*i[o];t.moveTo(e[u],e[u+1]),t.lineTo(e[a],e[a+1])}return this.renderHull(t),n&&n.value()}renderPoints(t,n){void 0!==n||t&&"function"==typeof t.moveTo||(n=t,t=null),n=void 0==n?2:+n;let e=null==t?t=new oU:void 0,{points:r}=this;for(let e=0,i=r.length;e<i;e+=2){let i=r[e],o=r[e+1];t.moveTo(i+n,o),t.arc(i,o,n,0,oX)}return e&&e.value()}renderHull(t){let n=null==t?t=new oU:void 0,{hull:e,points:r}=this,i=2*e[0],o=e.length;t.moveTo(r[i],r[i+1]);for(let n=1;n<o;++n){let i=2*e[n];t.lineTo(r[i],r[i+1])}return t.closePath(),n&&n.value()}hullPolygon(){let t=new oY;return this.renderHull(t),t.value()}renderTriangle(t,n){let e=null==n?n=new oU:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],u=2*i[t+1],a=2*i[t+2];return n.moveTo(r[o],r[o+1]),n.lineTo(r[u],r[u+1]),n.lineTo(r[a],r[a+1]),n.closePath(),e&&e.value()}*trianglePolygons(){let{triangles:t}=this;for(let n=0,e=t.length/3;n<e;++n)yield this.trianglePolygon(n)}trianglePolygon(t){let n=new oY;return this.renderTriangle(t,n),n.value()}constructor(t){this._delaunator=new oB(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}}var oQ=t=>()=>t;function o$(t,n){let{sourceEvent:e,subject:r,target:i,identifier:o,active:u,x:a,y:c,dx:f,dy:l,dispatch:s}=n;Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:r,enumerable:!0,configurable:!0},target:{value:i,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:u,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:c,enumerable:!0,configurable:!0},dx:{value:f,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:s}})}function o0(t){return!t.ctrlKey&&!t.button}function o1(){return this.parentNode}function o2(t,n){return null==n?{x:t.x,y:t.y}:n}function o6(){return navigator.maxTouchPoints||"ontouchstart"in this}function o3(){var t,n,e,r,i=o0,o=o1,u=o2,a=o6,c={},f=nv("start","drag","end"),l=0,s=0;function h(t){t.on("mousedown.drag",d).filter(a).on("touchstart.drag",v).on("touchmove.drag",y,n8).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(u,a){if(!r&&i.call(this,u,a)){var c=m(this,o.call(this,u,a),u,a,"mouse");c&&(n4(u.view).on("mousemove.drag",p,n7).on("mouseup.drag",g,n7),en(u.view),n9(u),e=!1,t=u.clientX,n=u.clientY,c("start",u))}}function p(r){if(et(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>s}c.mouse("drag",r)}function g(t){n4(t.view).on("mousemove.drag mouseup.drag",null),ee(t.view,e),et(t),c.mouse("end",t)}function v(t,n){if(i.call(this,t,n)){var e,r,u=t.changedTouches,a=o.call(this,t,n),c=u.length;for(e=0;e<c;++e)(r=m(this,a,t,n,u[e].identifier,u[e]))&&(n9(t),r("start",t,u[e]))}}function y(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=c[r[n].identifier])&&(et(t),e("drag",t,r[n]))}function b(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=c[i[n].identifier])&&(n9(t),e("end",t,i[n]))}function m(t,n,e,r,i,o){var a,s,d,p=f.copy(),g=e4(o||e,n);if(null!=(d=u.call(t,new o$("beforestart",{sourceEvent:e,target:h,identifier:i,active:l,x:g[0],y:g[1],dx:0,dy:0,dispatch:p}),r)))return a=d.x-g[0]||0,s=d.y-g[1]||0,function e(o,u,f){var v,y=g;switch(o){case"start":c[i]=e,v=l++;break;case"end":delete c[i],--l;case"drag":g=e4(f||u,n),v=l}p.call(o,t,new o$(o,{sourceEvent:u,subject:d,target:h,identifier:i,active:v,x:g[0]+a,y:g[1]+s,dx:g[0]-y[0],dy:g[1]-y[1],dispatch:p}),r)}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:oQ(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:oQ(t),h):o},h.subject=function(t){return arguments.length?(u="function"==typeof t?t:oQ(t),h):u},h.touchable=function(t){return arguments.length?(a="function"==typeof t?t:oQ(!!t),h):a},h.on=function(){var t=f.on.apply(f,arguments);return t===f?h:t},h.clickDistance=function(t){return arguments.length?(s=(t=+t)*t,h):Math.sqrt(s)},h}o$.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var o5={},o4={};function o8(t){return Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+'] || ""'}).join(",")+"}")}function o7(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}function o9(t,n){var e=t+"",r=e.length;return r<n?Array(n-r+1).join(0)+e:e}function ut(t){var n=RegExp('["'+t+"\n\r]"),e=t.charCodeAt(0);function r(t,n){var r,i=[],o=t.length,u=0,a=0,c=o<=0,f=!1;function l(){if(c)return o4;if(f)return f=!1,o5;var n,r,i=u;if(34===t.charCodeAt(i)){for(;u++<o&&34!==t.charCodeAt(u)||34===t.charCodeAt(++u););return(n=u)>=o?c=!0:10===(r=t.charCodeAt(u++))?f=!0:13===r&&(f=!0,10===t.charCodeAt(u)&&++u),t.slice(i+1,n-1).replace(/""/g,'"')}for(;u<o;){if(10===(r=t.charCodeAt(n=u++)))f=!0;else if(13===r)f=!0,10===t.charCodeAt(u)&&++u;else if(r!==e)continue;return t.slice(i,n)}return c=!0,t.slice(i,o)}for(10===t.charCodeAt(o-1)&&--o,13===t.charCodeAt(o-1)&&--o;(r=l())!==o4;){for(var s=[];r!==o5&&r!==o4;)s.push(r),r=l();n&&null==(s=n(s,a++))||i.push(s)}return i}function i(n,e){return n.map(function(n){return e.map(function(t){return u(n[t])}).join(t)})}function o(n){return n.map(u).join(t)}function u(t){var e,r,i,o,u,a;return null==t?"":t instanceof Date?(r=(e=t).getUTCHours(),i=e.getUTCMinutes(),o=e.getUTCSeconds(),u=e.getUTCMilliseconds(),isNaN(e)?"Invalid Date":((a=e.getUTCFullYear())<0?"-"+o9(-a,6):a>9999?"+"+o9(a,6):o9(a,4))+"-"+o9(e.getUTCMonth()+1,2)+"-"+o9(e.getUTCDate(),2)+(u?"T"+o9(r,2)+":"+o9(i,2)+":"+o9(o,2)+"."+o9(u,3)+"Z":o?"T"+o9(r,2)+":"+o9(i,2)+":"+o9(o,2)+"Z":i||r?"T"+o9(r,2)+":"+o9(i,2)+"Z":"")):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var e,i,o=r(t,function(t,r){var o;if(e)return e(t,r-1);i=t,e=n?(o=o8(t),function(e,r){return n(o(e),r,t)}):o8(t)});return o.columns=i||[],o},parseRows:r,format:function(n,e){return null==e&&(e=o7(n)),[e.map(u).join(t)].concat(i(n,e)).join("\n")},formatBody:function(t,n){return null==n&&(n=o7(t)),i(t,n).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:u}}var un=ut(","),ue=un.parse,ur=un.parseRows,ui=un.format,uo=un.formatBody,uu=un.formatRows,ua=un.formatRow,uc=un.formatValue,uf=ut("	"),ul=uf.parse,us=uf.parseRows,uh=uf.format,ud=uf.formatBody,up=uf.formatRows,ug=uf.formatRow,uv=uf.formatValue;function uy(t){for(var n in t){var e,r,i=t[n].trim();if(i){if("true"===i)i=!0;else if("false"===i)i=!1;else if("NaN"===i)i=NaN;else if(isNaN(e=+i)){if(!(r=i.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/)))continue;ub&&r[4]&&!r[7]&&(i=i.replace(/-/g,"/").replace(/T/," ")),i=new Date(i)}else i=e}else i=null;t[n]=i}return t}let ub=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours(),um=t=>+t;function u_(t){return t*t}function ux(t){return t*(2-t)}function uw(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}var uM=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),uT=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),uA=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),uk=Math.PI,uS=uk/2;function uN(t){return 1==+t?1:1-Math.cos(t*uS)}function uE(t){return Math.sin(t*uS)}function uC(t){return(1-Math.cos(uk*t))/2}function uP(t){return(Math.pow(2,-10*t)-9765625e-10)*1.0009775171065494}function uR(t){return uP(1-+t)}function uO(t){return 1-uP(t)}function uD(t){return((t*=2)<=1?uP(1-t):2-uP(t-1))/2}function uz(t){return 1-Math.sqrt(1-t*t)}function uL(t){return Math.sqrt(1- --t*t)}function uj(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var uB=4/11,uV=6/11,uI=8/11,uF=3/4,uG=9/11,uq=10/11,uU=15/16,uY=21/22,uH=63/64,uX=1/(4/11)/(4/11);function uZ(t){return 1-uW(1-t)}function uW(t){return(t=+t)<uB?uX*t*t:t<uI?uX*(t-=uV)*t+uF:t<uq?uX*(t-=uG)*t+uU:uX*(t-=uY)*t+uH}function uJ(t){return((t*=2)<=1?1-uW(1-t):uW(t-1)+1)/2}var uK=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(1.70158),uQ=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(1.70158),u$=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),u0=2*Math.PI,u1=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=u0);function i(t){return n*uP(- --t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*u0)},i.period=function(e){return t(n,e)},i}(1,.3),u2=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=u0);function i(t){return 1-n*uP(t=+t)*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*u0)},i.period=function(e){return t(n,e)},i}(1,.3),u6=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=u0);function i(t){return((t=2*t-1)<0?n*uP(-t)*Math.sin((r-t)/e):2-n*uP(t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*u0)},i.period=function(e){return t(n,e)},i}(1,.3);function u3(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.blob()}function u5(t,n){return fetch(t,n).then(u3)}function u4(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.arrayBuffer()}function u8(t,n){return fetch(t,n).then(u4)}function u7(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.text()}function u9(t,n){return fetch(t,n).then(u7)}function at(t){return function(n,e,r){return 2==arguments.length&&"function"==typeof e&&(r=e,e=void 0),u9(n,e).then(function(n){return t(n,r)})}}function an(t,n,e,r){3==arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=ut(t);return u9(n,e).then(function(t){return i.parse(t,r)})}var ae=at(ue),ar=at(ul);function ai(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})}function ao(t){if(!t.ok)throw Error(t.status+" "+t.statusText);if(204!==t.status&&205!==t.status)return t.json()}function au(t,n){return fetch(t,n).then(ao)}function aa(t){return(n,e)=>u9(n,e).then(n=>(new DOMParser).parseFromString(n,t))}var ac=aa("application/xml"),af=aa("text/html"),al=aa("image/svg+xml");function as(t,n){var e,r=1;function i(){var i,o,u=e.length,a=0,c=0;for(i=0;i<u;++i)a+=(o=e[i]).x,c+=o.y;for(a=(a/u-t)*r,c=(c/u-n)*r,i=0;i<u;++i)o=e[i],o.x-=a,o.y-=c}return null==t&&(t=0),null==n&&(n=0),i.initialize=function(t){e=t},i.x=function(n){return arguments.length?(t=+n,i):t},i.y=function(t){return arguments.length?(n=+t,i):n},i.strength=function(t){return arguments.length?(r=+t,i):r},i}function ah(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,c,f,l,s,h,d=t._root,p={data:r},g=t._x0,v=t._y0,y=t._x1,b=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((f=n>=(o=(g+y)/2))?g=o:y=o,(l=e>=(u=(v+b)/2))?v=u:b=u,i=d,!(d=d[s=l<<1|f]))return i[s]=p,t;if(a=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===a&&e===c)return p.next=d,i?i[s]=p:t._root=p,t;do i=i?i[s]=[,,,,]:t._root=[,,,,],(f=n>=(o=(g+y)/2))?g=o:y=o,(l=e>=(u=(v+b)/2))?v=u:b=u;while((s=l<<1|f)==(h=(c>=u)<<1|a>=o));return i[h]=d,i[s]=p,t}function ad(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function ap(t){return t[0]}function ag(t){return t[1]}function av(t,n,e){var r=new ay(null==n?ap:n,null==e?ag:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function ay(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function ab(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var am=av.prototype=ay.prototype;function a_(t){return function(){return t}}function ax(t){return(t()-.5)*1e-6}function aw(t){return t.x+t.vx}function aM(t){return t.y+t.vy}function aT(t){var n,e,r,i=1,o=1;function u(){for(var t,u,c,f,l,s,h,d=n.length,p=0;p<o;++p)for(t=0,u=av(n,aw,aM).visitAfter(a);t<d;++t)h=(s=e[(c=n[t]).index])*s,f=c.x+c.vx,l=c.y+c.vy,u.visit(g);function g(t,n,e,o,u){var a=t.data,d=t.r,p=s+d;if(a){if(a.index>c.index){var g=f-a.x-a.vx,v=l-a.y-a.vy,y=g*g+v*v;y<p*p&&(0===g&&(y+=(g=ax(r))*g),0===v&&(y+=(v=ax(r))*v),y=(p-(y=Math.sqrt(y)))/y*i,c.vx+=(g*=y)*(p=(d*=d)/(h+d)),c.vy+=(v*=y)*p,a.vx-=g*(p=1-p),a.vy-=v*p)}return}return n>f+p||o<f-p||e>l+p||u<l-p}}function a(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function c(){if(n){var r,i,o=n.length;for(r=0,e=Array(o);r<o;++r)e[(i=n[r]).index]=+t(i,r,n)}}return"function"!=typeof t&&(t=a_(null==t?1:+t)),u.initialize=function(t,e){n=t,r=e,c()},u.iterations=function(t){return arguments.length?(o=+t,u):o},u.strength=function(t){return arguments.length?(i=+t,u):i},u.radius=function(n){return arguments.length?(t="function"==typeof n?n:a_(+n),c(),u):t},u}function aA(t){return t.index}function ak(t,n){var e=t.get(n);if(!e)throw Error("node not found: "+n);return e}function aS(t){var n,e,r,i,o,u,a=aA,c=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},f=a_(30),l=1;function s(r){for(var i=0,a=t.length;i<l;++i)for(var c,f,s,h,d,p,g,v=0;v<a;++v)f=(c=t[v]).source,p=((p=Math.sqrt((h=(s=c.target).x+s.vx-f.x-f.vx||ax(u))*h+(d=s.y+s.vy-f.y-f.vy||ax(u))*d))-e[v])/p*r*n[v],h*=p,d*=p,s.vx-=h*(g=o[v]),s.vy-=d*g,f.vx+=h*(g=1-g),f.vy+=d*g}function h(){if(r){var u,c,f=r.length,l=t.length,s=new Map(r.map((t,n)=>[a(t,n,r),t]));for(u=0,i=Array(f);u<l;++u)(c=t[u]).index=u,"object"!=typeof c.source&&(c.source=ak(s,c.source)),"object"!=typeof c.target&&(c.target=ak(s,c.target)),i[c.source.index]=(i[c.source.index]||0)+1,i[c.target.index]=(i[c.target.index]||0)+1;for(u=0,o=Array(l);u<l;++u)c=t[u],o[u]=i[c.source.index]/(i[c.source.index]+i[c.target.index]);n=Array(l),d(),e=Array(l),p()}}function d(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+c(t[e],e,t)}function p(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+f(t[n],n,t)}return null==t&&(t=[]),s.initialize=function(t,n){r=t,u=n,h()},s.links=function(n){return arguments.length?(t=n,h(),s):t},s.id=function(t){return arguments.length?(a=t,s):a},s.iterations=function(t){return arguments.length?(l=+t,s):l},s.strength=function(t){return arguments.length?(c="function"==typeof t?t:a_(+t),d(),s):c},s.distance=function(t){return arguments.length?(f="function"==typeof t?t:a_(+t),p(),s):f},s}function aN(t){return t.x}function aE(t){return t.y}am.copy=function(){var t,n,e=new ay(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=ab(r),e;for(t=[{source:r,target:e._root=[,,,,]}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=[,,,,]}):r.target[i]=ab(n));return e},am.add=function(t){let n=+this._x.call(null,t),e=+this._y.call(null,t);return ah(this.cover(n,e),n,e,t)},am.addAll=function(t){var n,e,r,i,o=t.length,u=Array(o),a=Array(o),c=1/0,f=1/0,l=-1/0,s=-1/0;for(e=0;e<o;++e)!(isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n)))&&(u[e]=r,a[e]=i,r<c&&(c=r),r>l&&(l=r),i<f&&(f=i),i>s&&(s=i));if(c>l||f>s)return this;for(this.cover(c,f).cover(l,s),e=0;e<o;++e)ah(this,u[e],a[e],t[e]);return this},am.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u,a,c=i-e||1,f=this._root;e>t||t>=i||r>n||n>=o;)switch(a=(n<r)<<1|t<e,(u=[,,,,])[a]=f,f=u,c*=2,a){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},am.data=function(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t},am.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},am.find=function(t,n,e){var r,i,o,u,a,c,f,l=this._x0,s=this._y0,h=this._x1,d=this._y1,p=[],g=this._root;for(g&&p.push(new ad(g,l,s,h,d)),null==e?e=1/0:(l=t-e,s=n-e,h=t+e,d=n+e,e*=e);c=p.pop();)if((g=c.node)&&!((i=c.x0)>h)&&!((o=c.y0)>d)&&!((u=c.x1)<l)&&!((a=c.y1)<s)){if(g.length){var v=(i+u)/2,y=(o+a)/2;p.push(new ad(g[3],v,y,u,a),new ad(g[2],i,y,v,a),new ad(g[1],v,o,u,y),new ad(g[0],i,o,v,y)),(f=(n>=y)<<1|t>=v)&&(c=p[p.length-1],p[p.length-1]=p[p.length-1-f],p[p.length-1-f]=c)}else{var b=t-+this._x.call(null,g.data),m=n-+this._y.call(null,g.data),_=b*b+m*m;if(_<e){var x=Math.sqrt(e=_);l=t-x,s=n-x,h=t+x,d=n+x,r=g.data}}}return r},am.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,c,f,l,s,h,d=this._root,p=this._x0,g=this._y0,v=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((f=o>=(a=(p+v)/2))?p=a:v=a,(l=u>=(c=(g+y)/2))?g=c:y=c,n=d,!(d=d[s=l<<1|f]))return this;if(!d.length)break;(n[s+1&3]||n[s+2&3]||n[s+3&3])&&(e=n,h=s)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return((i=d.next)&&delete d.next,r)?i?r.next=i:delete r.next:n?(i?n[s]=i:delete n[s],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d)):this._root=i,this},am.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},am.root=function(){return this._root},am.size=function(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t},am.visit=function(t){var n,e,r,i,o,u,a=[],c=this._root;for(c&&a.push(new ad(c,this._x0,this._y0,this._x1,this._y1));n=a.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.x1,u=n.y1)&&c.length){var f=(r+o)/2,l=(i+u)/2;(e=c[3])&&a.push(new ad(e,f,l,o,u)),(e=c[2])&&a.push(new ad(e,r,l,f,u)),(e=c[1])&&a.push(new ad(e,f,i,o,l)),(e=c[0])&&a.push(new ad(e,r,i,f,l))}return this},am.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new ad(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,u=n.x0,a=n.y0,c=n.x1,f=n.y1,l=(u+c)/2,s=(a+f)/2;(o=i[0])&&e.push(new ad(o,u,a,l,s)),(o=i[1])&&e.push(new ad(o,l,a,c,s)),(o=i[2])&&e.push(new ad(o,u,s,l,f)),(o=i[3])&&e.push(new ad(o,l,s,c,f))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},am.x=function(t){return arguments.length?(this._x=t,this):this._x},am.y=function(t){return arguments.length?(this._y=t,this):this._y};var aC=Math.PI*(3-Math.sqrt(5));function aP(t){let n;var e,r=1,i=.001,o=1-Math.pow(.001,1/300),u=0,a=.6,c=new Map,f=rl(h),l=nv("tick","end"),s=(n=1,()=>(n=(1664525*n+1013904223)%4294967296)/4294967296);function h(){d(),l.call("tick",e),r<i&&(f.stop(),l.call("end",e))}function d(n){var i,f,l=t.length;void 0===n&&(n=1);for(var s=0;s<n;++s)for(r+=(u-r)*o,c.forEach(function(t){t(r)}),i=0;i<l;++i)null==(f=t[i]).fx?f.x+=f.vx*=a:(f.x=f.fx,f.vx=0),null==f.fy?f.y+=f.vy*=a:(f.y=f.fy,f.vy=0);return e}function p(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=10*Math.sqrt(.5+e),o=e*aC;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function g(n){return n.initialize&&n.initialize(t,s),n}return null==t&&(t=[]),p(),e={tick:d,restart:function(){return f.restart(h),e},stop:function(){return f.stop(),e},nodes:function(n){return arguments.length?(t=n,p(),c.forEach(g),e):t},alpha:function(t){return arguments.length?(r=+t,e):r},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(o=+t,e):+o},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(a=1-t,e):1-a},randomSource:function(t){return arguments.length?(s=t,c.forEach(g),e):s},force:function(t,n){return arguments.length>1?(null==n?c.delete(t):c.set(t,g(n)),e):c.get(t)},find:function(n,e,r){var i,o,u,a,c,f=0,l=t.length;for(null==r?r=1/0:r*=r,f=0;f<l;++f)(u=(i=n-(a=t[f]).x)*i+(o=e-a.y)*o)<r&&(c=a,r=u);return c},on:function(t,n){return arguments.length>1?(l.on(t,n),e):l.on(t)}}}function aR(){var t,n,e,r,i,o=a_(-30),u=1,a=1/0,c=.81;function f(e){var i,o=t.length,u=av(t,aN,aE).visitAfter(s);for(r=e,i=0;i<o;++i)n=t[i],u.visit(h)}function l(){if(t){var n,e,r=t.length;for(n=0,i=Array(r);n<r;++n)i[(e=t[n]).index]=+o(e,n,t)}}function s(t){var n,e,r,o,u,a=0,c=0;if(t.length){for(r=o=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,c+=e,r+=e*n.x,o+=e*n.y);t.x=r/c,t.y=o/c}else{(n=t).x=n.data.x,n.y=n.data.y;do a+=i[n.data.index];while(n=n.next)}t.value=a}function h(t,o,f,l){if(!t.value)return!0;var s=t.x-n.x,h=t.y-n.y,d=l-o,p=s*s+h*h;if(d*d/c<p)return p<a&&(0===s&&(p+=(s=ax(e))*s),0===h&&(p+=(h=ax(e))*h),p<u&&(p=Math.sqrt(u*p)),n.vx+=s*t.value*r/p,n.vy+=h*t.value*r/p),!0;if(!t.length&&!(p>=a)){(t.data!==n||t.next)&&(0===s&&(p+=(s=ax(e))*s),0===h&&(p+=(h=ax(e))*h),p<u&&(p=Math.sqrt(u*p)));do t.data!==n&&(d=i[t.data.index]*r/p,n.vx+=s*d,n.vy+=h*d);while(t=t.next)}}return f.initialize=function(n,r){t=n,e=r,l()},f.strength=function(t){return arguments.length?(o="function"==typeof t?t:a_(+t),l(),f):o},f.distanceMin=function(t){return arguments.length?(u=t*t,f):Math.sqrt(u)},f.distanceMax=function(t){return arguments.length?(a=t*t,f):Math.sqrt(a)},f.theta=function(t){return arguments.length?(c=t*t,f):Math.sqrt(c)},f}function aO(t,n,e){var r,i,o,u=a_(.1);function a(t){for(var u=0,a=r.length;u<a;++u){var c=r[u],f=c.x-n||1e-6,l=c.y-e||1e-6,s=Math.sqrt(f*f+l*l),h=(o[u]-s)*i[u]*t/s;c.vx+=f*h,c.vy+=l*h}}function c(){if(r){var n,e=r.length;for(n=0,i=Array(e),o=Array(e);n<e;++n)o[n]=+t(r[n],n,r),i[n]=isNaN(o[n])?0:+u(r[n],n,r)}}return"function"!=typeof t&&(t=a_(+t)),null==n&&(n=0),null==e&&(e=0),a.initialize=function(t){r=t,c()},a.strength=function(t){return arguments.length?(u="function"==typeof t?t:a_(+t),c(),a):u},a.radius=function(n){return arguments.length?(t="function"==typeof n?n:a_(+n),c(),a):t},a.x=function(t){return arguments.length?(n=+t,a):n},a.y=function(t){return arguments.length?(e=+t,a):e},a}function aD(t){var n,e,r,i=a_(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vx+=(r[o]-i.x)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=a_(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:a_(+t),u(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:a_(+n),u(),o):t},o}function az(t){var n,e,r,i=a_(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vy+=(r[o]-i.y)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=a_(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:a_(+t),u(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:a_(+n),u(),o):t},o}function aL(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function aj(t){return(t=aL(Math.abs(t)))?t[1]:NaN}var aB=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function aV(t){var n;if(!(n=aB.exec(t)))throw Error("invalid format: "+t);return new aI({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function aI(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function aF(t,n){var e=aL(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+Array(i-r.length+2).join("0")}aV.prototype=aI.prototype,aI.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var aG={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>aF(100*t,n),r:aF,s:function(t,n){var e=aL(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(cv=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=r.length;return o===u?r:o>u?r+Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+Array(1-o).join("0")+aL(t,Math.max(0,n+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function aq(t){return t}var aU=Array.prototype.map,aY=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aH(t){var n,e,r,i=void 0===t.grouping||void 0===t.thousands?aq:(n=aU.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,o=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(t.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(e)}),o=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?aq:(r=aU.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return r[+t]})}),f=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function h(t){var n=(t=aV(t)).fill,e=t.align,r=t.sign,h=t.symbol,d=t.zero,p=t.width,g=t.comma,v=t.precision,y=t.trim,b=t.type;"n"===b?(g=!0,b="g"):aG[b]||(void 0===v&&(v=12),y=!0,b="g"),(d||"0"===n&&"="===e)&&(d=!0,n="0",e="=");var m="$"===h?o:"#"===h&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",_="$"===h?u:/[%p]/.test(b)?f:"",x=aG[b],w=/[defgprs%]/.test(b);function M(t){var o,u,f,h=m,M=_;if("c"===b)M=x(t)+M,t="";else{var T=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:x(Math.abs(t),v),y&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),T&&0==+t&&"+"!==r&&(T=!1),h=(T?"("===r?r:l:"-"===r||"("===r?"":r)+h,M=("s"===b?aY[8+cv/3]:"")+M+(T&&"("===r?")":""),w){for(o=-1,u=t.length;++o<u;)if(48>(f=t.charCodeAt(o))||f>57){M=(46===f?a+t.slice(o+1):t.slice(o))+M,t=t.slice(0,o);break}}}g&&!d&&(t=i(t,1/0));var A=h.length+t.length+M.length,k=A<p?Array(p-A+1).join(n):"";switch(g&&d&&(t=i(k+t,k.length?p-M.length:1/0),k=""),e){case"<":t=h+t+M+k;break;case"=":t=h+k+t+M;break;case"^":t=k.slice(0,A=k.length>>1)+h+t+M+k.slice(A);break;default:t=k+h+t+M}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),M.toString=function(){return t+""},M}return{format:h,formatPrefix:function(t,n){var e=h(((t=aV(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(aj(n)/3))),i=Math.pow(10,-r),o=aY[8+r/3];return function(t){return e(i*t)+o}}}}function aX(t){return cb=(cy=aH(t)).format,cm=cy.formatPrefix,cy}function aZ(t){return Math.max(0,-aj(Math.abs(t)))}function aW(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(aj(n)/3)))-aj(Math.abs(t)))}function aJ(t,n){return Math.max(0,aj(n=Math.abs(n)-(t=Math.abs(t)))-aj(t))+1}aX({thousands:",",grouping:[3],currency:["$",""]});var aK=Math.PI,aQ=aK/2,a$=aK/4,a0=2*aK,a1=180/aK,a2=aK/180,a6=Math.abs,a3=Math.atan,a5=Math.atan2,a4=Math.cos,a8=Math.ceil,a7=Math.exp,a9=Math.hypot,ct=Math.log,cn=Math.pow,ce=Math.sin,cr=Math.sign||function(t){return t>0?1:t<0?-1:0},ci=Math.sqrt,co=Math.tan;function cu(t){return t>1?0:t<-1?aK:Math.acos(t)}function ca(t){return t>1?aQ:t<-1?-aQ:Math.asin(t)}function cc(){}function cf(t,n){t&&cs.hasOwnProperty(t.type)&&cs[t.type](t,n)}var cl={Feature:function(t,n){cf(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)cf(e[r].geometry,n)}},cs={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){ch(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)ch(e[r],n,0)},Polygon:function(t,n){cd(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)cd(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)cf(e[r],n)}};function ch(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function cd(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)ch(t[e],n,1);n.polygonEnd()}function cp(t,n){t&&cl.hasOwnProperty(t.type)?cl[t.type](t,n):cf(t,n)}var cg,cv,cy,cb,cm,c_,cx,cw,cM,cT,cA=new B,ck=new B,cS={point:cc,lineStart:cc,lineEnd:cc,polygonStart:function(){cA=new B,cS.lineStart=cN,cS.lineEnd=cE},polygonEnd:function(){var t=+cA;ck.add(t<0?a0+t:t),this.lineStart=this.lineEnd=this.point=cc},sphere:function(){ck.add(a0)}};function cN(){cS.point=cC}function cE(){cP(c_,cx)}function cC(t,n){cS.point=cP,c_=t,cx=n,t*=a2,n*=a2,cw=t,cM=a4(n=n/2+a$),cT=ce(n)}function cP(t,n){t*=a2,n*=a2;var e=t-cw,r=e>=0?1:-1,i=r*e,o=a4(n=n/2+a$),u=ce(n),a=cT*u,c=cM*o+a*a4(i),f=a*r*ce(i);cA.add(a5(f,c)),cw=t,cM=o,cT=u}function cR(t){return ck=new B,cp(t,cS),2*ck}function cO(t){return[a5(t[1],t[0]),ca(t[2])]}function cD(t){var n=t[0],e=t[1],r=a4(e);return[r*a4(n),r*ce(n),ce(e)]}function cz(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function cL(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function cj(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function cB(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function cV(t){var n=ci(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var cI={point:cF,lineStart:cq,lineEnd:cU,polygonStart:function(){cI.point=cY,cI.lineStart=cH,cI.lineEnd=cX,f0=new B,cS.polygonStart()},polygonEnd:function(){cS.polygonEnd(),cI.point=cF,cI.lineStart=cq,cI.lineEnd=cU,cA<0?(fH=-(fZ=180),fX=-(fW=90)):f0>1e-6?fW=90:f0<-.000001&&(fX=-90),f2[0]=fH,f2[1]=fZ},sphere:function(){fH=-(fZ=180),fX=-(fW=90)}};function cF(t,n){f1.push(f2=[fH=t,fZ=t]),n<fX&&(fX=n),n>fW&&(fW=n)}function cG(t,n){var e=cD([t*a2,n*a2]);if(f$){var r=cL(f$,e),i=cL([r[1],-r[0],0],r);cV(i),i=cO(i);var o,u=t-fJ,a=u>0?1:-1,c=i[0]*a1*a,f=a6(u)>180;f^(a*fJ<c&&c<a*t)?(o=i[1]*a1)>fW&&(fW=o):f^(a*fJ<(c=(c+360)%360-180)&&c<a*t)?(o=-i[1]*a1)<fX&&(fX=o):(n<fX&&(fX=n),n>fW&&(fW=n)),f?t<fJ?cZ(fH,t)>cZ(fH,fZ)&&(fZ=t):cZ(t,fZ)>cZ(fH,fZ)&&(fH=t):fZ>=fH?(t<fH&&(fH=t),t>fZ&&(fZ=t)):t>fJ?cZ(fH,t)>cZ(fH,fZ)&&(fZ=t):cZ(t,fZ)>cZ(fH,fZ)&&(fH=t)}else f1.push(f2=[fH=t,fZ=t]);n<fX&&(fX=n),n>fW&&(fW=n),f$=e,fJ=t}function cq(){cI.point=cG}function cU(){f2[0]=fH,f2[1]=fZ,cI.point=cF,f$=null}function cY(t,n){if(f$){var e=t-fJ;f0.add(a6(e)>180?e+(e>0?360:-360):e)}else fK=t,fQ=n;cS.point(t,n),cG(t,n)}function cH(){cS.lineStart()}function cX(){cY(fK,fQ),cS.lineEnd(),a6(f0)>1e-6&&(fH=-(fZ=180)),f2[0]=fH,f2[1]=fZ,f$=null}function cZ(t,n){return(n-=t)<0?n+360:n}function cW(t,n){return t[0]-n[0]}function cJ(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function cK(t){var n,e,r,i,o,u,a;if(fW=fZ=-(fH=fX=1/0),f1=[],cp(t,cI),e=f1.length){for(f1.sort(cW),n=1,o=[r=f1[0]];n<e;++n)cJ(r,(i=f1[n])[0])||cJ(r,i[1])?(cZ(r[0],i[1])>cZ(r[0],r[1])&&(r[1]=i[1]),cZ(i[0],r[1])>cZ(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,e=o.length-1,n=0,r=o[e];n<=e;r=i,++n)i=o[n],(a=cZ(r[1],i[0]))>u&&(u=a,fH=i[0],fZ=r[1])}return f1=f2=null,fH===1/0||fX===1/0?[[NaN,NaN],[NaN,NaN]]:[[fH,fX],[fZ,fW]]}var cQ={sphere:cc,point:c$,lineStart:c1,lineEnd:c3,polygonStart:function(){cQ.lineStart=c5,cQ.lineEnd=c4},polygonEnd:function(){cQ.lineStart=c1,cQ.lineEnd=c3}};function c$(t,n){t*=a2;var e=a4(n*=a2);c0(e*a4(t),e*ce(t),ce(n))}function c0(t,n,e){++f6,f5+=(t-f5)/f6,f4+=(n-f4)/f6,f8+=(e-f8)/f6}function c1(){cQ.point=c2}function c2(t,n){t*=a2;var e=a4(n*=a2);lu=e*a4(t),la=e*ce(t),lc=ce(n),cQ.point=c6,c0(lu,la,lc)}function c6(t,n){t*=a2;var e=a4(n*=a2),r=e*a4(t),i=e*ce(t),o=ce(n),u=a5(ci((u=la*o-lc*i)*u+(u=lc*r-lu*o)*u+(u=lu*i-la*r)*u),lu*r+la*i+lc*o);f3+=u,f7+=u*(lu+(lu=r)),f9+=u*(la+(la=i)),lt+=u*(lc+(lc=o)),c0(lu,la,lc)}function c3(){cQ.point=c$}function c5(){cQ.point=c8}function c4(){c7(li,lo),cQ.point=c$}function c8(t,n){li=t,lo=n,t*=a2,n*=a2,cQ.point=c7;var e=a4(n);lu=e*a4(t),la=e*ce(t),lc=ce(n),c0(lu,la,lc)}function c7(t,n){t*=a2;var e=a4(n*=a2),r=e*a4(t),i=e*ce(t),o=ce(n),u=la*o-lc*i,a=lc*r-lu*o,c=lu*i-la*r,f=a9(u,a,c),l=ca(f),s=f&&-l/f;ln.add(s*u),le.add(s*a),lr.add(s*c),f3+=l,f7+=l*(lu+(lu=r)),f9+=l*(la+(la=i)),lt+=l*(lc+(lc=o)),c0(lu,la,lc)}function c9(t){f6=f3=f5=f4=f8=f7=f9=lt=0,ln=new B,le=new B,lr=new B,cp(t,cQ);var n=+ln,e=+le,r=+lr,i=a9(n,e,r);return i<1e-12&&(n=f7,e=f9,r=lt,f3<1e-6&&(n=f5,e=f4,r=f8),(i=a9(n,e,r))<1e-12)?[NaN,NaN]:[a5(e,n)*a1,ca(r/i)*a1]}function ft(t){return function(){return t}}function fn(t,n){function e(e,r){return n((e=t(e,r))[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function fe(t,n){return a6(t)>aK&&(t-=Math.round(t/a0)*a0),[t,n]}function fr(t,n,e){return(t%=a0)?n||e?fn(fo(t),fu(n,e)):fo(t):n||e?fu(n,e):fe}function fi(t){return function(n,e){return a6(n+=t)>aK&&(n-=Math.round(n/a0)*a0),[n,e]}}function fo(t){var n=fi(t);return n.invert=fi(-t),n}function fu(t,n){var e=a4(t),r=ce(t),i=a4(n),o=ce(n);function u(t,n){var u=a4(n),a=a4(t)*u,c=ce(t)*u,f=ce(n),l=f*e+a*r;return[a5(c*i-l*o,a*e-f*r),ca(l*i+c*o)]}return u.invert=function(t,n){var u=a4(n),a=a4(t)*u,c=ce(t)*u,f=ce(n),l=f*i-c*o;return[a5(c*i+f*o,a*e+l*r),ca(l*e-a*r)]},u}function fa(t){function n(n){return n=t(n[0]*a2,n[1]*a2),n[0]*=a1,n[1]*=a1,n}return t=fr(t[0]*a2,t[1]*a2,t.length>2?t[2]*a2:0),n.invert=function(n){return n=t.invert(n[0]*a2,n[1]*a2),n[0]*=a1,n[1]*=a1,n},n}function fc(t,n,e,r,i,o){if(e){var u=a4(n),a=ce(n),c=r*e;null==i?(i=n+r*a0,o=n-c/2):(i=ff(u,i),o=ff(u,o),(r>0?i<o:i>o)&&(i+=r*a0));for(var f,l=i;r>0?l>o:l<o;l-=c)f=cO([u,-a*a4(l),-a*ce(l)]),t.point(f[0],f[1])}}function ff(t,n){n=cD(n),n[0]-=t,cV(n);var e=cu(-n[1]);return((0>-n[2]?-e:e)+a0-1e-6)%a0}function fl(){var t,n,e=ft([0,0]),r=ft(90),i=ft(2),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=a1,e[1]*=a1}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*a2,c=i.apply(this,arguments)*a2;return t=[],n=fr(-u[0]*a2,-u[1]*a2,0).invert,fc(o,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"==typeof t?t:ft([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"==typeof t?t:ft(+t),u):r},u.precision=function(t){return arguments.length?(i="function"==typeof t?t:ft(+t),u):i},u}function fs(){var t,n=[];return{point:function(n,e,r){t.push([n,e,r])},lineStart:function(){n.push(t=[])},lineEnd:cc,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function fh(t,n){return 1e-6>a6(t[0]-n[0])&&1e-6>a6(t[1]-n[1])}function fd(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function fp(t,n,e,r,i){var o,u,a=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(fh(r,u)){if(!r[2]&&!u[2]){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd();return}u[0]+=2e-6}a.push(e=new fd(r,t,null,!0)),c.push(e.o=new fd(r,null,e,!1)),a.push(e=new fd(u,t,null,!1)),c.push(e.o=new fd(u,null,e,!0))}}),a.length){for(c.sort(n),fg(a),fg(c),o=0,u=c.length;o<u;++o)c[o].e=e=!e;for(var f,l,s=a[0];;){for(var h=s,d=!0;h.v;)if((h=h.n)===s)return;f=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,u=f.length;o<u;++o)i.point((l=f[o])[0],l[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(o=(f=h.p.z).length-1;o>=0;--o)i.point((l=f[o])[0],l[1]);else r(h.x,h.p.x,-1,i);h=h.p}f=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}}function fg(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}function fv(t){return a6(t[0])<=aK?t[0]:cr(t[0])*((a6(t[0])+aK)%a0-aK)}function fy(t,n){var e=fv(n),r=n[1],i=ce(r),o=[ce(e),-a4(e),0],u=0,a=0,c=new B;1===i?r=aQ+1e-6:-1===i&&(r=-aQ-1e-6);for(var f=0,l=t.length;f<l;++f)if(h=(s=t[f]).length)for(var s,h,d=s[h-1],p=fv(d),g=d[1]/2+a$,v=ce(g),y=a4(g),b=0;b<h;++b,p=_,v=w,y=M,d=m){var m=s[b],_=fv(m),x=m[1]/2+a$,w=ce(x),M=a4(x),T=_-p,A=T>=0?1:-1,k=A*T,S=k>aK,N=v*w;if(c.add(a5(N*A*ce(k),y*M+N*a4(k))),u+=S?T+A*a0:T,S^p>=e^_>=e){var E=cL(cD(d),cD(m));cV(E);var C=cL(o,E);cV(C);var P=(S^T>=0?-1:1)*ca(C[2]);(r>P||r===P&&(E[0]||E[1]))&&(a+=S^T>=0?1:-1)}}return(u<-.000001||u<1e-6&&c<-.000000000001)^1&a}function fb(t,n,e,r){return function(i){var o,u,a,c=n(i),f=fs(),l=n(f),s=!1,h={point:d,lineStart:g,lineEnd:v,polygonStart:function(){h.point=y,h.lineStart=b,h.lineEnd=m,u=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=g,h.lineEnd=v,u=tj(u);var t=fy(o,r);u.length?(s||(i.polygonStart(),s=!0),fp(u,f_,t,e,i)):t&&(s||(i.polygonStart(),s=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),s&&(i.polygonEnd(),s=!1),u=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(n,e){t(n,e)&&i.point(n,e)}function p(t,n){c.point(t,n)}function g(){h.point=p,c.lineStart()}function v(){h.point=d,c.lineEnd()}function y(t,n){a.push([t,n]),l.point(t,n)}function b(){l.lineStart(),a=[]}function m(){y(a[0][0],a[0][1]),l.lineEnd();var t,n,e,r,c=l.clean(),h=f.result(),d=h.length;if(a.pop(),o.push(a),a=null,d){if(1&c){if((n=(e=h[0]).length-1)>0){for(s||(i.polygonStart(),s=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}return}d>1&&2&c&&h.push(h.pop().concat(h.shift())),u.push(h.filter(fm))}}return h}}function fm(t){return t.length>1}function f_(t,n){return((t=t.x)[0]<0?t[1]-aQ-1e-6:aQ-t[1])-((n=n.x)[0]<0?n[1]-aQ-1e-6:aQ-n[1])}fe.invert=fe;var fx=fb(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var a,c,f,l,s,h,d=o>0?aK:-aK,p=a6(o-e);1e-6>a6(p-aK)?(t.point(e,r=(r+u)/2>0?aQ:-aQ),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),t.point(o,r),n=0):i!==d&&p>=aK&&(1e-6>a6(e-i)&&(e-=1e-6*i),1e-6>a6(o-d)&&(o-=1e-6*d),a=e,c=r,r=a6(h=ce(a-(f=o)))>1e-6?a3((ce(c)*(s=a4(u))*ce(f)-ce(u)*(l=a4(c))*ce(a))/(l*s*h)):(c+u)/2,t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),n=0),t.point(e=o,r=u),i=d},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*aQ,r.point(-aK,i),r.point(0,i),r.point(aK,i),r.point(aK,0),r.point(aK,-i),r.point(0,-i),r.point(-aK,-i),r.point(-aK,0),r.point(-aK,i);else if(a6(t[0]-n[0])>1e-6){var o=t[0]<n[0]?aK:-aK;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-aK,-aQ]);function fw(t){var n=a4(t),e=2*a2,r=n>0,i=a6(n)>1e-6;function o(t,e){return a4(t)*a4(e)>n}function u(t,e,r){var i=cD(t),o=cD(e),u=[1,0,0],a=cL(i,o),c=cz(a,a),f=a[0],l=c-f*f;if(!l)return!r&&t;var s=cL(u,a),h=cB(u,n*c/l);cj(h,cB(a,-n*f/l));var d=cz(h,s),p=cz(s,s),g=d*d-p*(cz(h,h)-1);if(!(g<0)){var v=ci(g),y=cB(s,(-d-v)/p);if(cj(y,h),y=cO(y),!r)return y;var b,m=t[0],_=e[0],x=t[1],w=e[1];_<m&&(b=m,m=_,_=b);var M=_-m,T=1e-6>a6(M-aK);if(!T&&w<x&&(b=x,x=w,w=b),T||M<1e-6?T?x+w>0^y[1]<(1e-6>a6(y[0]-m)?x:w):x<=y[1]&&y[1]<=w:M>aK^(m<=y[0]&&y[0]<=_)){var A=cB(s,(-d+v)/p);return cj(A,h),[y,cO(A)]}}}function a(n,e){var i=r?t:aK-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return fb(o,function(t){var n,e,c,f,l;return{lineStart:function(){f=c=!1,l=1},point:function(s,h){var d,p,g=[s,h],v=o(s,h),y=r?v?0:a(s,h):v?a(s+(s<0?aK:-aK),h):0;!n&&(f=c=v)&&t.lineStart(),v!==c&&(!(p=u(n,g))||fh(n,p)||fh(g,p))&&(g[2]=1),v!==c?(l=0,v?(t.lineStart(),p=u(g,n),t.point(p[0],p[1])):(p=u(n,g),t.point(p[0],p[1],2),t.lineEnd()),n=p):i&&n&&r^v&&!(y&e)&&(d=u(g,n,!0))&&(l=0,r?(t.lineStart(),t.point(d[0][0],d[0][1]),t.point(d[1][0],d[1][1]),t.lineEnd()):(t.point(d[1][0],d[1][1]),t.lineEnd(),t.lineStart(),t.point(d[0][0],d[0][1],3))),!v||n&&fh(n,g)||t.point(g[0],g[1]),n=g,c=v,e=y},lineEnd:function(){c&&t.lineEnd(),n=null},clean:function(){return l|(f&&c)<<1}}},function(n,r,i,o){fc(o,t,e,i,n,r)},r?[0,-t]:[-aK,t-aK])}function fM(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,a,f){var l=0,s=0;if(null==i||(l=u(i,a))!==(s=u(o,a))||0>c(i,o)^a>0)do f.point(0===l||3===l?t:e,l>1?r:n);while((l=(l+a+4)%4)!==s);else f.point(o[0],o[1])}function u(r,i){return 1e-6>a6(r[0]-t)?i>0?0:3:1e-6>a6(r[0]-e)?i>0?2:1:1e-6>a6(r[1]-n)?i>0?1:0:i>0?3:2}function a(t,n){return c(t.x,n.x)}function c(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var c,f,l,s,h,d,p,g,v,y,b,m=u,_=fs(),x={point:w,lineStart:function(){x.point=M,f&&f.push(l=[]),y=!0,v=!1,p=g=NaN},lineEnd:function(){c&&(M(s,h),d&&v&&_.rejoin(),c.push(_.result())),x.point=w,v&&m.lineEnd()},polygonStart:function(){m=_,c=[],f=[],b=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=f.length;e<i;++e)for(var o,u,a=f[e],c=1,l=a.length,s=a[0],h=s[0],d=s[1];c<l;++c)o=h,u=d,h=(s=a[c])[0],d=s[1],u<=r?d>r&&(h-o)*(r-u)>(d-u)*(t-o)&&++n:d<=r&&(h-o)*(r-u)<(d-u)*(t-o)&&--n;return n}(),e=b&&n,i=(c=tj(c)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&fp(c,a,n,o,u),u.polygonEnd()),m=u,c=f=l=null}};function w(t,n){i(t,n)&&m.point(t,n)}function M(o,u){var a=i(o,u);if(f&&l.push([o,u]),y)s=o,h=u,d=a,y=!1,a&&(m.lineStart(),m.point(o,u));else if(a&&v)m.point(o,u);else{var c=[p=Math.max(-1e9,Math.min(1e9,p)),g=Math.max(-1e9,Math.min(1e9,g))],_=[o=Math.max(-1e9,Math.min(1e9,o)),u=Math.max(-1e9,Math.min(1e9,u))];!function(t,n,e,r,i,o){var u,a=t[0],c=t[1],f=n[0],l=n[1],s=0,h=1,d=f-a,p=l-c;if(u=e-a,d||!(u>0)){if(u/=d,d<0){if(u<s)return;u<h&&(h=u)}else if(d>0){if(u>h)return;u>s&&(s=u)}if(u=i-a,d||!(u<0)){if(u/=d,d<0){if(u>h)return;u>s&&(s=u)}else if(d>0){if(u<s)return;u<h&&(h=u)}if(u=r-c,p||!(u>0)){if(u/=p,p<0){if(u<s)return;u<h&&(h=u)}else if(p>0){if(u>h)return;u>s&&(s=u)}if(u=o-c,p||!(u<0)){if(u/=p,p<0){if(u>h)return;u>s&&(s=u)}else if(p>0){if(u<s)return;u<h&&(h=u)}return s>0&&(t[0]=a+s*d,t[1]=c+s*p),h<1&&(n[0]=a+h*d,n[1]=c+h*p),!0}}}}}(c,_,t,n,e,r)?a&&(m.lineStart(),m.point(o,u),b=!1):(v||(m.lineStart(),m.point(c[0],c[1])),m.point(_[0],_[1]),a||m.lineEnd(),b=!1)}p=o,g=u,v=a}return x}}function fT(){var t,n,e,r=0,i=0,o=960,u=500;return e={stream:function(e){return t&&n===e?t:t=fM(r,i,o,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],i=+a[0][1],o=+a[1][0],u=+a[1][1],t=n=null,e):[[r,i],[o,u]]}}}var fA={sphere:cc,point:cc,lineStart:function(){fA.point=fS,fA.lineEnd=fk},lineEnd:cc,polygonStart:cc,polygonEnd:cc};function fk(){fA.point=fA.lineEnd=cc}function fS(t,n){t*=a2,n*=a2,ll=t,ls=ce(n),lh=a4(n),fA.point=fN}function fN(t,n){t*=a2;var e=ce(n*=a2),r=a4(n),i=a6(t-ll),o=a4(i),u=r*ce(i),a=lh*e-ls*r*o,c=ls*e+lh*r*o;lf.add(a5(ci(u*u+a*a),c)),ll=t,ls=e,lh=r}function fE(t){return lf=new B,cp(t,fA),+lf}var fC=[null,null],fP={type:"LineString",coordinates:fC};function fR(t,n){return fC[0]=t,fC[1]=n,fE(fP)}var fO={Feature:function(t,n){return fz(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(fz(e[r].geometry,n))return!0;return!1}},fD={Sphere:function(){return!0},Point:function(t,n){return 0===fR(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(0===fR(e[r],n))return!0;return!1},LineString:function(t,n){return fL(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(fL(e[r],n))return!0;return!1},Polygon:function(t,n){return fj(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(fj(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(fz(e[r],n))return!0;return!1}};function fz(t,n){return!!(t&&fD.hasOwnProperty(t.type))&&fD[t.type](t,n)}function fL(t,n){for(var e,r,i,o=0,u=t.length;o<u;o++){if(0===(r=fR(t[o],n))||o>0&&(i=fR(t[o],t[o-1]))>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<1e-12*i)return!0;e=r}return!1}function fj(t,n){return!!fy(t.map(fB),fV(n))}function fB(t){return(t=t.map(fV)).pop(),t}function fV(t){return[t[0]*a2,t[1]*a2]}function fI(t,n){return(t&&fO.hasOwnProperty(t.type)?fO[t.type]:fz)(t,n)}function fF(t,n,e){var r=tF(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function fG(t,n,e){var r=tF(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function fq(){var t,n,e,r,i,o,u,a,c,f,l,s,h=10,d=10,p=90,g=360,v=2.5;function y(){return{type:"MultiLineString",coordinates:b()}}function b(){return tF(a8(r/p)*p,e,p).map(l).concat(tF(a8(a/g)*g,u,g).map(s)).concat(tF(a8(n/h)*h,t,h).filter(function(t){return a6(t%p)>1e-6}).map(c)).concat(tF(a8(o/d)*d,i,d).filter(function(t){return a6(t%g)>1e-6}).map(f))}return y.lines=function(){return b().map(function(t){return{type:"LineString",coordinates:t}})},y.outline=function(){return{type:"Polygon",coordinates:[l(r).concat(s(u).slice(1),l(e).reverse().slice(1),s(a).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],a=+t[0][1],u=+t[1][1],r>e&&(t=r,r=e,e=t),a>u&&(t=a,a=u,u=t),y.precision(v)):[[r,a],[e,u]]},y.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),y.precision(v)):[[n,o],[t,i]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(p=+t[0],g=+t[1],y):[p,g]},y.stepMinor=function(t){return arguments.length?(h=+t[0],d=+t[1],y):[h,d]},y.precision=function(h){return arguments.length?(v=+h,c=fF(o,i,90),f=fG(n,t,v),l=fF(a,u,90),s=fG(r,e,v),y):v},y.extentMajor([[-180,-89.999999],[180,89.999999]]).extentMinor([[-180,-80.000001],[180,80.000001]])}function fU(){return fq()()}function fY(t,n){var e,r,i=t[0]*a2,o=t[1]*a2,u=n[0]*a2,a=n[1]*a2,c=a4(o),f=ce(o),l=a4(a),s=ce(a),h=c*a4(i),d=c*ce(i),p=l*a4(u),g=l*ce(u),v=2*ca(ci((e=ce((e=a-o)/2))*e+c*l*((r=ce((r=u-i)/2))*r))),y=ce(v),b=v?function(t){var n=ce(t*=v)/y,e=ce(v-t)/y,r=e*h+n*p,i=e*d+n*g;return[a5(i,r)*a1,a5(e*f+n*s,ci(r*r+i*i))*a1]}:function(){return[i*a1,o*a1]};return b.distance=v,b}var fH,fX,fZ,fW,fJ,fK,fQ,f$,f0,f1,f2,f6,f3,f5,f4,f8,f7,f9,lt,ln,le,lr,li,lo,lu,la,lc,lf,ll,ls,lh,ld,lp,lg,lv,ly=t=>t,lb=new B,lm=new B,l_={point:cc,lineStart:cc,lineEnd:cc,polygonStart:function(){l_.lineStart=lx,l_.lineEnd=lT},polygonEnd:function(){l_.lineStart=l_.lineEnd=l_.point=cc,lb.add(a6(lm)),lm=new B},result:function(){var t=lb/2;return lb=new B,t}};function lx(){l_.point=lw}function lw(t,n){l_.point=lM,ld=lg=t,lp=lv=n}function lM(t,n){lm.add(lv*t-lg*n),lg=t,lv=n}function lT(){lM(ld,lp)}var lA,lk,lS,lN,lE=1/0,lC=1/0,lP=-1/0,lR=lP,lO={point:function(t,n){t<lE&&(lE=t),t>lP&&(lP=t),n<lC&&(lC=n),n>lR&&(lR=n)},lineStart:cc,lineEnd:cc,polygonStart:cc,polygonEnd:cc,result:function(){var t=[[lE,lC],[lP,lR]];return lP=lR=-(lC=lE=1/0),t}},lD=0,lz=0,lL=0,lj=0,lB=0,lV=0,lI=0,lF=0,lG=0,lq={point:lU,lineStart:lY,lineEnd:lZ,polygonStart:function(){lq.lineStart=lW,lq.lineEnd=lJ},polygonEnd:function(){lq.point=lU,lq.lineStart=lY,lq.lineEnd=lZ},result:function(){var t=lG?[lI/lG,lF/lG]:lV?[lj/lV,lB/lV]:lL?[lD/lL,lz/lL]:[NaN,NaN];return lD=lz=lL=lj=lB=lV=lI=lF=lG=0,t}};function lU(t,n){lD+=t,lz+=n,++lL}function lY(){lq.point=lH}function lH(t,n){lq.point=lX,lU(lS=t,lN=n)}function lX(t,n){var e=t-lS,r=n-lN,i=ci(e*e+r*r);lj+=i*(lS+t)/2,lB+=i*(lN+n)/2,lV+=i,lU(lS=t,lN=n)}function lZ(){lq.point=lU}function lW(){lq.point=lK}function lJ(){lQ(lA,lk)}function lK(t,n){lq.point=lQ,lU(lA=lS=t,lk=lN=n)}function lQ(t,n){var e=t-lS,r=n-lN,i=ci(e*e+r*r);lj+=i*(lS+t)/2,lB+=i*(lN+n)/2,lV+=i,lI+=(i=lN*t-lS*n)*(lS+t),lF+=i*(lN+n),lG+=3*i,lU(lS=t,lN=n)}function l$(t){this._context=t}l$.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,a0)}},result:cc};var l0,l1,l2,l6,l3,l5=new B,l4={point:cc,lineStart:function(){l4.point=l8},lineEnd:function(){l0&&l7(l1,l2),l4.point=cc},polygonStart:function(){l0=!0},polygonEnd:function(){l0=null},result:function(){var t=+l5;return l5=new B,t}};function l8(t,n){l4.point=l7,l1=l6=t,l2=l3=n}function l7(t,n){l6-=t,l3-=n,l5.add(ci(l6*l6+l3*l3)),l6=t,l3=n}function l9(){let t=iM(["M",",",""]);return l9=function(){return t},t}function st(){let t=iM(["L",",",""]);return st=function(){return t},t}function sn(){let t=iM(["M",",",""]);return sn=function(){return t},t}function se(){let t=iM(["m0,","a",","," 0 1,1 0,","a",","," 0 1,1 0,","z"]);return se=function(){return t},t}class sr{pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:this._append(l9(),t,n),this._point=1;break;case 1:this._append(st(),t,n);break;default:if(this._append(sn(),t,n),this._radius!==o||this._append!==i){let t=this._radius,n=this._;this._="",this._append(se(),t,t,t,-2*t,t,t,2*t),o=t,i=this._append,u=this._,this._=n}this._+=u}}result(){let t=this._;return this._="",t.length?t:null}constructor(t){this._append=null==t?si:function(t){let n=Math.floor(t);if(!(n>=0))throw RangeError("invalid digits: ".concat(t));if(n>15)return si;if(n!==r){let t=10**n;r=n,i=function(n){let e=1;this._+=n[0];for(let r=n.length;e<r;++e)this._+=Math.round(arguments[e]*t)/t+n[e]}}return i}(t),this._radius=4.5,this._=""}}function si(t){let n=1;this._+=t[0];for(let e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function so(t,n){let e=3,r=4.5,i,o;function u(t){return t&&("function"==typeof r&&o.pointRadius(+r.apply(this,arguments)),cp(t,i(o))),o.result()}return u.area=function(t){return cp(t,i(l_)),l_.result()},u.measure=function(t){return cp(t,i(l4)),l4.result()},u.bounds=function(t){return cp(t,i(lO)),lO.result()},u.centroid=function(t){return cp(t,i(lq)),lq.result()},u.projection=function(n){return arguments.length?(i=null==n?(t=null,ly):(t=n).stream,u):t},u.context=function(t){return arguments.length?(o=null==t?(n=null,new sr(e)):new l$(n=t),"function"!=typeof r&&o.pointRadius(r),u):n},u.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(o.pointRadius(+t),+t),u):r},u.digits=function(t){if(!arguments.length)return e;if(null==t)e=null;else{let n=Math.floor(t);if(!(n>=0))throw RangeError("invalid digits: ".concat(t));e=n}return null===n&&(o=new sr(e)),u},u.projection(t).digits(e).context(n)}function su(t){return{stream:sa(t)}}function sa(t){return function(n){var e=new sc;for(var r in t)e[r]=t[r];return e.stream=n,e}}function sc(){}function sf(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),cp(e,t.stream(lO)),n(lO.result()),null!=r&&t.clipExtent(r),t}function sl(t,n,e){return sf(t,function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),u=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,a=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([u,a])},e)}function ss(t,n,e){return sl(t,[[0,0],n],e)}function sh(t,n,e){return sf(t,function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,u=-i*e[0][1];t.scale(150*i).translate([o,u])},e)}function sd(t,n,e){return sf(t,function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],u=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,u])},e)}sc.prototype={constructor:sc,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var sp=a4(30*a2);function sg(t,n){return+n?function(t,n){function e(r,i,o,u,a,c,f,l,s,h,d,p,g,v){var y=f-r,b=l-i,m=y*y+b*b;if(m>4*n&&g--){var _=u+h,x=a+d,w=c+p,M=ci(_*_+x*x+w*w),T=ca(w/=M),A=1e-6>a6(a6(w)-1)||1e-6>a6(o-s)?(o+s)/2:a5(x,_),k=t(A,T),S=k[0],N=k[1],E=S-r,C=N-i,P=b*E-y*C;(P*P/m>n||a6((y*E+b*C)/m-.5)>.3||u*h+a*d+c*p<sp)&&(e(r,i,o,u,a,c,S,N,A,_/=M,x/=M,w,g,v),v.point(S,N),e(S,N,A,_,x,w,f,l,s,h,d,p,g,v))}}return function(n){var r,i,o,u,a,c,f,l,s,h,d,p,g={point:v,lineStart:y,lineEnd:m,polygonStart:function(){n.polygonStart(),g.lineStart=_},polygonEnd:function(){n.polygonEnd(),g.lineStart=y}};function v(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){l=NaN,g.point=b,n.lineStart()}function b(r,i){var o=cD([r,i]),u=t(r,i);e(l,s,f,h,d,p,l=u[0],s=u[1],f=r,h=o[0],d=o[1],p=o[2],16,n),n.point(l,s)}function m(){g.point=v,n.lineEnd()}function _(){y(),g.point=x,g.lineEnd=w}function x(t,n){b(r=t,n),i=l,o=s,u=h,a=d,c=p,g.point=b}function w(){e(l,s,f,h,d,p,i,o,r,u,a,c,16,n),g.lineEnd=m,m()}return g}}(t,n):sa({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}var sv=sa({point:function(t,n){this.stream.point(t*a2,n*a2)}});function sy(t,n,e,r,i,o){if(!o)return function(t,n,e,r,i){function o(o,u){return[n+t*(o*=r),e-t*(u*=i)]}return o.invert=function(o,u){return[(o-n)/t*r,(e-u)/t*i]},o}(t,n,e,r,i);var u=a4(o),a=ce(o),c=u*t,f=a*t,l=u/t,s=a/t,h=(a*e-u*n)/t,d=(a*n+u*e)/t;function p(t,o){return[c*(t*=r)-f*(o*=i)+n,e-f*t-c*o]}return p.invert=function(t,n){return[r*(l*t-s*n+h),i*(d-s*t-l*n)]},p}function sb(t){return sm(function(){return t})()}function sm(t){var n,e,r,i,o,u,a,c,f,l,s=150,h=480,d=250,p=0,g=0,v=0,y=0,b=0,m=0,_=1,x=1,w=null,M=fx,T=null,A=ly,k=.5;function S(t){return c(t[0]*a2,t[1]*a2)}function N(t){return(t=c.invert(t[0],t[1]))&&[t[0]*a1,t[1]*a1]}function E(){var t=sy(s,0,0,_,x,m).apply(null,n(p,g)),r=sy(s,h-t[0],d-t[1],_,x,m);return e=fr(v,y,b),a=fn(n,r),c=fn(e,a),u=sg(a,k),C()}function C(){return f=l=null,S}return S.stream=function(t){var n;return f&&l===t?f:f=sv((n=e,sa({point:function(t,e){var r=n(t,e);return this.stream.point(r[0],r[1])}}))(M(u(A(l=t)))))},S.preclip=function(t){return arguments.length?(M=t,w=void 0,C()):M},S.postclip=function(t){return arguments.length?(A=t,T=r=i=o=null,C()):A},S.clipAngle=function(t){return arguments.length?(M=+t?fw(w=t*a2):(w=null,fx),C()):w*a1},S.clipExtent=function(t){return arguments.length?(A=null==t?(T=r=i=o=null,ly):fM(T=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),C()):null==T?null:[[T,r],[i,o]]},S.scale=function(t){return arguments.length?(s=+t,E()):s},S.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],E()):[h,d]},S.center=function(t){return arguments.length?(p=t[0]%360*a2,g=t[1]%360*a2,E()):[p*a1,g*a1]},S.rotate=function(t){return arguments.length?(v=t[0]%360*a2,y=t[1]%360*a2,b=t.length>2?t[2]%360*a2:0,E()):[v*a1,y*a1,b*a1]},S.angle=function(t){return arguments.length?(m=t%360*a2,E()):m*a1},S.reflectX=function(t){return arguments.length?(_=t?-1:1,E()):_<0},S.reflectY=function(t){return arguments.length?(x=t?-1:1,E()):x<0},S.precision=function(t){return arguments.length?(u=sg(a,k=t*t),C()):ci(k)},S.fitExtent=function(t,n){return sl(S,t,n)},S.fitSize=function(t,n){return ss(S,t,n)},S.fitWidth=function(t,n){return sh(S,t,n)},S.fitHeight=function(t,n){return sd(S,t,n)},function(){return n=t.apply(this,arguments),S.invert=n.invert&&N,E()}}function s_(t){var n=0,e=aK/3,r=sm(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*a2,e=t[1]*a2):[n*a1,e*a1]},i}function sx(t,n){var e=ce(t),r=(e+ce(n))/2;if(1e-6>a6(r))return function(t){var n=a4(t);function e(t,e){return[t*n,ce(e)/n]}return e.invert=function(t,e){return[t/n,ca(e*n)]},e}(t);var i=1+e*(2*r-e),o=ci(i)/r;function u(t,n){var e=ci(i-2*r*ce(n))/r;return[e*ce(t*=r),o-e*a4(t)]}return u.invert=function(t,n){var e=o-n,u=a5(t,a6(e))*cr(e);return e*r<0&&(u-=aK*cr(t)*cr(e)),[u/r,ca((i-(t*t+e*e)*r*r)/(2*r))]},u}function sw(){return s_(sx).scale(155.424).center([0,33.6442])}function sM(){return sw().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function sT(){var t,n,e,r,i,o,u=sM(),a=sw().rotate([154,0]).center([-2,58.5]).parallels([55,65]),c=sw().rotate([157,0]).center([-3,19.9]).parallels([8,18]),f={point:function(t,n){o=[t,n]}};function l(t){var n=t[0],u=t[1];return o=null,e.point(n,u),o||(r.point(n,u),o)||(i.point(n,u),o)}function s(){return t=n=null,l}return l.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?a:i>=.166&&i<.234&&r>=-.214&&r<-.115?c:u).invert(t)},l.stream=function(e){var r,i;return t&&n===e?t:(i=(r=[u.stream(n=e),a.stream(e),c.stream(e)]).length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}})},l.precision=function(t){return arguments.length?(u.precision(t),a.precision(t),c.precision(t),s()):u.precision()},l.scale=function(t){return arguments.length?(u.scale(t),a.scale(.35*t),c.scale(t),l.translate(u.translate())):u.scale()},l.translate=function(t){if(!arguments.length)return u.translate();var n=u.scale(),o=+t[0],l=+t[1];return e=u.translate(t).clipExtent([[o-.455*n,l-.238*n],[o+.455*n,l+.238*n]]).stream(f),r=a.translate([o-.307*n,l+.201*n]).clipExtent([[o-.425*n+1e-6,l+.12*n+1e-6],[o-.214*n-1e-6,l+.234*n-1e-6]]).stream(f),i=c.translate([o-.205*n,l+.212*n]).clipExtent([[o-.214*n+1e-6,l+.166*n+1e-6],[o-.115*n-1e-6,l+.234*n-1e-6]]).stream(f),s()},l.fitExtent=function(t,n){return sl(l,t,n)},l.fitSize=function(t,n){return ss(l,t,n)},l.fitWidth=function(t,n){return sh(l,t,n)},l.fitHeight=function(t,n){return sd(l,t,n)},l.scale(1070)}function sA(t){return function(n,e){var r=a4(n),i=a4(e),o=t(r*i);return o===1/0?[2,0]:[o*i*ce(n),o*ce(e)]}}function sk(t){return function(n,e){var r=ci(n*n+e*e),i=t(r),o=ce(i);return[a5(n*o,r*a4(i)),ca(r&&e*o/r)]}}var sS=sA(function(t){return ci(2/(1+t))});function sN(){return sb(sS).scale(124.75).clipAngle(179.999)}sS.invert=sk(function(t){return 2*ca(t/2)});var sE=sA(function(t){return(t=cu(t))&&t/ce(t)});function sC(){return sb(sE).scale(79.4188).clipAngle(179.999)}function sP(t,n){return[t,ct(co((aQ+n)/2))]}function sR(){return sO(sP).scale(961/a0)}function sO(t){var n,e,r,i=sb(t),o=i.center,u=i.scale,a=i.translate,c=i.clipExtent,f=null;function l(){var o=aK*u(),a=i(fa(i.rotate()).invert([0,0]));return c(null==f?[[a[0]-o,a[1]-o],[a[0]+o,a[1]+o]]:t===sP?[[Math.max(a[0]-o,f),n],[Math.min(a[0]+o,e),r]]:[[f,Math.max(a[1]-o,n)],[e,Math.min(a[1]+o,r)]])}return i.scale=function(t){return arguments.length?(u(t),l()):u()},i.translate=function(t){return arguments.length?(a(t),l()):a()},i.center=function(t){return arguments.length?(o(t),l()):o()},i.clipExtent=function(t){return arguments.length?(null==t?f=n=e=r=null:(f=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),l()):null==f?null:[[f,n],[e,r]]},l()}function sD(t){return co((aQ+t)/2)}function sz(t,n){var e=a4(t),r=t===n?ce(t):ct(e/a4(n))/ct(sD(n)/sD(t)),i=e*cn(sD(t),r)/r;if(!r)return sP;function o(t,n){i>0?n<-aQ+1e-6&&(n=-aQ+1e-6):n>aQ-1e-6&&(n=aQ-1e-6);var e=i/cn(sD(n),r);return[e*ce(r*t),i-e*a4(r*t)]}return o.invert=function(t,n){var e=i-n,o=cr(r)*ci(t*t+e*e),u=a5(t,a6(e))*cr(e);return e*r<0&&(u-=aK*cr(t)*cr(e)),[u/r,2*a3(cn(i/o,1/r))-aQ]},o}function sL(){return s_(sz).scale(109.5).parallels([30,30])}function sj(t,n){return[t,n]}function sB(){return sb(sj).scale(152.63)}function sV(t,n){var e=a4(t),r=t===n?ce(t):(e-a4(n))/(n-t),i=e/r+t;if(1e-6>a6(r))return sj;function o(t,n){var e=i-n,o=r*t;return[e*ce(o),i-e*a4(o)]}return o.invert=function(t,n){var e=i-n,o=a5(t,a6(e))*cr(e);return e*r<0&&(o-=aK*cr(t)*cr(e)),[o/r,i-cr(r)*ci(t*t+e*e)]},o}function sI(){return s_(sV).scale(131.154).center([0,13.9389])}sE.invert=sk(function(t){return t}),sP.invert=function(t,n){return[t,2*a3(a7(n))-aQ]},sj.invert=sj;var sF=ci(3)/2;function sG(t,n){var e=ca(sF*ce(n)),r=e*e,i=r*r*r;return[t*a4(e)/(sF*(1.340264+-.24331799999999998*r+i*(.0062510000000000005+.034164*r))),e*(1.340264+-.081106*r+i*(893e-6+.003796*r))]}function sq(){return sb(sG).scale(177.158)}function sU(t,n){var e=a4(n),r=a4(t)*e;return[e*ce(t)/r,ce(n)/r]}function sY(){return sb(sU).scale(144.049).clipAngle(60)}function sH(){var t,n,e,r,i,o,u,a=1,c=0,f=0,l=1,s=1,h=0,d=null,p=1,g=1,v=sa({point:function(t,n){var e=m([t,n]);this.stream.point(e[0],e[1])}}),y=ly;function b(){return p=a*l,g=a*s,o=u=null,m}function m(e){var r=e[0]*p,i=e[1]*g;if(h){var o=i*t-r*n;r=r*t+i*n,i=o}return[r+c,i+f]}return m.invert=function(e){var r=e[0]-c,i=e[1]-f;if(h){var o=i*t+r*n;r=r*t-i*n,i=o}return[r/p,i/g]},m.stream=function(t){return o&&u===t?o:o=v(y(u=t))},m.postclip=function(t){return arguments.length?(y=t,d=e=r=i=null,b()):y},m.clipExtent=function(t){return arguments.length?(y=null==t?(d=e=r=i=null,ly):fM(d=+t[0][0],e=+t[0][1],r=+t[1][0],i=+t[1][1]),b()):null==d?null:[[d,e],[r,i]]},m.scale=function(t){return arguments.length?(a=+t,b()):a},m.translate=function(t){return arguments.length?(c=+t[0],f=+t[1],b()):[c,f]},m.angle=function(e){return arguments.length?(n=ce(h=e%360*a2),t=a4(h),b()):h*a1},m.reflectX=function(t){return arguments.length?(l=t?-1:1,b()):l<0},m.reflectY=function(t){return arguments.length?(s=t?-1:1,b()):s<0},m.fitExtent=function(t,n){return sl(m,t,n)},m.fitSize=function(t,n){return ss(m,t,n)},m.fitWidth=function(t,n){return sh(m,t,n)},m.fitHeight=function(t,n){return sd(m,t,n)},m}function sX(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(-.013791+r*(.003971*e-.001529*r))),n*(1.007226+e*(.015085+r*(-.044475+.028874*e-.005916*r)))]}function sZ(){return sb(sX).scale(175.295)}function sW(t,n){return[a4(n)*ce(t),ce(n)]}function sJ(){return sb(sW).scale(249.5).clipAngle(90.000001)}function sK(t,n){var e=a4(n),r=1+a4(t)*e;return[e*ce(t)/r,ce(n)/r]}function sQ(){return sb(sK).scale(250).clipAngle(142)}function s$(t,n){return[ct(co((aQ+n)/2)),-t]}function s0(){var t=sO(s$),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)}function s1(t,n){return t.parent===n.parent?1:2}function s2(t,n){return t+n.x}function s6(t,n){return Math.max(t,n.y)}function s3(){var t=s1,n=1,e=1,r=!1;function i(i){var o,u=0;i.eachAfter(function(n){var e=n.children;e?(n.x=e.reduce(s2,0)/e.length,n.y=1+e.reduce(s6,0)):(n.x=o?u+=t(n,o):0,n.y=0,o=n)});var a=function(t){for(var n;n=t.children;)t=n[0];return t}(i),c=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(i),f=a.x-t(a,c)/2,l=c.x+t(c,a)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*n,t.y=(i.y-t.y)*e}:function(t){t.x=(t.x-f)/(l-f)*n,t.y=(1-(i.y?t.y/i.y:1))*e})}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function s5(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function s4(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=s7)):void 0===n&&(n=s8);for(var e,r,i,o,u,a=new hn(t),c=[a];e=c.pop();)if((i=n(e.data))&&(u=(i=Array.from(i)).length))for(e.children=i,o=u-1;o>=0;--o)c.push(r=i[o]=new hn(i[o])),r.parent=e,r.depth=e.depth+1;return a.eachBefore(ht)}function s8(t){return t.children}function s7(t){return Array.isArray(t)?t[1]:null}function s9(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function ht(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function hn(t){this.data=t,this.depth=this.height=0,this.parent=null}function he(t){return null==t?null:hr(t)}function hr(t){if("function"!=typeof t)throw Error();return t}function hi(){return 0}function ho(t){return function(){return t}}function hu(){let t=1;return()=>(t=(1664525*t+1013904223)%4294967296)/4294967296}function ha(t){return hc(t,hu())}function hc(t,n){for(var e,r,i=0,o=(t=function(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}(Array.from(t),n)).length,u=[];i<o;)e=t[i],r&&hl(r,e)?++i:(r=function(t){switch(t.length){case 1:var n;return{x:(n=t[0]).x,y:n.y,r:n.r};case 2:return hh(t[0],t[1]);case 3:return hd(t[0],t[1],t[2])}}(u=function(t,n){var e,r;if(hs(n,t))return[n];for(e=0;e<t.length;++e)if(hf(n,t[e])&&hs(hh(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(hf(hh(t[e],t[r]),n)&&hf(hh(t[e],n),t[r])&&hf(hh(t[r],n),t[e])&&hs(hd(t[e],t[r],n),t))return[t[e],t[r],n];throw Error()}(u,e)),i=0);return r}function hf(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function hl(t,n){var e=t.r-n.r+1e-9*Math.max(t.r,n.r,1),r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function hs(t,n){for(var e=0;e<n.length;++e)if(!hl(t,n[e]))return!1;return!0}function hh(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,f=u-r,l=a-i,s=Math.sqrt(c*c+f*f);return{x:(e+o+c/s*l)/2,y:(r+u+f/s*l)/2,r:(s+i+a)/2}}function hd(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,f=e.x,l=e.y,s=e.r,h=r-u,d=r-f,p=i-a,g=i-l,v=c-o,y=s-o,b=r*r+i*i-o*o,m=b-u*u-a*a+c*c,_=b-f*f-l*l+s*s,x=d*p-h*g,w=(p*_-g*m)/(2*x)-r,M=(g*v-p*y)/x,T=(d*m-h*_)/(2*x)-i,A=(h*y-d*v)/x,k=M*M+A*A-1,S=2*(o+w*M+T*A),N=w*w+T*T-o*o,E=-(Math.abs(k)>1e-6?(S+Math.sqrt(S*S-4*k*N))/(2*k):N/S);return{x:r+w+M*E,y:i+T+A*E,r:E}}function hp(t,n,e){var r,i,o,u,a=t.x-n.x,c=t.y-n.y,f=a*a+c*c;f?(i=n.r+e.r,i*=i,u=t.r+e.r,i>(u*=u)?(r=(f+u-i)/(2*f),o=Math.sqrt(Math.max(0,u/f-r*r)),e.x=t.x-r*a-o*c,e.y=t.y-r*c+o*a):(r=(f+i-u)/(2*f),o=Math.sqrt(Math.max(0,i/f-r*r)),e.x=n.x+r*a-o*c,e.y=n.y+r*c+o*a)):(e.x=n.x+e.r,e.y=n.y)}function hg(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function hv(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function hy(t){this._=t,this.next=null,this.previous=null}function hb(t,n){var e,r,i,o,u,a,c,f,l,s,h,d;if(!(o=(t="object"==typeof(d=t)&&"length"in d?d:Array.from(d)).length))return 0;if((e=t[0]).x=0,e.y=0,!(o>1))return e.r;if(r=t[1],e.x=-r.r,r.x=e.r,r.y=0,!(o>2))return e.r+r.r;hp(r,e,i=t[2]),e=new hy(e),r=new hy(r),i=new hy(i),e.next=i.previous=r,r.next=e.previous=i,i.next=r.previous=e;e:for(c=3;c<o;++c){hp(e._,r._,i=t[c]),i=new hy(i),f=r.next,l=e.previous,s=r._.r,h=e._.r;do if(s<=h){if(hg(f._,i._)){r=f,e.next=r,r.previous=e,--c;continue e}s+=f._.r,f=f.next}else{if(hg(l._,i._)){(e=l).next=r,r.previous=e,--c;continue e}h+=l._.r,l=l.previous}while(f!==l.next);for(i.previous=e,i.next=r,e.next=r.previous=r=i,u=hv(e);(i=i.next)!==r;)(a=hv(i))<u&&(e=i,u=a);r=e.next}for(e=[r._],i=r;(i=i.next)!==r;)e.push(i._);for(c=0,i=hc(e,n);c<o;++c)e=t[c],e.x-=i.x,e.y-=i.y;return i.r}function hm(t){return hb(t,hu()),t}function h_(t){return Math.sqrt(t.value)}function hx(){var t=null,n=1,e=1,r=hi;function i(i){let o=hu();return i.x=n/2,i.y=e/2,t?i.eachBefore(hw(t)).eachAfter(hM(r,.5,o)).eachBefore(hT(1)):i.eachBefore(hw(h_)).eachAfter(hM(hi,1,o)).eachAfter(hM(r,i.r/Math.min(n,e),o)).eachBefore(hT(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=he(n),i):t},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:ho(+t),i):r},i}function hw(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function hM(t,n,e){return function(r){if(i=r.children){var i,o,u,a=i.length,c=t(r)*n||0;if(c)for(o=0;o<a;++o)i[o].r+=c;if(u=hb(i,e),c)for(o=0;o<a;++o)i[o].r-=c;r.r=u+c}}}function hT(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function hA(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function hk(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(r-n)/t.value;++a<c;)(o=u[a]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*f}function hS(){var t=1,n=1,e=0,r=!1;function i(i){var o,u=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/u,i.eachBefore((o=n,function(t){t.children&&hk(t,t.x0,o*(t.depth+1)/u,t.x1,o*(t.depth+2)/u);var n=t.x0,r=t.y0,i=t.x1-e,a=t.y1-e;i<n&&(n=i=(n+i)/2),a<r&&(r=a=(r+a)/2),t.x0=n,t.y0=r,t.x1=i,t.y1=a})),r&&i.eachBefore(hA),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i}sG.invert=function(t,n){for(var e,r,i=n,o=i*i,u=o*o*o,a=0;a<12&&(r=i*(1.340264+-.081106*o+u*(893e-6+.003796*o))-n,i-=e=r/(1.340264+-.24331799999999998*o+u*(.0062510000000000005+.034164*o)),u=(o=i*i)*o*o,!(1e-12>a6(e)));++a);return[sF*t*(1.340264+-.24331799999999998*o+u*(.0062510000000000005+.034164*o))/a4(i),ca(ce(i)/sF)]},sU.invert=sk(a3),sX.invert=function(t,n){var e,r=n,i=25;do{var o=r*r,u=o*o;r-=e=(r*(1.007226+o*(.015085+u*(-.044475+.028874*o-.005916*u)))-n)/(1.007226+o*(.045255+u*(-.311325+.259866*o-.005916*11*u)))}while(a6(e)>1e-6&&--i>0);return[t/(.8707+(o=r*r)*(-.131979+o*(-.013791+o*o*o*(.003971-.001529*o)))),r]},sW.invert=sk(ca),sK.invert=sk(function(t){return 2*a3(t)}),s$.invert=function(t,n){return[-n,2*a3(a7(t))-aQ]},hn.prototype=s4.prototype={constructor:hn,count:function(){return this.eachAfter(s5)},each:function(t,n){let e=-1;for(let r of this)t.call(n,r,++e,this);return this},eachAfter:function(t,n){for(var e,r,i,o=this,u=[o],a=[],c=-1;o=u.pop();)if(a.push(o),e=o.children)for(r=0,i=e.length;r<i;++r)u.push(e[r]);for(;o=a.pop();)t.call(n,o,++c,this);return this},eachBefore:function(t,n){for(var e,r,i=this,o=[i],u=-1;i=o.pop();)if(t.call(n,i,++u,this),e=i.children)for(r=e.length-1;r>=0;--r)o.push(e[r]);return this},find:function(t,n){let e=-1;for(let r of this)if(t.call(n,r,++e,this))return r},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)r.push(n=n.parent);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return s4(this).eachBefore(s9)},[Symbol.iterator]:function*(){var t,n,e,r,i=this,o=[i];do for(t=o.reverse(),o=[];i=t.pop();)if(yield i,n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);while(o.length)}};var hN={depth:-1},hE={},hC={};function hP(t){return t.id}function hR(t){return t.parentId}function hO(){var t,n=hP,e=hR;function r(r){var i,o,u,a,c,f,l,s,h=Array.from(r),d=n,p=e,g=new Map;if(null!=t){let n=h.map((n,e)=>{var i;let o;return i=t(n,e,r),o=(i="".concat(i)).length,hz(i,o-1)&&!hz(i,o-2)&&(i=i.slice(0,-1)),"/"===i[0]?i:"/".concat(i)}),e=n.map(hD),i=new Set(n).add("");for(let t of e)i.has(t)||(i.add(t),n.push(t),e.push(hD(t)),h.push(hC));d=(t,e)=>n[e],p=(t,n)=>e[n]}for(u=0,i=h.length;u<i;++u)o=h[u],f=h[u]=new hn(o),null!=(l=d(o,u,r))&&(l+="")&&(s=f.id=l,g.set(s,g.has(s)?hE:f)),null!=(l=p(o,u,r))&&(l+="")&&(f.parent=l);for(u=0;u<i;++u)if(l=(f=h[u]).parent){if(!(c=g.get(l)))throw Error("missing: "+l);if(c===hE)throw Error("ambiguous: "+l);c.children?c.children.push(f):c.children=[f],f.parent=c}else{if(a)throw Error("multiple roots");a=f}if(!a)throw Error("no root");if(null!=t){for(;a.data===hC&&1===a.children.length;)a=a.children[0],--i;for(let t=h.length-1;t>=0&&(f=h[t]).data===hC;--t)f.data=null}if(a.parent=hN,a.eachBefore(function(t){t.depth=t.parent.depth+1,--i}).eachBefore(ht),a.parent=null,i>0)throw Error("cycle");return a}return r.id=function(t){return arguments.length?(n=he(t),r):n},r.parentId=function(t){return arguments.length?(e=he(t),r):e},r.path=function(n){return arguments.length?(t=he(n),r):t},r}function hD(t){let n=t.length;if(n<2)return"";for(;--n>1&&!hz(t,n););return t.slice(0,n)}function hz(t,n){if("/"===t[n]){let e=0;for(;n>0&&"\\"===t[--n];)++e;if((1&e)==0)return!0}return!1}function hL(t,n){return t.parent===n.parent?1:2}function hj(t){var n=t.children;return n?n[0]:t.t}function hB(t){var n=t.children;return n?n[n.length-1]:t.t}function hV(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function hI(){var t=hL,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,u=new hV(t,0),a=[u];n=a.pop();)if(r=n._.children)for(n.children=Array(o=r.length),i=o-1;i>=0;--i)a.push(e=n.children[i]=new hV(r[i],i)),e.parent=n;return(u.parent=new hV(null,0)).children=[u],u}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(u),r)i.eachBefore(a);else{var f=i,l=i,s=i;i.eachBefore(function(t){t.x<f.x&&(f=t),t.x>l.x&&(l=t),t.depth>s.depth&&(s=t)});var h=f===l?1:t(f,l)/2,d=h-f.x,p=n/(l.x+h+d),g=e/(s.depth||1);i.eachBefore(function(t){t.x=(t.x+d)*p,t.y=t.depth*g})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)n=i[o],n.z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o,u,a=n,c=n,f=e,l=a.parent.children[0],s=a.m,h=c.m,d=f.m,p=l.m;f=hB(f),a=hj(a),f&&a;)l=hj(l),(c=hB(c)).a=n,(u=f.z+d-a.z-s+t(f._,a._))>0&&(function(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}((i=f,o=r,i.a.parent===n.parent?i.a:o),n,u),s+=u,h+=u),d+=f.m,s+=a.m,p+=l.m,h+=c.m;f&&!hB(c)&&(c.t=f,c.m+=d-h),a&&!hj(l)&&(l.t=a,l.m+=s-p,r=n)}return r}(n,i,n.parent.A||r[0])}function u(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function a(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function hF(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(i-e)/t.value;++a<c;)(o=u[a]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*f}hV.prototype=Object.create(hn.prototype);var hG=(1+Math.sqrt(5))/2;function hq(t,n,e,r,i,o){for(var u,a,c,f,l,s,h,d,p,g,v,y=[],b=n.children,m=0,_=0,x=b.length,w=n.value;m<x;){c=i-e,f=o-r;do l=b[_++].value;while(!l&&_<x);for(s=h=l,p=Math.max(h/(v=l*l*(g=Math.max(f/c,c/f)/(w*t))),v/s);_<x;++_){if(l+=a=b[_].value,a<s&&(s=a),a>h&&(h=a),(d=Math.max(h/(v=l*l*g),v/s))>p){l-=a;break}p=d}y.push(u={value:l,dice:c<f,children:b.slice(m,_)}),u.dice?hk(u,e,r,i,w?r+=f*l/w:o):hF(u,e,r,w?e+=c*l/w:i,o),w-=l,m=_}return y}var hU=function t(n){function e(t,e,r,i,o){hq(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(hG);function hY(){var t=hU,n=!1,e=1,r=1,i=[0],o=hi,u=hi,a=hi,c=hi,f=hi;function l(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(s),i=[0],n&&t.eachBefore(hA),t}function s(n){var e=i[n.depth],r=n.x0+e,l=n.y0+e,s=n.x1-e,h=n.y1-e;s<r&&(r=s=(r+s)/2),h<l&&(l=h=(l+h)/2),n.x0=r,n.y0=l,n.x1=s,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=f(n)-e,l+=u(n)-e,s-=a(n)-e,h-=c(n)-e,s<r&&(r=s=(r+s)/2),h<l&&(l=h=(l+h)/2),t(n,r,l,s,h))}return l.round=function(t){return arguments.length?(n=!!t,l):n},l.size=function(t){return arguments.length?(e=+t[0],r=+t[1],l):[e,r]},l.tile=function(n){return arguments.length?(t=hr(n),l):t},l.padding=function(t){return arguments.length?l.paddingInner(t).paddingOuter(t):l.paddingInner()},l.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:ho(+t),l):o},l.paddingOuter=function(t){return arguments.length?l.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):l.paddingTop()},l.paddingTop=function(t){return arguments.length?(u="function"==typeof t?t:ho(+t),l):u},l.paddingRight=function(t){return arguments.length?(a="function"==typeof t?t:ho(+t),l):a},l.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:ho(+t),l):c},l.paddingLeft=function(t){return arguments.length?(f="function"==typeof t?t:ho(+t),l):f},l}function hH(t,n,e,r,i){var o,u,a=t.children,c=a.length,f=Array(c+1);for(f[0]=u=o=0;o<c;++o)f[o+1]=u+=a[o].value;!function t(n,e,r,i,o,u,c){if(n>=e-1){var l=a[n];l.x0=i,l.y0=o,l.x1=u,l.y1=c;return}for(var s=f[n],h=r/2+s,d=n+1,p=e-1;d<p;){var g=d+p>>>1;f[g]<h?d=g+1:p=g}h-f[d-1]<f[d]-h&&n+1<d&&--d;var v=f[d]-s,y=r-v;if(u-i>c-o){var b=r?(i*y+u*v)/r:u;t(n,d,v,i,o,b,c),t(d,e,y,b,o,u,c)}else{var m=r?(o*y+c*v)/r:c;t(n,d,v,i,o,u,m),t(d,e,y,i,m,u,c)}}(0,c,t.value,n,e,r,i)}function hX(t,n,e,r,i){(1&t.depth?hF:hk)(t,n,e,r,i)}var hZ=function t(n){function e(t,e,r,i,o){if((u=t._squarify)&&u.ratio===n)for(var u,a,c,f,l,s=-1,h=u.length,d=t.value;++s<h;){for(c=(a=u[s]).children,f=a.value=0,l=c.length;f<l;++f)a.value+=c[f].value;a.dice?hk(a,e,r,i,d?r+=(o-r)*a.value/d:o):hF(a,e,r,d?e+=(i-e)*a.value/d:i,o),d-=a.value}else t._squarify=u=hq(n,t,e,r,i,o),u.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(hG);function hW(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}function hJ(t,n){var e=eG(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}}function hK(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function hQ(t){return((t=Math.exp(t))+1/t)/2}var h$=function t(n,e,r){function i(t,i){var o,u,a=t[0],c=t[1],f=t[2],l=i[0],s=i[1],h=i[2],d=l-a,p=s-c,g=d*d+p*p;if(g<1e-12)u=Math.log(h/f)/n,o=function(t){return[a+t*d,c+t*p,f*Math.exp(n*t*u)]};else{var v=Math.sqrt(g),y=(h*h-f*f+r*g)/(2*f*e*v),b=(h*h-f*f-r*g)/(2*h*e*v),m=Math.log(Math.sqrt(y*y+1)-y);u=(Math.log(Math.sqrt(b*b+1)-b)-m)/n,o=function(t){var r,i,o=t*u,l=hQ(m),s=f/(e*v)*(((r=Math.exp(2*(r=n*o+m)))-1)/(r+1)*l-((i=Math.exp(i=m))-1/i)/2);return[a+s*d,c+s*p,f*l/hQ(n*o+m)]}}return o.duration=1e3*u*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4);function h0(t){return function(n,e){var r=t((n=eR(n)).h,(e=eR(e)).h),i=eq(n.s,e.s),o=eq(n.l,e.l),u=eq(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var h1=h0(eG),h2=h0(eq);function h6(t,n){var e=eq((t=on(t)).l,(n=on(n)).l),r=eq(t.a,n.a),i=eq(t.b,n.b),o=eq(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=o(n),t+""}}function h3(t){return function(n,e){var r=t((n=of(n)).h,(e=of(e)).h),i=eq(n.c,e.c),o=eq(n.l,e.l),u=eq(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var h5=h3(eG),h4=h3(eq);function h8(t){return function n(e){function r(n,r){var i=t((n=od(n)).h,(r=od(r)).h),o=eq(n.s,r.s),u=eq(n.l,r.l),a=eq(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=u(Math.pow(t,e)),n.opacity=a(t),n+""}}return e=+e,r.gamma=n,r}(1)}var h7=h8(eG),h9=h8(eq);function dt(t,n){void 0===n&&(n=t,t=e3);for(var e=0,r=n.length-1,i=n[0],o=Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}function dn(t,n){for(var e=Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e}function de(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2}function dr(t){for(var n,e,r=-1,i=t.length,o=0,u=0,a=t[i-1],c=0;++r<i;)n=a,a=t[r],c+=e=n[0]*a[1]-a[0]*n[1],o+=(n[0]+a[0])*e,u+=(n[1]+a[1])*e;return[o/(c*=3),u/c]}function di(t,n){return t[0]-n[0]||t[1]-n[1]}function du(t){let n=t.length,e=[0,1],r=2,i;for(i=2;i<n;++i){for(var o,u,a;r>1&&0>=(o=t[e[r-2]],u=t[e[r-1]],a=t[i],(u[0]-o[0])*(a[1]-o[1])-(u[1]-o[1])*(a[0]-o[0]));)--r;e[r++]=i}return e.slice(0,r)}function da(t){if((e=t.length)<3)return null;var n,e,r=Array(e),i=Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(di),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=du(r),u=du(i),a=u[0]===o[0],c=u[u.length-1]===o[o.length-1],f=[];for(n=o.length-1;n>=0;--n)f.push(t[r[o[n]][2]]);for(n=+a;n<u.length-c;++n)f.push(t[r[u[n]][2]]);return f}function dc(t,n){for(var e,r,i=t.length,o=t[i-1],u=n[0],a=n[1],c=o[0],f=o[1],l=!1,s=0;s<i;++s)e=(o=t[s])[0],(r=o[1])>a!=f>a&&u<(c-e)*(a-r)/(f-r)+e&&(l=!l),c=e,f=r;return l}function df(t){for(var n,e,r=-1,i=t.length,o=t[i-1],u=o[0],a=o[1],c=0;++r<i;)n=u,e=a,u=(o=t[r])[0],a=o[1],n-=u,e-=a,c+=Math.hypot(n,e);return c}var dl=Math.random,ds=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,1==arguments.length?(e=t,t=0):e-=t,function(){return n()*e+t}}return e.source=t,e}(dl),dh=function t(n){function e(t,e){return arguments.length<2&&(e=t,t=0),e=Math.floor(e)-(t=Math.floor(t)),function(){return Math.floor(n()*e+t)}}return e.source=t,e}(dl),dd=function t(n){function e(t,e){var r,i;return t=null==t?0:+t,e=null==e?1:+e,function(){var o;if(null!=r)o=r,r=null;else do r=2*n()-1,o=2*n()-1,i=r*r+o*o;while(!i||i>1);return t+e*o*Math.sqrt(-2*Math.log(i)/i)}}return e.source=t,e}(dl),dp=function t(n){var e=dd.source(n);function r(){var t=e.apply(this,arguments);return function(){return Math.exp(t())}}return r.source=t,r}(dl),dg=function t(n){function e(t){return(t=+t)<=0?()=>0:function(){for(var e=0,r=t;r>1;--r)e+=n();return e+r*n()}}return e.source=t,e}(dl),dv=function t(n){var e=dg.source(n);function r(t){if(0==(t=+t))return n;var r=e(t);return function(){return r()/t}}return r.source=t,r}(dl),dy=function t(n){function e(t){return function(){return-Math.log1p(-n())/t}}return e.source=t,e}(dl),db=function t(n){function e(t){if((t=+t)<0)throw RangeError("invalid alpha");return t=-(1/t),function(){return Math.pow(1-n(),t)}}return e.source=t,e}(dl),dm=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return function(){return Math.floor(n()+t)}}return e.source=t,e}(dl),d_=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return 0===t?()=>1/0:1===t?()=>1:(t=Math.log1p(-t),function(){return 1+Math.floor(Math.log1p(-n())/t)})}return e.source=t,e}(dl),dx=function t(n){var e=dd.source(n)();function r(t,r){if((t=+t)<0)throw RangeError("invalid k");if(0===t)return()=>0;if(r=null==r?1:+r,1===t)return()=>-Math.log1p(-n())*r;var i=(t<1?t+1:t)-1/3,o=1/(3*Math.sqrt(i)),u=t<1?()=>Math.pow(n(),1/t):()=>1;return function(){do{do var t=e(),a=1+o*t;while(a<=0);a*=a*a;var c=1-n()}while(c>=1-.0331*t*t*t*t&&Math.log(c)>=.5*t*t+i*(1-a+Math.log(a)));return i*a*u()*r}}return r.source=t,r}(dl),dw=function t(n){var e=dx.source(n);function r(t,n){var r=e(t),i=e(n);return function(){var t=r();return 0===t?0:t/(t+i())}}return r.source=t,r}(dl),dM=function t(n){var e=d_.source(n),r=dw.source(n);function i(t,n){return(t=+t,(n=+n)>=1)?()=>t:n<=0?()=>0:function(){for(var i=0,o=t,u=n;o*u>16&&o*(1-u)>16;){var a=Math.floor((o+1)*u),c=r(a,o-a+1)();c<=u?(i+=a,o-=a,u=(u-c)/(1-c)):(o=a-1,u/=c)}for(var f=u<.5,l=e(f?u:1-u),s=l(),h=0;s<=o;++h)s+=l();return i+(f?h:o-h)}}return i.source=t,i}(dl),dT=function t(n){function e(t,e,r){var i;return 0==(t=+t)?i=t=>-Math.log(t):(t=1/t,i=n=>Math.pow(n,t)),e=null==e?0:+e,r=null==r?1:+r,function(){return e+r*i(-Math.log1p(-n()))}}return e.source=t,e}(dl),dA=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){return t+e*Math.tan(Math.PI*n())}}return e.source=t,e}(dl),dk=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){var r=n();return t+e*Math.log(r/(1-r))}}return e.source=t,e}(dl),dS=function t(n){var e=dx.source(n),r=dM.source(n);function i(t){return function(){for(var i=0,o=t;o>16;){var u=Math.floor(.875*o),a=e(u)();if(a>o)return i+r(u-1,o/a)();i+=u,o-=a}for(var c=-Math.log1p(-n()),f=0;c<=o;++f)c-=Math.log1p(-n());return i+f}}return i.source=t,i}(dl);let dN=1/4294967296;function dE(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Math.random(),n=(0<=t&&t<1?t/dN:Math.abs(t))|0;return()=>dN*((n=1664525*n+1013904223|0)>>>0)}function dC(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function dP(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}let dR=Symbol("implicit");function dO(){var t=new F,n=[],e=[],r=dR;function i(i){let o=t.get(i);if(void 0===o){if(r!==dR)return r;t.set(i,o=n.push(i)-1)}return e[o%e.length]}return i.domain=function(e){if(!arguments.length)return n.slice();for(let r of(n=[],t=new F,e))t.has(r)||t.set(r,n.push(r)-1);return i},i.range=function(t){return arguments.length?(e=Array.from(t),i):e.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return dO(n,e).unknown(r)},dC.apply(i,arguments),i}function dD(){var t,n,e=dO().unknown(void 0),r=e.domain,i=e.range,o=0,u=1,a=!1,c=0,f=0,l=.5;function s(){var e=r().length,s=u<o,h=s?u:o,d=s?o:u;t=(d-h)/Math.max(1,e-c+2*f),a&&(t=Math.floor(t)),h+=(d-h-t*(e-c))*l,n=t*(1-c),a&&(h=Math.round(h),n=Math.round(n));var p=tF(e).map(function(n){return h+t*n});return i(s?p.reverse():p)}return delete e.unknown,e.domain=function(t){return arguments.length?(r(t),s()):r()},e.range=function(t){return arguments.length?([o,u]=t,o=+o,u=+u,s()):[o,u]},e.rangeRound=function(t){return[o,u]=t,o=+o,u=+u,a=!0,s()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(a=!!t,s()):a},e.padding=function(t){return arguments.length?(c=Math.min(1,f=+t),s()):c},e.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),s()):c},e.paddingOuter=function(t){return arguments.length?(f=+t,s()):f},e.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),s()):l},e.copy=function(){return dD(r(),[o,u]).round(a).paddingInner(c).paddingOuter(f).align(l)},dC.apply(s(),arguments)}function dz(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(dD.apply(null,arguments).paddingInner(1))}function dL(t){return+t}var dj=[0,1];function dB(t){return t}function dV(t,n){var e;return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e})}function dI(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=dV(i,r),o=e(u,o)):(r=dV(r,i),o=e(o,u)),function(t){return o(r(t))}}function dF(t,n,e){var r=Math.min(t.length,n.length)-1,i=Array(r),o=Array(r),u=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++u<r;)i[u]=dV(t[u],t[u+1]),o[u]=e(n[u],n[u+1]);return function(n){var e=x(t,n,1,r)-1;return o[e](i[e](n))}}function dG(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function dq(){var t,n,e,r,i,o,u=dj,a=dj,c=e3,f=dB;function l(){var t,n,e,c=Math.min(u.length,a.length);return f!==dB&&(t=u[0],n=u[c-1],t>n&&(e=t,t=n,n=e),f=function(e){return Math.max(t,Math.min(n,e))}),r=c>2?dF:dI,i=o=null,s}function s(n){return null==n||isNaN(n=+n)?e:(i||(i=r(u.map(t),a,c)))(t(f(n)))}return s.invert=function(e){return f(n((o||(o=r(a,u.map(t),e$)))(e)))},s.domain=function(t){return arguments.length?(u=Array.from(t,dL),l()):u.slice()},s.range=function(t){return arguments.length?(a=Array.from(t),l()):a.slice()},s.rangeRound=function(t){return a=Array.from(t),c=hK,l()},s.clamp=function(t){return arguments.length?(f=!!t||dB,l()):f!==dB},s.interpolate=function(t){return arguments.length?(c=t,l()):c},s.unknown=function(t){return arguments.length?(e=t,s):e},function(e,r){return t=e,n=r,l()}}function dU(){return dq()(dB,dB)}function dY(t,n,e,r){var i,o=tb(t,n,e);switch((r=aV(null==r?",f":r)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=aW(o,u))||(r.precision=i),cm(r,u);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=aJ(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=aZ(o))||(r.precision=i-("%"===r.type)*2)}return cb(r)}function dH(t){var n=t.domain;return t.ticks=function(t){var e=n();return tv(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return dY(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,i,o=n(),u=0,a=o.length-1,c=o[u],f=o[a],l=10;for(f<c&&(i=c,c=f,f=i,i=u,u=a,a=i);l-- >0;){if((i=ty(c,f,e))===r)return o[u]=c,o[a]=f,n(o);if(i>0)c=Math.floor(c/i)*i,f=Math.ceil(f/i)*i;else if(i<0)c=Math.ceil(c*i)/i,f=Math.floor(f*i)/i;else break;r=i}return t},t}function dX(t,n){t=t.slice();var e,r=0,i=t.length-1,o=t[r],u=t[i];return u<o&&(e=r,r=i,i=e,e=o,o=u,u=e),t[r]=n.floor(o),t[i]=n.ceil(u),t}function dZ(t){return Math.log(t)}function dW(t){return Math.exp(t)}function dJ(t){return-Math.log(-t)}function dK(t){return-Math.exp(-t)}function dQ(t){return isFinite(t)?+("1e"+t):t<0?0:t}function d$(t){return(n,e)=>-t(-n,e)}function d0(t){let n,e;let r=t(dZ,dW),i=r.domain,o=10;function u(){var u,a;return n=(u=o)===Math.E?Math.log:10===u&&Math.log10||2===u&&Math.log2||(u=Math.log(u),t=>Math.log(t)/u),e=10===(a=o)?dQ:a===Math.E?Math.exp:t=>Math.pow(a,t),i()[0]<0?(n=d$(n),e=d$(e),t(dJ,dK)):t(dZ,dW),r}return r.base=function(t){return arguments.length?(o=+t,u()):o},r.domain=function(t){return arguments.length?(i(t),u()):i()},r.ticks=t=>{let r,u;let a=i(),c=a[0],f=a[a.length-1],l=f<c;l&&([c,f]=[f,c]);let s=n(c),h=n(f),d=null==t?10:+t,p=[];if(!(o%1)&&h-s<d){if(s=Math.floor(s),h=Math.ceil(h),c>0){for(;s<=h;++s)for(r=1;r<o;++r)if(!((u=s<0?r/e(-s):r*e(s))<c)){if(u>f)break;p.push(u)}}else for(;s<=h;++s)for(r=o-1;r>=1;--r)if(!((u=s>0?r/e(-s):r*e(s))<c)){if(u>f)break;p.push(u)}2*p.length<d&&(p=tv(c,f,d))}else p=tv(s,h,Math.min(h-s,d)).map(e);return l?p.reverse():p},r.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=aV(i)).precision||(i.trim=!0),i=cb(i)),t===1/0)return i;let u=Math.max(1,o*t/r.ticks().length);return t=>{let r=t/e(Math.round(n(t)));return r*o<o-.5&&(r*=o),r<=u?i(t):""}},r.nice=()=>i(dX(i(),{floor:t=>e(Math.floor(n(t))),ceil:t=>e(Math.ceil(n(t)))})),r}function d1(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function d2(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function d6(t){var n=1,e=t(d1(1),d2(n));return e.constant=function(e){return arguments.length?t(d1(n=+e),d2(n)):n},dH(e)}function d3(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function d5(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function d4(t){return t<0?-t*t:t*t}function d8(t){var n=t(dB,dB),e=1;return n.exponent=function(n){return arguments.length?1==(e=+n)?t(dB,dB):.5===e?t(d5,d4):t(d3(e),d3(1/e)):e},dH(n)}function d7(){var t=d8(dq());return t.copy=function(){return dG(t,d7()).exponent(t.exponent())},dC.apply(t,arguments),t}function d9(){return d7.apply(null,arguments).exponent(.5)}function pt(t){return Math.sign(t)*t*t}let pn=new Date,pe=new Date;function pr(t,n,e,r){function i(n){return t(n=0==arguments.length?new Date:new Date(+n)),n}return i.floor=n=>(t(n=new Date(+n)),n),i.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e),i.round=t=>{let n=i(t),e=i.ceil(t);return t-n<e-t?n:e},i.offset=(t,e)=>(n(t=new Date(+t),null==e?1:Math.floor(e)),t),i.range=(e,r,o)=>{let u;let a=[];if(e=i.ceil(e),o=null==o?1:Math.floor(o),!(e<r)||!(o>0))return a;do a.push(u=new Date(+e)),n(e,o),t(e);while(u<e&&e<r);return a},i.filter=e=>pr(n=>{if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)},(t,r)=>{if(t>=t){if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););}}),e&&(i.count=(n,r)=>(pn.setTime(+n),pe.setTime(+r),t(pn),t(pe),Math.floor(e(pn,pe))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(r?n=>r(n)%t==0:n=>i.count(0,n)%t==0):i:null),i}let pi=pr(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);pi.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?pr(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):pi:null;let po=pi.range,pu=pr(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+1e3*n)},(t,n)=>(n-t)/1e3,t=>t.getUTCSeconds()),pa=pu.range,pc=pr(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getMinutes()),pf=pc.range,pl=pr(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getUTCMinutes()),ps=pl.range,ph=pr(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getHours()),pd=ph.range,pp=pr(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getUTCHours()),pg=pp.range,pv=pr(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1),py=pv.range,pb=pr(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>t.getUTCDate()-1),pm=pb.range,p_=pr(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>Math.floor(t/864e5)),px=p_.range;function pw(t){return pr(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}let pM=pw(0),pT=pw(1),pA=pw(2),pk=pw(3),pS=pw(4),pN=pw(5),pE=pw(6),pC=pM.range,pP=pT.range,pR=pA.range,pO=pk.range,pD=pS.range,pz=pN.range,pL=pE.range;function pj(t){return pr(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/6048e5)}let pB=pj(0),pV=pj(1),pI=pj(2),pF=pj(3),pG=pj(4),pq=pj(5),pU=pj(6),pY=pB.range,pH=pV.range,pX=pI.range,pZ=pF.range,pW=pG.range,pJ=pq.range,pK=pU.range,pQ=pr(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12,t=>t.getMonth()),p$=pQ.range,p0=pr(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth()),p1=p0.range,p2=pr(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());p2.every=t=>isFinite(t=Math.floor(t))&&t>0?pr(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)}):null;let p6=p2.range,p3=pr(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());p3.every=t=>isFinite(t=Math.floor(t))&&t>0?pr(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null;let p5=p3.range;function p4(t,n,e,r,i,o){let u=[[pu,1,1e3],[pu,5,5e3],[pu,15,15e3],[pu,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[r,1,864e5],[r,2,1728e5],[e,1,6048e5],[n,1,2592e6],[n,3,7776e6],[t,1,31536e6]];function a(n,e,r){let i=Math.abs(e-n)/r,o=f(t=>{let[,,n]=t;return n}).right(u,i);if(o===u.length)return t.every(tb(n/31536e6,e/31536e6,r));if(0===o)return pi.every(Math.max(tb(n,e,r),1));let[a,c]=u[i/u[o-1][2]<u[o][2]/i?o-1:o];return a.every(c)}return[function(t,n,e){let r=n<t;r&&([t,n]=[n,t]);let i=e&&"function"==typeof e.range?e:a(t,n,e),o=i?i.range(t,+n+1):[];return r?o.reverse():o},a]}let[p8,p7]=p4(p3,p0,pB,p_,pp,pl),[p9,gt]=p4(p2,pQ,pM,pv,ph,pc);function gn(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ge(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function gr(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function gi(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,u=t.shortDays,a=t.months,c=t.shortMonths,f=gs(i),l=gh(i),s=gs(o),h=gh(o),d=gs(u),p=gh(u),g=gs(a),v=gh(a),y=gs(c),b=gh(c),m={a:function(t){return u[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return a[t.getMonth()]},c:null,d:gO,e:gO,f:gB,g:gW,G:gK,H:gD,I:gz,j:gL,L:gj,m:gV,M:gI,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:vl,s:vs,S:gF,u:gG,U:gq,V:gY,w:gH,W:gX,x:null,X:null,y:gZ,Y:gJ,Z:gQ,"%":vf},_={a:function(t){return u[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return a[t.getUTCMonth()]},c:null,d:g$,e:g$,f:g3,g:vo,G:va,H:g0,I:g1,j:g2,L:g6,m:g5,M:g4,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:vl,s:vs,S:g8,u:g7,U:g9,V:vn,w:ve,W:vr,x:null,X:null,y:vi,Y:vu,Z:vc,"%":vf},x={a:function(t,n,e){var r=d.exec(n.slice(e));return r?(t.w=p.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(t,n,e){var r=s.exec(n.slice(e));return r?(t.w=h.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(t,n,e){var r=y.exec(n.slice(e));return r?(t.m=b.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(t,n,e){var r=g.exec(n.slice(e));return r?(t.m=v.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(t,e,r){return T(t,n,e,r)},d:gM,e:gM,f:gE,g:gm,G:gb,H:gA,I:gA,j:gT,L:gN,m:gw,M:gk,p:function(t,n,e){var r=f.exec(n.slice(e));return r?(t.p=l.get(r[0].toLowerCase()),e+r[0].length):-1},q:gx,Q:gP,s:gR,S:gS,u:gp,U:gg,V:gv,w:gd,W:gy,x:function(t,n,r){return T(t,e,n,r)},X:function(t,n,e){return T(t,r,n,e)},y:gm,Y:gb,Z:g_,"%":gC};function w(t,n){return function(e){var r,i,o,u=[],a=-1,c=0,f=t.length;for(e instanceof Date||(e=new Date(+e));++a<f;)37===t.charCodeAt(a)&&(u.push(t.slice(c,a)),null!=(i=go[r=t.charAt(++a)])?r=t.charAt(++a):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),u.push(r),c=a+1);return u.push(t.slice(c,a)),u.join("")}}function M(t,n){return function(e){var r,i,o=gr(1900,void 0,1);if(T(o,t,e+="",0)!=e.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!n||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(r=(i=(r=ge(gr(o.y,0,1))).getUTCDay())>4||0===i?pV.ceil(r):pV(r),r=pb.offset(r,(o.V-1)*7),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(r=(i=(r=gn(gr(o.y,0,1))).getDay())>4||0===i?pT.ceil(r):pT(r),r=pv.offset(r,(o.V-1)*7),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?ge(gr(o.y,0,1)).getUTCDay():gn(gr(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,ge(o)):gn(o)}}function T(t,n,e,r){for(var i,o,u=0,a=n.length,c=e.length;u<a;){if(r>=c)return -1;if(37===(i=n.charCodeAt(u++))){if(!(o=x[(i=n.charAt(u++))in go?n.charAt(u++):i])||(r=o(t,e,r))<0)return -1}else if(i!=e.charCodeAt(r++))return -1}return r}return m.x=w(e,m),m.X=w(r,m),m.c=w(n,m),_.x=w(e,_),_.X=w(r,_),_.c=w(n,_),{format:function(t){var n=w(t+="",m);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",_);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t+="",!0);return n.toString=function(){return t},n}}}var go={"-":"",_:" ",0:"0"},gu=/^\s*\d+/,ga=/^%/,gc=/[\\^$*+?|[\]().{}]/g;function gf(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?Array(e-o+1).join(n)+i:i)}function gl(t){return t.replace(gc,"\\$&")}function gs(t){return RegExp("^(?:"+t.map(gl).join("|")+")","i")}function gh(t){return new Map(t.map((t,n)=>[t.toLowerCase(),n]))}function gd(t,n,e){var r=gu.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function gp(t,n,e){var r=gu.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function gg(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function gv(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function gy(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function gb(t,n,e){var r=gu.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function gm(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function g_(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function gx(t,n,e){var r=gu.exec(n.slice(e,e+1));return r?(t.q=3*r[0]-3,e+r[0].length):-1}function gw(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function gM(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function gT(t,n,e){var r=gu.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function gA(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function gk(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function gS(t,n,e){var r=gu.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function gN(t,n,e){var r=gu.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function gE(t,n,e){var r=gu.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function gC(t,n,e){var r=ga.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function gP(t,n,e){var r=gu.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function gR(t,n,e){var r=gu.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function gO(t,n){return gf(t.getDate(),n,2)}function gD(t,n){return gf(t.getHours(),n,2)}function gz(t,n){return gf(t.getHours()%12||12,n,2)}function gL(t,n){return gf(1+pv.count(p2(t),t),n,3)}function gj(t,n){return gf(t.getMilliseconds(),n,3)}function gB(t,n){return gj(t,n)+"000"}function gV(t,n){return gf(t.getMonth()+1,n,2)}function gI(t,n){return gf(t.getMinutes(),n,2)}function gF(t,n){return gf(t.getSeconds(),n,2)}function gG(t){var n=t.getDay();return 0===n?7:n}function gq(t,n){return gf(pM.count(p2(t)-1,t),n,2)}function gU(t){var n=t.getDay();return n>=4||0===n?pS(t):pS.ceil(t)}function gY(t,n){return t=gU(t),gf(pS.count(p2(t),t)+(4===p2(t).getDay()),n,2)}function gH(t){return t.getDay()}function gX(t,n){return gf(pT.count(p2(t)-1,t),n,2)}function gZ(t,n){return gf(t.getFullYear()%100,n,2)}function gW(t,n){return gf((t=gU(t)).getFullYear()%100,n,2)}function gJ(t,n){return gf(t.getFullYear()%1e4,n,4)}function gK(t,n){var e=t.getDay();return gf((t=e>=4||0===e?pS(t):pS.ceil(t)).getFullYear()%1e4,n,4)}function gQ(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+gf(n/60|0,"0",2)+gf(n%60,"0",2)}function g$(t,n){return gf(t.getUTCDate(),n,2)}function g0(t,n){return gf(t.getUTCHours(),n,2)}function g1(t,n){return gf(t.getUTCHours()%12||12,n,2)}function g2(t,n){return gf(1+pb.count(p3(t),t),n,3)}function g6(t,n){return gf(t.getUTCMilliseconds(),n,3)}function g3(t,n){return g6(t,n)+"000"}function g5(t,n){return gf(t.getUTCMonth()+1,n,2)}function g4(t,n){return gf(t.getUTCMinutes(),n,2)}function g8(t,n){return gf(t.getUTCSeconds(),n,2)}function g7(t){var n=t.getUTCDay();return 0===n?7:n}function g9(t,n){return gf(pB.count(p3(t)-1,t),n,2)}function vt(t){var n=t.getUTCDay();return n>=4||0===n?pG(t):pG.ceil(t)}function vn(t,n){return t=vt(t),gf(pG.count(p3(t),t)+(4===p3(t).getUTCDay()),n,2)}function ve(t){return t.getUTCDay()}function vr(t,n){return gf(pV.count(p3(t)-1,t),n,2)}function vi(t,n){return gf(t.getUTCFullYear()%100,n,2)}function vo(t,n){return gf((t=vt(t)).getUTCFullYear()%100,n,2)}function vu(t,n){return gf(t.getUTCFullYear()%1e4,n,4)}function va(t,n){var e=t.getUTCDay();return gf((t=e>=4||0===e?pG(t):pG.ceil(t)).getUTCFullYear()%1e4,n,4)}function vc(){return"+0000"}function vf(){return"%"}function vl(t){return+t}function vs(t){return Math.floor(+t/1e3)}function vh(t){return y=(v=gi(t)).format,b=v.parse,m=v.utcFormat,_=v.utcParse,v}function vd(t){return new Date(t)}function vp(t){return t instanceof Date?+t:+new Date(+t)}function vg(t,n,e,r,i,o,u,a,c,f){var l=dU(),s=l.invert,h=l.domain,d=f(".%L"),p=f(":%S"),g=f("%I:%M"),v=f("%I %p"),y=f("%a %d"),b=f("%b %d"),m=f("%B"),_=f("%Y");function x(t){return(c(t)<t?d:a(t)<t?p:u(t)<t?g:o(t)<t?v:r(t)<t?i(t)<t?y:b:e(t)<t?m:_)(t)}return l.invert=function(t){return new Date(s(t))},l.domain=function(t){return arguments.length?h(Array.from(t,vp)):h().map(vd)},l.ticks=function(n){var e=h();return t(e[0],e[e.length-1],null==n?10:n)},l.tickFormat=function(t,n){return null==n?x:f(n)},l.nice=function(t){var e=h();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?h(dX(e,t)):l},l.copy=function(){return dG(l,vg(t,n,e,r,i,o,u,a,c,f))},l}function vv(){return dC.apply(vg(p9,gt,p2,pQ,pM,pv,ph,pc,pu,y).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function vy(){return dC.apply(vg(p8,p7,p3,p0,pB,pb,pp,pl,pu,m).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function vb(){var t,n,e,r,i,o=0,u=1,a=dB,c=!1;function f(n){return null==n||isNaN(n=+n)?i:a(0===e?.5:(n=(r(n)-t)*e,c?Math.max(0,Math.min(1,n)):n))}function l(t){return function(n){var e,r;return arguments.length?([e,r]=n,a=t(e,r),f):[a(0),a(1)]}}return f.domain=function(i){return arguments.length?([o,u]=i,t=r(o=+o),n=r(u=+u),e=t===n?0:1/(n-t),f):[o,u]},f.clamp=function(t){return arguments.length?(c=!!t,f):c},f.interpolator=function(t){return arguments.length?(a=t,f):a},f.range=l(e3),f.rangeRound=l(hK),f.unknown=function(t){return arguments.length?(i=t,f):i},function(i){return r=i,t=i(o),n=i(u),e=t===n?0:1/(n-t),f}}function vm(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function v_(){var t=d8(vb());return t.copy=function(){return vm(t,v_()).exponent(t.exponent())},dP.apply(t,arguments)}function vx(){return v_.apply(null,arguments).exponent(.5)}function vw(){var t,n,e,r,i,o,u,a=0,c=.5,f=1,l=1,s=dB,h=!1;function d(t){return isNaN(t=+t)?u:(t=.5+((t=+o(t))-n)*(l*t<l*n?r:i),s(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(n){var e,r,i;return arguments.length?([e,r,i]=n,s=dt(t,[e,r,i]),d):[s(0),s(.5),s(1)]}}return d.domain=function(u){return arguments.length?([a,c,f]=u,t=o(a=+a),n=o(c=+c),e=o(f=+f),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),l=n<t?-1:1,d):[a,c,f]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(s=t,d):s},d.range=p(e3),d.rangeRound=p(hK),d.unknown=function(t){return arguments.length?(u=t,d):u},function(u){return o=u,t=u(a),n=u(c),e=u(f),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),l=n<t?-1:1,d}}function vM(){var t=d8(vw());return t.copy=function(){return vm(t,vM()).exponent(t.exponent())},dP.apply(t,arguments)}function vT(){return vM.apply(null,arguments).exponent(.5)}function vA(t){for(var n=t.length/6|0,e=Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}vh({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var vk=vA("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),vS=vA("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),vN=vA("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),vE=vA("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),vC=vA("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),vP=vA("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),vR=vA("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),vO=vA("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),vD=vA("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),vz=vA("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),vL=vA("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),vj=t=>eH(t[t.length-1]),vB=[,,,].concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(vA),vV=vj(vB),vI=[,,,].concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(vA),vF=vj(vI),vG=[,,,].concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(vA),vq=vj(vG),vU=[,,,].concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(vA),vY=vj(vU),vH=[,,,].concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(vA),vX=vj(vH),vZ=[,,,].concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(vA),vW=vj(vZ),vJ=[,,,].concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(vA),vK=vj(vJ),vQ=[,,,].concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(vA),v$=vj(vQ),v0=[,,,].concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(vA),v1=vj(v0),v2=[,,,].concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(vA),v6=vj(v2),v3=[,,,].concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(vA),v5=vj(v3),v4=[,,,].concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(vA),v8=vj(v4),v7=[,,,].concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(vA),v9=vj(v7),yt=[,,,].concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(vA),yn=vj(yt),ye=[,,,].concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(vA),yr=vj(ye),yi=[,,,].concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(vA),yo=vj(yi),yu=[,,,].concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(vA),ya=vj(yu),yc=[,,,].concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(vA),yf=vj(yc),yl=[,,,].concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(vA),ys=vj(yl),yh=[,,,].concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(vA),yd=vj(yh),yp=[,,,].concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(vA),yg=vj(yp),yv=[,,,].concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(vA),yy=vj(yv),yb=[,,,].concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(vA),ym=vj(yb),y_=[,,,].concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(vA),yx=vj(y_),yw=[,,,].concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(vA),yM=vj(yw),yT=[,,,].concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(vA),yA=vj(yT),yk=[,,,].concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(vA),yS=vj(yk);function yN(t){return"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-(t=Math.max(0,Math.min(1,t)))*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-2710.57*t)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-67.37*t)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-2475.67*t)))))))+")"}var yE=h9(od(300,.5,0),od(-240,.5,1)),yC=h9(od(-100,.75,.35),od(80,1.5,.8)),yP=h9(od(260,.75,.35),od(80,1.5,.8)),yR=od();function yO(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return yR.h=360*t-100,yR.s=1.5-1.5*n,yR.l=.8-.9*n,yR+""}var yD=eM(),yz=Math.PI/3,yL=2*Math.PI/3;function yj(t){var n;return t=(.5-t)*Math.PI,yD.r=255*(n=Math.sin(t))*n,yD.g=255*(n=Math.sin(t+yz))*n,yD.b=255*(n=Math.sin(t+yL))*n,yD+""}function yB(t){return"rgb("+Math.max(0,Math.min(255,Math.round(34.61+(t=Math.max(0,Math.min(1,t)))*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-14825.05*t)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+707.56*t)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-6838.66*t)))))))+")"}function yV(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var yI=yV(vA("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),yF=yV(vA("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),yG=yV(vA("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),yq=yV(vA("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));function yU(t){return n4(nZ(t).call(document.documentElement))}var yY=0;function yH(){return new yX}function yX(){this._="@"+(++yY).toString(36)}function yZ(t,n){return t.target&&(t=e5(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,t=>e4(t,n))}function yW(t){return"string"==typeof t?new n6([document.querySelectorAll(t)],[document.documentElement]):new n6([nm(t)],n2)}function yJ(t){return function(){return t}}yX.prototype=yH.prototype={constructor:yX,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};let yK=Math.abs,yQ=Math.atan2,y$=Math.cos,y0=Math.max,y1=Math.min,y2=Math.sin,y6=Math.sqrt,y3=Math.PI,y5=y3/2,y4=2*y3;function y8(t){return t>=1?y5:t<=-1?-y5:Math.asin(t)}function y7(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{let t=Math.floor(e);if(!(t>=0))throw RangeError("invalid digits: ".concat(e));n=t}return t},()=>new iG(n)}function y9(t){return t.innerRadius}function bt(t){return t.outerRadius}function bn(t){return t.startAngle}function be(t){return t.endAngle}function br(t){return t&&t.padAngle}function bi(t,n,e,r,i,o,u){var a=t-e,c=n-r,f=(u?o:-o)/y6(a*a+c*c),l=f*c,s=-f*a,h=t+l,d=n+s,p=e+l,g=r+s,v=(h+p)/2,y=(d+g)/2,b=p-h,m=g-d,_=b*b+m*m,x=i-o,w=h*g-p*d,M=(m<0?-1:1)*y6(y0(0,x*x*_-w*w)),T=(w*m-b*M)/_,A=(-w*b-m*M)/_,k=(w*m+b*M)/_,S=(-w*b+m*M)/_,N=T-v,E=A-y,C=k-v,P=S-y;return N*N+E*E>C*C+P*P&&(T=k,A=S),{cx:T,cy:A,x01:-l,y01:-s,x11:T*(i/x-1),y11:A*(i/x-1)}}function bo(){var t=y9,n=bt,e=yJ(0),r=null,i=bn,o=be,u=br,a=null,c=y7(f);function f(){var f,l,s=+t.apply(this,arguments),h=+n.apply(this,arguments),d=i.apply(this,arguments)-y5,p=o.apply(this,arguments)-y5,g=yK(p-d),v=p>d;if(a||(a=f=c()),h<s&&(l=h,h=s,s=l),h>1e-12){if(g>y4-1e-12)a.moveTo(h*y$(d),h*y2(d)),a.arc(0,0,h,d,p,!v),s>1e-12&&(a.moveTo(s*y$(p),s*y2(p)),a.arc(0,0,s,p,d,v));else{var y,b,m=d,_=p,x=d,w=p,M=g,T=g,A=u.apply(this,arguments)/2,k=A>1e-12&&(r?+r.apply(this,arguments):y6(s*s+h*h)),S=y1(yK(h-s)/2,+e.apply(this,arguments)),N=S,E=S;if(k>1e-12){var C=y8(k/s*y2(A)),P=y8(k/h*y2(A));(M-=2*C)>1e-12?(C*=v?1:-1,x+=C,w-=C):(M=0,x=w=(d+p)/2),(T-=2*P)>1e-12?(P*=v?1:-1,m+=P,_-=P):(T=0,m=_=(d+p)/2)}var R=h*y$(m),O=h*y2(m),D=s*y$(w),z=s*y2(w);if(S>1e-12){var L,j=h*y$(_),B=h*y2(_),V=s*y$(x),I=s*y2(x);if(g<y3){if(L=function(t,n,e,r,i,o,u,a){var c=e-t,f=r-n,l=u-i,s=a-o,h=s*c-l*f;if(!(h*h<1e-12))return h=(l*(n-o)-s*(t-i))/h,[t+h*c,n+h*f]}(R,O,V,I,j,B,D,z)){var F,G=R-L[0],q=O-L[1],U=j-L[0],Y=B-L[1],H=1/y2(((F=(G*U+q*Y)/(y6(G*G+q*q)*y6(U*U+Y*Y)))>1?0:F<-1?y3:Math.acos(F))/2),X=y6(L[0]*L[0]+L[1]*L[1]);N=y1(S,(s-X)/(H-1)),E=y1(S,(h-X)/(H+1))}else N=E=0}}T>1e-12?E>1e-12?(y=bi(V,I,R,O,h,E,v),b=bi(j,B,D,z,h,E,v),a.moveTo(y.cx+y.x01,y.cy+y.y01),E<S?a.arc(y.cx,y.cy,E,yQ(y.y01,y.x01),yQ(b.y01,b.x01),!v):(a.arc(y.cx,y.cy,E,yQ(y.y01,y.x01),yQ(y.y11,y.x11),!v),a.arc(0,0,h,yQ(y.cy+y.y11,y.cx+y.x11),yQ(b.cy+b.y11,b.cx+b.x11),!v),a.arc(b.cx,b.cy,E,yQ(b.y11,b.x11),yQ(b.y01,b.x01),!v))):(a.moveTo(R,O),a.arc(0,0,h,m,_,!v)):a.moveTo(R,O),s>1e-12&&M>1e-12?N>1e-12?(y=bi(D,z,j,B,s,-N,v),b=bi(R,O,V,I,s,-N,v),a.lineTo(y.cx+y.x01,y.cy+y.y01),N<S?a.arc(y.cx,y.cy,N,yQ(y.y01,y.x01),yQ(b.y01,b.x01),!v):(a.arc(y.cx,y.cy,N,yQ(y.y01,y.x01),yQ(y.y11,y.x11),!v),a.arc(0,0,s,yQ(y.cy+y.y11,y.cx+y.x11),yQ(b.cy+b.y11,b.cx+b.x11),v),a.arc(b.cx,b.cy,N,yQ(b.y11,b.x11),yQ(b.y01,b.x01),!v))):a.arc(0,0,s,w,x,v):a.lineTo(D,z)}}else a.moveTo(0,0);if(a.closePath(),f)return a=null,f+""||null}return f.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-y3/2;return[y$(r)*e,y2(r)*e]},f.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:yJ(+n),f):t},f.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:yJ(+t),f):n},f.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:yJ(+t),f):e},f.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:yJ(+t),f):r},f.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:yJ(+t),f):i},f.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:yJ(+t),f):o},f.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:yJ(+t),f):u},f.context=function(t){return arguments.length?(a=null==t?null:t,f):a},f}var bu=Array.prototype.slice;function ba(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function bc(t){this._context=t}function bf(t){return new bc(t)}function bl(t){return t[0]}function bs(t){return t[1]}function bh(t,n){var e=yJ(!0),r=null,i=bf,o=null,u=y7(a);function a(a){var c,f,l,s=(a=ba(a)).length,h=!1;for(null==r&&(o=i(l=u())),c=0;c<=s;++c)!(c<s&&e(f=a[c],c,a))===h&&((h=!h)?o.lineStart():o.lineEnd()),h&&o.point(+t(f,c,a),+n(f,c,a));if(l)return o=null,l+""||null}return t="function"==typeof t?t:void 0===t?bl:yJ(t),n="function"==typeof n?n:void 0===n?bs:yJ(n),a.x=function(n){return arguments.length?(t="function"==typeof n?n:yJ(+n),a):t},a.y=function(t){return arguments.length?(n="function"==typeof t?t:yJ(+t),a):n},a.defined=function(t){return arguments.length?(e="function"==typeof t?t:yJ(!!t),a):e},a.curve=function(t){return arguments.length?(i=t,null!=r&&(o=i(r)),a):i},a.context=function(t){return arguments.length?(null==t?r=o=null:o=i(r=t),a):r},a}function bd(t,n,e){var r=null,i=yJ(!0),o=null,u=bf,a=null,c=y7(f);function f(f){var l,s,h,d,p,g=(f=ba(f)).length,v=!1,y=Array(g),b=Array(g);for(null==o&&(a=u(p=c())),l=0;l<=g;++l){if(!(l<g&&i(d=f[l],l,f))===v){if(v=!v)s=l,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),h=l-1;h>=s;--h)a.point(y[h],b[h]);a.lineEnd(),a.areaEnd()}}v&&(y[l]=+t(d,l,f),b[l]=+n(d,l,f),a.point(r?+r(d,l,f):y[l],e?+e(d,l,f):b[l]))}if(p)return a=null,p+""||null}function l(){return bh().defined(i).curve(u).context(o)}return t="function"==typeof t?t:void 0===t?bl:yJ(+t),n="function"==typeof n?n:void 0===n?yJ(0):yJ(+n),e="function"==typeof e?e:void 0===e?bs:yJ(+e),f.x=function(n){return arguments.length?(t="function"==typeof n?n:yJ(+n),r=null,f):t},f.x0=function(n){return arguments.length?(t="function"==typeof n?n:yJ(+n),f):t},f.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:yJ(+t),f):r},f.y=function(t){return arguments.length?(n="function"==typeof t?t:yJ(+t),e=null,f):n},f.y0=function(t){return arguments.length?(n="function"==typeof t?t:yJ(+t),f):n},f.y1=function(t){return arguments.length?(e=null==t?null:"function"==typeof t?t:yJ(+t),f):e},f.lineX0=f.lineY0=function(){return l().x(t).y(n)},f.lineY1=function(){return l().x(t).y(e)},f.lineX1=function(){return l().x(r).y(n)},f.defined=function(t){return arguments.length?(i="function"==typeof t?t:yJ(!!t),f):i},f.curve=function(t){return arguments.length?(u=t,null!=o&&(a=u(o)),f):u},f.context=function(t){return arguments.length?(null==t?o=a=null:a=u(o=t),f):o},f}function bp(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function bg(t){return t}function bv(){var t=bg,n=bp,e=null,r=yJ(0),i=yJ(y4),o=yJ(0);function u(u){var a,c,f,l,s,h=(u=ba(u)).length,d=0,p=Array(h),g=Array(h),v=+r.apply(this,arguments),y=Math.min(y4,Math.max(-y4,i.apply(this,arguments)-v)),b=Math.min(Math.abs(y)/h,o.apply(this,arguments)),m=b*(y<0?-1:1);for(a=0;a<h;++a)(s=g[p[a]=a]=+t(u[a],a,u))>0&&(d+=s);for(null!=n?p.sort(function(t,e){return n(g[t],g[e])}):null!=e&&p.sort(function(t,n){return e(u[t],u[n])}),a=0,f=d?(y-h*m)/d:0;a<h;++a,v=l)l=v+((s=g[c=p[a]])>0?s*f:0)+m,g[c]={data:u[c],index:a,value:s,startAngle:v,endAngle:l,padAngle:b};return g}return u.value=function(n){return arguments.length?(t="function"==typeof n?n:yJ(+n),u):t},u.sortValues=function(t){return arguments.length?(n=t,e=null,u):n},u.sort=function(t){return arguments.length?(e=t,n=null,u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:yJ(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:yJ(+t),u):i},u.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:yJ(+t),u):o},u}bc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var by=bm(bf);function bb(t){this._curve=t}function bm(t){function n(n){return new bb(t(n))}return n._curve=t,n}function b_(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(bm(t)):n()._curve},t}function bx(){return b_(bh().curve(by))}function bw(){var t=bd().curve(by),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return b_(e())},delete t.lineX0,t.lineEndAngle=function(){return b_(r())},delete t.lineX1,t.lineInnerRadius=function(){return b_(i())},delete t.lineY0,t.lineOuterRadius=function(){return b_(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(bm(t)):n()._curve},t}function bM(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]}bb.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),-(n*Math.cos(t)))}};class bT{areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}constructor(t,n){this._context=t,this._x=n}}class bA{lineStart(){this._point=0}lineEnd(){}point(t,n){if(t=+t,n=+n,0===this._point)this._point=1;else{let e=bM(this._x0,this._y0),r=bM(this._x0,this._y0=(this._y0+n)/2),i=bM(t,this._y0),o=bM(t,n);this._context.moveTo(...e),this._context.bezierCurveTo(...r,...i,...o)}this._x0=t,this._y0=n}constructor(t){this._context=t}}function bk(t){return new bT(t,!0)}function bS(t){return new bT(t,!1)}function bN(t){return new bA(t)}function bE(t){return t.source}function bC(t){return t.target}function bP(t){let n=bE,e=bC,r=bl,i=bs,o=null,u=null,a=y7(c);function c(){let c;let f=bu.call(arguments),l=n.apply(this,f),s=e.apply(this,f);if(null==o&&(u=t(c=a())),u.lineStart(),f[0]=l,u.point(+r.apply(this,f),+i.apply(this,f)),f[0]=s,u.point(+r.apply(this,f),+i.apply(this,f)),u.lineEnd(),c)return u=null,c+""||null}return c.source=function(t){return arguments.length?(n=t,c):n},c.target=function(t){return arguments.length?(e=t,c):e},c.x=function(t){return arguments.length?(r="function"==typeof t?t:yJ(+t),c):r},c.y=function(t){return arguments.length?(i="function"==typeof t?t:yJ(+t),c):i},c.context=function(n){return arguments.length?(null==n?o=u=null:u=t(o=n),c):o},c}function bR(){return bP(bk)}function bO(){return bP(bS)}function bD(){let t=bP(bN);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}let bz=y6(3);var bL={draw(t,n){let e=.59436*y6(n+y1(n/28,.75)),r=e/2,i=r*bz;t.moveTo(0,e),t.lineTo(0,-e),t.moveTo(-i,-r),t.lineTo(i,r),t.moveTo(-i,r),t.lineTo(i,-r)}},bj={draw(t,n){let e=y6(n/y3);t.moveTo(e,0),t.arc(0,0,e,0,y4)}},bB={draw(t,n){let e=y6(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}};let bV=y6(1/3),bI=2*bV;var bF={draw(t,n){let e=y6(n/bI),r=e*bV;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},bG={draw(t,n){let e=.62625*y6(n);t.moveTo(0,-e),t.lineTo(e,0),t.lineTo(0,e),t.lineTo(-e,0),t.closePath()}},bq={draw(t,n){let e=.87559*y6(n-y1(n/7,2));t.moveTo(-e,0),t.lineTo(e,0),t.moveTo(0,e),t.lineTo(0,-e)}},bU={draw(t,n){let e=y6(n),r=-e/2;t.rect(r,r,e,e)}},bY={draw(t,n){let e=.4431*y6(n);t.moveTo(e,e),t.lineTo(e,-e),t.lineTo(-e,-e),t.lineTo(-e,e),t.closePath()}};let bH=y2(y3/10)/y2(7*y3/10),bX=y2(y4/10)*bH,bZ=-y$(y4/10)*bH;var bW={draw(t,n){let e=y6(.8908130915292852*n),r=bX*e,i=bZ*e;t.moveTo(0,-e),t.lineTo(r,i);for(let n=1;n<5;++n){let o=y4*n/5,u=y$(o),a=y2(o);t.lineTo(a*e,-u*e),t.lineTo(u*r-a*i,a*r+u*i)}t.closePath()}};let bJ=y6(3);var bK={draw(t,n){let e=-y6(n/(3*bJ));t.moveTo(0,2*e),t.lineTo(-bJ*e,-e),t.lineTo(bJ*e,-e),t.closePath()}};let bQ=y6(3);var b$={draw(t,n){let e=.6824*y6(n),r=e/2,i=e*bQ/2;t.moveTo(0,-e),t.lineTo(i,r),t.lineTo(-i,r),t.closePath()}};let b0=y6(3)/2,b1=1/y6(12),b2=(b1/2+1)*3;var b6={draw(t,n){let e=y6(n/b2),r=e/2,i=e*b1,o=e*b1+e,u=-r;t.moveTo(r,i),t.lineTo(r,o),t.lineTo(u,o),t.lineTo(-.5*r-b0*i,b0*r+-.5*i),t.lineTo(-.5*r-b0*o,b0*r+-.5*o),t.lineTo(-.5*u-b0*o,b0*u+-.5*o),t.lineTo(-.5*r+b0*i,-.5*i-b0*r),t.lineTo(-.5*r+b0*o,-.5*o-b0*r),t.lineTo(-.5*u+b0*o,-.5*o-b0*u),t.closePath()}},b3={draw(t,n){let e=.6189*y6(n-y1(n/6,1.7));t.moveTo(-e,-e),t.lineTo(e,e),t.moveTo(-e,e),t.lineTo(e,-e)}};let b5=[bj,bB,bF,bU,bW,bK,b6],b4=[bj,bq,b3,b$,bL,bY,bG];function b8(t,n){let e=null,r=y7(i);function i(){let i;if(e||(e=i=r()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),i)return e=null,i+""||null}return t="function"==typeof t?t:yJ(t||bj),n="function"==typeof n?n:yJ(void 0===n?64:+n),i.type=function(n){return arguments.length?(t="function"==typeof n?n:yJ(n),i):t},i.size=function(t){return arguments.length?(n="function"==typeof t?t:yJ(+t),i):n},i.context=function(t){return arguments.length?(e=null==t?null:t,i):e},i}function b7(){}function b9(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function mt(t){this._context=t}function mn(t){return new mt(t)}function me(t){this._context=t}function mr(t){return new me(t)}function mi(t){this._context=t}function mo(t){return new mi(t)}function mu(t,n){this._basis=new mt(t),this._beta=n}mt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:b9(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:b9(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},me.prototype={areaStart:b7,areaEnd:b7,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:b9(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},mi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:b9(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},mu.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],u=t[e]-i,a=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*u),this._beta*n[c]+(1-this._beta)*(o+r*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var ma=function t(n){function e(t){return 1===n?new mt(t):new mu(t,n)}return e.beta=function(n){return t(+n)},e}(.85);function mc(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function mf(t,n){this._context=t,this._k=(1-n)/6}mf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:mc(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:mc(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var ml=function t(n){function e(t){return new mf(t,n)}return e.tension=function(n){return t(+n)},e}(0);function ms(t,n){this._context=t,this._k=(1-n)/6}ms.prototype={areaStart:b7,areaEnd:b7,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:mc(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var mh=function t(n){function e(t){return new ms(t,n)}return e.tension=function(n){return t(+n)},e}(0);function md(t,n){this._context=t,this._k=(1-n)/6}md.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:mc(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var mp=function t(n){function e(t){return new md(t,n)}return e.tension=function(n){return t(+n)},e}(0);function mg(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>1e-12){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>1e-12){var f=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,l=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*f+t._x1*t._l23_2a-n*t._l12_2a)/l,u=(u*f+t._y1*t._l23_2a-e*t._l12_2a)/l}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function mv(t,n){this._context=t,this._alpha=n}mv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:mg(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var my=function t(n){function e(t){return n?new mv(t,n):new mf(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function mb(t,n){this._context=t,this._alpha=n}mb.prototype={areaStart:b7,areaEnd:b7,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:mg(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var mm=function t(n){function e(t){return n?new mb(t,n):new ms(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function m_(t,n){this._context=t,this._alpha=n}m_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:mg(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var mx=function t(n){function e(t){return n?new m_(t,n):new md(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function mw(t){this._context=t}function mM(t){return new mw(t)}function mT(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),u=(e-t._y1)/(i||r<0&&-0);return((o<0?-1:1)+(u<0?-1:1))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs((o*i+u*r)/(r+i)))||0}function mA(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function mk(t,n,e){var r=t._x0,i=t._y0,o=t._x1,u=t._y1,a=(o-r)/3;t._context.bezierCurveTo(r+a,i+a*n,o-a,u-a*e,o,u)}function mS(t){this._context=t}function mN(t){this._context=new mE(t)}function mE(t){this._context=t}function mC(t){return new mS(t)}function mP(t){return new mN(t)}function mR(t){this._context=t}function mO(t){var n,e,r=t.length-1,i=Array(r),o=Array(r),u=Array(r);for(i[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,u[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,u[n]-=e*u[n-1];for(i[r-1]=u[r-1]/o[r-1],n=r-2;n>=0;--n)i[n]=(u[n]-i[n+1])/o[n];for(n=0,o[r-1]=(t[r]+i[r-1])/2;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}function mD(t){return new mR(t)}function mz(t,n){this._context=t,this._t=n}function mL(t){return new mz(t,.5)}function mj(t){return new mz(t,0)}function mB(t){return new mz(t,1)}function mV(t,n){if((i=t.length)>1)for(var e,r,i,o=1,u=t[n[0]],a=u.length;o<i;++o)for(r=u,u=t[n[o]],e=0;e<a;++e)u[e][1]+=u[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]}function mI(t){for(var n=t.length,e=Array(n);--n>=0;)e[n]=n;return e}function mF(t,n){return t[n]}function mG(t){let n=[];return n.key=t,n}function mq(){var t=yJ([]),n=mI,e=mV,r=mF;function i(i){var o,u,a=Array.from(t.apply(this,arguments),mG),c=a.length,f=-1;for(let t of i)for(o=0,++f;o<c;++o)(a[o][f]=[0,+r(t,a[o].key,f,i)]).data=t;for(o=0,u=ba(n(a));o<c;++o)a[u[o]].index=o;return e(a,u),a}return i.keys=function(n){return arguments.length?(t="function"==typeof n?n:yJ(Array.from(n)),i):t},i.value=function(t){return arguments.length?(r="function"==typeof t?t:yJ(+t),i):r},i.order=function(t){return arguments.length?(n=null==t?mI:"function"==typeof t?t:yJ(Array.from(t)),i):n},i.offset=function(t){return arguments.length?(e=null==t?mV:t,i):e},i}function mU(t,n){if((r=t.length)>0){for(var e,r,i,o=0,u=t[0].length;o<u;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}mV(t,n)}}function mY(t,n){if((a=t.length)>0)for(var e,r,i,o,u,a,c=0,f=t[n[0]].length;c<f;++c)for(o=u=0,e=0;e<a;++e)(i=(r=t[n[e]][c])[1]-r[0])>0?(r[0]=o,r[1]=o+=i):i<0?(r[1]=u,r[0]=u+=i):(r[0]=0,r[1]=i)}function mH(t,n){if((e=t.length)>0){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var u=0,a=0;u<e;++u)a+=t[u][r][1]||0;i[r][1]+=i[r][0]=-a/2}mV(t,n)}}function mX(t,n){if((i=t.length)>0&&(r=(e=t[n[0]]).length)>0){for(var e,r,i,o=0,u=1;u<r;++u){for(var a=0,c=0,f=0;a<i;++a){for(var l=t[n[a]],s=l[u][1]||0,h=(s-(l[u-1][1]||0))/2,d=0;d<a;++d){var p=t[n[d]];h+=(p[u][1]||0)-(p[u-1][1]||0)}c+=s,f+=h*s}e[u-1][1]+=e[u-1][0]=o,c&&(o-=f/c)}e[u-1][1]+=e[u-1][0]=o,mV(t,n)}}function mZ(t){var n=t.map(mW);return mI(t).sort(function(t,e){return n[t]-n[e]})}function mW(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}function mJ(t){var n=t.map(mK);return mI(t).sort(function(t,e){return n[t]-n[e]})}function mK(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}function mQ(t){return mJ(t).reverse()}function m$(t){var n,e,r=t.length,i=t.map(mK),o=mZ(t),u=0,a=0,c=[],f=[];for(n=0;n<r;++n)e=o[n],u<a?(u+=i[e],c.push(e)):(a+=i[e],f.push(e));return f.reverse().concat(c)}function m0(t){return mI(t).reverse()}mw.prototype={areaStart:b7,areaEnd:b7,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},mS.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:mk(this,this._t0,mA(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,mk(this,mA(this,e=mT(this,t,n)),e);break;default:mk(this,this._t0,e=mT(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(mN.prototype=Object.create(mS.prototype)).point=function(t,n){mS.prototype.point.call(this,n,t)},mE.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},mR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e){if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=mO(t),i=mO(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[u],n[u])}(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},mz.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}};var m1="%Y-%m-%dT%H:%M:%S.%LZ",m2=Date.prototype.toISOString?function(t){return t.toISOString()}:m(m1),m6=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:_(m1);function m3(t,n,e){var r=new rf,i=n;return null==n||(r._restart=r.restart,r.restart=function(t,n,e){n=+n,e=null==e?ra():+e,r._restart(function o(u){u+=i,r._restart(o,i+=n,e),t(u)},n,e)}),r.restart(t,n,e),r}var m5=t=>()=>t;function m4(t,n){let{sourceEvent:e,target:r,transform:i,dispatch:o}=n;Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},transform:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function m8(t,n,e){this.k=t,this.x=n,this.y=e}m8.prototype={constructor:m8,scale:function(t){return 1===t?this:new m8(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new m8(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var m7=new m8(1,0,0);function m9(t){for(;!t.__zoom;)if(!(t=t.parentNode))return m7;return t.__zoom}function _t(t){t.stopImmediatePropagation()}function _n(t){t.preventDefault(),t.stopImmediatePropagation()}function _e(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function _r(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function _i(){return this.__zoom||m7}function _o(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function _u(){return navigator.maxTouchPoints||"ontouchstart"in this}function _a(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function _c(){var t,n,e,r=_e,i=_r,o=_a,u=_o,a=_u,c=[0,1/0],f=[[-1/0,-1/0],[1/0,1/0]],l=250,s=h$,h=nv("start","zoom","end"),d=0,p=10;function g(t){t.property("__zoom",_i).on("wheel.zoom",w,{passive:!1}).on("mousedown.zoom",M).on("dblclick.zoom",T).filter(a).on("touchstart.zoom",A).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",S).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function v(t,n){return(n=Math.max(c[0],Math.min(c[1],n)))===t.k?t:new m8(n,t.x,t.y)}function y(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new m8(t.k,r,i)}function b(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function m(t,n,e,r){t.on("start.zoom",function(){_(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){_(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=_(this,t).event(r),u=i.apply(this,t),a=null==e?b(u):"function"==typeof e?e.apply(this,t):e,c=Math.max(u[1][0]-u[0][0],u[1][1]-u[0][1]),f=this.__zoom,l="function"==typeof n?n.apply(this,t):n,h=s(f.invert(a).concat(c/f.k),l.invert(a).concat(c/l.k));return function(t){if(1===t)t=l;else{var n=h(t),e=c/n[2];t=new m8(e,a[0]-n[0]*e,a[1]-n[1]*e)}o.zoom(null,t)}})}function _(t,n,e){return!e&&t.__zooming||new x(t,n)}function x(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function w(t){for(var n=arguments.length,e=Array(n>1?n-1:0),i=1;i<n;i++)e[i-1]=arguments[i];if(r.apply(this,arguments)){var a=_(this,e).event(t),l=this.__zoom,s=Math.max(c[0],Math.min(c[1],l.k*Math.pow(2,u.apply(this,arguments)))),h=e4(t);if(a.wheel)(a.mouse[0][0]!==h[0]||a.mouse[0][1]!==h[1])&&(a.mouse[1]=l.invert(a.mouse[0]=h)),clearTimeout(a.wheel);else{if(l.k===s)return;a.mouse=[h,l.invert(h)],rw(this),a.start()}_n(t),a.wheel=setTimeout(function(){a.wheel=null,a.end()},150),a.zoom("mouse",o(y(v(l,s),a.mouse[0],a.mouse[1]),a.extent,f))}}function M(t){for(var n=arguments.length,i=Array(n>1?n-1:0),u=1;u<n;u++)i[u-1]=arguments[u];if(!e&&r.apply(this,arguments)){var a=t.currentTarget,c=_(this,i,!0).event(t),l=n4(t.view).on("mousemove.zoom",function(t){if(_n(t),!c.moved){var n=t.clientX-h,e=t.clientY-p;c.moved=n*n+e*e>d}c.event(t).zoom("mouse",o(y(c.that.__zoom,c.mouse[0]=e4(t,a),c.mouse[1]),c.extent,f))},!0).on("mouseup.zoom",function(t){l.on("mousemove.zoom mouseup.zoom",null),ee(t.view,c.moved),_n(t),c.event(t).end()},!0),s=e4(t,a),h=t.clientX,p=t.clientY;en(t.view),_t(t),c.mouse=[s,this.__zoom.invert(s)],rw(this),c.start()}}function T(t){for(var n=arguments.length,e=Array(n>1?n-1:0),u=1;u<n;u++)e[u-1]=arguments[u];if(r.apply(this,arguments)){var a=this.__zoom,c=e4(t.changedTouches?t.changedTouches[0]:t,this),s=a.invert(c),h=a.k*(t.shiftKey?.5:2),d=o(y(v(a,h),c,s),i.apply(this,e),f);_n(t),l>0?n4(this).transition().duration(l).call(m,d,c,t):n4(this).call(g.transform,d,c,t)}}function A(e){for(var i=arguments.length,o=Array(i>1?i-1:0),u=1;u<i;u++)o[u-1]=arguments[u];if(r.apply(this,arguments)){var a,c,f,l,s=e.touches,h=s.length,d=_(this,o,e.changedTouches.length===h).event(e);for(_t(e),c=0;c<h;++c)l=[l=e4(f=s[c],this),this.__zoom.invert(l),f.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,a=!0,d.taps=1+!!t);t&&(t=clearTimeout(t)),a&&(d.taps<2&&(n=l[0],t=setTimeout(function(){t=null},500)),rw(this),d.start())}}function k(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];if(this.__zooming){var i,u,a,c,l=_(this,e).event(t),s=t.changedTouches,h=s.length;for(_n(t),i=0;i<h;++i)a=e4(u=s[i],this),l.touch0&&l.touch0[2]===u.identifier?l.touch0[0]=a:l.touch1&&l.touch1[2]===u.identifier&&(l.touch1[0]=a);if(u=l.that.__zoom,l.touch1){var d=l.touch0[0],p=l.touch0[1],g=l.touch1[0],b=l.touch1[1],m=(m=g[0]-d[0])*m+(m=g[1]-d[1])*m,x=(x=b[0]-p[0])*x+(x=b[1]-p[1])*x;u=v(u,Math.sqrt(m/x)),a=[(d[0]+g[0])/2,(d[1]+g[1])/2],c=[(p[0]+b[0])/2,(p[1]+b[1])/2]}else{if(!l.touch0)return;a=l.touch0[0],c=l.touch0[1]}l.zoom("touch",o(y(u,a,c),l.extent,f))}}function S(t){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];if(this.__zooming){var u,a,c=_(this,i).event(t),f=t.changedTouches,l=f.length;for(_t(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),u=0;u<l;++u)a=f[u],c.touch0&&c.touch0[2]===a.identifier?delete c.touch0:c.touch1&&c.touch1[2]===a.identifier&&delete c.touch1;if(c.touch1&&!c.touch0&&(c.touch0=c.touch1,delete c.touch1),c.touch0)c.touch0[1]=this.__zoom.invert(c.touch0[0]);else if(c.end(),2===c.taps&&(a=e4(a,this),Math.hypot(n[0]-a[0],n[1]-a[1])<p)){var s=n4(this).on("dblclick.zoom");s&&s.apply(this,arguments)}}}return g.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",_i),t!==i?m(t,n,e,r):i.interrupt().each(function(){_(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},g.scaleBy=function(t,n,e,r){g.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},g.scaleTo=function(t,n,e,r){g.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,u=null==e?b(t):"function"==typeof e?e.apply(this,arguments):e,a=r.invert(u),c="function"==typeof n?n.apply(this,arguments):n;return o(y(v(r,c),u,a),t,f)},e,r)},g.translateBy=function(t,n,e,r){g.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),f)},null,r)},g.translateTo=function(t,n,e,r,u){g.transform(t,function(){var t=i.apply(this,arguments),u=this.__zoom,a=null==r?b(t):"function"==typeof r?r.apply(this,arguments):r;return o(m7.translate(a[0],a[1]).scale(u.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,f)},r,u)},x.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=n4(this.that).datum();h.call(t,this.that,new m4(t,{sourceEvent:this.sourceEvent,target:g,type:t,transform:this.that.__zoom,dispatch:h}),n)}},g.wheelDelta=function(t){return arguments.length?(u="function"==typeof t?t:m5(+t),g):u},g.filter=function(t){return arguments.length?(r="function"==typeof t?t:m5(!!t),g):r},g.touchable=function(t){return arguments.length?(a="function"==typeof t?t:m5(!!t),g):a},g.extent=function(t){return arguments.length?(i="function"==typeof t?t:m5([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),g):i},g.scaleExtent=function(t){return arguments.length?(c[0]=+t[0],c[1]=+t[1],g):[c[0],c[1]]},g.translateExtent=function(t){return arguments.length?(f[0][0]=+t[0][0],f[1][0]=+t[1][0],f[0][1]=+t[0][1],f[1][1]=+t[1][1],g):[[f[0][0],f[0][1]],[f[1][0],f[1][1]]]},g.constrain=function(t){return arguments.length?(o=t,g):o},g.duration=function(t){return arguments.length?(l=+t,g):l},g.interpolate=function(t){return arguments.length?(s=t,g):s},g.on=function(){var t=h.on.apply(h,arguments);return t===h?g:t},g.clickDistance=function(t){return arguments.length?(d=(t=+t)*t,g):Math.sqrt(d)},g.tapDistance=function(t){return arguments.length?(p=+t,g):p},g}m9.prototype=m8.prototype}}]);