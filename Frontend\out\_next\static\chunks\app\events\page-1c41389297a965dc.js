(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{84772:function(e,t,o){Promise.resolve().then(o.t.bind(o,81749,23)),Promise.resolve().then(o.t.bind(o,25250,23)),Promise.resolve().then(o.bind(o,97753)),Promise.resolve().then(o.bind(o,57250)),Promise.resolve().then(o.t.bind(o,8041,23)),Promise.resolve().then(o.bind(o,98060)),Promise.resolve().then(o.t.bind(o,98633,23)),Promise.resolve().then(o.t.bind(o,26155,23)),Promise.resolve().then(o.t.bind(o,46282,23)),Promise.resolve().then(o.t.bind(o,25323,23))},64561:function(e,t,o){"use strict";o.d(t,{Z:function(){return M}});var n,r=o(16480),i=o.n(r),a=o(59390),l=o(97550),s=o(69275),d=o(44990);function c(e){if((!n&&0!==n||e)&&l.Z){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),n=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return n}var p=o(43756),u=o(45832),b=o(1564),x=o(40343),g=o(82562),y=o(2265),F=o(70133),h=o(46579),f=o(83534),m=o(12865),v=o(57437);let S=y.forwardRef((e,t)=>{let{className:o,bsPrefix:n,as:r="div",...a}=e;return n=(0,m.vE)(n,"modal-body"),(0,v.jsx)(r,{ref:t,className:i()(o,n),...a})});S.displayName="ModalBody";var _=o(14272);let E=y.forwardRef((e,t)=>{let{bsPrefix:o,className:n,contentClassName:r,centered:a,size:l,fullscreen:s,children:d,scrollable:c,...p}=e;o=(0,m.vE)(o,"modal");let u="".concat(o,"-dialog"),b="string"==typeof s?"".concat(o,"-fullscreen-").concat(s):"".concat(o,"-fullscreen");return(0,v.jsx)("div",{...p,ref:t,className:i()(u,n,l&&"".concat(o,"-").concat(l),a&&"".concat(u,"-centered"),c&&"".concat(u,"-scrollable"),s&&b),children:(0,v.jsx)("div",{className:i()("".concat(o,"-content"),r),children:d})})});E.displayName="ModalDialog";let L=y.forwardRef((e,t)=>{let{className:o,bsPrefix:n,as:r="div",...a}=e;return n=(0,m.vE)(n,"modal-footer"),(0,v.jsx)(r,{ref:t,className:i()(o,n),...a})});L.displayName="ModalFooter";var w=o(94241);let C=y.forwardRef((e,t)=>{let{bsPrefix:o,className:n,closeLabel:r="Close",closeButton:a=!1,...l}=e;return o=(0,m.vE)(o,"modal-header"),(0,v.jsx)(w.Z,{ref:t,...l,className:i()(n,o),closeLabel:r,closeButton:a})});C.displayName="ModalHeader";let k=(0,o(89764).Z)("h4"),N=y.forwardRef((e,t)=>{let{className:o,bsPrefix:n,as:r=k,...a}=e;return n=(0,m.vE)(n,"modal-title"),(0,v.jsx)(r,{ref:t,className:i()(o,n),...a})});function T(e){return(0,v.jsx)(f.Z,{...e,timeout:null})}function B(e){return(0,v.jsx)(f.Z,{...e,timeout:null})}N.displayName="ModalTitle";let z=y.forwardRef((e,t)=>{let{bsPrefix:o,className:n,style:r,dialogClassName:f,contentClassName:S,children:L,dialogAs:w=E,"data-bs-theme":C,"aria-labelledby":k,"aria-describedby":N,"aria-label":z,show:M=!1,animation:Z=!0,backdrop:X=!0,keyboard:R=!0,onEscapeKeyDown:W,onShow:j,onHide:D,container:A,autoFocus:P=!0,enforceFocus:H=!0,restoreFocus:O=!0,restoreFocusOptions:I,onEntered:K,onExit:U,onExiting:J,onEnter:Y,onEntering:q,onExited:G,backdropClassName:Q,manager:V,...$}=e,[ee,et]=(0,y.useState)({}),[eo,en]=(0,y.useState)(!1),er=(0,y.useRef)(!1),ei=(0,y.useRef)(!1),ea=(0,y.useRef)(null),[el,es]=(0,p.Z)(),ed=(0,b.Z)(t,es),ec=(0,u.Z)(D),ep=(0,m.SC)();o=(0,m.vE)(o,"modal");let eu=(0,y.useMemo)(()=>({onHide:ec}),[ec]);function eb(){return V||(0,h.t)({isRTL:ep})}function ex(e){if(!l.Z)return;let t=eb().getScrollbarWidth()>0,o=e.scrollHeight>(0,s.Z)(e).documentElement.clientHeight;et({paddingRight:t&&!o?c():void 0,paddingLeft:!t&&o?c():void 0})}let eg=(0,u.Z)(()=>{el&&ex(el.dialog)});(0,x.Z)(()=>{(0,d.Z)(window,"resize",eg),null==ea.current||ea.current()});let ey=()=>{er.current=!0},eF=e=>{er.current&&el&&e.target===el.dialog&&(ei.current=!0),er.current=!1},eh=()=>{en(!0),ea.current=(0,g.Z)(el.dialog,()=>{en(!1)})},ef=e=>{e.target===e.currentTarget&&eh()},em=e=>{if("static"===X){ef(e);return}if(ei.current||e.target!==e.currentTarget){ei.current=!1;return}null==D||D()},ev=(0,y.useCallback)(e=>(0,v.jsx)("div",{...e,className:i()("".concat(o,"-backdrop"),Q,!Z&&"show")}),[Z,Q,o]),eS={...r,...ee};return eS.display="block",(0,v.jsx)(_.Z.Provider,{value:eu,children:(0,v.jsx)(F.Z,{show:M,ref:ed,backdrop:X,container:A,keyboard:!0,autoFocus:P,enforceFocus:H,restoreFocus:O,restoreFocusOptions:I,onEscapeKeyDown:e=>{R?null==W||W(e):(e.preventDefault(),"static"===X&&eh())},onShow:j,onHide:D,onEnter:(e,t)=>{e&&ex(e),null==Y||Y(e,t)},onEntering:(e,t)=>{null==q||q(e,t),(0,a.ZP)(window,"resize",eg)},onEntered:K,onExit:e=>{null==ea.current||ea.current(),null==U||U(e)},onExiting:J,onExited:e=>{e&&(e.style.display=""),null==G||G(e),(0,d.Z)(window,"resize",eg)},manager:eb(),transition:Z?T:void 0,backdropTransition:Z?B:void 0,renderBackdrop:ev,renderDialog:e=>(0,v.jsx)("div",{role:"dialog",...e,style:eS,className:i()(n,o,eo&&"".concat(o,"-static"),!Z&&"show"),onClick:X?em:void 0,onMouseUp:eF,"data-bs-theme":C,"aria-label":z,"aria-labelledby":k,"aria-describedby":N,children:(0,v.jsx)(w,{...$,onMouseDown:ey,className:f,contentClassName:S,children:L})})})})});z.displayName="Modal";var M=Object.assign(z,{Body:S,Header:C,Title:N,Footer:L,Dialog:E,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},8041:function(e){e.exports={variables:'"@styles/variables.module.css"',gray400:"#E4E4E4",gray300:"#F3F3F3",colorBlack:"#000000",colorWhite:"#FFFFFF",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":"1440px","breakpoint-xl-1208":"1208px","breakpoint-xl-1024":"1024px","breakpoint-md":"768px","breakpoint-sm":"576px",card_title:"EventsListing_card_title__CBi5f",link:"EventsListing_link__0FLW9",card_preview:"EventsListing_card_preview__WrRj_",text_container:"EventsListing_text_container__8_hyy",industry_wrapper:"EventsListing_industry_wrapper__rB2YY",industry_title:"EventsListing_industry_title__J4C4L",previewImage:"EventsListing_previewImage__Jmy6e",eventsListing:"EventsListing_eventsListing__yLKit"}},98633:function(e){e.exports={brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",gray100:"#FCFCFC",gray200:"#F8F8F8",gray300:"#F3F3F3",gray400:"#E4E4E4",gray500:"#CDCDCD",gray600:"#B1B1B1",gray700:"#808080",gray800:"#646464",gray900:"#3A3A3A",error:"#FF6D60",success:"#23A881",grayBorder:"#8C8B8B",link:"#0075FF",grayBlueFonts:"#262531",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",halfSpace:"4px",oneSpace:"8px",twoSpace:"16px",threeSpace:"24px",fourSpace:"32px",fiveSpace:"40px",sixSpace:"48px",eightSpace:"64px",tenSpace:"80px",fifteenSpace:"120px",twentyFiveSpace:"200px",h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",bodyHeadingXL:"56px",bodyHeadingL:"24px",bodyHeadingM:"21px",bodyHeadingS:"20px",bodyHeadingXS:"18px",bodyHeadingXSS:"16px",buttonLabelXLargeFontSize:"26px",buttonLabelLargeFontSize:"20px",buttonLabelMediumFontSize:"16px",buttonLabelSmallFontSize:"14px",bodyTextXLarge:"26px",bodyTextLarge:"22px",bodyTextMedium:"20px",bodyTextSmall:"18px",bodyTextXSmall:"16px",bodyTextXXSmall:"14px",bodyTextXXXSSmall:"8px",bodyLinkXXLarge:"26px",bodyLinkXLarge:"22px",bodyLinkLarge:"19px",bodyLinkMedium:"18px",bodyLinkSmall:"17px",bodyLinkXSmall:"16px",bodyLinkXXSmall:"15px",fontWeight100:"100",fontWeight200:"200",fontWeight300:"300",fontWeight400:"400",fontWeight500:"500",fontWeight600:"600",fontWeight700:"700",fontWeight800:"800",fontWeight900:"900"}}},function(e){e.O(0,[5250,1607,843,8838,7250,7087,2971,8069,1744],function(){return e(e.s=84772)}),_N_E=e.O()}]);