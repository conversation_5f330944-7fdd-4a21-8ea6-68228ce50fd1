(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2357],{4353:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return u}});var o=t(57437);t(2265);var a=t(97753),r=t(8792),i=t(62806),l=t(21768),c=t(47907),s=t(61292),d=t.n(s);function u(e){let{data:n,variant:t}=e,s=(0,c.useRouter)();return(0,o.jsx)("section",{className:d().c<PERSON><PERSON><PERSON><PERSON>,children:"downloadOurBrand"!==t?(0,o.jsxs)(a.default,{className:d().ctaWrapper,children:[(0,o.jsx)(l.Z,{richTextValue:null==n?void 0:n.ctaTitle,headingType:"h2",className:d().ctaHeading}),(0,o.jsx)("div",{className:d().ctaBtn,children:(0,o.jsx)(i.Z,{label:null==n?void 0:n.ctaButtonText,className:d().btn,onClick:()=>{if("scrollToContactForm"===t){let e=document.getElementById("contact-us-form");e&&e.scrollIntoView({behavior:"smooth"})}else s.push("".concat(null==n?void 0:n.ctaLink))}})})]}):(0,o.jsxs)(a.default,{className:d().ctaWrapper,children:[(0,o.jsx)(l.Z,{richTextValue:null==n?void 0:n.ctaTitle,headingType:"h3",className:d().ctaHeading}),(0,o.jsx)(r.default,{href:null==n?void 0:n.ctaLink,target:"_blank",typeof:"application/pdf",prefetch:!1,download:!0,className:d().downloadLinkWrapper,children:(0,o.jsx)("div",{className:d().downloadLink,children:null==n?void 0:n.ctaButtonText})})]})})}},3396:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return j}});var o=t(57437),a=t(2265),r=t(97753),i=t(16480),l=t.n(i),c=t(84127),s=t(12865),d=t(14728);function u(e,n){return Array.isArray(e)?e.includes(n):e===n}let p=a.createContext({});p.displayName="AccordionContext";let f=a.forwardRef((e,n)=>{let{as:t="div",bsPrefix:r,className:i,children:c,eventKey:f,..._}=e,{activeEventKey:m}=(0,a.useContext)(p);return r=(0,s.vE)(r,"accordion-collapse"),(0,o.jsx)(d.Z,{ref:n,in:u(m,f),..._,className:l()(i,r),children:(0,o.jsx)(t,{children:a.Children.only(c)})})});f.displayName="AccordionCollapse";let _=a.createContext({eventKey:""});_.displayName="AccordionItemContext";let m=a.forwardRef((e,n)=>{let{as:t="div",bsPrefix:r,className:i,onEnter:c,onEntering:d,onEntered:u,onExit:p,onExiting:m,onExited:v,...x}=e;r=(0,s.vE)(r,"accordion-body");let{eventKey:y}=(0,a.useContext)(_);return(0,o.jsx)(f,{eventKey:y,onEnter:c,onEntering:d,onEntered:u,onExit:p,onExiting:m,onExited:v,children:(0,o.jsx)(t,{ref:n,...x,className:l()(i,r)})})});m.displayName="AccordionBody";let v=a.forwardRef((e,n)=>{let{as:t="button",bsPrefix:r,className:i,onClick:c,...d}=e;r=(0,s.vE)(r,"accordion-button");let{eventKey:f}=(0,a.useContext)(_),m=function(e,n){let{activeEventKey:t,onSelect:o,alwaysOpen:r}=(0,a.useContext)(p);return a=>{let i=e===t?null:e;r&&(i=Array.isArray(t)?t.includes(e)?t.filter(n=>n!==e):[...t,e]:[e]),null==o||o(i,a),null==n||n(a)}}(f,c),{activeEventKey:v}=(0,a.useContext)(p);return"button"===t&&(d.type="button"),(0,o.jsx)(t,{ref:n,onClick:m,...d,"aria-expanded":Array.isArray(v)?v.includes(f):f===v,className:l()(i,r,!u(v,f)&&"collapsed")})});v.displayName="AccordionButton";let x=a.forwardRef((e,n)=>{let{as:t="h2",bsPrefix:a,className:r,children:i,onClick:c,...d}=e;return a=(0,s.vE)(a,"accordion-header"),(0,o.jsx)(t,{ref:n,...d,className:l()(r,a),children:(0,o.jsx)(v,{onClick:c,children:i})})});x.displayName="AccordionHeader";let y=a.forwardRef((e,n)=>{let{as:t="div",bsPrefix:r,className:i,eventKey:c,...d}=e;r=(0,s.vE)(r,"accordion-item");let u=(0,a.useMemo)(()=>({eventKey:c}),[c]);return(0,o.jsx)(_.Provider,{value:u,children:(0,o.jsx)(t,{ref:n,...d,className:l()(i,r)})})});y.displayName="AccordionItem";let h=a.forwardRef((e,n)=>{let{as:t="div",activeKey:r,bsPrefix:i,className:d,onSelect:u,flush:f,alwaysOpen:_,...m}=(0,c.Ch)(e,{activeKey:"onSelect"}),v=(0,s.vE)(i,"accordion"),x=(0,a.useMemo)(()=>({activeEventKey:r,onSelect:u,alwaysOpen:_}),[r,u,_]);return(0,o.jsx)(p.Provider,{value:x,children:(0,o.jsx)(t,{ref:n,...m,className:l()(d,v,f&&"".concat(v,"-flush"))})})});h.displayName="Accordion";var b=Object.assign(h,{Button:v,Collapse:f,Item:y,Header:x,Body:m}),N=t(41396),C=t(21768),k=t(13570),F=t.n(k);function j(e){var n,t;let{title:a}=e.faqData;return(0,o.jsxs)(r.default,{fluid:!0,className:F().container,children:[(0,o.jsx)(C.Z,{headingType:"h2",title:a,className:F().title}),(0,o.jsx)("div",{className:F().faqsWrapper,children:(0,o.jsx)(b,{bsPrefix:F().accordion,children:null==e?void 0:null===(t=e.faqData)||void 0===t?void 0:null===(n=t.faq_items)||void 0===n?void 0:n.map(e=>(0,o.jsxs)(b.Item,{eventKey:"".concat(null==e?void 0:e.id),bsPrefix:F().accordion__item,children:[(0,o.jsx)(b.Header,{as:"h3",className:(0,N.Z)(F().accordion__header,"accordionIcon"),children:null==e?void 0:e.question}),(0,o.jsx)(b.Body,{bsPrefix:F().accordion__body,dangerouslySetInnerHTML:{__html:null==e?void 0:e.answer}})]},null==e?void 0:e.id))})})]})}},61292:function(e){e.exports={variables:'"@styles/variables.module.css"',colorBlack:"#000000",bodyHeadingS:"20px",brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorWhite:"#FFFFFF",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md-769":"769px","breakpoint-sm-550":"550px","breakpoint-sm-320":"320px","breakpoint-sm":"576px",ctaContainer:"Cta_ctaContainer___sfgZ",ctaWrapper:"Cta_ctaWrapper__TkOMF",ctaHeading:"Cta_ctaHeading___2l6Z",btn:"Cta_btn__Fsqyb",downloadLinkWrapper:"Cta_downloadLinkWrapper__dMcXu",downloadLink:"Cta_downloadLink__7uzCi"}},13570:function(e){e.exports={variables:'"@styles/variables.module.css"',colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":"768px","breakpoint-sm":"576px","breakpoint-xl-1400":"1400px",container:"Faq_container__UV9bI",title:"Faq_title__R4Xnr",faqsWrapper:"Faq_faqsWrapper__nvoeK",accordion__item:"Faq_accordion__item__EFT6Y",accordion__header:"Faq_accordion__header___9611",accordion__body:"Faq_accordion__body__tucUq"}},66679:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});let o={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function a(){let e,n,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=!1,l=!0,c=!1,s=0;function d(){if(t||!l)return;i||n.emit("autoplay:play");let{ownerWindow:o}=n.internalEngine();o.clearInterval(s),s=o.setInterval(m,e.delay),i=!0}function u(){if(t)return;i&&n.emit("autoplay:stop");let{ownerWindow:e}=n.internalEngine();e.clearInterval(s),s=0,i=!1}function p(){if(f())return l=i,u();l&&d()}function f(){let{ownerDocument:e}=n.internalEngine();return"hidden"===e.visibilityState}function _(e){void 0!==e&&(c=e),l=!0,d()}function m(){let{index:t}=n.internalEngine(),o=t.clone().add(1).get(),a=n.scrollSnapList().length-1;e.stopOnLastSnap&&o===a&&u(),n.canScrollNext()?n.scrollNext(c):n.scrollTo(0,c)}return{name:"autoplay",options:r,init:function(i,s){n=i;let{mergeOptions:_,optionsAtMedia:m}=s,v=_(o,a.globalOptions);if(e=m(_(v,r)),n.scrollSnapList().length<=1)return;c=e.jump,t=!1;let{eventStore:x,ownerDocument:y}=n.internalEngine(),h=n.rootNode(),b=e.rootNode&&e.rootNode(h)||h,N=n.containerNode();n.on("pointerDown",u),e.stopOnInteraction||n.on("pointerUp",d),e.stopOnMouseEnter&&(x.add(b,"mouseenter",()=>{l=!1,u()}),e.stopOnInteraction||x.add(b,"mouseleave",()=>{l=!0,d()})),e.stopOnFocusIn&&(x.add(N,"focusin",u),e.stopOnInteraction||x.add(N,"focusout",d)),x.add(y,"visibilitychange",p),e.playOnInit&&!f()&&d()},destroy:function(){n.off("pointerDown",u).off("pointerUp",d),u(),t=!0,i=!1},play:_,stop:function(){i&&u()},reset:function(){i&&_()},isPlaying:function(){return i}}}a.globalOptions=void 0}}]);