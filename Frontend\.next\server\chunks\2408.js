exports.id=2408,exports.ids=[2408],exports.modules={72829:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=async()=>{let e="user_ip_location",r=localStorage.getItem(e);if(r)try{return JSON.parse(r)}catch(e){console.warn("Failed to parse cached IP data:",e)}try{let r=await fetch("https://api.ipify.org?format=json");if(!r.ok)throw Error(`IP fetch error! Status: ${r.status}`);let t=(await r.json()).ip||"",a=await fetch(`https://ipapi.co/${t}/json/`);if(!a.ok)throw Error(`Location fetch error! Status: ${a.status}`);let o=await a.json(),i={ipAddress:t,location:{city:o.city||"",country:o.country_name||"",country_code:o.country_code||""}};return localStorage.setItem(e,JSON.stringify(i)),i}catch(e){return console.error("Error fetching IP or location:",e),{ipAddress:"",location:{city:"",country:"",country_code:""}}}}},13001:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(95344),o=t(3729),i=t.n(o),s=t(60646),n=t(55846),_=t(72767),l=t.n(_);t(22745);var u=t(87020),c=t.n(u),m=t(85551),d=t(55494);let p=i().forwardRef(({formData:e,source:r="HomePage"},t)=>{let{download_title:i,description:_,form_download_button:{title:u,link:p},form_values:{fieldNameFor_FirstName:h,fieldNameFor_EmailAddress:y,fieldNameFor_CompanyName:b,fieldNameFor_PhoneNumber:g}}=e,[f,v]=(0,o.useState)(!1),N=(0,d.Z)(),{values:w,errors:S,errorMessages:C,handleChange:x,handleSubmit:j}=(0,m.Z)({firstName:"",emailAddress:"",phoneNumber:"",companyName:"",consent:!1},{firstName:{empty:!1},emailAddress:{empty:!1,invalid:!1},phoneNumber:{empty:!1,invalid:!1},companyName:{empty:!1},consent:{empty:!1}},"caseStudy",r,p),k=async e=>{e.preventDefault(),v(!0);try{await j(e)}catch(e){console.error("Form submission failed:",e)}finally{v(!1)}};return(0,a.jsxs)(s.default,{ref:t,fluid:!0,className:c().CaseStudyFormContainer,children:[(0,a.jsxs)("div",{className:c().contentWrapper,children:[a.jsx("h2",{className:c().downloadTitle,children:i}),a.jsx("div",{className:c().description,children:_})]}),(0,a.jsxs)("form",{className:c().form,onSubmit:k,children:[(0,a.jsxs)("label",{className:S.firstName.empty?c().errorLabel:c().formFields,children:[h,"*"," "]}),a.jsx("input",{className:S.firstName.empty?`${c().errorInput} ${c().formInput} `:`${c().formInput}`,placeholder:"Full Name",type:"text",value:w.firstName,id:"firstName",name:"firstName",maxLength:50,onChange:e=>x(e?.target),onBlur:e=>x(e?.target)}),(0,a.jsxs)("label",{className:S.emailAddress.empty||S.emailAddress.invalid?c().errorLabel:c().formFields,children:[y,"*"]}),a.jsx("input",{className:S.emailAddress.empty?`${c().errorInput} ${c().formInput}`:`${c().formInput}`,type:"text",id:"emailAddress",name:"emailAddress",placeholder:"Your Email ID",value:w.emailAddress,maxLength:50,onChange:e=>x(e?.target),onBlur:e=>x(e?.target)}),(0,a.jsxs)("label",{className:S.phoneNumber.empty||S.phoneNumber.invalid?c().errorLabel:c().formFields,children:[g,"*"]}),a.jsx(l(),{inputClass:S.phoneNumber.empty||S.phoneNumber.invalid?`${c().errorInput} ${c().ph_number_countries_input_services_page}`:c().ph_number_countries_input_services_page,buttonClass:S.phoneNumber.empty||S.phoneNumber.invalid?`${c().errorInput} ${c().ph_number_countries_button_services_page}`:c().ph_number_countries_button_services_page,dropdownClass:c().ph_number_countries_dropdown_services_page,preferredCountries:["us","gb","sg","de","sa","in","nl","au","be","my"],country:N||"us",placeholder:"Your Phone Number",value:w.phoneNumber,onChange:e=>x({value:e,name:"phoneNumber"}),onBlur:e=>x(e?.target)}),a.jsx("label",{className:c().formFields,children:b}),a.jsx("input",{className:c().formInput,placeholder:"Your Company Name",type:"text",id:"companyName",name:"companyName",value:w.companyName,maxLength:50,onChange:e=>x(e?.target)}),f?a.jsx("div",{className:c().container_spinner,children:a.jsx("div",{className:c().spinner})}):a.jsx(n.Z,{className:c().submitButton,label:u,type:"submit"}),(0,a.jsxs)("div",{className:c().errorMessages,children:[a.jsx("div",{children:C.empty&&C.empty}),a.jsx("div",{children:C.invalid&&C.invalid})]})]})]})})},85551:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var a=t(72829);let o=async()=>{try{let e=document.referrer||"",r=new URLSearchParams(window.location.search),t=r.get("utm_medium")||"",a=r.get("utm_source")||"",o=r.get("utm_campaign")||"",i="";if(window.clarity)try{i=window.clarity("get","userId")}catch(e){console.error("Error fetching Clarity ID:",e)}let s="";try{s=globalThis.gaGlobal?.vid.match(/\d+\.\d+$/)?.[0]||""}catch(e){console.error("Error fetching GA4 Client ID:",e)}return{clarity:i,utm_medium:t,utm_source:a,utm_campaign:o,referrer:e,ga_client_id:s}}catch(e){return console.error("Error fetching user tracking data:",e),{clarity:"",utm_medium:"",utm_source:"",utm_campaign:"",referrer:"",ga_client_id:""}}};var i=t(3729);function s(e,r,t="default",s,n=""){let[_,l]=(0,i.useState)(e),[u,c]=(0,i.useState)(r),[m,d]=(0,i.useState)({empty:"",invalid:""}),p="caseStudy"===t?["firstName","emailAddress","phoneNumber"]:["firstName","lastName","emailAddress","phoneNumber","consent"],h=e=>{let r={...m};p.some(r=>e[r]?.empty)?r.empty="Please fill the highlighted fields":r.empty="",e.emailAddress?.invalid&&e.phoneNumber?.invalid?r.invalid="Please enter valid Email ID and Phone Number":e.emailAddress?.invalid?r.invalid="Please enter a valid Email ID":e.phoneNumber?.invalid?r.invalid="Please enter a valid Phone Number":r.invalid="",d(r)},y=(e,t)=>{let a={...u};t?("emailAddress"!==e||/\S+@\S+\.\S+/.test(t))&&("phoneNumber"!==e||/.{6,}/.test(t))?a[e]=r[e]:a[e]={empty:!1,invalid:!0}:a[e]={empty:!0,invalid:!1},c(a),h(a)},b=()=>{let e={...u};return p.forEach(r=>{_[r]||(e[r]={empty:!0,invalid:!1})}),c(e),h(e),!Object.values(e).some(e=>e.empty||e.invalid)},g=async t=>{if(t.preventDefault(),b()){try{let e=await (0,a.Z)(),r=await o(),t={firstName:_.firstName||"",lastName:_.lastName||"",emailAddress:_.emailAddress||"",phoneNumber:_.phoneNumber||"",howDidYouHearAboutUs:_.howDidYouHearAboutUs||"",companyName:_.companyName||"",howCanWeHelpYou:_.howCanWeHelpYou||"",utm_campaign:r.utm_campaign||"",utm_medium:r.utm_medium||"",utm_source:r.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:r.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:s||"",clarity:r.clarity||"",url:window.location.href||"",referrer:r.referrer||"",consent:_.consent||!1},i=await fetch("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev/contact-us",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(t)});i.ok?(("CaseStudy"===s||"eBooks"===s||"whitePapers"===s)&&n&&window.open(n,"_blank"),window.location.href="/thank-you/"):console.error("Error submitting form:",await i.json())}catch(e){console.error("Error in form submission:",e)}l(e),c(r)}},f=async(t,i,n)=>{if(b()){try{let e=await (0,a.Z)(),r=await o(),l={firstName:_.firstName||"",lastName:_.lastName||"",emailAddress:_.emailAddress||"",phoneNumber:_.phoneNumber||"",companyName:_.companyName||"",utm_campaign:r.utm_campaign||"",utm_medium:r.utm_medium||"",utm_source:r.utm_source||"",ip_address:e.ipAddress||"",ga_4_userid:r.ga_client_id||"",city:e.location.city||"",country:e.location.country||"",secondary_source:s||"",clarity:r.clarity||"",url:window.location.href||"",referrer:r.referrer||"",consent:_.consent||!1,do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_:t[0][0][0]||"",how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_:t[0][1][0]||"",do_you_have_budget_allocated_for_your_ai_project_:t[0][2][0]||"",do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_:t[1][0][0]||"",which_of_the_below_db_tools_do_you_currently_use_:t[1][1][0]||"",is_the_relevant_data_for_the_ai_project_available_and_accessible_:t[1][2][0]||"",do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__:t[1][3][0]||"",how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib:t[1][4][0]||"",does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_:t[2][0][0]||"",do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_:t[3][0][0]||"",do_you_have_risk_management_strategies_in_place_for_the_ai_project_:t[3][1][0]||"",do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions:t[4][0][0]||"",strategy___leadership:i[0]||"",data_readiness___infrastructure:i[1]||"",talent___skills:i[2]||"",execution___monitoring:i[3]||"",impact_evaliation:i[4]||"",average_of_all_score:i.final||""},u=await fetch("https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev/ai-readiness",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4"},body:JSON.stringify(l)});u.ok?n(t.length):console.error("Error submitting form:",await u.json())}catch(e){console.error("Error in form submission:",e)}l(e),c(r)}};return{values:_,errors:u,errorMessages:m,handleChange:({name:e,value:r,type:t="",checked:a=!1})=>{r="checkbox"===t?a:r;let o={..._};"firstName"===e||"lastName"===e?o[e]=r.replace(/[^a-zA-Z0-9 ]/g,"").trimStart():"emailAddress"===e?o[e]=r.replace(" ",""):o[e]=r,l(o),e in u&&y(e,r)},handleBlur:({name:e,value:r})=>{let t={..._};"string"==typeof r&&(t[e]=r.trim()),l(t),e in u&&y(e,r)},handleSubmit:g,handleSubmitAIReadiness:f}}},55494:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var a=t(3729),o=t(72829);function i(){let[e,r]=(0,a.useState)("");return(0,a.useEffect)(()=>{(async()=>{try{let e=await (0,o.Z)();r(e?.location?.country_code?.toLowerCase()||"")}catch(e){r("us")}})()},[]),e}},87020:(e,r,t)=>{var a=t(24640),o=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+a.colorBlack,colorWhite:""+a.colorWhite,brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,gray300:""+a.gray300,gray200:""+a.gray200,gray800:""+a.gray800,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+o["breakpoint-md"],"breakpoint-md-767":""+o["breakpoint-md-767"],"breakpoint-sm":""+o["breakpoint-sm"],"breakpoint-xl-1024":""+o["breakpoint-xl-1024"],"breakpoint-xl":""+o["breakpoint-xl"],"breakpoint-xl-1400":""+o["breakpoint-xl-1400"],"breakpoint-xl-1440":""+o["breakpoint-xl-1440"],CaseStudyFormContainer:"CaseStudyForm_CaseStudyFormContainer__ROKmQ",contentWrapper:"CaseStudyForm_contentWrapper__PZtZ_",downloadTitle:"CaseStudyForm_downloadTitle__G_Jri",description:"CaseStudyForm_description__KSRN3",form:"CaseStudyForm_form__P82ay",formFields:"CaseStudyForm_formFields___DgHl",formInput:"CaseStudyForm_formInput__iFWbg",submitButton:"CaseStudyForm_submitButton__ZKPgA",container_spinner:"CaseStudyForm_container_spinner__1yS_d",spinner:"CaseStudyForm_spinner__ysHBe",spin:"CaseStudyForm_spin__t9jrf",error:"CaseStudyForm_error__sZS_Z",ph_number_countries_input_services_page:"CaseStudyForm_ph_number_countries_input_services_page__Pbh1p",ph_number_countries_button_services_page:"CaseStudyForm_ph_number_countries_button_services_page__C3Mx6",ph_number_countries_dropdown_services_page:"CaseStudyForm_ph_number_countries_dropdown_services_page__kANmQ",errorInput:"CaseStudyForm_errorInput__7ePBP",errorMessages:"CaseStudyForm_errorMessages__2uvfX",errorLabel:"CaseStudyForm_errorLabel__oJOIZ"}},35992:(e,r,t)=>{"use strict";function a(e){return{title:e?.title||"AI & ML Solutions | Software Development Partner | Maruti Techlabs",description:e?.description||"Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.",alternates:{canonical:e?.url||"https://marutitech.com/"},keywords:e?.keywords?.keyword||"AI, ML, software development, custom software solutions, automation, business growth",openGraph:{title:e?.title,description:e?.description,type:e?.type||"website",url:e?.url,locale:e?.locale||"en_US",siteName:e?.site_name||"Maruti Techlabs",images:e?.image?.data?.attributes?.url?[{url:e.image.data.attributes.url,alt:e.title||"Maruti Techlabs Logo"}]:[]},twitter:{card:"summary_large_image",title:e?.title,description:e?.description,images:e?.image?.data?.attributes?.url?[e.image.data.attributes.url]:[],creator:"@MarutiTech"},other:{"application/ld+json":JSON.stringify(function(e){let r=e?.url||"https://marutitech.com/",t=e?.title||"Maruti Techlabs",a=e?.description||"Drive business growth with Maruti Techlabs’ secure, scalable, and custom software solutions to automate your processes.";return{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":`${r}#organization`,name:"Maruti Techlabs",url:"https://marutitech.com/",sameAs:[]},{"@type":"WebSite","@id":`${r}#website`,url:"https://marutitech.com/",name:"Maruti Techlabs",publisher:{"@id":`${r}#organization`},potentialAction:{"@type":"SearchAction",target:`${r}?s={search_term_string}`,"query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":`${r}#webpage`,url:r,inLanguage:"en-US",name:t,isPartOf:{"@id":`${r}#website`},about:{"@id":`${r}#organization`},image:{"@type":"ImageObject","@id":`${r}#primaryimage`,url:e?.image?.data?.attributes?.url||"",width:631,height:417,caption:"home-hero-image"},primaryImageOfPage:{"@id":`${r}#primaryimage`},datePublished:"2019-03-19T05:53:21+00:00",dateModified:"2020-11-02T08:06:30+00:00",description:a}]}}(e))}}}t.d(r,{Z:()=>a})}};