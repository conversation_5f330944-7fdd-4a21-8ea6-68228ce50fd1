exports.id=49,exports.ids=[49],exports.modules={42725:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>u});var l=t(95344),i=t(3729),r=t(60646),s=t(2522),n=t(79829),o=t(97906),_=t(44469),c=t.n(_),d=t(95079),b=t.n(d),m=t(81473),h=t(25609);let x=(e,a)=>{let t=[];for(let l=0;l<e.length;l+=a)t.push(e.slice(l,l+a));return t};function u({awardsRecognitionData:e}){let[a,t]=(0,s.Z)({align:"start",dragFree:!0}),{selectedIndex:_,scrollSnaps:d,onDotButtonClick:u}=(0,o.Z)(t),[p,g]=(0,i.useState)(1);(0,i.useEffect)(()=>{let e=()=>{window.innerWidth>1024?g(4):g(1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let v=x(e?.awards_box||[],p);return l.jsx(r.default,{fluid:!0,className:b().main_container,children:(0,l.jsxs)("div",{className:b().inner_container,children:[l.jsx(m.Z,{headingType:"h2",title:e?.title,className:b().main_title}),(0,l.jsxs)("section",{className:b().embla,children:[l.jsx("div",{className:b().embla__viewport,ref:a,children:l.jsx("div",{className:b().embla__container,children:v.map((e,a)=>l.jsx("div",{className:b().embla__slide,children:l.jsx("div",{className:b().card_box,children:e.map(e=>(0,l.jsxs)("div",{className:b().card,children:[l.jsx("div",{className:b().image_box,children:l.jsx(h.Z,{src:e?.image?.data?.attributes,alt:e?.image?.data?.attributes?.alternativeText,width:140,height:140})}),l.jsx("div",{className:b().line}),l.jsx(m.Z,{headingType:"h3",title:e?.title,className:b().awards_title})]},e?.id))})},a))})}),l.jsx("div",{className:`${c().embla__controls} ${b().embla__controls}`,children:l.jsx("div",{className:c().embla__dots,children:d.length>1&&d.map((e,a)=>l.jsx(n.Z,{onClick:()=>u(a),className:a===_?`${c().embla__dot} ${c().embla__dot_selected}`:c().embla__dot},a))})})]})]})})}},34317:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g});var l=t(95344);t(3729);var i=t(60646),r=t(17631),s=t.n(r),n=t(81473),o=t(97906),_=t(18924),c=t(22281),d=t(72885),b=t.n(d),m=t(2522),h=t(79829),x=t(44469),u=t.n(x),p=t(25609);function g({data:e,variantWhite:a=!0}){let[t,r]=(0,m.Z)({align:"center",dragFree:!0}),{selectedIndex:d,scrollSnaps:x,onDotButtonClick:g}=(0,o.Z)(r);return(0,_.Z)({query:`(max-width: ${b()["breakpoint-xl-1024"]})`})?(0,l.jsxs)(i.default,{fluid:!0,className:s().container,children:[l.jsx(n.Z,{title:e?.title,position:"center",headingType:"h4",className:s().title}),(0,l.jsxs)("div",{className:s().embla,children:[l.jsx("div",{className:s().embla__viewport,ref:t,children:l.jsx("div",{className:s().embla__container,children:e?.review_image?.data?.map((e,a)=>l.jsx("div",{className:s().embla__slide,children:l.jsx("div",{className:s().imageWrapper,children:l.jsx(p.Z,{src:e?.attributes,alt:e?.attributes?.alternativeText,width:350,height:439},e?.id)},e?.id)},e?.id))})}),l.jsx("div",{className:s().embla__controls,children:l.jsx("div",{className:u().embla__dots,children:x.length>1&&x.map((e,t)=>l.jsx(h.Z,{onClick:()=>g(t),className:t===d?`${u().embla__dot} ${u().embla__dot_selected}`:a?(0,c.Z)(u().embla__dot,u().embla__dot_bg_white):u().embla__dot},t))})})]})]}):(0,l.jsxs)(i.default,{fluid:!0,className:s().container,children:[l.jsx(n.Z,{title:e?.title,position:"center",headingType:"h2",className:s().title}),l.jsx("div",{className:s().imageContainer,children:e?.review_image?.data?.map((e,a)=>l.jsx("div",{className:s().imageWrapper,children:l.jsx(p.Z,{src:e?.attributes,alt:e?.attributes?.alternativeText,width:350,height:439},a)},a))})]})}},16479:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var l=t(95344);t(3729);var i=t(60646),r=t(81473),s=t(2522),n=t(97906),o=t(79829),_=t(66031),c=t.n(_),d=t(44469),b=t.n(d),m=t(22281);function h({datadeliverables:e,variantWhite:a=!0}){let[t,_]=(0,s.Z)({align:"start",dragFree:!0}),{selectedIndex:d,scrollSnaps:h,onDotButtonClick:x}=(0,n.Z)(_);return l.jsx(l.Fragment,{children:(0,l.jsxs)(i.default,{fluid:!0,className:c().container,children:[(0,l.jsxs)("div",{className:c().content,children:[l.jsx(r.Z,{className:c().title,title:e.title,headingType:"h2"}),l.jsx("div",{className:c().description,dangerouslySetInnerHTML:{__html:e.description}})]}),(0,l.jsxs)("div",{className:c().embla,children:[l.jsx("div",{className:c().embla__viewport,ref:t,children:l.jsx("div",{className:c().embla__container,children:e?.box?.map((e,a)=>l.jsx("div",{className:c().embla__slide,children:l.jsxs("div",{className:c().embla__slide__number,children:[l.jsx(r.Z,{className:c().box_title,title:e.title,headingType:"h3"}),l.jsx("div",{className:c().box_description,dangerouslySetInnerHTML:{__html:e.description}})]},a)},a))})}),l.jsx("div",{className:c().embla__controls,children:l.jsx("div",{className:b().embla__dots,children:h.length>1&&h.map((e,t)=>l.jsx(o.Z,{onClick:()=>x(t),className:t===d?`${b().embla__dot} ${b().embla__dot_selected}`:a?(0,m.Z)(b().embla__dot,b().embla__dot_bg_white):b().embla__dot},t))})})]})]})})}},93529:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>T});var l=t(95344),i=t(3729),r=t(60646),s=t(34132),n=t.n(s),o=t(2743),_=t(70136),c=t(3605);function d(e,a){return Array.isArray(e)?e.includes(a):e===a}let b=i.createContext({});b.displayName="AccordionContext";let m=i.forwardRef(({as:e="div",bsPrefix:a,className:t,children:r,eventKey:s,...o},m)=>{let{activeEventKey:h}=(0,i.useContext)(b);return a=(0,_.vE)(a,"accordion-collapse"),(0,l.jsx)(c.Z,{ref:m,in:d(h,s),...o,className:n()(t,a),children:(0,l.jsx)(e,{children:i.Children.only(r)})})});m.displayName="AccordionCollapse";let h=i.createContext({eventKey:""});h.displayName="AccordionItemContext";let x=i.forwardRef(({as:e="div",bsPrefix:a,className:t,onEnter:r,onEntering:s,onEntered:o,onExit:c,onExiting:d,onExited:b,...x},u)=>{a=(0,_.vE)(a,"accordion-body");let{eventKey:p}=(0,i.useContext)(h);return(0,l.jsx)(m,{eventKey:p,onEnter:r,onEntering:s,onEntered:o,onExit:c,onExiting:d,onExited:b,children:(0,l.jsx)(e,{ref:u,...x,className:n()(t,a)})})});x.displayName="AccordionBody";let u=i.forwardRef(({as:e="button",bsPrefix:a,className:t,onClick:r,...s},o)=>{a=(0,_.vE)(a,"accordion-button");let{eventKey:c}=(0,i.useContext)(h),m=function(e,a){let{activeEventKey:t,onSelect:l,alwaysOpen:r}=(0,i.useContext)(b);return i=>{let s=e===t?null:e;r&&(s=Array.isArray(t)?t.includes(e)?t.filter(a=>a!==e):[...t,e]:[e]),null==l||l(s,i),null==a||a(i)}}(c,r),{activeEventKey:x}=(0,i.useContext)(b);return"button"===e&&(s.type="button"),(0,l.jsx)(e,{ref:o,onClick:m,...s,"aria-expanded":Array.isArray(x)?x.includes(c):c===x,className:n()(t,a,!d(x,c)&&"collapsed")})});u.displayName="AccordionButton";let p=i.forwardRef(({as:e="h2",bsPrefix:a,className:t,children:i,onClick:r,...s},o)=>(a=(0,_.vE)(a,"accordion-header"),(0,l.jsx)(e,{ref:o,...s,className:n()(t,a),children:(0,l.jsx)(u,{onClick:r,children:i})})));p.displayName="AccordionHeader";let g=i.forwardRef(({as:e="div",bsPrefix:a,className:t,eventKey:r,...s},o)=>{a=(0,_.vE)(a,"accordion-item");let c=(0,i.useMemo)(()=>({eventKey:r}),[r]);return(0,l.jsx)(h.Provider,{value:c,children:(0,l.jsx)(e,{ref:o,...s,className:n()(t,a)})})});g.displayName="AccordionItem";let v=i.forwardRef((e,a)=>{let{as:t="div",activeKey:r,bsPrefix:s,className:c,onSelect:d,flush:m,alwaysOpen:h,...x}=(0,o.Ch)(e,{activeKey:"onSelect"}),u=(0,_.vE)(s,"accordion"),p=(0,i.useMemo)(()=>({activeEventKey:r,onSelect:d,alwaysOpen:h}),[r,d,h]);return(0,l.jsx)(b.Provider,{value:p,children:(0,l.jsx)(t,{ref:a,...x,className:n()(c,u,m&&`${u}-flush`)})})});v.displayName="Accordion";let j=Object.assign(v,{Button:u,Collapse:m,Item:g,Header:p,Body:x});var C=t(22281),N=t(81473),f=t(85977),k=t.n(f);function T(e){let{title:a}=e.faqData;return(0,l.jsxs)(r.default,{fluid:!0,className:k().container,children:[l.jsx(N.Z,{headingType:"h2",title:a,className:k().title}),l.jsx("div",{className:k().faqsWrapper,children:l.jsx(j,{bsPrefix:k().accordion,children:e?.faqData?.faq_items?.map(e=>l.jsxs(j.Item,{eventKey:`${e?.id}`,bsPrefix:k().accordion__item,children:[l.jsx(j.Header,{as:"h3",className:C.Z(k().accordion__header,"accordionIcon"),children:e?.question}),l.jsx(j.Body,{bsPrefix:k().accordion__body,dangerouslySetInnerHTML:{__html:e?.answer}})]},e?.id))})})]})}},6297:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l.default});var l=t(93261)},42485:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>A});var l=t(95344),i=t(2522),r=t(3729),s=t(60646),n=t(97906),o=t(27450),_=t.n(o),c=t(81473),d=t(89410),b=t(61985),m=t(87335),h=t(30669),x=t(89436),u=t(43590),p=t(94531),g=t(42725),v=t(34317),j=t(52626),C=t(93529),N=t(34643),f=t(79829),k=t(44469),T=t.n(k),y=t(6297),w=t(18924),Z=t(72885),F=t.n(Z),R=t(16479);function A({variant:e,tabChallengesData:a=null,awardsData:t=null,formData:o=null,trustedPartnersData:k=null}){let[Z,A]=(0,r.useState)(0),[W,D]=(0,i.Z)({align:"start",dragFree:!0}),{selectedIndex:S,scrollSnaps:B,onDotButtonClick:$}=(0,n.Z)(D),q=(0,w.Z)({query:`(max-width: ${F()["breakpoint-xl-1365"]})`}),E=(0,r.useRef)(null);return(0,l.jsxs)(l.Fragment,{children:["industry"===e&&(0,l.jsxs)(s.default,{fluid:!0,className:_().main_container,children:[l.jsx(c.Z,{headingType:"h2",title:a?.title,position:"left",className:_().main_title}),(0,l.jsxs)("div",{className:_().inner_container,children:[l.jsx("div",{className:_().tab_boxes,children:a?.tab_box.map((e,a)=>l.jsxs("div",{className:a===Z?_().single_image_selected:_().single_image,onClick:()=>{A(a),window.innerWidth<=450&&E.current&&E.current.scrollIntoView({behavior:"smooth"})},children:[l.jsx(d.default,{src:e?.card_image?.data?.attributes?.url,width:182,height:122,alt:"background image",className:_().imageWrapper}),a!==Z&&l.jsx("div",{className:_().overlay}),l.jsx("div",{className:_().titleWrapper,children:l.jsx("div",{className:a===Z?_().imageTitle_selected:_().imageTitle,children:e?.card_title})})]},e?.id))}),(0,l.jsxs)("div",{className:_().right_container,ref:E,children:[(0,l.jsxs)("div",{className:_().right_box,children:[l.jsx(c.Z,{headingType:"h3",title:a?.tab_box[Z]?.challenges_title,className:_().right_title}),l.jsx("div",{className:_().right_richText,dangerouslySetInnerHTML:{__html:a?.tab_box[Z]?.challenges_description}}),l.jsx(d.default,{src:"https://dev-cdn.marutitech.com/arrow_down_4a2eb1486f.svg",width:32,height:50,alt:"arrow down"})]}),(0,l.jsxs)("div",{className:_().right_box,children:[l.jsx(c.Z,{headingType:"h3",title:a?.tab_box[Z]?.solution_title,className:_().right_title}),l.jsx("div",{className:_().right_richText,dangerouslySetInnerHTML:{__html:a?.tab_box[Z]?.solution_description}})]})]})]})]}),"retail"===e&&(0,l.jsxs)(l.Fragment,{children:[a?.hero_section&&l.jsx(y.Z,{heroData:a?.hero_section,variant:"primary",industrySlug:a?.slug,industryName:a?.pageName}),(0,l.jsxs)("div",{className:_().tab_retail_section,children:[a?.tab_section?.title&&l.jsx(c.Z,{headingType:"h2",title:a?.tab_section?.title,className:_().retail_title}),a?.tab_section?.description&&l.jsx(c.Z,{headingType:"h3",className:_().retail_description,richTextValue:a?.tab_section?.description}),q?(0,l.jsxs)("section",{className:_().embla,children:[l.jsx("div",{className:_().embla__viewport,ref:W,children:l.jsx("div",{className:_().embla__container,children:a?.retail_components?.data.map((e,a)=>l.jsx("div",{className:_().embla__slide,children:l.jsx("div",{className:a===Z?`${_().tab_retail_box} ${_().tab_retail_box_selected}`:`${_().tab_retail_box}`,onClick:()=>A(a),children:l.jsx("div",{children:e?.attributes?.tab_title})})},e?.id))})}),l.jsx("div",{className:`${T().embla__controls} ${_().embla__controls}`,children:l.jsx("div",{className:T().embla__dots,children:B.length>1&&B.map((e,a)=>l.jsx(f.Z,{onClick:()=>$(a),className:a===S?`${T().embla__dot} ${T().embla__dot_selected}`:`${T().embla__dot} ${T().embla__dot_bg_white}`},a))})})]}):l.jsx("section",{className:_().retail_container,children:a?.retail_components?.data.map((e,a)=>l.jsx("div",{className:a===Z?`${_().tab_retail_box} ${_().tab_retail_box_selected}`:`${_().tab_retail_box}`,onClick:()=>A(a),children:l.jsx("div",{children:e?.attributes?.tab_title})},e?.id))})]}),a.retail_components?.data[Z]?.attributes?.challenges_and_solutions&&l.jsx(R.default,{datadeliverables:a.retail_components?.data[Z]?.attributes?.challenges_and_solutions}),a.retail_components?.data[Z]?.attributes?.what_service_we_are_offering&&l.jsx(b.default,{variant:"blackSlideCard",l2ServiceData:a.retail_components?.data[Z]?.attributes?.what_service_we_are_offering,background:"black",variantWhite:!1}),a.retail_components?.data[Z]?.attributes?.cta&&l.jsx(m.default,{data:a.retail_components?.data[Z]?.attributes?.cta,variant:"scrollToContactForm"}),k&&l.jsx(h.default,{data:k}),a.retail_components?.data[Z]?.attributes?.case_study_cards&&l.jsx(x.default,{case_study:a.retail_components?.data[Z]?.attributes?.case_study_cards,variantWhite:!0}),a.retail_components?.data[Z]?.attributes?.why_choose_maruti_techlabs&&l.jsx(u.default,{data:a.retail_components?.data[Z]?.attributes?.why_choose_maruti_techlabs}),a.retail_components?.data[Z]?.attributes?.cta_other&&l.jsx(m.default,{data:a.retail_components?.data[Z]?.attributes?.cta_other,variant:"scrollToContactForm"}),a.retail_components?.data[Z]?.attributes?.tech_stack&&l.jsx(p.default,{data:a.retail_components?.data[Z]?.attributes?.tech_stack}),t&&l.jsx(g.default,{awardsRecognitionData:t}),a.retail_components?.data[Z]?.attributes?.clutch_reviews&&l.jsx(v.default,{data:a.retail_components?.data[Z]?.attributes?.clutch_reviews,variantWhite:!0}),a.retail_components?.data[Z]?.attributes?.insights&&l.jsx(j.default,{data:a.retail_components?.data[Z]?.attributes?.insights}),a.retail_components?.data[Z]?.attributes?.faq&&l.jsx(C.default,{faqData:a.retail_components?.data[Z]?.attributes?.faq}),o&&l.jsx(N.default,{formData:o,source:"Retail"})]}),"cloud"===e&&(0,l.jsxs)(s.default,{fluid:!0,className:_().container_cloud,children:[(0,l.jsxs)("div",{className:_().content,children:[l.jsx(c.Z,{className:_().title_cloud,title:a.title,headingType:"h2"}),l.jsx("div",{className:_().description_cloud,dangerouslySetInnerHTML:{__html:a.description}})]}),(0,l.jsxs)("div",{className:_().inner_container_cloud,children:[l.jsx("div",{className:_().tab_boxes_cloud,children:a?.box.map((e,a)=>l.jsx("div",{className:a===Z?_().single_box_selected_cloud:_().single_box_cloud,onClick:()=>A(a),children:e?.title},e?.id))}),l.jsx("div",{className:_().right_container_cloud,children:l.jsx("div",{className:_().right_box_cloud,children:l.jsx("div",{className:_().right_richText_cloud,dangerouslySetInnerHTML:{__html:a?.box[Z]?.description}})})})]})]})]})}},95079:(e,a,t)=>{var l=t(24640),i=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+l.colorBlack,colorWhite:""+l.colorWhite,fifteenSpace:""+l.fifteenSpace,grayBorder:""+l.grayBorder,brandColorOne:""+l.brandColorOne,brandColorTwo:""+l.brandColorTwo,brandColorThree:""+l.brandColorThree,brandColorFour:""+l.brandColorFour,brandColorFive:""+l.brandColorFive,bodyTextXXXSSmall:""+l.bodyTextXXXSSmall,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+i["breakpoint-md"],"breakpoint-xl-2000":""+i["breakpoint-xl-2000"],"breakpoint-xl-1800":""+i["breakpoint-xl-1800"],"breakpoint-sm-450":""+i["breakpoint-sm-450"],"breakpoint-sm-550":""+i["breakpoint-sm-550"],"breakpoint-sm-320":""+i["breakpoint-sm-320"],"breakpoint-xl-1024":""+i["breakpoint-xl-1024"],"breakpoint-xl-1440":""+i["breakpoint-xl-1440"],"breakpoint-lg":""+i["breakpoint-lg"],"breakpoint-lg-991px":""+i["breakpoint-lg-991px"],main_container:"AwardsRecognition_main_container__bPZYw",inner_container:"AwardsRecognition_inner_container__yC_gY",main_title:"AwardsRecognition_main_title__Y92M5",card:"AwardsRecognition_card__veHDB",card_box:"AwardsRecognition_card_box__bFD7V",image_box:"AwardsRecognition_image_box__qB2zX",line:"AwardsRecognition_line__oE817",awards_title:"AwardsRecognition_awards_title__cIJ1S",embla:"AwardsRecognition_embla__0_ex8",embla__viewport:"AwardsRecognition_embla__viewport__pznrd",embla__container:"AwardsRecognition_embla__container__FX_lc",embla__slide:"AwardsRecognition_embla__slide__hXSKA"}},17631:(e,a,t)=>{var l=t(24640),i=t(70048);e.exports={variables:'"@styles/variables.module.css"',fontWeight600:""+l.fontWeight600,colorBlack:""+l.colorBlack,colorWhite:""+l.colorWhite,brandColorOne:""+l.brandColorOne,brandColorTwo:""+l.brandColorTwo,brandColorThree:""+l.brandColorThree,brandColorFour:""+l.brandColorFour,brandColorFive:""+l.brandColorFive,fifteenSpace:""+l.fifteenSpace,grayBorder:""+l.grayBorder,gray300:""+l.gray300,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":""+i["breakpoint-xl-1440"],"breakpoint-xl-1024":""+i["breakpoint-xl-1024"],"breakpoint-xl":""+i["breakpoint-xl"],"breakpoint-lg":""+i["breakpoint-lg"],"breakpoint-md":""+i["breakpoint-md"],"breakpoint-sm":""+i["breakpoint-sm"],"breakpoint-sm-427":""+i["breakpoint-sm-427"],container:"ClutchReviews_container__hPusp",title:"ClutchReviews_title__upiGH",embla__viewport:"ClutchReviews_embla__viewport__bQFAo",embla__container:"ClutchReviews_embla__container__oNpYv",embla__slide:"ClutchReviews_embla__slide___LSUE",embla__controls:"ClutchReviews_embla__controls__BARkD",imageContainer:"ClutchReviews_imageContainer__iBTm_",imageWrapper:"ClutchReviews_imageWrapper__4RGT2"}},66031:(e,a,t)=>{var l=t(24640),i=t(70048);e.exports={variables:'"@styles/variables.module.css"',gray300:""+l.gray300,colorBlack:""+l.colorBlack,colorWhite:""+l.colorWhite,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1440":""+i["breakpoint-xl-1440"],"breakpoint-lg":""+i["breakpoint-lg"],"breakpoint-md":""+i["breakpoint-md"],"breakpoint-sm":""+i["breakpoint-sm"],"breakpoint-sm-427":""+i["breakpoint-sm-427"],container:"Deliverables_container__FBXgP",content:"Deliverables_content__CSU0C",title:"Deliverables_title___JPMn",description:"Deliverables_description__JuxDZ",embla:"Deliverables_embla__p3Rlv",embla__viewport:"Deliverables_embla__viewport__OXMnL",embla__container:"Deliverables_embla__container__qlzfy",embla__slide:"Deliverables_embla__slide__IUgVj",embla__slide__number:"Deliverables_embla__slide__number__FiJJc",box_title:"Deliverables_box_title__e1TZe",box_description:"Deliverables_box_description__D8SCI",embla__controls:"Deliverables_embla__controls__UBL9D"}},85977:(e,a,t)=>{var l=t(24640),i=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+l.colorBlack,colorWhite:""+l.colorWhite,gray:""+l.gray,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+i["breakpoint-md"],"breakpoint-sm":""+i["breakpoint-sm"],"breakpoint-xl-1400":""+i["breakpoint-xl-1400"],container:"Faq_container__UV9bI",title:"Faq_title__R4Xnr",faqsWrapper:"Faq_faqsWrapper__nvoeK",accordion__item:"Faq_accordion__item__EFT6Y",accordion__header:"Faq_accordion__header___9611",accordion__body:"Faq_accordion__body__tucUq"}},27450:(e,a,t)=>{var l=t(24640),i=t(70048);e.exports={variables:'"@styles/variables.module.css"',gray300:""+l.gray300,colorBlack:""+l.colorBlack,colorWhite:""+l.colorWhite,brandColorOne:""+l.brandColorOne,brandColorTwo:""+l.brandColorTwo,brandColorThree:""+l.brandColorThree,brandColorFour:""+l.brandColorFour,brandColorFive:""+l.brandColorFive,grayBorder:""+l.grayBorder,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-xl-1365":""+i["breakpoint-xl-1365"],"breakpoint-md":""+i["breakpoint-md"],"breakpoint-sm-450":""+i["breakpoint-sm-450"],"breakpoint-sm-550":""+i["breakpoint-sm-550"],main_container:"TabChallenges_main_container__Yeok_",main_title:"TabChallenges_main_title___LzQS",inner_container:"TabChallenges_inner_container__VIBOE",tab_boxes:"TabChallenges_tab_boxes__jz7r7",single_image:"TabChallenges_single_image__Th1fg",single_image_selected:"TabChallenges_single_image_selected__Id4CY",imageWrapper:"TabChallenges_imageWrapper__GztbE",titleWrapper:"TabChallenges_titleWrapper__ywxOV",imageTitle:"TabChallenges_imageTitle__EDND1",imageTitle_selected:"TabChallenges_imageTitle_selected__vUMg8",overlay:"TabChallenges_overlay___leCZ",right_container:"TabChallenges_right_container__xP3Yh",right_box:"TabChallenges_right_box__DUt0G",right_title:"TabChallenges_right_title__101FW",right_richText:"TabChallenges_right_richText__dG9HY",tab_retail_section:"TabChallenges_tab_retail_section__6jmlo",retail_title:"TabChallenges_retail_title__T5dts",retail_description:"TabChallenges_retail_description__xa4Uy",embla__viewport:"TabChallenges_embla__viewport__qS7gX",retail_container:"TabChallenges_retail_container__7jUTI",embla__container:"TabChallenges_embla__container__5bakk",tab_retail_box:"TabChallenges_tab_retail_box__lITM9",tab_retail_box_selected:"TabChallenges_tab_retail_box_selected__lxORI",container_cloud:"TabChallenges_container_cloud__4PTzR",content_cloud:"TabChallenges_content_cloud__qoP9m",title_cloud:"TabChallenges_title_cloud__06Edn",description_cloud:"TabChallenges_description_cloud__Y1wA_",inner_container_cloud:"TabChallenges_inner_container_cloud__UiKza",tab_boxes_cloud:"TabChallenges_tab_boxes_cloud__HpCw7",single_box_cloud:"TabChallenges_single_box_cloud__2IuJ5",single_box_selected_cloud:"TabChallenges_single_box_selected_cloud__1iS4T",right_container_cloud:"TabChallenges_right_container_cloud__F2P91",right_box_cloud:"TabChallenges_right_box_cloud__CgAJh"}},61919:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});let l=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\TabChallenges\TabChallenges.tsx`),{__esModule:i,$$typeof:r}=l,s=l.default}};