3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","whatsapp-chatbot-real-estate","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","whatsapp-chatbot-real-estate","d"],{"children":["__PAGE__?{\"blogDetails\":\"whatsapp-chatbot-real-estate\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","whatsapp-chatbot-real-estate","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
12:T4de,<p>Valued at a whopping US$ 120 billion and expected to grow to<a href="https://www.ibef.org/industry/real-estate-india.aspx" target="_blank" rel="noopener"> US$ 1 trillion</a> by 2030, the Indian real estate market is growing by leaps and bounds. However, the high cost of a potential purchase makes property closing deal a very long and time-consuming process.</p><p>Furthermore, the customers usually have a lot of questions regarding the house and the surrounding infrastructure such as where is the nearest school or a pharmacy. And if they do not get all the required information or enough attention, they end up going to your competitors.</p><p>Another significant challenge faced by the real estate sector is ineffective lead communication. Real estate agents struggle to manage time with listings, past clients, goals, while at the same time address queries of potential buyers. However, it is important to cater to as many leads as possible to stand strong and be successful in the real estate sector. This is where WhatsApp chatbot for real estate comes in.&nbsp;</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></p>13:T51f,<p>WhatsApp chatbot for real estate is designed to give users a seamless experience by responding to their queries instantly and at the same time save resources for your business.</p><p>A real estate WhatsApp chatbot is essentially a computer program specifically developed and designed to assist the property buyers and sellers with their queries regarding availability, location or prices.&nbsp;</p><p>With a large number of property units under their purview, real estate companies receive a lot of enquiries. Not only can real estate WhatsApp chatbot instantly address such queries, a support agent can also jump in seamlessly to <a href="https://wotnot.io/human-handover/" target="_blank" rel="noopener">address the complex queries</a>. This helps to improve both the overall response time and quality of support provided to the customer.</p><p>Some of the other key objectives accomplished by <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp chatbots</a>:</p><ul><li>24×7 support to customers</li><li>Interactive marketing of real estate properties</li><li>Automatic lead generation</li><li>Qualifying leads round the clock</li><li>Multilingual Support</li><li>Assistance in facilitating buying, selling and renting properties</li><li>Better time management</li></ul>14:T2782,<p>With 1.6 billion monthly users, WhatsApp is the most popular messaging app in the world. It is no surprise that it has become a crucial tool for businesses in providing superlative customer service and converting more leads. If you are also a part of the real estate sector and wondering how chatbot technology can help you drive more business, here are some of the most important use cases for real estate WhatsApp chatbot you need to know about.&nbsp;&nbsp;</p><p style="text-align:center;"><img class="image_resized" style="width:75%;" src="https://cdn.marutitech.com/7f592891_whatsapp_real_estate_use_cases_768x1188_8478dadc7d.png" alt="7f592891-whatsapp-real-estate-use-cases-768x1188.png" srcset="https://cdn.marutitech.com/thumbnail_7f592891_whatsapp_real_estate_use_cases_768x1188_8478dadc7d.png 101w,https://cdn.marutitech.com/small_7f592891_whatsapp_real_estate_use_cases_768x1188_8478dadc7d.png 323w,https://cdn.marutitech.com/medium_7f592891_whatsapp_real_estate_use_cases_768x1188_8478dadc7d.png 485w,https://cdn.marutitech.com/large_7f592891_whatsapp_real_estate_use_cases_768x1188_8478dadc7d.png 646w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Lead Generation</strong></span></h3><p>Generating quality leads for a real estate business can be quite exhaustive considering the cutthroat competition. On top of that, losing a prospective lead due to ineffective communication means that a business opportunity is lost. From greeting your visitors and offering details on multiple property listings based on budget, geography, and amenities to answering customers’ inquiries, the WhatsApp chatbot for real estate does it all.</p><p>Using a WhatsApp Chatbot is a great way to connect with customers on a platform they are already familiar with. It allows real estate agents and companies to collect lead details like name, phone number, email ID, budget, number of rooms, location, etc. Once they’ve generated the lead, they can simply continue the conversation on the same WhatsApp thread and enhance their chances of converting them to a sale.&nbsp;</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Lead Qualification</strong></span></h3><p>After a lead generation, the next step is the lead qualification. <a href="https://marutitech.com/whatsapp-business-chatbot/" target="_blank" rel="noopener">Chatbots for WhatsApp</a> can help ask targeted questions that allows the company to easily segment the customer. Lead qualification also helps them identify high-value customers they can service selectively.</p><p>A conversational WhatsApp chatbot can easily handle multiple customers simultaneously without having them to wait for a live agent to handle their queries. This kind of automation combined with instant response allows the real estate firms to lower the operational costs and increase customer satisfaction.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Site Visit and Confirmation</strong></span></h3><p>When the <a href="https://marutitech.com/chatbots-in-real-estate/" target="_blank" rel="noopener">real estate chatbot</a> qualifies the lead as a potential buyer for the property, customers can easily schedule a site visit via the bot. Besides this, chatbots can also help you with fixing a meeting with potential clients. They can do this by first providing essential information about the agent/company assigned to the property that the visitors are interested in exploring and then asking them for a convenient time to schedule the appointment.</p><p>Further, the bot can make a note of the date and time and redirect the customer to an actual real estate agent whenever needed for more detailed communication. Additionally, companies and agents can also enable the chatbot to send automated follow-ups to all potential customers via their preferred medium such as email or SMS.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Automated Scheduling</strong></span></h3><p>Coordinating site visit is an important step after generating and qualifying a lead. Whatsapp chatbot for real estate allows you to get away from the hassle of unlimited chain of emails and back &amp; forth calls to the customers. Not only can it schedule visits on WhatsApp but companies can also send them reminders on or near the date of the visit.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Virtual Property Tours</strong></span></h3><p>WhatsApp chatbots for real estate can be used to give the prospective leads a virtual tour of the property. This is particularly valuable to accommodate foreign clients or long-distance leads who can see the properties from anywhere around the world. WhatsApp bots also allow real estate companies to save time by showcasing unfinished properties to help pre-book under-construction properties. The prospects can easily enquire based on city, pin code or neighbourhood.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Document Submission</strong></span></h3><p>Document submission is an extremely cumbersome and complex process in real estate property dealings. Using WhatsApp chatbot, it becomes super simple to submit a complete set of documents without having the customer to take the hassle of waiting in a queue or running after the property agents to submit and get them reviewed.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. Build Lasting Customer Relationship</strong></span></h3><p>One of the important aspects of real estate business is offering great service and building relationships with the customers. With the help of a real estate WhatsApp chatbot, companies and agents can communicate with their customers in the language they want to speak. It allows companies to start building a relationship with the customers from the get-go in a much more friendly, knowledgeable, and attentive way and help them find the property in the area they are searching for.</p><p>Further, chatbots can help you with directing the attention of your website visitors or social media followers to a particular CTA (call to action) by asking them to follow you on social media, subscribe to the newsletter or contact your property agent thus making the entire experience more interactive.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>8. Track Conversations</strong></span></h3><p>This is a great advantage of <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp chatbots</a> over the usual phone and email conversations. They log all the conversation that happened between the user and the chatbot. This gives an invaluable advantage to the company when an actual human takes over the lead to close the sale. It allows them to read through the entire log, understand the complete conversation, and service the customers better.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>9. Property Submission</strong></span></h3><p>The importance of WhatsApp <a href="https://marutitech.com/chatbots-in-real-estate/" target="_blank" rel="noopener">real estate chatbot</a> is not just improving the buyer experience but sellers’ as well. Apart from answering some of the basic questions, real estate companies can use a chatbot to let sellers submit all the essential information and resources needed for customers to advertise and sell their property. This allows real estate companies to substantially reduce the time spent manually collecting all the important information, thereby increasing their ROI through cost savings.&nbsp;&nbsp;</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt="Contact-Us: Maruti Techlabs"></a></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>10. Payment Reminder</strong></span></h3><p>Another use case of WhatsApp chatbot for real estate is sending out payment reminders. Customers can be sent easy reminders for various property related processes such as pending payments, EMI’s, and maintenance. Real estate chatbot templates for WhatsApp makes it possible to easily integrate the API with the company’s existing financial system and send automatic reminders if any of the dues haven’t been paid on time. It is an effective way to gently remind customers to clear their dues and also ensure that you don’t have to chase them manually.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>11. Seamless After-Sale Support</strong></span></h3><p>WhatsApp chatbot for real estate is an effective tool to offer exceptional after-sale service at a minimal cost. There are a number of queries and issues ranging from help for finding the local amenities to recommendations for a plumbing service that the new owners or tenants might have even after the sale is closed. WhatsApp chatbot for real estate can easily address such issues and questions in a friendly manner. You can either choose to automate the entire process or allow the bot to transfer more complex queries to be taken over by humans. Without a doubt, the real estate WhatsApp chatbot enhances customer satisfaction.&nbsp;</p><p style="text-align:center;"><img class="image_resized" style="width:50%;" src="https://cdn.marutitech.com/bbe2b3a3_whatsapp_chatbot_real_estate_450x841_64689c713d.png" alt="Example of Whatsapp Chatbot for real estate industry" srcset="https://cdn.marutitech.com/thumbnail_bbe2b3a3_whatsapp_chatbot_real_estate_450x841_64689c713d.png 83w,https://cdn.marutitech.com/small_bbe2b3a3_whatsapp_chatbot_real_estate_450x841_64689c713d.png 268w,https://cdn.marutitech.com/medium_bbe2b3a3_whatsapp_chatbot_real_estate_450x841_64689c713d.png 401w," sizes="100vw"></p>15:T9d5,<p>In today’s technology-driven world, customers expect personalized and convenient customer service from the businesses they interact with. WhatsApp chatbot for real estate can be used as an active customer support tool to answer general user queries, generate genuine leads, and book customers’ appointments with the agents for property visits and related queries. It can prove instrumental for companies and agents who want to<a href="https://www.ibm.com/blogs/watson/2017/10/how-chatbots-reduce-customer-service-costs-by-30-percent/" target="_blank" rel="noopener"> reduce the cost related to customer support</a>, generate more qualified leads, and increase the overall ROI of the business.</p><p>The choice and complexity of the WhatsApp chatbot template for real estate should largely depend on the individual business needs of the company. For e.g., developing a scripted bot is best for a business that requires chatbot to be available 24/7 to answer simple queries/questions that their customers have. Whereas, custom AI or a platform-based chatbot is good to have for the company who want their bot to perform more complex tasks, such as follow-ups on leads, lead validation, and offering personalized recommendations to customers.&nbsp;</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt="Contact-Us: Maruti Techlabs"></a></p><p>At <a href="https://marutitech.com/services/interactive-experience/chatbot-development/" target="_blank" rel="noopener">Maruti Techlabs</a>, we have worked with leading property aggregators in the real estate industry and developed bespoke chatbot solutions that have resulted in pre-qualified leads for their sales team, while at the same time addressing FAQs and customer support.</p><p>Lastly, the question that remains is how long are you willing to continue with the outdated methods of lead generation that barely give results? It is time to bot-o-mate your real estate business and enjoy the <a href="https://marutitech.com/trends-need-to-know-about-conversational-marketing/#What_are_the_Benefits_of_Chatbot_and_Conversational_Marketing" target="_blank" rel="noopener">benefits of conversational marketing</a>. Drop us a note at <a href="mailto:<EMAIL>" target="_blank" rel="noopener"><EMAIL></a> and our bot development experts will guide you the process of leveraging conversational marketing for your real estate business.</p>16:T53e,<p>Chatbots have been assisting humans for quite some time. If we turn back the pages of time, we can recall the introduction of the <a href="https://en.wikipedia.org/wiki/Turing_test" target="_blank" rel="noopener">Turing test in 1950 by Alan Turing</a>. The test was then published as an article titled “Computing Machinery and Intelligence” and is now considered as a criterion of intelligence.</p><p>If we’ll look at the historic chatbots like <a href="https://www.theatlantic.com/technology/archive/2014/06/when-parry-met-eliza-a-ridiculous-chatbot-conversation-from-1972/372428/" target="_blank" rel="noopener">PARRY (1972) and ELIZA (1996) </a>things were different in comparison to the recent ones like ALICE, Jabberwocky, and DUDE. However, things have changed drastically, and the chatbots are now part of the messaging platforms, apps and websites, company’s internal platform, education industry, and toys.</p><p>Despite the fact that they are today playing a role in different industries, <a href="https://chatbotsjournal.com/5-learnings-from-our-chatbot-survey-2017-72a6a4fc209c" target="_blank" rel="noopener">a report released by chatbots journal</a> says a majority of businesses didn’t hear about chatbots until 2015. The same report also reveals that 54% developers worked on these for the first time in 2016.</p>17:T4bb,<p>In the last couple of years,<a href="https://www.inman.com/2016/10/27/how-facebook-messenger-just-changed-lead-generation/" target="_blank" rel="noopener"> Facebook has changed the way leads were generated</a>. The credit undoubtedly goes to chatbots in Facebook messenger. They have encouraged third-party developers to build Bots.</p><p><a href="https://venturebeat.com/2016/06/30/facebook-messenger-now-has-11000-chatbots-for-you-to-try/" target="_blank" rel="noopener">According to a report,</a> there were more than 11,000 chatbots in Facebook Messenger. However, these Bots aren’t solely made by Facebook. It includes a significant portion of third-party developers.</p><p><a href="https://research.fb.com/publications/" target="_blank" rel="noopener">Facebook recently released research</a> which outlines their efforts in building and training artificially intelligent chatbots to negotiate with humans.</p><p>After assisting humans, <a href="http://www.wotnot.io" target="_blank" rel="noopener">chatbots</a> are now getting trained to negotiate like humans.</p><p>Before we talk further about this, let us first have a quick look at their journey till date and the contribution they have made.</p>18:Tccf,<p>Chatbots have the potential to change the way brands communicate with their consumers. However, we can’t deny the fact that both brands and consumers are relishing <a href="https://marutitech.com/benefits-chatbot/" target="_blank" rel="noopener">the benefits of chatbots equally</a>. Let us have a look at some use cases:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Shopping</span></h3><p>The online market is growing with every passing day. Chatbots can become one of the most resourceful inclusions for your retail business. They can give a personalised experience to your customers and help in generating new leads with the same. One such example is Shop Spring. The users don’t have to chat to with the bot. They simply need to choose some of the answers, and the Bot gives them narrowed options. This action empowers them to make a prompt decision and saves their time. &nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Payments</span></h3><p>It was September 2016 when Facebook Messenger allowed its users not just to do the shopping but also in making payments through systems like MasterCard, Visa, American Express, Braintree, Stripe, and PayPal. It isn’t just the ease they avail, but they also look after the security of transaction and other vital factors.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Restaurants and delivery</span></h3><p>People order food online because they don’t want to spend their time waiting in long queues. ChatBots are instantly available online at any time to take the order. Few of the best examples to this include the one deployed by Pizza Hut and Dominos. <a href="https://www.facebook.com/messages/t/FoodieYourFoodBot" target="_blank" rel="noopener">We have developed a Food ordering Bot “Foodie”. Try it out!!</a> The bots not just save time, but they are designed with a distinct sense of humour, which helps them build a healthy and engaging interaction with the customers.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Health Care</span></h3><p>Here ChatBots help the patients in booking their appointment with a doctor, store and track their payments, paying invoices, and most importantly can guide patients. The usage of <a href="https://marutitech.com/chatbots-as-your-doctors/" target="_blank" rel="noopener">ChatBots in the healthcare industry</a> can help them build a steady and long term relationship with the patients.</p><h3><a href="https://marutitech.com/chatbots-and-service-industry/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;">Customer Support</span></a></h3><p>Customer support undoubtedly takes a lot of resource from the enterprises. Not just humans but the set up for them takes away a good sum of your capital. Let us consider that you are not replacing your current customer support team entirely with the ChatBot; even partial usage can help you impeccably. They can help you realise that most of the queries are identical and also acknowledge you about the new complaints. With this, you can automate repetitive requests. The prompt reply and solution to them will indeed earn you appreciation and loyalty of your customers.</p>19:Tfaf,<figure class="image"><img src="https://cdn.marutitech.com/chatbot_negotiation_c2cbfb7534.jpg" alt="chatbot-negotiation.jpg" srcset="https://cdn.marutitech.com/thumbnail_chatbot_negotiation_c2cbfb7534.jpg 245w,https://cdn.marutitech.com/small_chatbot_negotiation_c2cbfb7534.jpg 500w," sizes="100vw"></figure><p>Chatbots can negotiate like humans</p><p>This isn’t the first time that a question of this kind is raised. If we’ll go back the year 2015-16, the same questions were raised on ChatBots in context to helping customers. However, today we can see the contribution they are doing in different industries and the continuously increasing rate of acceptance.</p><p><a href="http://www.wotnot.io" target="_blank" rel="noopener">ChatBots</a> have indeed empowered systems to make small conversations and execute simple tasks. However, expecting them to make complex meaningful conversations with real humans, understanding the sentences and then building a sentence by self to achieve the goal, indeed sounds like a tough task.</p><p>Researchers at Facebook Artificial Intelligence Research showed us that a ray of hope through open-sourced codes and the published research. The researchers have claimed that Bots can negotiate the way humans do. Here are some key-takeaways:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Task: Multi Issue bargaining</span></h3><p>The researchers gave a multi-issue bargaining task to two agents. Both the agents were shown the same set of items and were assigned the task to divide them accordingly by negotiating a split of the terms. Both of them were given their own set of product-values to set the priority levels. The experts at FAIR created many such scenarios to test and ensured that cracking the negotiation and getting the best deal is impossible for both the agents.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Dialog Rollouts</span></h3><p>Negotiation is a mixture of both, linguistic and reasoning problem. It requires an intention for something which needs to be verbalised. Dialogue rollout is an idea formulated by FAIR researchers’ for building long term planning dialogue agents.</p><p>The Dialogue rollouts are designed keeping in mind that the agent can simulate a conversation’s future by rolling out a dialogue model. We have seen simple ideas being used in designing the game environments, but it is for the first time that it is being implemented into conversation and negotiations.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Negotiation data set</span></h3><p>The team crowdsourced a collection of negotiations amidst a couple of people to train negotiation agents and to conduct large-scale quantitative evaluations. Surprising many around, in most of the cases people did not even realise that they were interacting with a machine.</p><p style="text-align:center;">Chatbot negotiating with humans</p><p><img src="https://cdn.marutitech.com/chatbot_negotiation_1_34185e427d.jpg" alt="chatbot-negotiation machine" srcset="https://cdn.marutitech.com/thumbnail_chatbot_negotiation_1_34185e427d.jpg 208w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Before signing off</span></h3><p>The work portrayed in the research clearly indicates the future of chatbots. The technology will soon come adorned with more power to reason, negotiate, and converse. Basically with all key ingredients that can make a perfect digital assistant. Looking at these we can definitely expect a brighter future of ChatBots.</p><p>However, <a href="https://www.theverge.com/2017/6/14/15799068/chatbot-negotiations-ai-facebook-fair" target="_blank" rel="noopener">in one of its article</a>, the Verge warns people not to get too excited about the project as there have been instances when Bots couldn’t work up to expectations. Hence all we can say for now is that we’ll have to wait for an undefined duration to relish the essence of these chatbots.</p>1a:T50c,<p><i><strong>“In some countries, WhatsApp is like oxygen.”</strong></i><strong> – Jan Koum.</strong><br>And WhatsApp chatbots have become no less than oxygen for businesses, big and small alike. Let us explore the what and how of WhatsApp Business, the benefits of<a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener"> WhatsApp chatbot</a>, and why your business should be on the most popular messaging app.</p><p>With over 1.5 billion monthly active users spread across 180 countries, Whatsapp has emerged as the global leader in the area of messaging. Even though it initially started as a platform for users to connect with their friends and family, a section of it has slowly evolved into a potential medium for businesses to engage with clients.</p><p>In fact, the rise of Facebook messenger and chatbots for facilitating business-customer interactions birthed the idea of WhatsApp Business API and WhatsApp Business application.&nbsp;WhatsApp Business is an application developed by WhatsApp, dedicated to small business owners enabling them to connect with their customers. WhatsApp Business API, on the other hand, is for medium and large businesses helping them connect with customers from around the world and send automated quick replies at scale.</p>1b:T1692,<p>Announced in 2018, WhatsApp Business is the business version of the popular messaging app. It allows small businesses to freely get in touch with their customers, who are using the regular WhatsApp application. It is important to note that the customers are not required to install any special app to connect or be connected with businesses. Further, as WhatsApp Business accounts are listed as businesses, users will know that they are being contacted by a business account rather than a regular account.&nbsp;&nbsp;</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt="Contact Us: Maruti Techlabs"></a></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Key Features of WhatsApp Business App</strong></span></h3><p>In the words of the WhatsApp team, “<a href="https://www.whatsapp.com/business/" target="_blank" rel="noopener">WhatsApp Business app is built with the small business owner in mind</a>.” It is packed with features to help the small business owners reach their customers and showcase their products. Let us have a look at some of the features:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Availability of Business Profile</strong></span></h3><p>Through a WhatsApp Business profile, you can make important information easily available to the customers. This includes your:&nbsp;</p><ul><li>Business hours,</li><li>Store/business address,</li><li>Corresponding location pin,</li><li>Email ID,</li><li>Website links,</li><li>Contact details, and</li><li>Brief business description</li></ul><p>This not only makes your business highly accessible and discoverable but also adds transparency.&nbsp;</p><p>How to Add a Business Profile:</p><ul><li>Open the WhatsApp Business app.</li><li>Navigate to ‘Settings’&nbsp;and tap on ‘Business Settings’.</li><li>Tap on&nbsp;‘Profile’&nbsp;and add the necessary details against the fields available.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Smart Replies and Messaging</strong></span></h3><p>Save frequently sent messages as templates and save time with the Quick Replies feature. For example, typing “/thanks” will deliver the message “Thank you for your business! We look forward to working with you again.” to your customer. These messages can be customized as per your preference.</p><p>You can even set up an “away” automated message if customers contact you outside of business hours. Along the same lines, you could also set up a greetings message, that introduces the customer to your products or services when they initiate a chat with your business.&nbsp;</p><p>How to Set Up Quick Replies:&nbsp;</p><ul><li>Enter your WhatsApp Business app settings.</li><li>Tap on ‘Business Settings’ and select ‘Quick replies’.</li><li>Tap on the “+” symbol on the bottom-right corner.</li><li>Enter the message or select media and input its corresponding shortcut and keywords (keywords are optional).</li></ul><p>How to Set Up Away Message:&nbsp;</p><ul><li>Open your WhatsApp Business settings.</li><li>Tap on ‘Away Message’.</li><li>Toggle the ‘Send away message’ on and edit or create your away message.</li><li>Set up a schedule during which the away message will be sent.</li><li>You may also select the contacts that will receive the away message.</li></ul><p>How to Set Up Greetings:&nbsp;</p><ul><li>Navigate to the Settings of your WhatsApp Business app.</li><li>Select ‘Greeting Message’.</li><li>Edit the existing greeting message or create a new one.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Labels</strong></span></h3><p>WhatsApp Business app allows businesses to create and allot labels to active chats. As a result, you can categorize different customers and sort through conversations using the labels as filters to respond to their requests.&nbsp;&nbsp;</p><p>How to Create and Add a Customer Label:&nbsp;</p><ul><li>Open the client’s chat page.</li><li>Tap on more options indicated by three vertical dots⋮.</li><li>Select ‘Label Chat’ and choose the label.</li><li>If the required label does not exist, tap on ‘New label’ and create it.&nbsp;</li></ul><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Vital Messaging Stats</strong></span></h3><p>Messaging Statistics is an incredibly handy feature of WhatsApp Business. It provides you with an insight into how many messages were sent, successfully delivered, and read by the clients. Studying these metrics can drastically improve how you conduct business.&nbsp;</p><p>How to View Messaging Stats:&nbsp;</p><ul><li>Navigate to WhatsApp Business Settings and choose ‘Business Settings’.</li><li>Select ‘Statistics’.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. WhatsApp Web</strong></span></h3><p>With WhatsApp Web, you can make your WhatsApp Business account available on the browsers of other devices such as desktops and laptops. As a result, if you have multiple teams maintaining a single account, they can easily work in co-ordination without having to fight for resources.&nbsp;</p><p>How to Access WhatsApp Web:&nbsp;</p><ul><li>Tap on the More menu (as indicated by the three vertical dots on your home screen).</li><li>Select ‘WhatsApp Web’ to open the QR scanner.</li><li>Open the WhatsApp web link on your browser to access the QR code.</li><li>Scan the QR code.</li></ul>1c:T169c,<p>WhatsApp, in the form of <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp business chatbots</a>, offers businesses the golden opportunity to reach out to a wider set of audience and provide them the best customer service that keeps them coming back to you. Elucidated below are different benefits of WhatsApp chatbot for different stakeholders involved:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. For Clients</strong></span></h3><p>Clients can gain the following benefits of WhatsApp chatbot:&nbsp;</p><ul><li>With chatbots, clients can not only initiate conversations with businesses but also have instant resolutions to their queries.</li><li>Getting instant replies means that they can have a two-way conversation with businesses.</li><li>Round the clock support means higher client satisfaction rates.</li><li>AI-powered chatbots are capable of personalizing conversations, which will add to the value and quality of the interaction.</li><li>End-to-end encryption, two-fact authentication, and business verification offer users’ data and identity protection.</li><li>Businesses are available over an already-available platform rather than downloading a different app.</li><li>Additionally, users are already accustomed to the layout and functioning of the app.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. For Business Owners&nbsp;</strong></span></h3><p>For business owners, here are some of the benefits of&nbsp;WhatsApp chatbot:</p><ul><li>The platform is available to all, be it small, home-run businesses or large enterprises.</li><li>WhatsApp chatbots offer a richer customer experience that helps with customer retention and engagement.</li><li>Builds brand awareness and brand loyalty while enhancing customer relationships.</li><li>Makes your business available over multiple channels.</li><li>Ability to send broadcasts and outbound notifications to clients.</li><li>Facilitates businesses to connect with customers through an interactive and visual medium.</li><li>Global availability of WhatsApp (barring China), which allows you to develop a customer-centric chatbot catering to international audience.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. For Marketers</strong></span></h3><p>For marketers, WhatsApp business chatbot brings the following opportunities:&nbsp;</p><ul><li>WhatsApp chatbots can carry out the bulk of the repetitive work that marketers need to carry out, freeing them up for more strategic work.</li><li>It helps to set up an effective channel to generate and contact leads.</li><li>It allows the team to direct and navigate the customers down the marketing funnel.</li><li>Personalized chats allow chatbots to offer attractive discounts to frequent users or potential leads.</li><li>Various formatting options and inclusion of multimedia allows the marketing team to be more creative with their skills.</li><li>Make use of Broadcast Lists to execute your marketing campaigns.</li></ul><p><img src="https://cdn.marutitech.com/benefits_of_whatsapp_chatbot_e2d9a5ada6.png" alt="benefits-of-whatsapp-chatbot" srcset="https://cdn.marutitech.com/thumbnail_benefits_of_whatsapp_chatbot_e2d9a5ada6.png 125w,https://cdn.marutitech.com/small_benefits_of_whatsapp_chatbot_e2d9a5ada6.png 401w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. For Sales Executives</strong></span></h3><p>Once the marketing team has generated leads, the sales team can enjoy the following benefits of WhatsApp&nbsp;chatbot:&nbsp;</p><ul><li>Leads the customers down the sales funnel.</li><li>Chatbots can help customers make any decisions related to sales. From locating products to directing to payment gateways – chatbots can carry out a major chunk of the conversation on its back.</li><li>Run promotions for leads that are growing cold.</li><li>Share updates on customer orders and other relevant notifications.</li><li>The 24-hour WhatsApp Status feature is a great way to announce deals and flash sales.</li><li>Quick replies will keep your customers engaged during the purchase journey.</li><li>Allows your business to up-sell&nbsp;by recommending relevant and suitable products (along with images and purchase links)</li></ul><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><span style="font-family:Poppins, sans-serif;font-size:18px;"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt="Contact Us: Maruti Techlabs"></span></a></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. For Customer Care and Customer Support Executives</strong></span></h3><p>Here are the main WhatsApp business chatbot benefits from the customer support angle:&nbsp;</p><ul><li>WhatsApp chatbots can be customized to deal with frequently asked questions.</li><li>Customers will receive instant responses from your business, and this two-way conversation adds to brand loyalty.</li><li>Complex queries can be automatically handed from the bot to the customer support executive using chatbot-to-human handover.</li><li>If unable to provide a satisfactory resolution, chatbots can also suggest nearby stores or service centers and even share their location pins.</li><li>Chatbots can share ticket status and important details with clients. For example, a WhatsApp business chatbot representing a travel agency could share check-in details, live flight status, itinerary, etc.</li><li>Through WhatsApp chatbot, organizations can set up and collect surveys from clients. This can help with the enhancement of their products and services (and even chatbot workflows in some instances).&nbsp;</li></ul>1d:T86d,<p>As a business, to be able to reach out to your customers on WhatsApp, you need to get them to opt in for the same. A WhatsApp opt-in is when customers agree to receive WhatsApp messages from your business by providing you their contact number through a different channel. Another way to send them updates and notifications is by having them reach out to you first. While this is easier said than done, some companies have successfully ran creative WhatsApp marketing campaigns resulting in a boost in product sign-ups and overall brand engagement. Here’s looking at some iconic WhatsApp marketing campaigns that garnered a lot of eyeballs and leads for the business.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Hellman’s</strong></span></h3><p>Hellman, a renowned mayonnaise brand, became a viral sensation through its WhatsApp-based marketing campaign – WhatsCook. Brazilian users could share images of the items available in their pantry and refrigerator and have Hellman’s chef instruct them on how to create delicious dishes with the ingredients at hand.&nbsp;&nbsp;</p><p>The campaign attracted over 13,000 sign-ups and received 99.5% users approval. Needless to say, it was a huge success, and similar models were replicated in Chile, Argentina, and Uruguay.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Absolut Vodka</strong></span></h3><p>Absolut Vodka is another brand that successfully executed a smart WhatsApp marketing campaign. During the launch of its limited-edition “Absolut Unique” vodka, the company announced that <i>two</i> tickets of the launch party would be available to the general public. Those interested in the offer were asked to interact with Sven, the virtual doorman available over WhatsApp, and convince him to give them the tickets. The users came up with some of the wittiest, quirkiest, and most creative messages that included voice notes and videos.&nbsp;</p><p>Within a span of 3 days, Absolut Vodka registered that Sven had interacted with over 600 users and received more than 1000 videos, images, and audio clips.&nbsp;&nbsp;</p>1e:T59f,<p>Businesses today understand the importance of reaching out the customers where they already are. One of the greatest advantages of WhatsApp Business and WhatsApp Business chatbots is that it can help build a stable and long-term relationship with your customers without them having to seek you out on different channels. Upon the development of a user-friendly chatbot, businesses can enjoy the benefits of a loyal audience base and higher engagement offered by WhatsApp. With the right chatbot platform, customer care operations can be streamlined and your customers can enjoy better customer service than your competitors.</p><p><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt="Contact Us: Maruti Techlabs"></a></p><p><a href="https://wotnot.io/" target="_blank" rel="noopener">WotNot</a>, by Maruti Techlabs, is one of the few chatbot platforms that not only provides access to WhatsApp Business API, but also designs effective bot conversations tailor-made for your business needs. Ready to reap the benefits of WhatsApp chatbots? Simply drop us a note at&nbsp;<a href="mailto:<EMAIL>" target="_blank" rel="noopener"><EMAIL></a> to see how <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp chatbots</a> can help you take your business where your customers are!</p>1f:T16c8,<p>It’s 2018, and chatbots have now truly evolved to now reach almost every aspect of our lives. Right from Facebook Messenger, to Skype to phones – we talk and interact with them. Some assist us in planning our trips while some crack some jokes. When it comes to <a href="https://marutitech.com/whatsapp-chatbot-real-estate/" target="_blank" rel="noopener">chatbots in real estate</a>, they have revolutionized the way we buy, sell or rent properties by turning long static forms into an interactive experience.</p><p>Back in 2016, big tech players like Facebook, Microsoft, and Google, launched their own bots and <a href="https://marutitech.com/14-powerful-chatbot-platforms/" target="_blank" rel="noopener">chatbot platforms</a>. Ever since then, AI-based applications started to boom, and many interesting bot concepts started to take shape. Like with any other new technology, in the beginning, everyone just wants to explore how to use this “AI engine” to create a bot, but as the AI infrastructure keeps improving at such a rapid rate, it is clear that AI will surely help people in their everyday lives and make businesses run more efficiently &amp; <a href="https://customerthink.com/how-artificial-intelligence-powered-customer-service-will-help-customer-support-agents/" target="_blank" rel="noopener">enhance their customer service</a>. However, the key here is to continue engaging with customers particularly beyond normal hours of operation &amp; to address questions that don’t really need human input.</p><p>With bots being deployed across a plethora of industries such as <a href="https://chatbotsmagazine.com/is-conversational-ai-the-future-of-healthcare-658a3d8e9dd5" target="_blank" rel="noopener">healthcare</a>, e-commerce, retail or <a href="https://marutitech.com/ebooks/artificial-intelligence-in-hotels/" target="_blank" rel="noopener">hospitality</a> have made a significant impact in terms of ROI and customer engagement. Bots are well and truly poised to be helpful in the world of real estate as well.&nbsp;Be it a real estate agent or a customer, <a href="https://wotnot.io/real-estate-chatbot/" target="_blank" rel="noopener">Real Estate chatbots</a> prove to be of assistance to both when it comes to saving time, money and additional resources.</p><p>When it comes to assisting a visitor who could be a potential lead for you, here’s how a chatbot can turn a potential lead from lukewarm to hot.</p><p>Let us assume a visitor is looking to move into a new place or a potential seller is looking to sell their apartment. In both the scenarios, both parties need quick answers, however, when it comes to a real estate agent or a broker or a developer that is taking care of new and old listings, addressing their own sales and marketing goals, and managing past clients — it is quite difficult for them to take time out and cater to the requirements of a random visitor on their website.</p><p>Investing time out to ascertain the overall seriousness of the lead from scratch is pretty time-consuming, to say the least. However, it is quite evident that to be successful in real estate, you need to capture as many leads as possible to make sure that you have a healthy pipeline in place from time to time.</p><p>Given that majority of buyers and sellers are starting their journeys online, it is prudent to deploy custom <a href="https://chatbotsmagazine.com/real-estate-chatbots-laying-the-foundation-of-trust-171be7ae897c" target="_blank" rel="noopener">chatbots in real estate</a> that assist them in building their sales funnel.</p><p>In today’s time of digitisation and online presence of businesses, most of the customers are converted from leads online. In such a scenario, letting all that online traffic go is something one cannot afford to do. <a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener">Intelligent chatbots</a> in real estate help you tap into that traffic in order to collect and convert leads into customers.</p><p><img src="https://cdn.marutitech.com/1_Mtech_c44b5ce644.png" alt="Questions your bot can ask a visitor" srcset="https://cdn.marutitech.com/thumbnail_1_Mtech_c44b5ce644.png 95w,https://cdn.marutitech.com/small_1_Mtech_c44b5ce644.png 306w,https://cdn.marutitech.com/medium_1_Mtech_c44b5ce644.png 458w,https://cdn.marutitech.com/large_1_Mtech_c44b5ce644.png 611w," sizes="100vw"></p><p>We all know, in any business, lead generation is the most important and yet the most daunting task. Important, because that is how you come across people who are interested and willing to buy your product. Daunting, because you cannot do this without facing rejections &amp; facing rejections results in a lot of your time being taken up, without any success in getting prospective customers. This leads to frustration and wastage of time.</p><p>In the real estate industry, lead generation becomes all the more difficult because of the complexity of the industry.</p><p>Properties are not something you can pack into your bags and go door-to-door showcasing them to the people.</p><p>A lot of nitty-gritty goes into the process of lead generation solely and it also takes time to figure out what the real estate brokers or agents have to offer &amp; what the customer is looking for.&nbsp;Elucidated below are 9 reasons why chatbots in real estate tend to guarantee success –</p><p style="text-align:center;"><img class="image_resized" style="width:50%;" src="https://cdn.marutitech.com/2_Mtech_08e1ab6e56.png" alt="Need of real-estate chatbot" srcset="https://cdn.marutitech.com/thumbnail_2_Mtech_08e1ab6e56.png 39w,https://cdn.marutitech.com/small_2_Mtech_08e1ab6e56.png 124w,https://cdn.marutitech.com/medium_2_Mtech_08e1ab6e56.png 186w,https://cdn.marutitech.com/large_2_Mtech_08e1ab6e56.png 248w," sizes="100vw"></p>20:T8bf,<figure class="image"><img src="https://cdn.marutitech.com/Chatbot_in_Real_Estate_1_107d0f7587.gif" alt="benefits of chatbots "></figure><p>While the above covers the salient features, there are more <a href="https://marutitech.com/benefits-chatbot/" target="_blank" rel="noopener">benefits of chatbots</a> in real estate as covered below –</p><ul><li><strong>Cross platform</strong> – Not only your website, but various online platforms or channels of your choice like Facebook, WhatsApp, Skype can support chatbots.</li><li><strong>Additional functionality</strong> – Apart from the information gathered on screen, chatbots also help you get additional information like the location from where the client contacted you (through IP-based information), from which page the bot was invoked and other similar ads the client has surfed through, etc.</li><li><strong>Language no bar</strong> – <a href="https://wotnot.io/" target="_blank" rel="noopener"><span style="color:#f05443;">Chatbots</span></a> can communicate with your targeted audience in their language, thus further personalizing the customer’s experience.</li><li><strong>Direct database entry &amp; integration with CRM</strong> – Data handling becomes so much easier with bots. Chatbots in real estate collect information and feed it directly to the database or CRM, without needing your assistance.</li></ul><p>At Maruti Techlabs, we have worked with leading developers and property aggregators in Real Estate and developed bespoke AI based chatbot solutions that have assisted our clients in generating pre-qualified leads for their sales team, while also addressing FAQs and customer support across the smorgasbord of buy/sell/rent scenarios, at scale.</p><p>The widespread adoption and advent of bots over the last two years along with the participation of tech behemoths are major indications that conversational AI is no longer just a fad, but well and truly here to stay. Being in real estate, it would be prudent to consider <a href="https://app.wotnot.io/preview/interact?url=&amp;themeColor=%23F44336&amp;alignment=right&amp;templateKey=real_estate" target="_blank" rel="noopener">bot-o-mating your sales and customer service process</a>.</p><p>&nbsp;</p>2:[null,["$","$L11",null,{"blogData":{"data":[{"id":222,"attributes":{"createdAt":"2022-09-15T07:30:50.191Z","updatedAt":"2025-06-16T10:42:14.081Z","publishedAt":"2022-09-15T10:51:15.893Z","title":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits","description":"Here are some critical use cases for real estate WhatsApp chatbot you need to know about. ","type":"Chatbot","slug":"whatsapp-chatbot-real-estate","content":[{"id":13920,"title":null,"description":"<p>Whether you’re a real estate agent, broker or property consultant, buying or selling a property is a harrowing process that involves tons of calls, emails, and a lot of follow-ups. With a WhatsApp <a href=\"https://marutitech.com/chatbots-in-real-estate/\" target=\"_blank\" rel=\"noopener\">chatbot for real estate</a>, you can easily handle all such mundane stuff without much hassle.</p><p>Chatbots have been one of the biggest technology disruptions in the world of marketing over the last few years. In the real estate business specifically, chatbots are revolutionizing the way companies buy, sell, and rent properties.</p>","twitter_link":null,"twitter_link_text":null},{"id":13921,"title":"Problems faced in the Real Estate Industry","description":"$12","twitter_link":null,"twitter_link_text":null},{"id":13922,"title":"How WhatsApp Chatbot for Real Estate Can Help in Better Conversion","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13923,"title":"11 Top Use Cases of WhatsApp Chatbot for Real Estate","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13924,"title":"In a nutshell","description":"$15","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":419,"attributes":{"name":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","alternativeText":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","caption":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","width":1500,"height":750,"formats":{"thumbnail":{"name":"thumbnail_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":245,"height":123,"size":26.04,"sizeInBytes":26037,"url":"https://cdn.marutitech.com//thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"small":{"name":"small_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":500,"height":250,"size":100.01,"sizeInBytes":100012,"url":"https://cdn.marutitech.com//small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"large":{"name":"large_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":1000,"height":500,"size":393.55,"sizeInBytes":393554,"url":"https://cdn.marutitech.com//large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"medium":{"name":"medium_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":750,"height":375,"size":216.42,"sizeInBytes":216415,"url":"https://cdn.marutitech.com//medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"}},"hash":"0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","size":147.49,"url":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:49.815Z","updatedAt":"2024-12-16T11:46:49.815Z"}}},"audio_file":{"data":null},"suggestions":{"id":1988,"blogs":{"data":[{"id":122,"attributes":{"createdAt":"2022-09-12T05:04:10.520Z","updatedAt":"2025-06-16T10:42:00.696Z","publishedAt":"2022-09-12T11:48:39.066Z","title":"Chatbots vs. Humans: Who Negotiates Better? A Comparative Study","description":"Read how chatbots can do better business negotiations compare to human agents? What are the benefits of using chatbots for closing the deal?","type":"Chatbot","slug":"can-chatbots-business-negotiations-better-human-employees","content":[{"id":13289,"title":null,"description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13290,"title":"Why are we here?","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13291,"title":"Chatbots today","description":"<p><a href=\"https://www.slideshare.net/Mobileappszen/chatbots-survey-2017-chatbot-market-research-report\" target=\"_blank\" rel=\"noopener\">According to a report</a>, 90% developers believe that businesses lack knowledge about chatbots while 75% companies believe that chatbots haven’t yet proved themselves completely.</p><p>Amidst the complete chaos, chatbots are continuously trying their best to evolve and transform the way we live, interact, and work.</p>","twitter_link":null,"twitter_link_text":null},{"id":13292,"title":"How did it start?","description":"<p>The year 2016 became the game changer for the chatbots. It was the same year when Microsoft and Facebook shared their plans to support chatbots. While Microsoft announced its Bot framework in March 2016, Facebook made the announcement in April 2016.</p><p>As a result of this, developing and releasing approved bots has now become easier with Facebook Messenger, Slack, Skype, Telegram, and few others. <a href=\"https://www.slideshare.net/Mobileappszen/chatbots-survey-2017-chatbot-market-research-report\" target=\"_blank\" rel=\"noopener\">According to a report</a>, the Facebook messenger is leading the list as the most preferred platform by 92%, followed by Slack and Twitter. The same report also shares that the top three industries taking the most benefits through bots include <a href=\"https://marutitech.com/complete-guide-chatbots/\" target=\"_blank\" rel=\"noopener\">E-commerce, Insurance, and Health care</a>.</p>","twitter_link":null,"twitter_link_text":null},{"id":13293,"title":"What are the benefits of using chatbots?","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13294,"title":"Let’s get back to where we started?","description":"<p>ChatBots have indeed proven themselves as a powerful tool to customer satisfaction and an unmatched resource for the enterprises helping them save a lot of time and money. &nbsp;</p><p>Now, getting back to Facebook’s endeavours in designing and developing Bots to make negotiations the way humans do, let us analyse the chances of the success of this research. This new technology will not only change the way we do business but also non-commercial activities. The example of non-commercial activities can include fixing meeting time. The Bots can fix up the meetings keeping in mind the availability of everyone involved in the meeting.</p>","twitter_link":null,"twitter_link_text":null},{"id":13295,"title":"Can chatbot handle negotiations like humans?","description":"$19","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":363,"attributes":{"name":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","alternativeText":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","caption":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","width":1000,"height":563,"formats":{"thumbnail":{"name":"thumbnail_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"thumbnail_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":8.23,"sizeInBytes":8234,"url":"https://cdn.marutitech.com//thumbnail_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"},"small":{"name":"small_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"small_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":26.71,"sizeInBytes":26708,"url":"https://cdn.marutitech.com//small_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"},"medium":{"name":"medium_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"medium_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":51.54,"sizeInBytes":51540,"url":"https://cdn.marutitech.com//medium_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"}},"hash":"c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","size":81.53,"url":"https://cdn.marutitech.com//c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:43:40.445Z","updatedAt":"2024-12-16T11:43:40.445Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":215,"attributes":{"createdAt":"2022-09-15T07:30:48.300Z","updatedAt":"2025-06-16T10:42:13.113Z","publishedAt":"2022-09-15T10:25:47.569Z","title":"Why Your Business Should Take The WhatsApp Chatbot Plunge","description":"Check out why your business should be on the most popular messaging app. ","type":"Chatbot","slug":"benefits-of-whatsapp-chatbot","content":[{"id":13863,"title":null,"description":"$1a","twitter_link":null,"twitter_link_text":null},{"id":13864,"title":"What is WhatsApp Business App?","description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13865,"title":"WhatsApp in Numbers","description":"<p>The vastness of WhatsApp is seen in the huge number of users the app has, which makes the app the most popular messaging app in the world. Let us have a look at the numbers:</p><p><img src=\"https://cdn.marutitech.com/b235c98b-whatsapp-in-numbers.png\" alt=\"Benefits of WhatsApp Chatbot\" srcset=\"https://cdn.marutitech.com/b235c98b-whatsapp-in-numbers.png 591w, https://cdn.marutitech.com/b235c98b-whatsapp-in-numbers-450x318.png 450w\" sizes=\"(max-width: 591px) 100vw, 591px\" width=\"591\"></p><ul><li>Daily active WhatsApp users: 1 billion</li><li>Largest WhatsApp markets: India (200 million users) and Brazil (120 million users)</li><li>Highest penetration rate: Netherlands (85%), Spain (83.1%), and Italy (83%)</li><li>WhatsApp Business Users: 3 million</li><li>WhatsApp messages sent (per day): 65 billion (29 million messages per minute)</li></ul>","twitter_link":null,"twitter_link_text":null},{"id":13866,"title":"What is WhatsApp Business API?","description":"<p>WhatsApp Business API is available for medium and large-sized business owners so that they can reach out to customers from around the world at scale. With the help of <a href=\"https://marutitech.com/whatsapp-business-chatbot/\" target=\"_blank\" rel=\"noopener\">WhatsApp Business API</a>, WhatsApp business chatbots are built to instantly address customers’ queries at scale and increase your brand reach.</p><p><a href=\"https://wa.me/************?text=Hi\" target=\"_blank\" rel=\"noopener\"><img src=\"https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif\" alt=\"\"></a></p><p>WhatsApp business chatbot involves holding an automated conversation with the customers, which mimics a regular human interaction. With well-designed NLP algorithms and training, it gets a hold of the user behavior and can offer a richer customer experience.&nbsp;&nbsp;</p><p>Businesses are increasingly using WhatsApp business chatbots to increase their engagement rate, brand visibility, and provide stellar customer service.</p>","twitter_link":null,"twitter_link_text":null},{"id":13867,"title":"Benefits of WhatsApp Chatbot","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13868,"title":"How Brands Made Customers Reach Out To Them On WhatsApp","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13869,"title":"Wrapping it Up","description":"$1e","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":453,"attributes":{"name":"wepik-photo-mode-2022826-151443.jpg","alternativeText":"wepik-photo-mode-2022826-151443.jpg","caption":"wepik-photo-mode-2022826-151443.jpg","width":1894,"height":1071,"formats":{"thumbnail":{"name":"thumbnail_wepik-photo-mode-2022826-151443.jpg","hash":"thumbnail_wepik_photo_mode_2022826_151443_47e1da99d5","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":7.16,"sizeInBytes":7157,"url":"https://cdn.marutitech.com//thumbnail_wepik_photo_mode_2022826_151443_47e1da99d5.jpg"},"medium":{"name":"medium_wepik-photo-mode-2022826-151443.jpg","hash":"medium_wepik_photo_mode_2022826_151443_47e1da99d5","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":424,"size":35.68,"sizeInBytes":35679,"url":"https://cdn.marutitech.com//medium_wepik_photo_mode_2022826_151443_47e1da99d5.jpg"},"small":{"name":"small_wepik-photo-mode-2022826-151443.jpg","hash":"small_wepik_photo_mode_2022826_151443_47e1da99d5","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":19.78,"sizeInBytes":19782,"url":"https://cdn.marutitech.com//small_wepik_photo_mode_2022826_151443_47e1da99d5.jpg"},"large":{"name":"large_wepik-photo-mode-2022826-151443.jpg","hash":"large_wepik_photo_mode_2022826_151443_47e1da99d5","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":565,"size":54.97,"sizeInBytes":54973,"url":"https://cdn.marutitech.com//large_wepik_photo_mode_2022826_151443_47e1da99d5.jpg"}},"hash":"wepik_photo_mode_2022826_151443_47e1da99d5","ext":".jpg","mime":"image/jpeg","size":142.75,"url":"https://cdn.marutitech.com//wepik_photo_mode_2022826_151443_47e1da99d5.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:49:06.144Z","updatedAt":"2024-12-16T11:49:06.144Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":218,"attributes":{"createdAt":"2022-09-15T07:30:48.831Z","updatedAt":"2025-06-16T10:42:13.546Z","publishedAt":"2022-09-15T10:49:48.555Z","title":"Chatbots in Real Estate: 9 Essential Benefits For Success","description":"Check how chatbots in real estate have revolutionized how we buy, sell and rent properties.","type":"Chatbot","slug":"chatbots-in-real-estate","content":[{"id":13886,"title":null,"description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":13887,"title":"1. Real-time Enquiry","description":"<p>When a user lands on your website, they can immediately get their queries answered by the chatbots. They do not have to wait for assistance from a human agent in order to seek answers about the property they are interested in.</p>","twitter_link":null,"twitter_link_text":null},{"id":13888,"title":"2. Available 24×7","description":"<p>As real estate agents have time constraints like meeting deadlines, shift timings, it is not possible for them to remain available to the user throughout the day.&nbsp;With chatbots in real estate being available round the clock, 365 days a year – your customer’s queries can be addressed even outside of operational hours.</p>","twitter_link":null,"twitter_link_text":null},{"id":13889,"title":"3. User Specific","description":"<p>Not everyone is looking for the same type of apartment or property type. Chatbots in real estate are the key element here in providing the customer exactly what they are looking for by probing the visitor with a series of questions and offering relevant information in an interactive manner. This is in stark contrast to traditional methods of collecting information via lengthy forms which in turn, keeps the user engaged till the end.</p>","twitter_link":null,"twitter_link_text":null},{"id":13890,"title":"4. Better Engagement","description":"<p>Earlier we used to have physical copies of forms given out to the people to capture the type of product they are interested in. But, truth be told, most of those forms ended up in the trashcan. They were slowly replaced by online forms, which proved to be better than their predecessors, but at the end of the day, they were still forms that required a lot of input from the customer’s side.</p><p>Forms are less interactive and are not much effective when it comes to holding the attention of the customer. Even if a lead fills out the form, they are just providing you information but are not getting any, which they are looking for.&nbsp;With chatbots in real estate, customers can engage in real-time basis, responding to their queries and at the same time, collecting information about their preferences.</p>","twitter_link":null,"twitter_link_text":null},{"id":13891,"title":"5. Better Time Management","description":"<p>The biggest plus of any robot is saving ample amount of time. Chatbots are no different. As a realtor, you will not be wasting your time in fruitless queries. You and your sales team will be dealing with a much narrower, filtered &amp; pre-qualified lead base which will save you time and effort.&nbsp;Chatbots work at the grassroot level, by interacting with each potential lead in a personalized manner save the collected information to a database.</p><p>As a realtor, you can access the database and have all the information about what the customer wants, prior to making that first call. This way, you’re only concerned with closing the deal and not spending time prospecting or answering FAQs.</p>","twitter_link":null,"twitter_link_text":null},{"id":13892,"title":"6. Automate your follow-up process","description":"<p>As a realtor, you have a lot on your plate other than following up on people who are yet to be customers. <a href=\"https://wotnot.io/\">Chatbots</a> can be very easily utilised to follow up on your leads via the medium they choose.&nbsp;Whether they want to be contacted via email or text message for more information or would directly prefer talking to the realtor, is all asked to the user.</p><p>A text message or an email will then be sent to the prospect automatically (based on what you have fed in the system), or if you would prefer to take it from there manually, then that too, is possible.</p>","twitter_link":null,"twitter_link_text":null},{"id":13893,"title":"7. History of Interaction","description":"<p>Imagine the amount of paperwork it would have taken had you documented each and every reply of each and every lead you have interacted with. Unimaginable, right? Chatbots help you out here by keeping record of all the conversations till date. Anytime you need to look up what the customer had said, you can just refer the logs stored in the system.</p><p>This way, trends can be identified between customer and bot interactions. If you want to find out if there’s a particular type of property, belonging to a particular category (area wise, budget wise etc.) garnering a lot of interest, you can do so easily using all the data stacked up in your logs.</p>","twitter_link":null,"twitter_link_text":null},{"id":13894,"title":"8. Schedule Property Viewings","description":"<p>Once the prospect is deeper into the sales funnel, the bot can schedule a home tour and in a way, take care of introducing the client and the real estate agent. At this point, the agents or sales team can take over the reigns.</p>","twitter_link":null,"twitter_link_text":null},{"id":13895,"title":"9. 360 Degree Virtual Tours","description":"<p>With prospects being too busy to see the property in person, chatbots in real estate can give interested prospects a quick virtual tour through the bot itself. This gives them a fair idea of what the property would look like before even scheduling a site visit.</p>","twitter_link":null,"twitter_link_text":null},{"id":13896,"title":null,"description":"$20","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":424,"attributes":{"name":"1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","alternativeText":"1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","caption":"1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","width":1749,"height":1140,"formats":{"thumbnail":{"name":"thumbnail_1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","hash":"thumbnail_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48","ext":".jpg","mime":"image/jpeg","path":null,"width":239,"height":156,"size":8.21,"sizeInBytes":8210,"url":"https://cdn.marutitech.com//thumbnail_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48.jpg"},"small":{"name":"small_1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","hash":"small_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":326,"size":26.82,"sizeInBytes":26815,"url":"https://cdn.marutitech.com//small_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48.jpg"},"large":{"name":"large_1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","hash":"large_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":652,"size":75.19,"sizeInBytes":75189,"url":"https://cdn.marutitech.com//large_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48.jpg"},"medium":{"name":"medium_1d5bcf1b-9-reasons-why-you-need-chatbots-in-real-estate.jpg","hash":"medium_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":489,"size":49.1,"sizeInBytes":49097,"url":"https://cdn.marutitech.com//medium_1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48.jpg"}},"hash":"1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48","ext":".jpg","mime":"image/jpeg","size":185.6,"url":"https://cdn.marutitech.com//1d5bcf1b_9_reasons_why_you_need_chatbots_in_real_estate_ff348f6d48.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:47:04.796Z","updatedAt":"2024-12-16T11:47:04.796Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1988,"title":"How a WhatsApp Chatbot helped UKHealth address COVID-19 related concerns","link":"https://marutitech.com/case-study/covid-awareness-whatsapp-chatbot/","cover_image":{"data":{"id":409,"attributes":{"name":"6 (5).png","alternativeText":"6 (5).png","caption":"6 (5).png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_6 (5).png","hash":"thumbnail_6_5_e07ef968b9","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":17.76,"sizeInBytes":17759,"url":"https://cdn.marutitech.com//thumbnail_6_5_e07ef968b9.png"},"small":{"name":"small_6 (5).png","hash":"small_6_5_e07ef968b9","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":65.02,"sizeInBytes":65022,"url":"https://cdn.marutitech.com//small_6_5_e07ef968b9.png"},"medium":{"name":"medium_6 (5).png","hash":"medium_6_5_e07ef968b9","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":149.29,"sizeInBytes":149289,"url":"https://cdn.marutitech.com//medium_6_5_e07ef968b9.png"},"large":{"name":"large_6 (5).png","hash":"large_6_5_e07ef968b9","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":271.03,"sizeInBytes":271033,"url":"https://cdn.marutitech.com//large_6_5_e07ef968b9.png"}},"hash":"6_5_e07ef968b9","ext":".png","mime":"image/png","size":91.3,"url":"https://cdn.marutitech.com//6_5_e07ef968b9.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:17.214Z","updatedAt":"2024-12-16T11:46:17.214Z"}}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]},"seo":{"id":2218,"title":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits","description":"How WhatsApp Chatbot can benefit the Real Estate? Know-how chatbots are revolutionizing the way real estate companies buy, sell, and rent properties","type":"article","url":"https://marutitech.com/whatsapp-chatbot-real-estate/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":419,"attributes":{"name":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","alternativeText":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","caption":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","width":1500,"height":750,"formats":{"thumbnail":{"name":"thumbnail_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":245,"height":123,"size":26.04,"sizeInBytes":26037,"url":"https://cdn.marutitech.com//thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"small":{"name":"small_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":500,"height":250,"size":100.01,"sizeInBytes":100012,"url":"https://cdn.marutitech.com//small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"large":{"name":"large_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":1000,"height":500,"size":393.55,"sizeInBytes":393554,"url":"https://cdn.marutitech.com//large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"medium":{"name":"medium_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":750,"height":375,"size":216.42,"sizeInBytes":216415,"url":"https://cdn.marutitech.com//medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"}},"hash":"0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","size":147.49,"url":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:49.815Z","updatedAt":"2024-12-16T11:46:49.815Z"}}}},"image":{"data":{"id":419,"attributes":{"name":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","alternativeText":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","caption":"0a9aeb65-whatsapp-real-estate-chatbot-cover.png","width":1500,"height":750,"formats":{"thumbnail":{"name":"thumbnail_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":245,"height":123,"size":26.04,"sizeInBytes":26037,"url":"https://cdn.marutitech.com//thumbnail_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"small":{"name":"small_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":500,"height":250,"size":100.01,"sizeInBytes":100012,"url":"https://cdn.marutitech.com//small_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"large":{"name":"large_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":1000,"height":500,"size":393.55,"sizeInBytes":393554,"url":"https://cdn.marutitech.com//large_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"},"medium":{"name":"medium_0a9aeb65-whatsapp-real-estate-chatbot-cover.png","hash":"medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","path":null,"width":750,"height":375,"size":216.42,"sizeInBytes":216415,"url":"https://cdn.marutitech.com//medium_0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"}},"hash":"0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10","ext":".png","mime":"image/png","size":147.49,"url":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:46:49.815Z","updatedAt":"2024-12-16T11:46:49.815Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
21:T66f,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/whatsapp-chatbot-real-estate/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#webpage","url":"https://marutitech.com/whatsapp-chatbot-real-estate/","inLanguage":"en-US","name":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits","isPartOf":{"@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#website"},"about":{"@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#primaryimage","url":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/whatsapp-chatbot-real-estate/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"How WhatsApp Chatbot can benefit the Real Estate? Know-how chatbots are revolutionizing the way real estate companies buy, sell, and rent properties"}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits"}],["$","meta","3",{"name":"description","content":"How WhatsApp Chatbot can benefit the Real Estate? Know-how chatbots are revolutionizing the way real estate companies buy, sell, and rent properties"}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$21"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/whatsapp-chatbot-real-estate/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits"}],["$","meta","9",{"property":"og:description","content":"How WhatsApp Chatbot can benefit the Real Estate? Know-how chatbots are revolutionizing the way real estate companies buy, sell, and rent properties"}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/whatsapp-chatbot-real-estate/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"}],["$","meta","14",{"property":"og:image:alt","content":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"How WhatsApp Chatbots Can Streamline Real Estate Operations: 11 Benefits"}],["$","meta","19",{"name":"twitter:description","content":"How WhatsApp Chatbot can benefit the Real Estate? Know-how chatbots are revolutionizing the way real estate companies buy, sell, and rent properties"}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//0a9aeb65_whatsapp_real_estate_chatbot_cover_3b3636ed10.png"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
