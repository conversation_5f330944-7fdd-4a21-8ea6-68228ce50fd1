3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","conversational-ui-business-communication","d"]
0:["2Mj1WjatiEVO-Cb9ObuQz",[[["",{"children":["blog",{"children":[["blogDetails","conversational-ui-business-communication","d"],{"children":["__PAGE__?{\"blogDetails\":\"conversational-ui-business-communication\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","conversational-ui-business-communication","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-bc2769982ca4f412.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-3ff356f3771c9a97.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:T6e9,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/conversational-ui-business-communication/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/conversational-ui-business-communication/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/conversational-ui-business-communication/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/conversational-ui-business-communication/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/conversational-ui-business-communication/#webpage","url":"https://marutitech.com/conversational-ui-business-communication/","inLanguage":"en-US","name":"Conversational UI - A paradigm shift in business communication","isPartOf":{"@id":"https://marutitech.com/conversational-ui-business-communication/#website"},"about":{"@id":"https://marutitech.com/conversational-ui-business-communication/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/conversational-ui-business-communication/#primaryimage","url":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/conversational-ui-business-communication/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"Conversational UI facilitate frictionless experiences for a user using a blend of language, reasoning frameworks, big data and machine learning."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Conversational UI - A paradigm shift in business communication"}],["$","meta","3",{"name":"description","content":"Conversational UI facilitate frictionless experiences for a user using a blend of language, reasoning frameworks, big data and machine learning."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$11"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/conversational-ui-business-communication/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"Conversational UI - A paradigm shift in business communication"}],["$","meta","9",{"property":"og:description","content":"Conversational UI facilitate frictionless experiences for a user using a blend of language, reasoning frameworks, big data and machine learning."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/conversational-ui-business-communication/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"Conversational UI - A paradigm shift in business communication"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"Conversational UI - A paradigm shift in business communication"}],["$","meta","19",{"name":"twitter:description","content":"Conversational UI facilitate frictionless experiences for a user using a blend of language, reasoning frameworks, big data and machine learning."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"}],["$","link","21",{"rel":"icon","href":"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
12:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-50eab2fef03e5c84.js"],""]
13:T473,<p>A Conversational UI gives the privilege of interacting with the computer on human terms. It is a paradigm shift from the earlier communications achieved either by entering syntax-specific commands or clicking icons. Conversational interface allows a user to tell the computer what to do. Conversational UI is more social in the way the user ”contacts”, ”invites” and ”messages” than the traditional apps that are technological in nature where the user downloads and installs.</p><p>Rewinding to the BC days, before chatbots arrived, customers were assisted by shop assistants during their visit to a shop. The shop assistant used pre-defined scripts to respond to customer queries. Fast forward to the AC, time after the chatbots hit the market; chatbots on a website are creating conversational websites and interacting with the customer in the same way a shop assistant would do in the past. &nbsp;Conversational UI takes two forms – <a href="https://marutitech.com/banking-need-digital-voice-assistant/" target="_blank" rel="noopener">voice assistant</a> that allows you to talk and chatbots that allow you to type.</p>14:T512,<p>Tech giants Amazon, Google, Microsoft and Google have not only introduced voice assistants but are also making the voice assistants smarter by the day. Hey Cortana from Microsoft, Ok Google from Google, Hey Siri from Apple and Echo from Amazon are classic cases of voice assistants responding to the user by voice. Users can ask these voice assistants to show the trailer of a movie, book tables at a restaurant, schedule an appointment among other things.</p><figure class="image"><img src="https://cdn.marutitech.com/Evolution.jpg" alt="Evolution of UI" srcset="https://cdn.marutitech.com/Evolution.jpg 698w, https://cdn.marutitech.com/Evolution-450x258.jpg 450w" sizes="(max-width: 698px) 100vw, 698px" width="698"></figure><p style="text-align:center;">Evolution of UI</p><p>On the Chatbot front, Facebook M is a classic example that allows real time communication. The human-assisted chatbot allows customers to do several things from transferring money to buying a car. Slack’s slackbot is another shining example of a chatbot. This human-assisted <a href="https://wotnot.io/" target="_blank" rel="noopener">chatbot</a> allows the user to do many things. If there is a slackbot for scheduling meetings, there is a slackbot for tracking coworkers’ happiness and taking lunch orders.</p>15:T42a,<p>Apple, Facebook and Mattel have one thing in common. They have all set up conversation-based interfaces powered by the AI chatbots that have come good to serve several business purposes. Yesterday, customer responses were a phone call or a web-search away. Today, it is a chatbot away. Chatbot takes its place in chat products and also serve as stand-alone interfaces to handle requests.</p><p>Take 1–800-Flowers for instance. They encourage customers to talk to a chatbot and order flowers. The company is now leveraging the natural-language ordering mechanism through Facebook Messenger to make this possible. That’s not all. 1–800-Flowers came up with a startling revelation that 70% of its Messenger orders came from new customers once it introduced the Facebook chatbot.</p><p>KLM, an international airline, allows customers to receive their boarding pass, booking confirmation, check-in details and flight status updates through Facebook Messenger. Customers can book flights on their website and opt to receive personalized messages on Messenger.</p>16:T4b7,<p>Conversational UI is evolving into the interface of the future. The conversation assistant capability made available through Nuance’s Dragon Mobile Assistant, Samsung’s S-Voice and Apple’s Siri is just the beginning. Looking into the future, language and reasoning frameworks are going to blend with big data and machine learning to give way for conversational user interfaces that better understand customer needs and wants, better understand the customer and his surroundings.</p><p>More and more business models will benefit from chatbots. Retail, media companies distributing content, research and consulting are some of the industries that will drive business value from chatbots.</p><p>Check out this video of how you can build a lead generation chatbot for your business in minutes using&nbsp;<a href="https://wotnot.io/" target="_blank" rel="noopener">WotNot’s&nbsp;chatbot development platform</a>.</p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/BwwsSlcYZKk" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>17:T53e,<p>Chatbots have been assisting humans for quite some time. If we turn back the pages of time, we can recall the introduction of the <a href="https://en.wikipedia.org/wiki/Turing_test" target="_blank" rel="noopener">Turing test in 1950 by Alan Turing</a>. The test was then published as an article titled “Computing Machinery and Intelligence” and is now considered as a criterion of intelligence.</p><p>If we’ll look at the historic chatbots like <a href="https://www.theatlantic.com/technology/archive/2014/06/when-parry-met-eliza-a-ridiculous-chatbot-conversation-from-1972/372428/" target="_blank" rel="noopener">PARRY (1972) and ELIZA (1996) </a>things were different in comparison to the recent ones like ALICE, Jabberwocky, and DUDE. However, things have changed drastically, and the chatbots are now part of the messaging platforms, apps and websites, company’s internal platform, education industry, and toys.</p><p>Despite the fact that they are today playing a role in different industries, <a href="https://chatbotsjournal.com/5-learnings-from-our-chatbot-survey-2017-72a6a4fc209c" target="_blank" rel="noopener">a report released by chatbots journal</a> says a majority of businesses didn’t hear about chatbots until 2015. The same report also reveals that 54% developers worked on these for the first time in 2016.</p>18:T4bb,<p>In the last couple of years,<a href="https://www.inman.com/2016/10/27/how-facebook-messenger-just-changed-lead-generation/" target="_blank" rel="noopener"> Facebook has changed the way leads were generated</a>. The credit undoubtedly goes to chatbots in Facebook messenger. They have encouraged third-party developers to build Bots.</p><p><a href="https://venturebeat.com/2016/06/30/facebook-messenger-now-has-11000-chatbots-for-you-to-try/" target="_blank" rel="noopener">According to a report,</a> there were more than 11,000 chatbots in Facebook Messenger. However, these Bots aren’t solely made by Facebook. It includes a significant portion of third-party developers.</p><p><a href="https://research.fb.com/publications/" target="_blank" rel="noopener">Facebook recently released research</a> which outlines their efforts in building and training artificially intelligent chatbots to negotiate with humans.</p><p>After assisting humans, <a href="http://www.wotnot.io" target="_blank" rel="noopener">chatbots</a> are now getting trained to negotiate like humans.</p><p>Before we talk further about this, let us first have a quick look at their journey till date and the contribution they have made.</p>19:Tccf,<p>Chatbots have the potential to change the way brands communicate with their consumers. However, we can’t deny the fact that both brands and consumers are relishing <a href="https://marutitech.com/benefits-chatbot/" target="_blank" rel="noopener">the benefits of chatbots equally</a>. Let us have a look at some use cases:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Shopping</span></h3><p>The online market is growing with every passing day. Chatbots can become one of the most resourceful inclusions for your retail business. They can give a personalised experience to your customers and help in generating new leads with the same. One such example is Shop Spring. The users don’t have to chat to with the bot. They simply need to choose some of the answers, and the Bot gives them narrowed options. This action empowers them to make a prompt decision and saves their time. &nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Payments</span></h3><p>It was September 2016 when Facebook Messenger allowed its users not just to do the shopping but also in making payments through systems like MasterCard, Visa, American Express, Braintree, Stripe, and PayPal. It isn’t just the ease they avail, but they also look after the security of transaction and other vital factors.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Restaurants and delivery</span></h3><p>People order food online because they don’t want to spend their time waiting in long queues. ChatBots are instantly available online at any time to take the order. Few of the best examples to this include the one deployed by Pizza Hut and Dominos. <a href="https://www.facebook.com/messages/t/FoodieYourFoodBot" target="_blank" rel="noopener">We have developed a Food ordering Bot “Foodie”. Try it out!!</a> The bots not just save time, but they are designed with a distinct sense of humour, which helps them build a healthy and engaging interaction with the customers.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Health Care</span></h3><p>Here ChatBots help the patients in booking their appointment with a doctor, store and track their payments, paying invoices, and most importantly can guide patients. The usage of <a href="https://marutitech.com/chatbots-as-your-doctors/" target="_blank" rel="noopener">ChatBots in the healthcare industry</a> can help them build a steady and long term relationship with the patients.</p><h3><a href="https://marutitech.com/chatbots-and-service-industry/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;">Customer Support</span></a></h3><p>Customer support undoubtedly takes a lot of resource from the enterprises. Not just humans but the set up for them takes away a good sum of your capital. Let us consider that you are not replacing your current customer support team entirely with the ChatBot; even partial usage can help you impeccably. They can help you realise that most of the queries are identical and also acknowledge you about the new complaints. With this, you can automate repetitive requests. The prompt reply and solution to them will indeed earn you appreciation and loyalty of your customers.</p>1a:Tfaf,<figure class="image"><img src="https://cdn.marutitech.com/chatbot_negotiation_c2cbfb7534.jpg" alt="chatbot-negotiation.jpg" srcset="https://cdn.marutitech.com/thumbnail_chatbot_negotiation_c2cbfb7534.jpg 245w,https://cdn.marutitech.com/small_chatbot_negotiation_c2cbfb7534.jpg 500w," sizes="100vw"></figure><p>Chatbots can negotiate like humans</p><p>This isn’t the first time that a question of this kind is raised. If we’ll go back the year 2015-16, the same questions were raised on ChatBots in context to helping customers. However, today we can see the contribution they are doing in different industries and the continuously increasing rate of acceptance.</p><p><a href="http://www.wotnot.io" target="_blank" rel="noopener">ChatBots</a> have indeed empowered systems to make small conversations and execute simple tasks. However, expecting them to make complex meaningful conversations with real humans, understanding the sentences and then building a sentence by self to achieve the goal, indeed sounds like a tough task.</p><p>Researchers at Facebook Artificial Intelligence Research showed us that a ray of hope through open-sourced codes and the published research. The researchers have claimed that Bots can negotiate the way humans do. Here are some key-takeaways:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Task: Multi Issue bargaining</span></h3><p>The researchers gave a multi-issue bargaining task to two agents. Both the agents were shown the same set of items and were assigned the task to divide them accordingly by negotiating a split of the terms. Both of them were given their own set of product-values to set the priority levels. The experts at FAIR created many such scenarios to test and ensured that cracking the negotiation and getting the best deal is impossible for both the agents.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Dialog Rollouts</span></h3><p>Negotiation is a mixture of both, linguistic and reasoning problem. It requires an intention for something which needs to be verbalised. Dialogue rollout is an idea formulated by FAIR researchers’ for building long term planning dialogue agents.</p><p>The Dialogue rollouts are designed keeping in mind that the agent can simulate a conversation’s future by rolling out a dialogue model. We have seen simple ideas being used in designing the game environments, but it is for the first time that it is being implemented into conversation and negotiations.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Negotiation data set</span></h3><p>The team crowdsourced a collection of negotiations amidst a couple of people to train negotiation agents and to conduct large-scale quantitative evaluations. Surprising many around, in most of the cases people did not even realise that they were interacting with a machine.</p><p style="text-align:center;">Chatbot negotiating with humans</p><p><img src="https://cdn.marutitech.com/chatbot_negotiation_1_34185e427d.jpg" alt="chatbot-negotiation machine" srcset="https://cdn.marutitech.com/thumbnail_chatbot_negotiation_1_34185e427d.jpg 208w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;">Before signing off</span></h3><p>The work portrayed in the research clearly indicates the future of chatbots. The technology will soon come adorned with more power to reason, negotiate, and converse. Basically with all key ingredients that can make a perfect digital assistant. Looking at these we can definitely expect a brighter future of ChatBots.</p><p>However, <a href="https://www.theverge.com/2017/6/14/15799068/chatbot-negotiations-ai-facebook-fair" target="_blank" rel="noopener">in one of its article</a>, the Verge warns people not to get too excited about the project as there have been instances when Bots couldn’t work up to expectations. Hence all we can say for now is that we’ll have to wait for an undefined duration to relish the essence of these chatbots.</p>1b:T790,<p>The utility industry is transforming from a highly traditional sector to a sophisticated technology-driven industry. And as an industry that works on infrastructure and provides uninterrupted basic amenities, a very less proportion of the overall budget in the utilities sector is dedicated to customer service.</p><p>In such a structure, technological tootbotls powered by artificial intelligence have come to the rescue of the utilities sector to provide impeccable customer service and cut down on operational costs. One such tool is the utilities chatbot on WhatsApp which is an implementation of customer-facing AI.</p><p>In the present scheme of things in the utilities sector, an onboarded customer is often left confused about the workings of the utilities provided and how to benefit from them in an organised setup. This confusion and a lack of direct access to information restrict the optimal usage of resources.&nbsp;</p><p>For instance, when a customer needs clarification on their billing amount, they need to look up the customer care number or an email address to get in touch with the concerned person. After scouring through different resources, when the customer manages to find the right contact number or email address, there is no guarantee when – or if at all the query will be solved timely.</p><p>With the utilities <a href="https://marutitech.com/whatsapp-business-chatbot/" target="_blank" rel="noopener">chatbot on WhatsApp</a>, customers can simply type in their queries and get instant responses to their issues. A <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp chatbot</a> can respond to different questions with relevant user information from your database.&nbsp;</p><figure class="image"><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></figure>1c:T15e5,<p>Utility chatbot on WhatsApp creates a consistent channel for connectivity and interaction for the wide customer base. This connectivity not only aids customer communication and experience but also helps in reducing operational costs.</p><p>Due to advanced process flows achieved with the help of technologies like <a href="https://marutitech.com/machine-learning-services/" target="_blank" rel="noopener">machine learning</a> and <a href="https://marutitech.com/natural-language-processing-services/" target="_blank" rel="noopener">natural language processing</a>, chatbots have the ability to monitor systems and meet customer expectations.&nbsp;</p><p>For instance, as soon as the chatbot receives an outage-related complaint, it can fetch information from the internal system and update the customer of the current status. This reduces the overall execution time, thereby improving customer satisfaction.</p><p>Elucidated below are the key use cases of the utilities sector addressed by a utilities chatbot on WhatsApp-&nbsp;</p><p><img src="https://cdn.marutitech.com/05a119be_whatsapp_utility_chatbot_973x1500_1_min_f0d23fe935.png" alt="05a119be-whatsapp-utility-chatbot-973x1500 (1)-min.png" srcset="https://cdn.marutitech.com/thumbnail_05a119be_whatsapp_utility_chatbot_973x1500_1_min_f0d23fe935.png 101w,https://cdn.marutitech.com/small_05a119be_whatsapp_utility_chatbot_973x1500_1_min_f0d23fe935.png 324w,https://cdn.marutitech.com/medium_05a119be_whatsapp_utility_chatbot_973x1500_1_min_f0d23fe935.png 487w,https://cdn.marutitech.com/large_05a119be_whatsapp_utility_chatbot_973x1500_1_min_f0d23fe935.png 649w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Payments &amp; Billing</strong></span></h3><p>It becomes difficult for the customer to manage countless unorganized bills of the utilities sector. As a result, customers struggle to review utilities bill, modify account details, and analyse pending payments.</p><p>Utility chatbot on WhatsApp improves the accounting and billing structure of the utilities sector by bridging the gaps in documentation, manual accounting, data consolidation, and data entry.</p><p>Here’s how the provider can offer billing-related benefits through a WhatsApp chatbot for utilities sector:</p><ul><li>View utilities bills<br>&nbsp;</li><li>Change account details<br>&nbsp;</li><li>Track payment history<br>&nbsp;</li><li>Inquire about late payments and additional charges<br>&nbsp;</li><li>Utilize multiple payment options&nbsp;</li></ul><figure class="image"><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></figure><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Customer Service</strong></span></h3><p>Delayed response is one of the biggest concerns that push customers to seek out other options (read: your competitors).&nbsp; Quick response is costly to achieve as it requires you to appoint more manpower in your customer support team. And yet that does not guarantee real-time response as the customer support team can only handle so many queries at a time.</p><p>A utility chatbot on WhatsApp can be used to respond to customers instantly. <a href="https://wotnot.io/" target="_blank" rel="noopener">Chatbots</a> function 24×7 and hold personalized communication with every individual, thereby cutting the waiting time for your customers.</p><p>Common customer queries that usually take the customer support team 2-3 days to address and resolve, can be resolved in minutes using utility chatbot on WhatsApp, such as:</p><ul><li>Technical support to change passwords, sign-in, or recover passwords using security questions.<br>&nbsp;</li><li>Raising installation and set-up service requests through WhatsApp.<br>&nbsp;</li><li>Scheduling a visit for maintenance and issue resolution at the customer’s location.<br>&nbsp;</li><li>Requesting start of service after proper installation.&nbsp;</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Usage Review</strong></span></h3><p>The users of the utilities sector often review the energy usage once the bill for utilities is already generated. Thus, the customer is unable to optimize energy consumption and choose the right plan according to their usage and requirement.</p><p>A utility chatbot on WhatsApp can automate usage-related updates to offer quick, real-time information to users. With the help of this chatbot, users can review and analyse the following:</p><ul><li>Check current energy usage for budgeting.<br>&nbsp;</li><li>Analyse current usage to avoid extra energy consumption.<br>&nbsp;</li><li>Review meter readings to identify meter faults.<br>&nbsp;</li><li>Receive updates about power outages in advance.</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Offers &amp; Discounts</strong></span></h3><p>Due to poor accessibility to information available in the utilities sector, many customers are unaware of the offers and other details. Hence, many offers in the utilities sector expire without providing any benefits to a large number of users.</p><p>A WhatsApp chatbot for the utilities can streamline this workflow and notify the customers about the ongoing offers and rebates. Using the bot, customers can do the following:</p><ul><li>Review points and rebates available<br>&nbsp;</li><li>Check and evaluate current energy price caps<br>&nbsp;</li><li>Utilize account credits before the expiry date</li></ul>1d:T1514,<h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Enhance Customer Experience</strong></span></h3><p>With the help of WhatsApp chatbot for utilities, you can automate live support and allow your customers to reach out to you through a medium they are well-acquainted with.</p><p>Instead of waiting on hold during a call or dropping several emails to get a simple query answered, it would be easier for your customers just to open WhatsApp messenger, convey their issue, and instantly receive a solution for the same.</p><p>WhatsApp has 1.5 million users across multiple countries. Naturally, utilizing the popular and user-friendly app to communicate with your users is a fruitful way to retain customers and enhance your brand value.</p><p>Here are some of the customer service-related benefits you can offer to your users with WhatsApp chatbot for utilities:</p><ul><li>Receive e-bills instantly</li><li>Reporting issues and complaints</li><li>Check available balance and usage</li><li>Receive updates on planned outages</li><li>Check payment dates and late payments</li><li>Change basic details, such as billing address</li><li>Reset and change the password of the account&nbsp;</li></ul><figure class="image"><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></figure><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Cost-Cutting</strong></span></h3><p>Through chatbots, you can automate and streamline workflows that enhance customer service and hence increase revenue. Automation using utilities chatbot on WhatsApp can help in the following activities:</p><ul><li>Streamlining of customer communications</li><li>Energy and usage assessment service</li><li>Online bill and account payments</li><li>Hassle-free product or service upgrade</li><li>Information related to policies</li><li>Broadcasting of special offers</li></ul><p>By upgrading to an <a href="https://marutitech.com/banking-need-digital-voice-assistant/" target="_blank" rel="noopener">automated virtual assistant</a> i.e. a utilities WhatsApp chatbot, providers can reduce time and resources on manual execution of operational activities, saving money and time in the utilities sector.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. One-Stop Shop for all Consumer Interactions</strong></span></h3><p>There are limited reasons why a consumer needs to interact with their utilities provider. It is usually general queries or complaints related to the service. With the help of a utilities chatbot on WhatsApp, all of these interactions can be put under one single shed. The bot can be programmed to perform all of the following tasks, making the process smoother, efficient, and satisfying.</p><ul><li>Queries related to bills, account details, required changes, late payments</li><li>Status of technical issues</li><li>Scheduling visits</li><li>Installation requests</li><li>Guidelines to budget and keeping a check on usage</li><li>Analysis of meter readings</li><li>Information regarding rewards, account credits, energy price caps</li></ul><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Scalable</strong></span></h3><p>There are only so many queries that your customer support team can handle at a given time. With WhatsApp chatbots, you can scale up your customer support without having to add more manpower.</p><p>What’s more, with WhatsApp chatbot for utilities, your customer support team can concentrate on solving more complex queries whereas the common queries can be addressed by the chatbot.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Easy Database Entry</strong></span></h3><p>The details collected by the chatbot can be directly fed to the internal database or CRM seamlessly. This way, you can have a consolidated view of the log, past conversations, the leads generated, common complaints registered, etc. This reduces the overheads required to manage customer service data.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Analysis</strong></span></h3><p>You can monitor the overall performance of the chatbot via chatbot analytics and figure out what is working and what is not. Unlock insights from data to create the right conversational experiences for customer service. <a href="https://wotnot.io/chatbot-analytics/" target="_blank" rel="noopener">Chatbot analytics</a> continually analyzes conversational experience, uncovering gaps, and suggesting fixes.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. Bot-to-Human Handover</strong></span></h3><p>In the case of complex queries, a human agent can instantly jump in and take over from the bot, and address the concerns of the customers using <a href="https://wotnot.io/human-handover/" target="_blank" rel="noopener">bot-to-human handover</a>. Agents can also monitor the bot conversation history allowing them to jump in with the context. This ensures smooth customer-experience resulting in happy, satisfied customers.&nbsp;</p><figure class="image"><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></figure>1e:T730,<p>The San Diego Gas and Electric Company serves more than 20 million users with their vast and comprehensive infrastructure. The company was going through a power leakage issue, which reduced customer experience and increased the cost of maintenance.</p><p>Every time the company received a complaint about this issue, they had to arrange a staff visit to the location to understand the issues.</p><p>As a solution, the company utilized AI-powered tech in their office to resolve the issue. Machine learning abilities were used to analyze and understand different datasets that were facing issues to exactly locate the outage source without sending personnel for inspection. The manpower and time which was needed to execute this operation were reduced to a great extent, which helped the company improve its customer satisfaction and brand value.</p><p>Another success story related to AI is Exelon, an electricity provider with over 10 million consumers. The company was experiencing consumer churn because the users were unable to access information easily.</p><p>The company created an AI-powered chatbot that helped its customers ask several questions and understand the information related to their utility bills and outages. Now, the organization is even able to exact insights based on the chatbot interactions, which further helps them cater to the unique requirements of the users.</p><figure class="image image_resized" style="width:50%;"><img src="https://cdn.marutitech.com/5162096b_whatsapp_450x841_ebbf0de974.png" alt="5162096b-whatsapp-450x841.png" srcset="https://cdn.marutitech.com/thumbnail_5162096b_whatsapp_450x841_ebbf0de974.png 83w,https://cdn.marutitech.com/small_5162096b_whatsapp_450x841_ebbf0de974.png 268w,https://cdn.marutitech.com/medium_5162096b_whatsapp_450x841_ebbf0de974.png 401w," sizes="100vw"></figure>1f:T74d,<p>Heavy investments in infrastructure and operations in the utilities sector often tend to put customer service in the backseat. This is changing with the proper implementation of technology.</p><p>The utilities sector is increasingly implementing <a href="https://marutitech.com/benefits-of-whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp chatbots</a> in order to streamline its customer experience and automate many service offerings. As a tool, your customers are already familiar with using, WhatsApp makes the perfect channel to facilitate quick resolution of customer queries, notify about billing, payments, and outages – making it a one-stop solution for customer queries.&nbsp;</p><figure class="image"><a href="https://wa.me/************?text=Hi" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/07e78722-whatsapp-gif-ad-banner.gif" alt=""></a></figure><p>What’s more, queries that the chatbot is not trained to solve can be seamlessly transferred to the human agent using <a href="https://wotnot.io/human-handover/" target="_blank" rel="noopener">bot-to-human handover</a>. Human agents can also monitor the bot conversation history which allows them to jump in with the context.</p><p>Utilities <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">chatbot on WhatsApp</a> can make a world of difference in improving the overall customer experience for the utilities sector. At <a href="https://marutitech.com/bot-development-services/" target="_blank" rel="noopener">Maruti Techlabs</a>, we understand the complexity of the utilities space and deliver a chatbot solution that is tailor-made to suit the use-case of your organization. Interested in exploring the possibility of your own utility chatbot over WhatsApp? Simply drop us a <NAME_EMAIL> and we’ll take it from there!</p>20:T50e,<p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Are you tired of the cheesy and corny sales emails that land in your inbox daily? Do you feel cornered by marketers and salespersons over buying a product? If you said, "Yes!" you are not alone.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Like you, several other people want a no-strings-attached human conversation with the company representative before making a purchase, filling out a form, or starting a subscription.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">That's where conversational marketing comes in. It puts the human element into marketing.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing is a new way of thinking about how you talk to your customers and prospects. It's more human, less transactional—and more effective than traditional marketing channels at creating customer loyalty and driving sales.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Let's dive deeper into conversational marketing strategies your business can implement to engage more prospects and convert leads better.</span></p>21:T558,<p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing is a process of interacting with website visitors and converting leads via dialogue-driven activities. This inbound marketing style focuses on consumer interactions, not simply one-way communication by the brand.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Sales and marketing professionals constantly strive to beat the algorithms and drive traffic and revenue. So it gets strenuous for them to provide leads and customers with a personalized experience among all the bustle.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Over the last few years, conversational marketing emerged as the perfect solution for sales and marketing worldwide.&nbsp;</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing strategy orchestrates conversations with customers wherever they are in their buying cycle (or lack thereof). As a result, you don't have to wait for long hours or days to get a response to a ticket you raised or a form you have filled out. With conversational marketing, the lines between human-to-human and human-to-computer conversations get blurred through chatbots, live chat, and targeted messaging.</span></p>22:T1a5c,<p>It is a good idea to see how chatbot technology has fared so far and the conversational marketing trends that we can expect to see at present and beyond –&nbsp;</p><p><img src="https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min.png" alt="Conversational Marketing Trends" srcset="https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min.png 1633w, https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min-768x1283.png 768w, https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min-898x1500.png 898w, https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min-422x705.png 422w, https://cdn.marutitech.com/62158dcd-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_3-min-450x752.png 450w" sizes="(max-width: 1633px) 100vw, 1633px" width="1633"></p><h3><strong>&nbsp; &nbsp;&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Messaging Platforms</strong></span></h3><p>Interacting on messaging applications is as convenient and straightforward as communicating with a friend. According to a survey, <a href="https://www.textrequest.com/blog/texting-statistics-answer-questions/" target="_blank" rel="noopener">73 trillion messages have been sent by people via chat applications</a>. As per Business Insider, Apple handles about 40 billion iMessage notifications per day worldwide.</p><p>Interactive messaging platforms like <a href="https://wotnot.io/whatsapp-chatbot/" target="_blank" rel="noopener">WhatsApp are leveraging chatbots</a> because an interactive environment encourages continuous engagement with real-time interactions.</p><p>You can expect a vast number of <a href="https://wotnot.io/" target="_blank" rel="noopener">conversational messaging</a> applications to be launched to promote conversational advertising.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. More Businesses Will Adopt Chatbots</strong></span></h3><p>Chatbots are very useful in squeezing the ROI from your marketing efforts. Chatbots are quite helpful in turning anonymous traffic into a prospect by identifying potential leads, initiating their interest in products, and cultivating customer and brand relationships. As per the <a href="https://landbot.io/blog/why-choose-chatbot-lead-generation-strategies/" target="_blank" rel="noopener">survey</a>, companies that used chatbots have increased the visitor-to-lead conversion rate by 10%.</p><p>Chatbots will be responsible for converting more visitors into potential leads. Among the reasons why businesses are betting high on bots – chatbots are available round the clock, provide instant responses to inquiries, and answers to simple questions. In the current times, we are seeing businesses deploying <a href="https://marutitech.com/nlp-based-chatbot/" target="_blank" rel="noopener">NLP based chatbots</a> that bring more human-like capabilities to the chatbot.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Customer Experience will be Highly Optimized</strong></span></h3><p>Earlier, companies used to train their sales representatives to convince customers to buy that company’s products. It is found that 46% of customers abandon a brand or product if they are not valued enough or are not happy with the customer service experience.</p><p>We have come very far in marketing. Today, priorities for companies have moved towards providing the best customer experience.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Growing Demands for Voice-Driven Interfaces</strong></span></h3><p>Siri and Alexa have taken over the market over the last couple of years. Juniper’s research states that there will be <a href="https://www.forbes.com/sites/ciocentral/2019/02/25/ai-bi-and-data-whos-going-to-win-by-2020/#52fccccd15ff" target="_blank" rel="noopener">an increase of 1000% in the usage of voice-driven interfaces</a> in the next five years. Voice-enabled chatbots work similarly to traditional text-based chatbots. The only difference is instead of typing your question or entering your input, you speak directly to the chatbot.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 5. Artificial Intelligence</strong></span></h3><p>As per <a href="https://techgrabyte.com/economic-value-artificial-intelligence-growth-impact/?fbclid=IwAR2rJDGCm3JzpPx5_Njn189kjmKlE-ph_skZdbKGl3EoRBR4zLMGQOeLTQE" target="_blank" rel="noopener">Techgrabyte</a>, artificial intelligence will be one of the sectors that will open up vast opportunities for companies over the next few decades. <a href="https://marutitech.com/artificial-intelligence-and-machine-learning/" target="_blank" rel="noopener">AI and machine learning</a> can analyze the customer’s behavior and search patterns. An<a href="https://marutitech.com/make-intelligent-chatbot/" target="_blank" rel="noopener"> intelligent chatbot</a> is one of the examples of AI in practice. In the coming years, AI-backed conversational marketing will be the driving force of customer acquisition and retention for many businesses.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 6. More Focus on Customer Data</strong></span></h3><p>A formulaic question-and-answer scenario has prevailed for a very long time, but this approach does not provide customer satisfaction and often lacks in solving customer’s problems at hand.&nbsp;</p><p>Conversational AI can assist businesses to reimagine the overall user engagement process, even in absolute basic transactions, and collect data to assist in future interactions.</p><p>For example, a chatbot for an airline can help find hotels and transportation for a customer’s trip and provide suggestions for future travel.</p><h3><strong>&nbsp; &nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> 7. Network of Knowledge</strong></span></h3><p>Knowledge is an inherent value of AI. It gives the ability for individual bots to learn from experience. Marketers and customer success teams can deploy AI to harness repositories of knowledge to provide absolute personalization for the end-user. With bots being interconnected and having access to vast data pools, businesses can deliver personalized engagement in real-time that connects with the user to provide real value.</p>23:T147f,<p>Did you know that according to research by Drift, <a href="https://winsomewriter.com/conversational-marketing-ultimate-guide/#Conversational_Marketing_Statistics_The_Trends_You_Need_to_Know_In_2021" target="_blank" rel="noopener">86%</a> of consumers choose chatbot overfilling a website form?&nbsp;</p><p>That is because humans are naturally drawn to a two-way communication that is feedback oriented, like chatbots. Messaging is fast, easy, efficient and feels much more natural and intuitive than a static form on a website. Hence, <a href="https://marutitech.com/services/interactive-experience/chatbot-development/" target="_blank" rel="noopener">conversational chatbots </a>and <a href="https://wotnot.io/live-chat-tool/" target="_blank" rel="noopener">live chat platforms</a> are some of the best ways of scaling up your company’s marketing game beyond 2022.</p><p>Below are a few more <a href="https://marutitech.com/benefits-chatbot/" target="_blank" rel="noopener">benefits of chatbot</a> and conversational marketing that every business owner, marketer and salesperson must know.</p><p><img src="https://cdn.marutitech.com/e6b5507e-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_2-min.png" alt="Benefits of Chatbot and Conversational Marketing" srcset="https://cdn.marutitech.com/e6b5507e-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_2-min.png 954w, https://cdn.marutitech.com/e6b5507e-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_2-min-768x860.png 768w, https://cdn.marutitech.com/e6b5507e-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_2-min-630x705.png 630w, https://cdn.marutitech.com/e6b5507e-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_2-min-450x504.png 450w" sizes="(max-width: 954px) 100vw, 954px" width="954"></p><h3><strong>&nbsp;&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp;1. Engage, Qualify, and Generate More Leads</strong></span></h3><p>Businesses are often prompted to hire more people to optimize conversion funnels and close more deals. Thus, increasing operational costs, cost per lead and/or diluting profits! With chatbots, you can leverage the advantages of natural-sounding human language without increasing headcount at your workplace.&nbsp;</p><p>Furthermore, the application of chatbots is not limited to customer support but extends to converting leads, closing sales, generating leads and more!&nbsp;</p><p>For example, a chatbot can bring in leads from social media and qualify them as Marketing Qualified Leads (MQLs). Then the chatbot can convert MQLs into Sales Qualified Leads (SQLs). Thus, the need for a person to qualify MQLs to SQLs is eliminated.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Understanding and Profiling Customers</strong></span></h3><p>Understanding prospects and customers are critical for a business to curate high-ROI marketing strategies. Therefore, the conventional method is to gather information manually.&nbsp;</p><p>With chatbots, businesses and websites cannot collect vast amounts of data on auto-pilot. This data is then collated intelligently by <a href="https://marutitech.com/services/artificial-intelligence-consulting/machine-learning/" target="_blank" rel="noopener">Machine Learning (ML)</a> systems and neural engines to create accurate customer profiles without any human intervention! Visitors can provide their data or indulge in mini-surveys through chatbots before actively engaging with the brand.&nbsp;</p><p>Brands can use these customer profiles to effectively segment their audience and create marketing strategies based on these segmentations. Thus, increasing their overall conversion rates and Return on Investment (ROI).&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Provide Improved and Personalized Customer Experiences</strong></span></h3><p>Conversational marketing and chatbots provide an experience to online customers and website visitors that is unique to them.&nbsp;</p><p>For example, if you have an online store, a chatbot can assist a customer in making a buying decision, just like how humans would assist walk-in customers in a physical store in the real world.&nbsp;</p><p>Chatbots can greet new visitors when they enter the website and do so much more—providing a humane experience unlike anywhere else in the digital marketplace!&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Providing Insights and Feedback on Customer Interactions</strong></span></h3><p>Chatbots can profile the customers intelligently and gather other quantitative and qualitative insights from them, like what they think about your products, website design, and more.&nbsp;</p><p>You can use these insights to tweak your website such that a more significant percentage of customers are willing to return and your churn rate decreases.&nbsp;</p><p>Additionally, chatbots communicate to the customers of any potential website updates, policy or maintenance schedules.&nbsp;</p>24:Tade,<p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing is about understanding your audience's needs and starting conversations with them. Your conversational marketing strategy should focus on being personable with customers.&nbsp;</span></p><h3><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Choose Apt Channels</span></h3><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">An important conversational marketing strategy is choosing proper channels. Understanding your customers and their demands may help you decide what channels you need and for what reason, whether it's to answer general FAQs, give information about your products or services, aid in the finalization of a transaction, or promote gated content or demonstrations.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">The channel of communication should be decided based on the presence of your target audience. The type of questions and answers your bot covers should be determined by the information your sales team requires to qualify a lead.</span></p><h3><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Personalize Your Conversations</span></h3><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">A vital part of a successful conversational marketing strategy is to personalize conversations. Customizing your dialogues according to your client's behavior and stage of the customer journey allows you to present them with the correct material through the right channels at the right time.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">People like it when they see relevant and timely information and can participate in real-time. It would be best to constantly optimize your marketing dialogues to make the most of your approach.</span></p><h3><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Complete Conversation Journey</span></h3><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversations are about nurturing relationships, not just closing sales. It is about building trust and loyalty. That's why it is essential to take care of how conversations begin and end - the tone of the messages, should your conversations point to any landing page, etc., are some points to keep in mind to better frame these conversations.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Loose-ended conversations are a waste of time for your company and your customers. Each conversation should have a defined start and a clear call to action as a part of your strategy.</span></p>25:T1160,<p>2022 has brought about several new scenarios where chatbots and conversational marketing can be used effectively. Let’s look at a few conversational marketing trends here.</p><p><img src="https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min.png" alt="use cases of conversationalmarketing" srcset="https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min.png 1633w, https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min-768x851.png 768w, https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min-1354x1500.png 1354w, https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min-636x705.png 636w, https://cdn.marutitech.com/4952c46c-7_trends_you_need_to_know_about_conversational_marketing_in_2020_-_infographic_1-min-450x498.png 450w" sizes="(max-width: 1633px) 100vw, 1633px" width="1633"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Virtual Consultation with Doctors</strong></span></h3><p>As the coronavirus pandemic rages, the healthcare sector is tremendously stressed, and the hospitals are filled to the brim with little room for less severe ailments like flu and dry cough. Hence, virtual consultation with doctors has become the norm.&nbsp;</p><p>However, the initial screening of patients like temperature reading and heartbeat measuring does not require a medically qualified expert as long as proper measuring equipment or instructions are available.&nbsp;</p><p><a href="https://marutitech.com/chatbots-as-your-doctors/" target="_blank" rel="noopener">Chatbots can assist doctors</a> by screening the patients beforehand. Hence, all the information about the patient is readily available to the doctors even before the two interact. Therefore, a lot of time and resources are saved.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Assisting Customer to Select Products on an E-Commerce Website</strong></span><strong>&nbsp;</strong></h3><p>Customers are easily overwhelmed when shopping online because of the abundance of options but no way to get close to the products in reality before making a purchase.&nbsp;</p><p>Take the example of an online clothing store. Customers may not know what kind of jeans would be the best for their unique size, height and weight. Chatbots can solve this problem by asking customer-specific questions like their favourite colours, weight and size before providing options of jeans tailored for them.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Promote Blog and Video Content</strong></span><strong>&nbsp;</strong></h3><p>Often, website visitors land on the website to learn something about your brand’s niche.&nbsp;</p><p>Maybe they are there to learn how to make noodles from your noodle shop instead of ordering a pack. In such cases, conversational marketing can market your blog posts on videos on making noodles to these customers, thereby increasing rapport and brand engagement.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Affiliate Marketing</strong></span></h3><p>In the era of large and micro-influencers, affiliate marketing is taking giant strides. If you are an influencer, marketing a product to your followers may consume a lot of time.</p><p>With chatbots, influencers can automate responding to basic queries from their fans and jump in for consultations through the bot only in particular scenarios.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 5. Keeping Fans Informed</strong></span></h3><p>Sports teams like the football club Manchester United have millions of fans across the globe. Attending all the messages of their fans on social media and websites in real-time is not practically possible. Hence, they use chatbots to naturally converse with their fans and keep them updated on all events. However, <a href="https://marutitech.com/event-chatbot/" target="_blank" rel="noopener">can chatbots really help make your event a huge success?</a> Find out the answer in our detailed blog.&nbsp;</p>26:Tb7b,<p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing is about improving the relationships with your customers and prospects and adding value to your business.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Maruti Techlabs has been one of the pioneers in the conversational marketing industry, with a&nbsp;</span><a href="https://wotnot.io/"><span style="background-color:transparent;color:#4a6ee0;font-family:Arial;"><u>no-code chatbot platform</u></span></a><span style="background-color:transparent;color:#0e101a;font-family:Arial;"> of our own. We have helped companies across the globe foster personal buying experiences for their customers, establishing loyal relationships and boosting revenues. Here's how we helped one of our clients-</span></p><h3><span style="background-color:transparent;color:#0e101a;font-family:Arial;"><strong>The Challenge</strong></span></h3><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Our client in the healthcare space was struggling with a common issue that organizations face. Due to finite resources, the client struggled to manage administrative operations like booking appointments, answering FAQs, etc.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">The result was a high workload, overworked and stressed administrative staff, and poor patient management.</span></p><h3><span style="background-color:transparent;color:#0e101a;font-family:Arial;"><strong>The Solution</strong></span></h3><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">To solve the challenge, our team decided to deploy chatbots across different channels. For this, we chose the no-code chatbot platform - WotNot, so that non-tech folks from the hospital could easily manage the bots.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">We deployed bots across two channels:</span></p><ol><li><span style="background-color:transparent;color:#0e101a;font-family:Arial;">WhatsApp</span></li><li><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Facebook Messenger.</span></li></ol><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">The chatbot helped automate and optimize booking an appointment with a specific doctor. It also answered FAQs, informed patients about various health packages, and sent targeted notifications to remind them about reports and appointments.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">The chatbot reduced the turn-around time on appointment booking by 80% and increased the sale of health packages by 65% within three months of implementing the bot. It also facilitated the round-the-clock availability of information and helped enhance the brand's image.</span></p>27:T852,<p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Conversational marketing can be extremely beneficial for you and your customers at every stage of the online customer journey.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">Whether they're a visitor interested in learning more about your brand, a prospect looking for information on your products, or an existing customer needing support with a new purchase, conversation marketing can help move them through the buyer's journey faster and shorten sales cycles. Plus, it's a great way to build trust and develop stronger customer relationships.</span></p><p><span style="background-color:transparent;color:#0e101a;font-family:Arial;">At Maruti Techlabs, we help businesses of all sizes improve customer engagement and automate business processes with&nbsp;</span><a href="https://marutitech.com/services/interactive-experience/chatbot-development/" target="_blank" rel="noopener"><span style="background-color:transparent;color:#4a6ee0;font-family:Arial;"><u>chatbot development services</u></span></a><span style="background-color:transparent;color:#0e101a;font-family:Arial;">. Write to <NAME_EMAIL>, or see how you can leverage channels like&nbsp;</span><a href="https://wotnot.io/whatsapp-chatbot/"><span style="background-color:transparent;color:#4a6ee0;font-family:Arial;"><u>WhatsApp for conversational marketing</u></span></a><span style="background-color:transparent;color:#0e101a;font-family:Arial;"> to start speaking with your customers.</span></p><figure class="image"><a href="https://marutitech.com/bot-development-services/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/0395d357-group-5618.png" alt="bot development services" srcset="https://cdn.marutitech.com/0395d357-group-5618.png 1210w, https://cdn.marutitech.com/0395d357-group-5618-768x347.png 768w, https://cdn.marutitech.com/0395d357-group-5618-705x318.png 705w, https://cdn.marutitech.com/0395d357-group-5618-450x203.png 450w" sizes="(max-width: 1210px) 100vw, 1210px" width="1210"></a></figure>2:[null,["$","$L12",null,{"blogData":{"data":[{"id":132,"attributes":{"createdAt":"2022-09-12T05:04:13.665Z","updatedAt":"2025-06-16T10:42:02.842Z","publishedAt":"2022-09-12T12:02:26.627Z","title":"Conversational UI - A paradigm shift in business communication","description":"Explore how conversational UI is the building bridge to bring the human touch to your business communication. ","type":"Chatbot","slug":"conversational-ui-business-communication","content":[{"id":13356,"title":null,"description":"<p>Ever since computing technology was introduced in the 1950s, there has been a struggle to bridge the divide between man and machine through natural spoken language. That rings true for the man-and-computer interactions too. Though computers perform complex calculation-based tasks, they lag in understanding language, until now. Conversational UI may just build the bridge – interfaces that bring a human touch with them. With Gartner predicting 2017 to be a year of conversational systems, there is going to be a rise in the demand for conversational interfaces.</p>","twitter_link":null,"twitter_link_text":null},{"id":13357,"title":"What is a conversational UI?","description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13358,"title":"A peek into voice assistant and chatbot","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13359,"title":"Why are companies betting high on Conversational UI?","description":"<p>There’s more to conversational interface than the way they recognize a&nbsp;voice. Conversational interfaces have kindled companies’ interest by presenting an intelligent interface. The intelligence does not result merely from words being recognized as text transcription, but from getting a natural-language understanding of intentions behind those words. The intelligence also combines voice technologies, artificial intelligence reasoning and contextual awareness.</p><p>The interface is platform-agnostic working well across desktop, smartphone and smartwatch. Conversational UI also work well in devices that do not have screens, as that of Amazon Echo. The most alluring feature of conversational interfaces is the way they facilitate frictionless experiences for a user working with a computer.</p>","twitter_link":null,"twitter_link_text":null},{"id":13360,"title":"How conversation interfaces serve the business purpose?","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13361,"title":"Are conversational interfaces on the rise?","description":"$16","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":441,"attributes":{"name":"business-team-discussing-their-ideas-while-working-office (1).jpg","alternativeText":"business-team-discussing-their-ideas-while-working-office (1).jpg","caption":"business-team-discussing-their-ideas-while-working-office (1).jpg","width":5717,"height":3735,"formats":{"thumbnail":{"name":"thumbnail_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":239,"height":156,"size":9.15,"sizeInBytes":9150,"url":"https://cdn.marutitech.com//thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"small":{"name":"small_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":326,"size":26.75,"sizeInBytes":26750,"url":"https://cdn.marutitech.com//small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"large":{"name":"large_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":653,"size":74.33,"sizeInBytes":74325,"url":"https://cdn.marutitech.com//large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"medium":{"name":"medium_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":490,"size":48.65,"sizeInBytes":48646,"url":"https://cdn.marutitech.com//medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"}},"hash":"business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","size":1022.42,"url":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:48:07.947Z","updatedAt":"2024-12-16T11:48:07.947Z"}}},"audio_file":{"data":null},"suggestions":{"id":1903,"blogs":{"data":[{"id":122,"attributes":{"createdAt":"2022-09-12T05:04:10.520Z","updatedAt":"2025-06-16T10:42:00.696Z","publishedAt":"2022-09-12T11:48:39.066Z","title":"Chatbots vs. Humans: Who Negotiates Better? A Comparative Study","description":"Read how chatbots can do better business negotiations compare to human agents? What are the benefits of using chatbots for closing the deal?","type":"Chatbot","slug":"can-chatbots-business-negotiations-better-human-employees","content":[{"id":13289,"title":null,"description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13290,"title":"Why are we here?","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13291,"title":"Chatbots today","description":"<p><a href=\"https://www.slideshare.net/Mobileappszen/chatbots-survey-2017-chatbot-market-research-report\" target=\"_blank\" rel=\"noopener\">According to a report</a>, 90% developers believe that businesses lack knowledge about chatbots while 75% companies believe that chatbots haven’t yet proved themselves completely.</p><p>Amidst the complete chaos, chatbots are continuously trying their best to evolve and transform the way we live, interact, and work.</p>","twitter_link":null,"twitter_link_text":null},{"id":13292,"title":"How did it start?","description":"<p>The year 2016 became the game changer for the chatbots. It was the same year when Microsoft and Facebook shared their plans to support chatbots. While Microsoft announced its Bot framework in March 2016, Facebook made the announcement in April 2016.</p><p>As a result of this, developing and releasing approved bots has now become easier with Facebook Messenger, Slack, Skype, Telegram, and few others. <a href=\"https://www.slideshare.net/Mobileappszen/chatbots-survey-2017-chatbot-market-research-report\" target=\"_blank\" rel=\"noopener\">According to a report</a>, the Facebook messenger is leading the list as the most preferred platform by 92%, followed by Slack and Twitter. The same report also shares that the top three industries taking the most benefits through bots include <a href=\"https://marutitech.com/complete-guide-chatbots/\" target=\"_blank\" rel=\"noopener\">E-commerce, Insurance, and Health care</a>.</p>","twitter_link":null,"twitter_link_text":null},{"id":13293,"title":"What are the benefits of using chatbots?","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13294,"title":"Let’s get back to where we started?","description":"<p>ChatBots have indeed proven themselves as a powerful tool to customer satisfaction and an unmatched resource for the enterprises helping them save a lot of time and money. &nbsp;</p><p>Now, getting back to Facebook’s endeavours in designing and developing Bots to make negotiations the way humans do, let us analyse the chances of the success of this research. This new technology will not only change the way we do business but also non-commercial activities. The example of non-commercial activities can include fixing meeting time. The Bots can fix up the meetings keeping in mind the availability of everyone involved in the meeting.</p>","twitter_link":null,"twitter_link_text":null},{"id":13295,"title":"Can chatbot handle negotiations like humans?","description":"$1a","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":363,"attributes":{"name":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","alternativeText":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","caption":"c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","width":1000,"height":563,"formats":{"thumbnail":{"name":"thumbnail_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"thumbnail_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":8.23,"sizeInBytes":8234,"url":"https://cdn.marutitech.com//thumbnail_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"},"small":{"name":"small_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"small_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":26.71,"sizeInBytes":26708,"url":"https://cdn.marutitech.com//small_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"},"medium":{"name":"medium_c9d44c91-can-chatbots-do-business-negotiations-better-than-human-emplyoees.jpg","hash":"medium_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":51.54,"sizeInBytes":51540,"url":"https://cdn.marutitech.com//medium_c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg"}},"hash":"c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c","ext":".jpg","mime":"image/jpeg","size":81.53,"url":"https://cdn.marutitech.com//c9d44c91_can_chatbots_do_business_negotiations_better_than_human_emplyoees_3f72ee5b8c.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:43:40.445Z","updatedAt":"2024-12-16T11:43:40.445Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":137,"attributes":{"createdAt":"2022-09-12T05:04:15.200Z","updatedAt":"2025-06-16T10:42:03.675Z","publishedAt":"2022-09-12T12:38:21.104Z","title":"WhatsApp Chatbots  - Transforming Customer Experience in the Utilities Sector","description":"Check how the utility sector implements WhatsApp chatbots to streamline its customer experience.","type":"Chatbot","slug":"utility-chatbot-on-whatsapp","content":[{"id":13386,"title":null,"description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13387,"title":"What is the need for WhatsApp Chatbot for the Utilities Sector?","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13388,"title":"Benefits of Utilities Chatbot on WhatsApp","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13389,"title":"Success Stories of AI Powered Technologies in the Utilities Sector","description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":13390,"title":"Concluding Thoughts","description":"$1f","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":3634,"attributes":{"name":"WhatsApp Chatbots.webp","alternativeText":"WhatsApp Chatbots","caption":null,"width":4373,"height":3236,"formats":{"medium":{"name":"medium_WhatsApp Chatbots.webp","hash":"medium_Whats_App_Chatbots_c42f7cf867","ext":".webp","mime":"image/webp","path":null,"width":750,"height":555,"size":46.33,"sizeInBytes":46332,"url":"https://cdn.marutitech.com/medium_Whats_App_Chatbots_c42f7cf867.webp"},"thumbnail":{"name":"thumbnail_WhatsApp Chatbots.webp","hash":"thumbnail_Whats_App_Chatbots_c42f7cf867","ext":".webp","mime":"image/webp","path":null,"width":211,"height":156,"size":6.43,"sizeInBytes":6432,"url":"https://cdn.marutitech.com/thumbnail_Whats_App_Chatbots_c42f7cf867.webp"},"small":{"name":"small_WhatsApp Chatbots.webp","hash":"small_Whats_App_Chatbots_c42f7cf867","ext":".webp","mime":"image/webp","path":null,"width":500,"height":370,"size":23.21,"sizeInBytes":23214,"url":"https://cdn.marutitech.com/small_Whats_App_Chatbots_c42f7cf867.webp"},"large":{"name":"large_WhatsApp Chatbots.webp","hash":"large_Whats_App_Chatbots_c42f7cf867","ext":".webp","mime":"image/webp","path":null,"width":1000,"height":740,"size":77.52,"sizeInBytes":77522,"url":"https://cdn.marutitech.com/large_Whats_App_Chatbots_c42f7cf867.webp"}},"hash":"Whats_App_Chatbots_c42f7cf867","ext":".webp","mime":"image/webp","size":1166.18,"url":"https://cdn.marutitech.com/Whats_App_Chatbots_c42f7cf867.webp","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-05-08T09:18:03.293Z","updatedAt":"2025-05-08T09:18:03.293Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}},{"id":140,"attributes":{"createdAt":"2022-09-12T05:04:16.232Z","updatedAt":"2025-06-16T10:42:04.056Z","publishedAt":"2022-09-12T12:35:52.637Z","title":"The Ultimate Guide To Conversational Marketing: Trends, Benefits & More","description":"Learn how businesses incorporate conversational marketing to provide a personalized experience to their customers.","type":"Chatbot","slug":"trends-need-to-know-about-conversational-marketing","content":[{"id":13399,"title":null,"description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13400,"title":"What is Conversational Marketing? ","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13401,"title":"Conversational Marketing vs. Inbound Marketing","description":"<p><span style=\"background-color:transparent;color:#0e101a;font-family:Arial;\">Conversational marketing is a subset of inbound marketing. As the word \"conversational\" suggests, your message aims to engage people in conversation.</span></p><p><span style=\"background-color:transparent;color:#0e101a;font-family:Arial;\">Inbound marketing brings leads to your website, and conversational marketing ensures they get value from it. Both strategies must align to achieve results.</span></p>","twitter_link":null,"twitter_link_text":null},{"id":13402,"title":"7 Latest Conversational Marketing Trends You Need to Know","description":"$22","twitter_link":null,"twitter_link_text":null},{"id":13403,"title":"What are the Benefits of Chatbot and Conversational Marketing? ","description":"$23","twitter_link":null,"twitter_link_text":null},{"id":13404,"title":"Conversational Marketing Strategy","description":"$24","twitter_link":null,"twitter_link_text":null},{"id":13405,"title":"What Are Some Conversational Marketing Use Cases to Drive Sales in 2022?","description":"$25","twitter_link":null,"twitter_link_text":null},{"id":13406,"title":"How Maruti Techlabs is Using Conversational Marketing","description":"$26","twitter_link":null,"twitter_link_text":null},{"id":13407,"title":"Conclusion","description":"$27","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":450,"attributes":{"name":"ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","alternativeText":"ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","caption":"ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","width":3176,"height":2000,"formats":{"thumbnail":{"name":"thumbnail_ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","hash":"thumbnail_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":154,"size":11.07,"sizeInBytes":11074,"url":"https://cdn.marutitech.com//thumbnail_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749.jpg"},"small":{"name":"small_ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","hash":"small_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":315,"size":34.42,"sizeInBytes":34425,"url":"https://cdn.marutitech.com//small_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749.jpg"},"medium":{"name":"medium_ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","hash":"medium_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":472,"size":63.92,"sizeInBytes":63915,"url":"https://cdn.marutitech.com//medium_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749.jpg"},"large":{"name":"large_ai-chatbot-smart-digital-customer-service-application-concept (2) (1).jpg","hash":"large_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":630,"size":100.45,"sizeInBytes":100449,"url":"https://cdn.marutitech.com//large_ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749.jpg"}},"hash":"ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749","ext":".jpg","mime":"image/jpeg","size":484.07,"url":"https://cdn.marutitech.com//ai_chatbot_smart_digital_customer_service_application_concept_2_1_58c9050749.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:48:52.039Z","updatedAt":"2024-12-16T11:48:52.039Z"}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1903,"title":"How a WhatsApp Chatbot helped UKHealth address COVID-19 related concerns","link":"https://marutitech.com/case-study/covid-awareness-whatsapp-chatbot/","cover_image":{"data":{"id":679,"attributes":{"name":"6.png","alternativeText":"6.png","caption":"6.png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_6.png","hash":"thumbnail_6_388a33dabd","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":17.76,"sizeInBytes":17759,"url":"https://cdn.marutitech.com//thumbnail_6_388a33dabd.png"},"small":{"name":"small_6.png","hash":"small_6_388a33dabd","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":65.02,"sizeInBytes":65022,"url":"https://cdn.marutitech.com//small_6_388a33dabd.png"},"medium":{"name":"medium_6.png","hash":"medium_6_388a33dabd","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":149.29,"sizeInBytes":149289,"url":"https://cdn.marutitech.com//medium_6_388a33dabd.png"},"large":{"name":"large_6.png","hash":"large_6_388a33dabd","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":271.03,"sizeInBytes":271033,"url":"https://cdn.marutitech.com//large_6_388a33dabd.png"}},"hash":"6_388a33dabd","ext":".png","mime":"image/png","size":91.3,"url":"https://cdn.marutitech.com//6_388a33dabd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-31T09:40:28.212Z","updatedAt":"2024-12-31T09:40:28.212Z"}}}},"authors":{"data":[{"id":9,"attributes":{"createdAt":"2022-09-02T07:14:58.840Z","updatedAt":"2025-06-16T10:42:34.188Z","publishedAt":"2022-09-02T07:15:00.198Z","name":"Mirant Hingrajia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mirant is a Technical Architect at Maruti Techlabs. From overseeing the design of enterprise applications to solving problems at the implementation level, he is the go-to person for all things software.</span></p>","slug":"mirant-hingrajia","linkedin_link":"https://www.linkedin.com/in/mirant/","twitter_link":"https://twitter.com/Mirant208","image":{"data":[{"id":524,"attributes":{"name":"Mirant Hingrajia.jpg","alternativeText":"Mirant Hingrajia.jpg","caption":"Mirant Hingrajia.jpg","width":2160,"height":2160,"formats":{"small":{"name":"small_Mirant Hingrajia.jpg","hash":"small_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.01,"sizeInBytes":23014,"url":"https://cdn.marutitech.com//small_Mirant_Hingrajia_a1b8e64c54.jpg"},"thumbnail":{"name":"thumbnail_Mirant Hingrajia.jpg","hash":"thumbnail_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.09,"sizeInBytes":4090,"url":"https://cdn.marutitech.com//thumbnail_Mirant_Hingrajia_a1b8e64c54.jpg"},"medium":{"name":"medium_Mirant Hingrajia.jpg","hash":"medium_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":51.04,"sizeInBytes":51038,"url":"https://cdn.marutitech.com//medium_Mirant_Hingrajia_a1b8e64c54.jpg"},"large":{"name":"large_Mirant Hingrajia.jpg","hash":"large_Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":95.23,"sizeInBytes":95233,"url":"https://cdn.marutitech.com//large_Mirant_Hingrajia_a1b8e64c54.jpg"}},"hash":"Mirant_Hingrajia_a1b8e64c54","ext":".jpg","mime":"image/jpeg","size":357.47,"url":"https://cdn.marutitech.com//Mirant_Hingrajia_a1b8e64c54.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:54:56.751Z","updatedAt":"2024-12-16T11:54:56.751Z"}}]}}}]},"seo":{"id":2133,"title":"Conversational UI - A paradigm shift in business communication","description":"Conversational UI facilitate frictionless experiences for a user using a blend of language, reasoning frameworks, big data and machine learning.","type":"article","url":"https://marutitech.com/conversational-ui-business-communication/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":441,"attributes":{"name":"business-team-discussing-their-ideas-while-working-office (1).jpg","alternativeText":"business-team-discussing-their-ideas-while-working-office (1).jpg","caption":"business-team-discussing-their-ideas-while-working-office (1).jpg","width":5717,"height":3735,"formats":{"thumbnail":{"name":"thumbnail_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":239,"height":156,"size":9.15,"sizeInBytes":9150,"url":"https://cdn.marutitech.com//thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"small":{"name":"small_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":326,"size":26.75,"sizeInBytes":26750,"url":"https://cdn.marutitech.com//small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"large":{"name":"large_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":653,"size":74.33,"sizeInBytes":74325,"url":"https://cdn.marutitech.com//large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"medium":{"name":"medium_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":490,"size":48.65,"sizeInBytes":48646,"url":"https://cdn.marutitech.com//medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"}},"hash":"business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","size":1022.42,"url":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:48:07.947Z","updatedAt":"2024-12-16T11:48:07.947Z"}}}},"image":{"data":{"id":441,"attributes":{"name":"business-team-discussing-their-ideas-while-working-office (1).jpg","alternativeText":"business-team-discussing-their-ideas-while-working-office (1).jpg","caption":"business-team-discussing-their-ideas-while-working-office (1).jpg","width":5717,"height":3735,"formats":{"thumbnail":{"name":"thumbnail_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":239,"height":156,"size":9.15,"sizeInBytes":9150,"url":"https://cdn.marutitech.com//thumbnail_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"small":{"name":"small_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":326,"size":26.75,"sizeInBytes":26750,"url":"https://cdn.marutitech.com//small_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"large":{"name":"large_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":653,"size":74.33,"sizeInBytes":74325,"url":"https://cdn.marutitech.com//large_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"},"medium":{"name":"medium_business-team-discussing-their-ideas-while-working-office (1).jpg","hash":"medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":490,"size":48.65,"sizeInBytes":48646,"url":"https://cdn.marutitech.com//medium_business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg"}},"hash":"business_team_discussing_their_ideas_while_working_office_1_65a170c6c5","ext":".jpg","mime":"image/jpeg","size":1022.42,"url":"https://cdn.marutitech.com//business_team_discussing_their_ideas_while_working_office_1_65a170c6c5.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:48:07.947Z","updatedAt":"2024-12-16T11:48:07.947Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
