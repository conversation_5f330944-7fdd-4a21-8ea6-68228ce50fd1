'use client';

import { ReactNode, useEffect } from 'react';
import useHashNavigation from '@hooks/useHashNavigation';
import { initializeHashNavigation } from '@utils/hashNavigationFallback';

interface EnhancedHashNavigationWrapperProps {
  children: ReactNode;
}

/**
 * Enhanced client-side wrapper component that enables robust hash navigation for static sites
 * This component combines the standard hash navigation hook with fallback strategies
 * for better reliability on static hosted sites
 */
export default function EnhancedHashNavigationWrapper({
  children,
}: EnhancedHashNavigationWrapperProps) {
  // Initialize the standard hash navigation hook
  useHashNavigation();

  // Add enhanced fallback strategies
  useEffect(() => {
    // Initialize comprehensive hash navigation with fallbacks
    // Enable debug mode in development
    const isDebug =
      process.env.NODE_ENV === 'development' ||
      (typeof window !== 'undefined' &&
        window.location.hostname === 'localhost');
    initializeHashNavigation(isDebug);
  }, []);

  return <>{children}</>;
}
