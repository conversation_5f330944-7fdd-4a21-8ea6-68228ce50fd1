"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2946],{64561:function(e,t,n){n.d(t,{Z:function(){return F}});var o,r=n(16480),l=n.n(r),i=n(59390),a=n(97550),s=n(69275),c=n(44990);function u(e){if((!o&&0!==o||e)&&a.Z){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),o=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return o}var d=n(43756),f=n(45832),p=n(1564),g=n(40343),m=n(82562),y=n(2265),v=n(70133),h=n(46579),E=n(83534),b=n(12865),N=n(57437);let w=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r="div",...i}=e;return o=(0,b.vE)(o,"modal-body"),(0,N.jsx)(r,{ref:t,className:l()(n,o),...i})});w.displayName="ModalBody";var S=n(14272);let x=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,contentClassName:r,centered:i,size:a,fullscreen:s,children:c,scrollable:u,...d}=e;n=(0,b.vE)(n,"modal");let f="".concat(n,"-dialog"),p="string"==typeof s?"".concat(n,"-fullscreen-").concat(s):"".concat(n,"-fullscreen");return(0,N.jsx)("div",{...d,ref:t,className:l()(f,o,a&&"".concat(n,"-").concat(a),i&&"".concat(f,"-centered"),u&&"".concat(f,"-scrollable"),s&&p),children:(0,N.jsx)("div",{className:l()("".concat(n,"-content"),r),children:c})})});x.displayName="ModalDialog";let O=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r="div",...i}=e;return o=(0,b.vE)(o,"modal-footer"),(0,N.jsx)(r,{ref:t,className:l()(n,o),...i})});O.displayName="ModalFooter";var Z=n(94241);let M=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,closeLabel:r="Close",closeButton:i=!1,...a}=e;return n=(0,b.vE)(n,"modal-header"),(0,N.jsx)(Z.Z,{ref:t,...a,className:l()(o,n),closeLabel:r,closeButton:i})});M.displayName="ModalHeader";let I=(0,n(89764).Z)("h4"),j=y.forwardRef((e,t)=>{let{className:n,bsPrefix:o,as:r=I,...i}=e;return o=(0,b.vE)(o,"modal-title"),(0,N.jsx)(r,{ref:t,className:l()(n,o),...i})});function D(e){return(0,N.jsx)(E.Z,{...e,timeout:null})}function R(e){return(0,N.jsx)(E.Z,{...e,timeout:null})}j.displayName="ModalTitle";let k=y.forwardRef((e,t)=>{let{bsPrefix:n,className:o,style:r,dialogClassName:E,contentClassName:w,children:O,dialogAs:Z=x,"data-bs-theme":M,"aria-labelledby":I,"aria-describedby":j,"aria-label":k,show:F=!1,animation:T=!0,backdrop:C=!0,keyboard:A=!0,onEscapeKeyDown:L,onShow:P,onHide:U,container:_,autoFocus:B=!0,enforceFocus:H=!0,restoreFocus:z=!0,restoreFocusOptions:W,onEntered:K,onExit:q,onExiting:G,onEnter:J,onEntering:Q,onExited:V,backdropClassName:X,manager:Y,...$}=e,[ee,et]=(0,y.useState)({}),[en,eo]=(0,y.useState)(!1),er=(0,y.useRef)(!1),el=(0,y.useRef)(!1),ei=(0,y.useRef)(null),[ea,es]=(0,d.Z)(),ec=(0,p.Z)(t,es),eu=(0,f.Z)(U),ed=(0,b.SC)();n=(0,b.vE)(n,"modal");let ef=(0,y.useMemo)(()=>({onHide:eu}),[eu]);function ep(){return Y||(0,h.t)({isRTL:ed})}function eg(e){if(!a.Z)return;let t=ep().getScrollbarWidth()>0,n=e.scrollHeight>(0,s.Z)(e).documentElement.clientHeight;et({paddingRight:t&&!n?u():void 0,paddingLeft:!t&&n?u():void 0})}let em=(0,f.Z)(()=>{ea&&eg(ea.dialog)});(0,g.Z)(()=>{(0,c.Z)(window,"resize",em),null==ei.current||ei.current()});let ey=()=>{er.current=!0},ev=e=>{er.current&&ea&&e.target===ea.dialog&&(el.current=!0),er.current=!1},eh=()=>{eo(!0),ei.current=(0,m.Z)(ea.dialog,()=>{eo(!1)})},eE=e=>{e.target===e.currentTarget&&eh()},eb=e=>{if("static"===C){eE(e);return}if(el.current||e.target!==e.currentTarget){el.current=!1;return}null==U||U()},eN=(0,y.useCallback)(e=>(0,N.jsx)("div",{...e,className:l()("".concat(n,"-backdrop"),X,!T&&"show")}),[T,X,n]),ew={...r,...ee};return ew.display="block",(0,N.jsx)(S.Z.Provider,{value:ef,children:(0,N.jsx)(v.Z,{show:F,ref:ec,backdrop:C,container:_,keyboard:!0,autoFocus:B,enforceFocus:H,restoreFocus:z,restoreFocusOptions:W,onEscapeKeyDown:e=>{A?null==L||L(e):(e.preventDefault(),"static"===C&&eh())},onShow:P,onHide:U,onEnter:(e,t)=>{e&&eg(e),null==J||J(e,t)},onEntering:(e,t)=>{null==Q||Q(e,t),(0,i.ZP)(window,"resize",em)},onEntered:K,onExit:e=>{null==ei.current||ei.current(),null==q||q(e)},onExiting:G,onExited:e=>{e&&(e.style.display=""),null==V||V(e),(0,c.Z)(window,"resize",em)},manager:ep(),transition:T?D:void 0,backdropTransition:T?R:void 0,renderBackdrop:eN,renderDialog:e=>(0,N.jsx)("div",{role:"dialog",...e,style:ew,className:l()(o,n,en&&"".concat(n,"-static"),!T&&"show"),onClick:C?eb:void 0,onMouseUp:ev,"data-bs-theme":M,"aria-label":k,"aria-labelledby":I,"aria-describedby":j,children:(0,N.jsx)(Z,{...$,onMouseDown:ey,className:E,contentClassName:w,children:O})})})})});k.displayName="Modal";var F=Object.assign(k,{Body:w,Header:M,Title:j,Footer:O,Dialog:x,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150})},66679:function(e,t,n){n.d(t,{Z:function(){return r}});let o={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function r(){let e,t,n,l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=!1,a=!0,s=!1,c=0;function u(){if(n||!a)return;i||t.emit("autoplay:play");let{ownerWindow:o}=t.internalEngine();o.clearInterval(c),c=o.setInterval(m,e.delay),i=!0}function d(){if(n)return;i&&t.emit("autoplay:stop");let{ownerWindow:e}=t.internalEngine();e.clearInterval(c),c=0,i=!1}function f(){if(p())return a=i,d();a&&u()}function p(){let{ownerDocument:e}=t.internalEngine();return"hidden"===e.visibilityState}function g(e){void 0!==e&&(s=e),a=!0,u()}function m(){let{index:n}=t.internalEngine(),o=n.clone().add(1).get(),r=t.scrollSnapList().length-1;e.stopOnLastSnap&&o===r&&d(),t.canScrollNext()?t.scrollNext(s):t.scrollTo(0,s)}return{name:"autoplay",options:l,init:function(i,c){t=i;let{mergeOptions:g,optionsAtMedia:m}=c,y=g(o,r.globalOptions);if(e=m(g(y,l)),t.scrollSnapList().length<=1)return;s=e.jump,n=!1;let{eventStore:v,ownerDocument:h}=t.internalEngine(),E=t.rootNode(),b=e.rootNode&&e.rootNode(E)||E,N=t.containerNode();t.on("pointerDown",d),e.stopOnInteraction||t.on("pointerUp",u),e.stopOnMouseEnter&&(v.add(b,"mouseenter",()=>{a=!1,d()}),e.stopOnInteraction||v.add(b,"mouseleave",()=>{a=!0,u()})),e.stopOnFocusIn&&(v.add(N,"focusin",d),e.stopOnInteraction||v.add(N,"focusout",u)),v.add(h,"visibilitychange",f),e.playOnInit&&!p()&&u()},destroy:function(){t.off("pointerDown",d).off("pointerUp",u),d(),n=!0,i=!1},play:g,stop:function(){i&&d()},reset:function(){i&&g()},isPlaying:function(){return i}}}r.globalOptions=void 0},8102:function(e,t,n){function o(e,t,n){return Math.min(Math.max(e,t),n)}function r(e){return"number"==typeof e&&!isNaN(e)}function l(){let e,t,n,l,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=[],s=0,c=0,u=0,d=!1;function f(){y(e.selectedScrollSnap(),1)}function p(){d=!1}function g(){d=!1,s=0,c=0}function m(){let t=e.internalEngine().scrollBody.duration();c=t?0:1,d=!0,t||f()}function y(t,n){e.scrollSnapList().forEach((r,l)=>{let i=Math.abs(n),c=a[l],f=l===t,p=o(f?c+i:c-i,0,1);a[l]=p;let g=f&&d,m=e.previousScrollSnap();g&&(a[m]=1-p),f&&function(t,n){let{index:o,dragHandler:r,scrollSnaps:l}=e.internalEngine(),i=r.pointerDown(),a=1/(l.length-1),c=t,d=i?e.selectedScrollSnap():e.previousScrollSnap();if(i&&c===d){let e=-1*Math.sign(s);c=d,d=o.clone().set(d).add(e).get()}u=d*a+(c-d)*a*n}(t,p),function(t){let n=e.internalEngine().slideRegistry[t],{scrollSnaps:o,containerRect:r}=e.internalEngine(),l=a[t];n.forEach(n=>{let i=e.slideNodes()[n].style,a=parseFloat(l.toFixed(2)),s=a>0,c=function(t){let{axis:n}=e.internalEngine(),o=n.scroll.toUpperCase();return"translate".concat(o,"(").concat(n.direction(t),"px)")}(s?o[t]:r.width+2);s&&(i.transform=c),i.opacity=a.toString(),i.pointerEvents=l>.5?"auto":"none",s||(i.transform=c)})}(l)})}function v(){let{dragHandler:t,index:n,scrollBody:o}=e.internalEngine(),r=e.selectedScrollSnap();if(!t.pointerDown())return r;let l=Math.sign(o.velocity()),i=Math.sign(s),a=n.clone().set(r).add(-1*l).get();return l&&i?i===l?a:r:null}let h=n=>{let{dragHandler:o,scrollBody:l}=n.internalEngine(),i=o.pointerDown(),u=l.velocity(),d=l.duration(),f=v(),p=!r(f);if(i){if(!u)return;s+=u,c=Math.abs(u/t),function(t){let{scrollSnaps:n,location:o,target:l}=e.internalEngine();r(t)&&!(a[t]<.5)&&(o.set(n[t]),l.set(o))}(f)}if(!i){if(!d||p)return;c+=(1-a[f])/d,c*=.68}p||y(f,c)};function E(){let{target:t,location:n}=e.internalEngine(),o=t.get()-n.get(),l=v(),i=!r(l);return h(e),!(i||Math.abs(o)>=1)&&a[l]>.999}function b(){return u}return{name:"fade",options:i,init:function(r){let i=(e=r).selectedScrollSnap(),{scrollBody:s,containerRect:c,axis:u}=e.internalEngine();t=o(.75*u.measureSize(c),200,500),d=!1,a=e.scrollSnapList().map((e,t)=>t===i?1:0),n=s.settled,l=e.scrollProgress,s.settled=E,e.scrollProgress=b,e.on("select",m).on("slideFocus",f).on("pointerDown",g).on("pointerUp",p),function(){let{translate:t,slideLooper:n}=e.internalEngine();t.clear(),t.toggleActive(!1),n.loopPoints.forEach(e=>{let{translate:t}=e;t.clear(),t.toggleActive(!1)})}(),f()},destroy:function(){let{scrollBody:t}=e.internalEngine();t.settled=n,e.scrollProgress=l,e.off("select",m).off("slideFocus",f).off("pointerDown",g).off("pointerUp",p),e.slideNodes().forEach(e=>{let t=e.style;t.opacity="",t.transform="",t.pointerEvents="",e.getAttribute("style")||e.removeAttribute("style")})}}}n.d(t,{Z:function(){return l}}),l.globalOptions=void 0}}]);