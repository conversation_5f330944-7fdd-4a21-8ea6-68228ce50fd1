exports.id=3676,exports.ids=[3676],exports.modules={70048:e=>{e.exports={"breakpoint-xs":"0","breakpoint-sm-195":"195px","breakpoint-sm-270":"270px","breakpoint-sm-200":"200px","breakpoint-sm-320":"320px","breakpoint-sm-326":"326px","breakpoint-sm-390":"390px","breakpoint-sm-367":"367px","breakpoint-sm-365":"365px","breakpoint-sm-340":"340px","breakpoint-sm-350":"350px","breakpoint-sm-370":"370px","breakpoint-sm-380":"380px","breakpoint-sm-424":"424px","breakpoint-sm-427":"427px","breakpoint-sm-420":"420px","breakpoint-sm-430":"430px","breakpoint-sm-450":"450px","breakpoint-sm-460":"460px","breakpoint-sm-484":"484px","breakpoint-sm-480":"480px","breakpoint-sm-532":"532px","breakpoint-sm-550":"550px","breakpoint-sm":"576px","breakpoint-md-579":"579px","breakpoint-md-585":"585px","breakpoint-md-767":"767px","breakpoint-md":"768px","breakpoint-md-769":"769px","breakpoint-md-820":"820px","breakpoint-md-850":"850px","breakpoint-lg-901":"901px","breakpoint-lg":"992px","breakpoint-lg-991px":"991px","breakpoint-xl-1024":"1024px","breakpoint-xl-1051":"1051px","breakpoint-xl-1208":"1208px","breakpoint-xl-1023":"1023px","breakpoint-xl-1199":"1199px","breakpoint-xl-1188":"1188px","breakpoint-xl":"1200px","breakpoint-xl-1365":"1365px","breakpoint-xl-1366":"1366px","breakpoint-xl-1309":"1309px","breakpoint-xl-1400":"1400px","breakpoint-xl-1439":"1439px","breakpoint-xl-1440":"1440px","breakpoint-xl-1405":"1405px","breakpoint-xl-1406":"1406px","breakpoint-xl-1600":"1600px","breakpoint-xl-1800":"1800px","breakpoint-xl-2000":"2000px","breakpoint-xl-2100":"2100px","breakpoint-xl-2442":"2442px","breakpoint-xl-2559":"2559px","breakpoint-xl-2560":"2560px"}},24640:e=>{e.exports={brandColorOne:"#FEBE10",brandColorTwo:"#F47A37",brandColorThree:"#F05443",brandColorFour:"#D91A5F",brandColorFive:"#B41F5E",colorBlack:"#000000",colorWhite:"#FFFFFF",gray:"#202020",gray100:"#FCFCFC",gray200:"#F8F8F8",gray300:"#F3F3F3",gray400:"#E4E4E4",gray500:"#CDCDCD",gray600:"#B1B1B1",gray700:"#808080",gray800:"#646464",gray900:"#3A3A3A",error:"#FF6D60",success:"#23A881",grayBorder:"#8C8B8B",link:"#0075FF",grayBlueFonts:"#262531",grayFonts:"#C3C3C3",grayBg:"#F5F5F5",halfSpace:"4px",oneSpace:"8px",twoSpace:"16px",threeSpace:"24px",fourSpace:"32px",fiveSpace:"40px",sixSpace:"48px",eightSpace:"64px",tenSpace:"80px",fifteenSpace:"120px",twentyFiveSpace:"200px",h1FontSize:"78px",h1MobileFontSize:"48px",h2FontSize:"64px",h2MobileFontSize:"44px",h3FontSize:"52px",h3MobileFontSize:"40px",h4FontSize:"40px",h4MobileFontSize:"28px",h5FontSize:"32px",h5MobileFontSize:"22px",h6FontSize:"24px",h6MobileFontSize:"18px",bodyHeadingXL:"56px",bodyHeadingL:"24px",bodyHeadingM:"21px",bodyHeadingS:"20px",bodyHeadingXS:"18px",bodyHeadingXSS:"16px",buttonLabelXLargeFontSize:"26px",buttonLabelLargeFontSize:"20px",buttonLabelMediumFontSize:"16px",buttonLabelSmallFontSize:"14px",bodyTextXLarge:"26px",bodyTextLarge:"22px",bodyTextMedium:"20px",bodyTextSmall:"18px",bodyTextXSmall:"16px",bodyTextXXSmall:"14px",bodyTextXXXSSmall:"8px",bodyLinkXXLarge:"26px",bodyLinkXLarge:"22px",bodyLinkLarge:"19px",bodyLinkMedium:"18px",bodyLinkSmall:"17px",bodyLinkXSmall:"16px",bodyLinkXXSmall:"15px",fontWeight100:"100",fontWeight200:"200",fontWeight300:"300",fontWeight400:"400",fontWeight500:"500",fontWeight600:"600",fontWeight700:"700",fontWeight800:"800",fontWeight900:"900"}},21498:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},8936:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12704,23)),Promise.resolve().then(r.bind(r,38354)),Promise.resolve().then(r.bind(r,4774)),Promise.resolve().then(r.bind(r,22717)),Promise.resolve().then(r.bind(r,29459)),Promise.resolve().then(r.bind(r,9666)),Promise.resolve().then(r.bind(r,137))},62554:(e,t,r)=>{Promise.resolve().then(r.bind(r,13932))},13932:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(95344);r(3729);var n=r(55846),i=r(15270),o=r.n(i),s=r(89410),l=r(60646),c=r(8428);function d(){let e=(0,c.useRouter)();return(0,a.jsxs)(l.default,{fluid:!0,className:o().container,children:[a.jsx(s.default,{src:"https://dev-cdn.marutitech.com/cuate_1745166738.svg",className:o().image,alt:"404",width:666,height:666}),a.jsx(n.Z,{className:o().button,type:"button",onClick:()=>{e.push("/")},children:(0,a.jsxs)("div",{className:o().backToHomeButton,children:[a.jsx(s.default,{src:"https://dev-cdn.marutitech.com/arrow_left_1c4ee29f6b.svg",width:24,height:24,alt:"arrow"}),a.jsx("span",{children:"Back To Home"})]})})]})}},4774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(3729);let n=function(){return(0,a.useEffect)(()=>{r(42525)},[]),null}},55846:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(95344),n=r(56506),i=r(22281),o=r(98775),s=r.n(o);function l({label:e="",className:t="",type:r="button",isLink:o=!1,leftIcon:l=null,rightIcon:c=null,href:d="",children:p=null,isExternal:h=!1,onClick:x=()=>{},dataID:m=null,onMouseDown:u=()=>{},onMouseUp:b=()=>{},onTouchStart:_=()=>{},onTouchEnd:k=()=>{},scrollToForm:g}){let f=(0,a.jsxs)("div",{className:s().innerWrapper,children:[l&&a.jsx("span",{className:s().leftWrapper,children:l}),e,c&&a.jsx("span",{className:s().rightWrapper,children:c})]}),v=e=>{g&&g(),x&&x(e)};return o?a.jsx(n.default,{href:d,target:h?"_blank":"_self",rel:h?"noreferrer":null,className:(0,i.Z)(s().link,t),"data-id":m,onClick:x,children:a.jsx("div",{children:f})}):(0,a.jsxs)("button",{type:r,className:(0,i.Z)(s().button,t),"data-id":m,onClick:e=>v(e),onMouseDown:u,onMouseUp:b,onTouchStart:_,onTouchEnd:k,children:[f,p]})}},22717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(95344),n=r(3729),i=r(89410),o=r(12100),s=r.n(o);function l({variant:e="large",scroll_to:t="false"}){let[r,o]=(0,n.useState)(!1);!0===t&&(0,n.useEffect)(()=>{let e=()=>{window.scrollY>600?o(!0):o(!1)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]);let{circle:l,arrow:c}={large:{circle:{url:"https://dev-cdn.marutitech.com/circle_large_5d770e7963.svg",width:98,height:98},arrow:{url:"https://dev-cdn.marutitech.com/arrow_large_4c0720e1c9.svg",width:53,height:53}},medium:{circle:{url:"https://dev-cdn.marutitech.com/circle_medium_cc9c77e620.svg",width:77,height:77},arrow:{url:"https://dev-cdn.marutitech.com/arrow_medium_2fd9472b7f.svg",width:43,height:43}},small:{circle:{url:"https://dev-cdn.marutitech.com/circle_small_b122f67035.svg",width:52,height:52},arrow:{url:"https://dev-cdn.marutitech.com/arrow_small_113c2e8618.svg",width:30,height:30}},scroll_to_top:{circle:{url:"https://dev-cdn.marutitech.com/Ellipse_2_Stroke_c048e39778.svg",width:60,height:60},arrow:{url:"https://dev-cdn.marutitech.com/Group_5_b251b9ec9d.svg",width:35,height:34}}}[e];return(0,a.jsxs)(a.Fragment,{children:[t&&r&&(0,a.jsxs)("div",{className:s().container,style:{width:l.width,height:l.height},onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},children:[a.jsx("div",{className:s().circle,children:a.jsx(i.default,{src:l.url,alt:"Circle",width:l.width,height:l.height})}),a.jsx("div",{className:s().arrow_scroll,style:{width:c.width,height:c.height},children:a.jsx(i.default,{src:c.url,alt:"Arrow",width:c.width,height:c.height,className:s().arrowImage})})]}),"false"===t&&(0,a.jsxs)("div",{className:s().container,style:{width:l.width,height:l.height},children:[a.jsx("div",{className:s().circle,children:a.jsx(i.default,{src:l.url,alt:"Circle",width:l.width,height:l.height})}),a.jsx("div",{className:s().arrow,style:{width:c.width,height:c.height},children:a.jsx(i.default,{src:c.url,alt:"Arrow",width:c.width,height:c.height,className:s().arrowImage})})]})]})}},29459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(95344),n=r(40934),i=r(56506);function o(){return a.jsx(a.Fragment,{children:(0,a.jsxs)(n.ZP,{debug:!1,style:{padding:"12px",background:"#59595990",textAlign:"center",display:"flex",justifyContent:"center",alignItems:"center",gap:"8px",fontSize:"12px",zIndex:"1111"},buttonStyle:{background:"#000000",color:"white",borderRadius:"6px",margin:"0px"},buttonText:"Ok",enableDeclineButton:!0,declineButtonText:"\xd7",declineButtonStyle:{background:"transparent",alignSelf:"anchor-center",color:"#ffffff",fontSize:"22px",padding:"0",margin:"0",position:"absolute",right:"1rem"},hideOnAccept:!0,children:["We use cookies to improve your browsing experience. Learn about our"," ",a.jsx(i.default,{href:"/privacy-policy/",children:"Privacy policy"}),"."]})})}},9666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(95344),n=r(60646),i=r(89410),o=r(56506),s=r(81473),l=r(29),c=r.n(l);function d({footerData:e}){let t=e.data.attributes;return a.jsx(n.default,{fluid:!0,className:c().main_container,"data-crawler-ignore":!0,children:(0,a.jsxs)(n.default,{className:c().inner_container,children:[(0,a.jsxs)("div",{className:c().first_second_row,children:[a.jsx("div",{className:c().firstrow,children:t?.sector_row?.map(e=>a.jsxs("div",{className:c().column,children:[a.jsx(o.default,{href:e?.link||"/",passHref:!0,prefetch:!1,className:c().title_firstrow,children:a.jsx(s.Z,{headingType:"h4",title:e?.title})},e?.id),a.jsx("ul",{className:c().link_title,children:e?.Sublinks.map(e=>a.jsx("li",{className:c().sublink_title,children:a.jsx(o.default,{href:e?.link||"/",passHref:!0,prefetch:!1,className:c().sublink_title,children:e?.title})},e?.id))})]},e?.id))}),a.jsx("div",{className:c().secondrow,children:t?.pages_row?.map(e=>a.jsxs("div",{className:c().column,children:[a.jsx(o.default,{href:e?.link||"/",passHref:!0,prefetch:!1,className:c().title_firstrow,children:a.jsx(s.Z,{headingType:"h4",title:e?.title})},e?.id),a.jsx("ul",{className:c().link_title,children:e?.Sublinks.map(e=>a.jsx("li",{className:c().sublink_title,children:a.jsx(o.default,{href:e?.link||"/",passHref:!0,prefetch:!1,className:c().sublink_title,children:e?.title})},e?.id))})]},e?.id))})]}),a.jsx("div",{className:c().thirdrow,children:t?.terms_and_condition_section.map(e=>a.jsx(o.default,{href:e?.link||"/",className:c().terms_and_condition_title,children:e.title},e?.id))}),(0,a.jsxs)("div",{className:c().company_logo_section,children:[a.jsx(o.default,{href:t?.company_logo_section?.link||"/",className:c().imageContainer,children:a.jsx(i.default,{src:t?.company_logo_section?.image?.data?.attributes?.url,alt:"Logo Image",width:175,height:32})}),a.jsx("div",{className:c().iconsContainer,children:t?.company_logo_section?.social_platforms.map(e=>a.jsx(o.default,{href:e?.link||"/",children:a.jsx(i.default,{src:e?.image?.data?.attributes?.url,alt:"Icons",width:40,height:40,quality:100,className:c().icon})},e.id))})]}),a.jsx("div",{className:c().copyright,children:t?.company_logo_section?.Copyright})]})})}},137:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var a=r(95344),n=r(3729),i=r(80442),o=r(60646),s=r(79138),l=r(89410),c=r(55846),d=r(21204),p=r(95209),h=r(97273),x=r.n(h),m=r(22281),u=r(12357);function b({linkTitle:e,href:t,onClick:r,fromSevices:i=!1}){let[o,s]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{s(!0)},[]),o)?a.jsx("div",{className:x().linkWrapper,children:a.jsx(u.Z,{className:(0,m.Z)(!0===i?x().link:x().linkTitle_others),href:t,onClick:r,children:e})}):null}function _({links:e,onClick:t,variant:r="default"}){let n="careers"===r||"";return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:x().megaMenuContent,children:a.jsx("div",{className:x().menuWrapper,children:a.jsx(d.Z,{children:e?.map(e=>a.jsx(p.Z,{className:m.Z("col-sm-12 col-md-6 col-lg-3 col-xl-3",x().flexDirectionColumn),children:a.jsx(b,{linkTitle:e?.title,href:e?.link,onClick:t,forCareers:n})},e?.id))})})}),a.jsx("div",{className:x().bottom_border})]})}function k(){return(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M12 5L12 19",stroke:"#FCFCFC",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:"M19 12L12 19L5 12",stroke:"#FCFCFC",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var g=r(18924),f=r(72885),v=r.n(f);function j({links:e,button:t,onClick:r}){let n=(0,g.Z)({query:`(max-width: ${v()["breakpoint-lg"]})`});return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:x().megaMenuContent,children:(0,a.jsxs)("div",{className:x().menuWrapper,children:[a.jsx(d.Z,{children:e?.map(e=>a.jsx(p.Z,{className:m.Z("col-sm-12 col-md-3 col-lg-3 col-xl-3",x().flexDirectionColumn),children:a.jsx(b,{linkTitle:e?.title,href:e?.link,onClick:r})},e?.id))}),(0,a.jsxs)(d.Z,{className:n&&x().hide,children:[a.jsx(p.Z,{className:"col-md-3"}),a.jsx(p.Z,{className:"col-md-3"}),a.jsx(p.Z,{className:"col-md-3"}),a.jsx(p.Z,{className:"col-md-3",children:t&&a.jsx("div",{className:x().brandsButton,children:a.jsx(c.Z,{type:"button",label:t?.title,rightIcon:a.jsx(k,{}),onClick:r})})})]})]})}),a.jsx("div",{className:x().bottom_border})]})}let w={src:"/_next/static/media/arrow-right.f4a53bdf.svg",height:24,width:24,blurWidth:0,blurHeight:0};function y({links:e,button:t,titleDescription:r,onClick:i}){let[o,s]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{s(!0)},[]),o)?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:x().megaMenuContent,children:(0,a.jsxs)(d.Z,{children:[a.jsx(p.Z,{children:a.jsx("div",{className:x().menuWrapper,children:a.jsx(d.Z,{children:e?.map(e=>a.jsx(p.Z,{className:m.Z("col-sm-12 col-md-12 col-lg-4 col-xl-4",x().flexDirectionColumn),children:a.jsx(b,{linkTitle:e?.title,href:e.link,onClick:i})},e?.id))})})}),a.jsx(p.Z,{className:(0,m.Z)("col-sm-12 col-md-12 col-lg-4 col-xl-3",x().flexDirectionColumn),children:a.jsx("div",{className:x().menuWrapper,children:(0,a.jsxs)("div",{className:(0,m.Z)(x().latestBlogWrapper,x().link),children:[a.jsx("p",{className:x().blogHeading,children:r?.title}),a.jsx("div",{className:(0,m.Z)(x().linkTitle,x().blogTitle),dangerouslySetInnerHTML:{__html:r?.description}}),t?.link&&a.jsx("div",{className:x().blogCTALink,children:(0,a.jsxs)(u.Z,{href:`${t?.link}`,onClick:i,className:x().ctaLink,children:[t?.title,a.jsx("span",{children:a.jsx(l.default,{src:w,alt:"right Arrow",width:24,height:24})})]})})]})})})]})}),a.jsx("div",{className:x().bottom_border})]}):null}function C(){return(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M5 12H19",stroke:"#FCFCFC",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:"M12 5L19 12L12 19",stroke:"#FCFCFC",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}var F=r(8428);function N({menuArray:e,button:t,onClick:r}){let i=(0,F.useRouter)(),o=(0,g.Z)({query:`(min-width: ${v()["breakpoint-xl-2442"]})`}),[s,l]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{l(!0)},[]),s)?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:(0,m.Z)(o?x().largeDeviceSpacing:x().megaMenuContent),children:(0,a.jsxs)(d.Z,{children:[e.map(e=>a.jsx(p.Z,{className:(0,m.Z)("col-sm-12 col-md-6 col-lg-3 col-xl-3",x().flexDirectionColumn),children:a.jsx(d.Z,{children:(0,a.jsxs)("div",{className:x().menuWrapper,children:[a.jsx(u.Z,{className:x().linkTitle,href:e?.link,onClick:r,children:e?.title}),e?.sublinks?.map((e,t)=>a.jsx(b,{linkTitle:e?.title,href:e?.link,onClick:r,fromSevices:!0},t))]})})},e?.id)),a.jsx(p.Z,{className:"col-md-3",children:a.jsx(d.Z,{className:"pt-3",children:a.jsx("div",{className:(0,m.Z)(x().menuWrapper,x().all_service_button),children:a.jsx(c.Z,{type:"button",label:t?.title,onClick:()=>{r(),i.push(`${t?.link}`)},rightIcon:a.jsx(C,{})})})})})]})}),a.jsx("div",{className:x().bottom_border})]}):null}function S(){return a.jsx("svg",{width:"8",height:"5",viewBox:"0 0 8 5",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.167368 0.381662C0.390524 0.158505 0.752333 0.158505 0.97549 0.381662L4 3.40617L7.02451 0.381662C7.24767 0.158505 7.60948 0.158505 7.83263 0.381662C8.05579 0.604819 8.05579 0.966627 7.83263 1.18978L4.40406 4.61836C4.1809 4.84151 3.8191 4.84151 3.59594 4.61836L0.167368 1.18978C-0.0557892 0.966627 -0.0557892 0.604819 0.167368 0.381662Z",fill:"currentColor"})})}function H({className:e}){return(0,a.jsxs)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[a.jsx("path",{d:"M35 16.6667H5",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:"M35 10H5",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:"M35 23.3333H5",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),a.jsx("path",{d:"M35 30H5",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}function L({className:e}){return a.jsx("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:a.jsx("path",{d:"M12 12 L28 28 M28 12 L12 28",stroke:"white",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})})}var Z=r(56506);function T({headerData:e}){let[t,r]=(0,n.useState)(!1),[d,p]=(0,n.useState)(0),[h,u]=(0,n.useState)(!1),[b,k]=(0,n.useState)(null),f=(0,F.useRouter)();(0,n.useEffect)(()=>{u(!1)},[]),(0,n.useEffect)(()=>{},[d,()=>{}]);let w=()=>{u(!h)},C=()=>{k(null)},{data:{attributes:{logo:{image:{data:{attributes:{url:T,height:W,width:B,alternativeText:M}}}},menu:z}}}=e,D=(0,g.Z)({query:`(max-width: ${v()["breakpoint-lg"]})`}),I=(0,g.Z)({query:`(max-width: ${v()["breakpoint-sm-427"]})`}),P=(e,t,r,n)=>{let i=()=>{k(null),D&&u(!1)};switch(e){case"Services":return a.jsx(N,{menuArray:t,button:r,onClick:i});case"ValueQuest":case"Resources":return a.jsx(y,{links:t,button:r,titleDescription:n,onClick:i});case"Industries":return a.jsx(_,{links:t,onClick:i});case"About Us":return a.jsx(j,{links:t,button:r,onClick:i});default:return""}};return a.jsx(i.Z,{expand:"lg",fixed:"top",className:(0,m.Z)(x().navbarWrapper,t&&x().hidden,h&&x().hidden_override),"data-crawler-ignore":!0,children:(0,a.jsxs)(o.default,{fluid:!0,className:x().applicationContainer,children:[a.jsx(Z.default,{href:"/",prefetch:!1,children:a.jsx(i.Z.Brand,{className:x().brandContainer,children:a.jsx(l.default,{src:T,height:28,width:150,className:(0,m.Z)("d-inline-block align-center",x().mtech_logo),alt:M,priority:!0})})}),D&&(0,a.jsxs)("div",{className:"d-flex",children:[a.jsx(c.Z,{type:"button",label:"Get In Touch",className:(0,m.Z)(x().button,I&&x().hide),onClick:()=>{h&&w(),f.push("/contact-us")}}),(0,a.jsxs)(i.Z.Toggle,{"aria-controls":"basic-navbar-nav",className:x().navBarToggler,onClick:w,children:[a.jsx(H,{className:`${x().menuIcons} ${h?x().clicked:""}`}),a.jsx(L,{className:`${x().menuIcons} ${h?"":x().clicked}`})]}),a.jsx(Z.default,{href:"/search","aria-label":"search",className:(0,m.Z)(x().search,x().searchWrapper),children:a.jsx(l.default,{src:"https://dev-cdn.marutitech.com/search_icon_3736bff546.svg",width:24,height:25,alt:"Search_icon"})})]}),D?a.jsx("div",{id:"basic-navbar-nav",className:(0,m.Z)(x().navbarCollapse),style:{display:h?"block":"none"},children:(0,a.jsxs)(s.Z,{className:x().nav,children:[z?.map((e,t)=>a.jsx(a.Fragment,{children:a.jsxs("div",{onMouseEnter:()=>k(e?.title),onMouseLeave:C,className:x().navItem,"data-title":e?.title,children:[a.jsx(Z.default,{href:e?.link,prefetch:!1,passHref:!0,legacyBehavior:!0,children:a.jsxs(s.Z.Link,{className:m.Z(x().navLink),"data-title":e?.title,children:[e?.title,a.jsx("span",{className:x().arrowIcon,children:a.jsx(S,{})})]})}),b===e?.title&&a.jsx("div",{className:m.Z(x().megamenu,{[x().visible]:b===e?.title}),"data-title":e?.title,children:P(e?.title,e?.subMenu||e?.subLinks,e?.button,e?.titleDescription)})]},t)})),a.jsx(c.Z,{type:"button",label:"Get In Touch",className:(0,m.Z)(x().button,x().visibility),onClick:()=>{h&&w(),f.push("/contact-us")}}),a.jsx(Z.default,{href:"/search","aria-label":"search",className:(0,m.Z)(x().search,x().searchWrapper,D&&x().hide),children:a.jsx(l.default,{src:"https://dev-cdn.marutitech.com/search_icon_3736bff546.svg",width:24,height:25,alt:"Search_icon"})})]})}):a.jsx(i.Z.Collapse,{id:"basic-navbar-nav",className:(0,m.Z)(x().navbarCollapse),children:(0,a.jsxs)(s.Z,{className:x().nav,children:[z?.map((e,t)=>a.jsxs("div",{onMouseEnter:()=>k(e?.title),onMouseLeave:C,className:x().navItem,"data-title":e?.title,children:[a.jsx(Z.default,{href:e?.link,prefetch:!1,passHref:!0,legacyBehavior:!0,children:a.jsxs(s.Z.Link,{className:m.Z(x().navLink),"data-title":e?.title,children:[e?.title,a.jsx("span",{className:x().arrowIcon,children:a.jsx(S,{})})]})}),b===e?.title&&a.jsx("div",{className:m.Z(x().megamenu,{[x().visible]:b===e?.title}),"data-title":e?.title,children:P(e?.title,e?.subMenu||e?.subLinks,e?.button,e?.titleDescription)})]},t)),a.jsx(c.Z,{type:"button",label:"Get In Touch",className:(0,m.Z)(x().button,x().visibility),onClick:()=>{w(),f.push("/contact-us")}}),a.jsx(Z.default,{href:"/search","aria-label":"search",className:(0,m.Z)(x().search,x().searchWrapper,D&&x().hide),children:a.jsx(l.default,{src:"https://dev-cdn.marutitech.com/search_icon_3736bff546.svg",width:24,height:25,alt:"Search_icon"})})]})})]})})}},81473:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(95344);r(3729);var n=r(22281),i=r(12659),o=r.n(i),s=r(6969),l=r.n(s);function c({headingType:e,title:t,position:r,style:i,className:s,richTextValue:c}){return a.jsx("div",{className:(0,n.Z)(l()[r],s),children:((e,t)=>{switch(e){case"h1":return c?a.jsx("h1",{className:o().h1,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h1",{className:o().h1,style:i,children:t});case"h2":return c?a.jsx("h2",{className:o().h2,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h2",{className:o().h2,style:i,children:t});case"h3":return c?a.jsx("h3",{className:o().h3,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h3",{className:o().h3,style:i,children:t});case"h4":return c?a.jsx("h4",{className:o().h4,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h4",{className:o().h4,style:i,children:t});case"h5":return c?a.jsx("h5",{className:o().h5,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h5",{className:o().h5,style:i,children:t});case"h6":return c?a.jsx("h6",{className:o().h6,style:i,dangerouslySetInnerHTML:{__html:c}}):a.jsx("h6",{className:o().h6,style:i,children:t});default:return null}})(e,t)})}},12357:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var a=r(95344);r(3729);var n=r(56506),i=r(22281);function o({id:e,children:t,href:r="",className:o,style:s,isExternal:l=!1,onClick:c,scroll:d=!0,shallow:p=!1,ariaLabel:h,dataID:x=null,suppressHydrationWarning:m=!1}){return l?a.jsx("a",{suppressHydrationWarning:m,id:e,className:(0,i.Z)(o),href:r,style:s,target:"_blank",rel:"noreferrer","aria-label":h,onClick:c,"data-id":x,children:t}):a.jsx(n.default,{href:r,scroll:d,shallow:p,suppressHydrationWarning:m,id:e,className:(0,i.Z)(o),style:s,onClick:c,"data-id":x,"aria-label":h,children:t})}},18924:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a}),r(3729);let a=()=>!1},22281:(e,t,r)=>{"use strict";function a(...e){return e.filter(Boolean).join(" ")}r.d(t,{Z:()=>a})},15270:(e,t,r)=>{var a=r(70048);e.exports={breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":""+a["breakpoint-sm"],container:"not-found_container__LRagC",image:"not-found_image__klXiS",backToHomeButton:"not-found_backToHomeButton__iMCST"}},98775:(e,t,r)=>{var a=r(24640);e.exports={variables:'"@styles/variables.module.css"',brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,colorBlack:""+a.colorBlack,gray300:""+a.gray300,colorWhite:""+a.colorWhite,button:"Button_button__exqP_",link:"Button_link__9n7Et",innerWrapper:"Button_innerWrapper__ITLB1",leftWrapper:"Button_leftWrapper__fWtI9",rightWrapper:"Button_rightWrapper__GkIh_"}},12100:e=>{e.exports={container:"CircularButtonWithArrow_container__9Cvr1",circle:"CircularButtonWithArrow_circle__H7jjo",arrow:"CircularButtonWithArrow_arrow__h3ojH",arrowImage:"CircularButtonWithArrow_arrowImage__G7E_X",arrow_scroll:"CircularButtonWithArrow_arrow_scroll__a_DTi"}},29:(e,t,r)=>{var a=r(24640),n=r(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+a.colorBlack,colorWhite:""+a.colorWhite,brandColorThree:""+a.brandColorThree,fifteenSpace:""+a.fifteenSpace,grayBorder:""+a.grayBorder,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+n["breakpoint-md"],"breakpoint-sm-450":""+n["breakpoint-sm-450"],"breakpoint-sm-550":""+n["breakpoint-sm-550"],"breakpoint-sm-320":""+n["breakpoint-sm-320"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-xl-1440":""+n["breakpoint-xl-1440"],"breakpoint-lg":""+n["breakpoint-lg"],"breakpoint-lg-991px":""+n["breakpoint-lg-991px"],main_container:"Footer_main_container__LZ2hx",first_second_row:"Footer_first_second_row__X_sZS",column:"Footer_column__tKCuc",firstrow:"Footer_firstrow__Sygqj",title_firstrow:"Footer_title_firstrow__C7F_t",link_title:"Footer_link_title__CRgh0",sublink_title:"Footer_sublink_title__NuYkY",secondrow:"Footer_secondrow__HqLZa",thirdrow:"Footer_thirdrow__EDmf_",terms_and_condition_title:"Footer_terms_and_condition_title__ml5gN",company_logo_section:"Footer_company_logo_section__TxSfQ",iconsContainer:"Footer_iconsContainer__u9PPI",imageContainer:"Footer_imageContainer__rapPm",copyright:"Footer_copyright__1v3uR",icon:"Footer_icon__UIUYS"}},97273:(e,t,r)=>{var a=r(24640),n=r(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+a.colorBlack,gray:""+a.gray,colorWhite:""+a.colorWhite,fifteenSpace:""+a.fifteenSpace,grayBorder:""+a.grayBorder,brandColorOne:""+a.brandColorOne,brandColorTwo:""+a.brandColorTwo,brandColorThree:""+a.brandColorThree,brandColorFour:""+a.brandColorFour,brandColorFive:""+a.brandColorFive,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+n["breakpoint-md"],"breakpoint-sm-320":""+n["breakpoint-sm-320"],"breakpoint-sm-427":""+n["breakpoint-sm-427"],"breakpoint-xl-1024":""+n["breakpoint-xl-1024"],"breakpoint-xl-1440":""+n["breakpoint-xl-1440"],"breakpoint-lg":""+n["breakpoint-lg"],"breakpoint-lg-991px":""+n["breakpoint-lg-991px"],"breakpoint-xl-2100":""+n["breakpoint-xl-2100"],"breakpoint-md-769":""+n["breakpoint-md-769"],"breakpoint-sm-430":""+n["breakpoint-sm-430"],navbarWrapper:"Header_navbarWrapper__HdM_E",hidden:"Header_hidden__pcG11","search-button":"Header_search-button__GF3KH",navLink:"Header_navLink__bX76H",navLink_mobile:"Header_navLink_mobile__XnrY5",navItem:"Header_navItem__pb6e5",link:"Header_link__p2PJz",linkTitle:"Header_linkTitle__FTnZl",linkTitle_others:"Header_linkTitle_others__oyfpn",navbarCollapse:"Header_navbarCollapse__nWJ4z",nav_dis:"Header_nav_dis__Z7pai",nav_nav_style:"Header_nav_nav_style__bwvFu",applicationContainer:"Header_applicationContainer__noTHf",nav:"Header_nav__LVYU2",arrowIcon:"Header_arrowIcon__ixZre",search:"Header_search__m3eU6",searchWrapper:"Header_searchWrapper__YvUwA","navbar-nav":"Header_navbar-nav__E5HZO","nav-link":"Header_nav-link__7xXdK",navBarToggler:"Header_navBarToggler__cCWUA",menuIcons:"Header_menuIcons__4NysM",clicked:"Header_clicked__eFyFJ",hide:"Header_hide__RbUcz",brandContainer:"Header_brandContainer__32TYX",visibility:"Header_visibility__Do2_f",menuWrapper:"Header_menuWrapper__weqYh",megaMenuContent:"Header_megaMenuContent__tg_ef",largeDeviceSpacing:"Header_largeDeviceSpacing__Spsib",megamenu:"Header_megamenu__N9gL8",latestBlogWrapper:"Header_latestBlogWrapper__bgX6a",blogHeading:"Header_blogHeading__hcugT",blogTitle:"Header_blogTitle__0mbcv",blogCTALink:"Header_blogCTALink__hK1J4",ctaLink:"Header_ctaLink__UeKwP",brandsButton:"Header_brandsButton__jLpK4",flexDirectionColumn:"Header_flexDirectionColumn__PGxpa","accordion-body":"Header_accordion-body__N4tpS",all_service_button:"Header_all_service_button__93M7L",mtech_logo:"Header_mtech_logo__B7aPt",bottom_border:"Header_bottom_border__035sm",hidden_override:"Header_hidden_override__0X_rN"}},6969:e=>{e.exports={center:"Heading_center__XBGsG",left:"Heading_left__ouHog",right:"Heading_right__jsN_Y"}},72885:e=>{e.exports={"breakpoint-xs":"0","breakpoint-sm-195":"195px","breakpoint-sm-270":"270px","breakpoint-sm-200":"200px","breakpoint-sm-320":"320px","breakpoint-sm-326":"326px","breakpoint-sm-390":"390px","breakpoint-sm-367":"367px","breakpoint-sm-365":"365px","breakpoint-sm-340":"340px","breakpoint-sm-350":"350px","breakpoint-sm-370":"370px","breakpoint-sm-380":"380px","breakpoint-sm-424":"424px","breakpoint-sm-427":"427px","breakpoint-sm-420":"420px","breakpoint-sm-430":"430px","breakpoint-sm-450":"450px","breakpoint-sm-460":"460px","breakpoint-sm-484":"484px","breakpoint-sm-480":"480px","breakpoint-sm-532":"532px","breakpoint-sm-550":"550px","breakpoint-sm":"576px","breakpoint-md-579":"579px","breakpoint-md-585":"585px","breakpoint-md-767":"767px","breakpoint-md":"768px","breakpoint-md-769":"769px","breakpoint-md-820":"820px","breakpoint-md-850":"850px","breakpoint-lg-901":"901px","breakpoint-lg":"992px","breakpoint-lg-991px":"991px","breakpoint-xl-1024":"1024px","breakpoint-xl-1051":"1051px","breakpoint-xl-1208":"1208px","breakpoint-xl-1023":"1023px","breakpoint-xl-1199":"1199px","breakpoint-xl-1188":"1188px","breakpoint-xl":"1200px","breakpoint-xl-1365":"1365px","breakpoint-xl-1366":"1366px","breakpoint-xl-1309":"1309px","breakpoint-xl-1400":"1400px","breakpoint-xl-1439":"1439px","breakpoint-xl-1440":"1440px","breakpoint-xl-1405":"1405px","breakpoint-xl-1406":"1406px","breakpoint-xl-1600":"1600px","breakpoint-xl-1800":"1800px","breakpoint-xl-2000":"2000px","breakpoint-xl-2100":"2100px","breakpoint-xl-2442":"2442px","breakpoint-xl-2559":"2559px","breakpoint-xl-2560":"2560px"}},12659:(e,t,r)=>{var a=r(24640),n=r(70048);e.exports={variables:'"./variables.module.css"',h1FontSize:""+a.h1FontSize,h1MobileFontSize:""+a.h1MobileFontSize,h2FontSize:""+a.h2FontSize,h2MobileFontSize:""+a.h2MobileFontSize,h3FontSize:""+a.h3FontSize,h3MobileFontSize:""+a.h3MobileFontSize,h4FontSize:""+a.h4FontSize,h4MobileFontSize:""+a.h4MobileFontSize,h5FontSize:""+a.h5FontSize,h5MobileFontSize:""+a.h5MobileFontSize,h6FontSize:""+a.h6FontSize,h6MobileFontSize:""+a.h6MobileFontSize,fontWeight600:""+a.fontWeight600,fontWeight700:""+a.fontWeight700,breakPoints:'"./breakpoints.module.css"',"breakpoint-sm-450":""+n["breakpoint-sm-450"],h1:"typography_h1__DecPZ",h2:"typography_h2__Dn0zf",h3:"typography_h3__o3Abb",h4:"typography_h4__lGrWj",h5:"typography_h5__DGJHL",h6:"typography_h6__vf_A0",caption:"typography_caption__hfk0A"}},24692:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>F,metadata:()=>w});var a=r(25036),n=r(42626),i=r.n(n);r(40002);var o=r(20970),s=r(86843);let l=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\BootstrapClient\BootstrapClient.tsx`),{__esModule:c,$$typeof:d}=l,p=l.default,h=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\Header\Header.tsx`),{__esModule:x,$$typeof:m}=h,u=h.default;r(18399),r(485);var b=r(95411);let _="GTM-M53BRHCX",k=()=>(0,a.jsxs)(a.Fragment,{children:[a.jsx(b.default,{id:"gtm-script",strategy:"lazyOnload",dangerouslySetInnerHTML:{__html:`
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${_}');
          `}}),a.jsx("noscript",{children:a.jsx("iframe",{src:`https://www.googletagmanager.com/ns.html?id=${_}`,height:"0",width:"0",loading:"lazy",style:{display:"none",visibility:"hidden"}})})]});var g=r(75509);let f=(0,o.default)(()=>r.e(4604).then(r.bind(r,34604)),{loadableGenerated:{modules:["C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\layout.tsx -> @components/CookiesConsentBanner"]},ssr:!1}),v=(0,o.default)(()=>r.e(208).then(r.bind(r,50208)),{loadableGenerated:{modules:["C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\layout.tsx -> @components/CircularButtonWithArrow"]}}),j=(0,o.default)(()=>r.e(3048).then(r.bind(r,53048)),{loadableGenerated:{modules:["C:\\Users\\<USER>\\Documents\\GitHub\\mtl-nextjs-aws-site\\Frontend\\src\\app\\layout.tsx -> @components/Footer"]}}),w={icons:{icon:"https://dev-cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"},verification:{google:"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}};async function y(){return await (0,g.Z)("header","populate=logo&populate=logo.image&populate=menu.subMenu&populate=menu.subMenu.image&populate=menu.subLinks&populate=menu.button&populate=menu.titleDescription")}async function C(){return await (0,g.Z)("footer","populate=sector_row.Sublinks,pages_row.Sublinks,terms_and_condition_section,company_logo_section.image,company_logo_section.Copyright,company_logo_section.social_platforms.image")}async function F({children:e}){let t=y(),r=C(),[n,o]=await Promise.all([t,r]);return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx(k,{}),a.jsx("link",{rel:"preconnect",href:"https://api.ipify.org"}),a.jsx("link",{rel:"preconnect",href:"https://ipwhois.app"})]}),(0,a.jsxs)("body",{className:i().className,children:[a.jsx(f,{}),a.jsx(u,{headerData:n}),e,a.jsx(j,{footerData:o}),a.jsx("div",{id:"scroll_to_top",children:a.jsx(v,{variant:"scroll_to_top",scroll_to:!0})}),a.jsx(p,{})]})]})}},48206:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});let a=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\app\not-found.tsx`),{__esModule:n,$$typeof:i}=a,o=a.default},75509:(e,t,r)=>{"use strict";async function a(e,t=""){try{let r=`https://dev-content.marutitech.com/api/${e}${t?`?${t}`:""}`,a=await fetch(r,{method:"GET",cache:"force-cache"});if(!a.ok)throw Error(`Strapi fetch error: ${a.status} - ${a.statusText}`);return await a.json()}catch(t){return console.error(`Failed to fetch ${e}:`,t),null}}r.d(t,{Z:()=>a})},485:()=>{}};