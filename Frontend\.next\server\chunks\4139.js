exports.id=4139,exports.ids=[4139],exports.modules={63756:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var r=t(95344);t(3729);var s=t(60646),i=t(56506),l=t(2522),n=t(79829),o=t(81473),_=t(97906),c=t(18924),d=t(22281),m=t(72885),b=t.n(m),p=t(44469),h=t.n(p),v=t(90638),x=t.n(v),u=t(25609);function C({data:e,variant:a="L2Services",variantWhite:t=!0}){let[m,p]=(0,l.Z)({dragFree:!0}),{selectedIndex:v,scrollSnaps:C,onDotButtonClick:g}=(0,_.Z)(p),S=(0,c.Z)({query:`(max-width: ${b()["breakpoint-xl"]})`}),j="L2Services"===a?e?.other_services_card.slice(0,2).map(e=>r.jsx("div",{className:x().embla__slide,children:r.jsx(i.default,{href:e?.service_page_link,prefetch:!1,className:x().card,children:r.jsxs("div",{className:x().contentWrapper,children:[r.jsx("div",{className:x().cardOverlay}),r.jsx(u.Z,{src:e?.on_hover_bg_image?.data?.attributes,fill:!0,alt:"onHoverBgImage",className:x().onHoverBgImage}),r.jsx(o.Z,{headingType:"h3",title:e?.title,className:x().cardTitle}),r.jsx("div",{className:x().cardDescription,dangerouslySetInnerHTML:{__html:e?.description}})]})},e?.id)},e?.id)):e?.other_services_card.map(e=>r.jsx("div",{className:x().embla__slide__l3,children:r.jsx(i.default,{href:e?.service_page_link,prefetch:!1,className:x().card,style:{width:"384px"},children:r.jsxs("div",{className:x().contentWrapper,children:[r.jsx("div",{className:x().cardOverlay}),r.jsx(u.Z,{src:e?.on_hover_bg_image?.data?.attributes,fill:!0,alt:"onHoverBgImage",className:x().onHoverBgImage}),r.jsx(o.Z,{headingType:"h3",title:e?.title,className:x().cardTitle}),r.jsx("div",{className:x().cardDescription,dangerouslySetInnerHTML:{__html:e?.description}})]})},e?.id)},e?.id));return(0,r.jsxs)(s.default,{fluid:!0,className:(0,d.Z)(x().OtherServicesCardContainer,"L3Services"===a&&x().l3OtherServicesCardContainer),children:[r.jsx(o.Z,{headingType:"h2",title:e?.title,className:x().title,position:"center"}),"L2Services"===a&&(S?(0,r.jsxs)("div",{className:x().embla,children:[r.jsx("div",{className:x().embla__viewport,ref:m,children:(0,r.jsxs)("div",{className:x().embla__container,children:[j,"L2Services"===a&&r.jsx("div",{className:x().embla__slide,children:r.jsx(i.default,{href:e?.all_services_card_link,prefetch:!1,className:x().allServicesCardWrapper,children:r.jsx("div",{className:x().allServicesCard,children:e?.all_services_card_title})})})]})}),r.jsx("div",{className:x().embla__controls,children:r.jsx("div",{className:h().embla__dots,children:C.map((e,a)=>r.jsx(n.Z,{onClick:()=>g(a),className:a===v?`${h().embla__dot} ${h().embla__dot_selected}`:t?(0,d.Z)(h().embla__dot,h().embla__dot_bg_white):h().embla__dot},a))})})]}):(0,r.jsxs)("div",{className:x().cardWrapper,children:[j,"L2Services"===a&&r.jsx(i.default,{href:e?.all_services_card_link,prefetch:!1,className:x().allServicesCardWrapper,children:r.jsx("div",{className:x().allServicesCard,children:e?.all_services_card_title})})]})),"L3Services"===a&&(0,r.jsxs)("div",{className:x().emblaL3,children:[r.jsx("div",{className:x().embla__viewport__l3,ref:m,children:r.jsx("div",{className:x().embla__container__l3,children:j})}),r.jsx("div",{className:x().embla__controls__l3,children:r.jsx("div",{className:x().embla__dots__l3,children:C.map((e,a)=>r.jsx(n.Z,{onClick:()=>g(a),className:h().embla__dot.concat(a===v?` ${h().embla__dot_selected}`:"")},a))})})]})]})}},43130:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>S});var r=t(95344),s=t(3729),i=t(89410),l=t(2522),n=t(30278),o=t(79829),_=t(81473),c=t(99036),d=t(97906),m=t(18924),b=t(22281),p=t(72885),h=t.n(p),v=t(44469),x=t.n(v),u=t(58090),C=t.n(u),g=t(25609);function S({data:e}){let[a,t]=(0,s.useState)(!1),[p,v]=(0,s.useState)(!1),u=(0,m.Z)({query:`(max-width: ${h()["breakpoint-sm-427"]})`}),[S,j]=(0,l.Z)({dragFree:!0,align:u?"center":"start"}),[k,N]=(0,s.useState)(""),T=e=>{N(e?.testimonial_video_link),t(!0),v(!0)},{selectedIndex:y,scrollSnaps:f,onDotButtonClick:w}=(0,d.Z)(j),O=(0,m.Z)({query:`(max-width: ${h()["breakpoint-md-850"]})`});return r.jsx("div",{className:C().testimonialWrapper,children:(0,r.jsxs)("div",{className:C().container,children:[(0,r.jsxs)("div",{className:C().testimonialSection,children:[(0,r.jsxs)("div",{className:C().testimonialHeading,children:[r.jsx(_.Z,{headingType:"h2",position:"left",className:C().title,richTextValue:e.title}),r.jsx("div",{className:C().testimonialTagline,children:r.jsx(n.Z,{text_url:e?.circular_text_line_svg?.data.attributes.url,href:e?.tagline_url})}),!u&&f.length>1&&r.jsx("div",{className:(0,b.Z)(C().embla__controls,C().desktopDots),children:r.jsx("div",{className:x().embla__dots,children:f.map((e,a)=>r.jsx(o.Z,{onClick:()=>w(a),className:x().embla__dot.concat(a===y?` ${x().embla__dot_selected}`:"")},a))})})]}),(0,r.jsxs)("div",{className:C().emblaWrapper,children:[r.jsx(i.default,{src:"https://dev-cdn.marutitech.com/Gredient_1_b44413eea5.png",width:240,height:197,alt:"topLeftGradient",className:C().bottomLeftGradient}),(0,r.jsxs)("div",{className:C().embla,children:[r.jsx("div",{className:C().embla__viewport,ref:S,children:r.jsx("div",{className:(0,b.Z)(C().sliderContainer,C().embla__container),children:e?.testimonials_slider?.map(a=>r.jsxs("div",{className:b.Z(C().slide,C().embla__slide),onClick:()=>T(a),role:"presentation",children:[r.jsx(g.Z,{src:a?.image?.data?.attributes,alt:a?.image?.data?.attributes?.alternativeText,width:387,height:300,className:C().img}),r.jsxs("div",{className:C().clientTestiMonial,children:[r.jsx("div",{className:C().playBtn,children:r.jsx(i.default,{src:e?.testimonial_playbtn_logo?.data?.attributes?.url,width:e?.testimonial_playbtn_logo?.data?.attributes?.width,height:e?.testimonial_playbtn_logo?.data?.attributes?.height,alt:"play-button"})}),r.jsxs("div",{className:C().clientInfo,children:[r.jsx(_.Z,{className:C().clientName,headingType:"h3",position:"left",title:a?.clientName}),r.jsx("div",{className:C().clientDesignation,dangerouslySetInnerHTML:{__html:a?.clientDescription}})]})]})]},a?.id))})}),O&&r.jsx("div",{className:(0,b.Z)(C().embla__controls,C().mobileDots),children:r.jsx("div",{className:x().embla__dots,children:f.length>1&&f.map((e,a)=>r.jsx(o.Z,{onClick:()=>w(a),className:x().embla__dot.concat(a===y?` ${x().embla__dot_selected}`:"")},a))})})]}),r.jsx(i.default,{src:"https://dev-cdn.marutitech.com/Group_5042_1_5259ae27e3.svg",width:240,height:197,alt:"topRightGradient",className:C().topRightGradient})]})]}),r.jsx(c.Z,{show:a,setShow:t,showClose:p,setShowClose:v,videoLink:k})]})})}},90638:(e,a,t)=>{var r=t(24640),s=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+r.colorBlack,colorWhite:""+r.colorWhite,gray:""+r.gray,brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-sm":""+s["breakpoint-sm"],"breakpoint-md-767":""+s["breakpoint-md-767"],"breakpoint-md-769":""+s["breakpoint-md-769"],"breakpoint-lg":""+s["breakpoint-lg"],"breakpoint-xl":""+s["breakpoint-xl"],"breakpoint-xl-1400":""+s["breakpoint-xl-1400"],"breakpoint-sm-390":""+s["breakpoint-sm-390"],"breakpoint-sm-550":""+s["breakpoint-sm-550"],OtherServicesCardContainer:"OtherServicesCard_OtherServicesCardContainer__fQ4j_",l3OtherServicesCardContainer:"OtherServicesCard_l3OtherServicesCardContainer__zHCGq",title:"OtherServicesCard_title__tyLPu",cardWrapper:"OtherServicesCard_cardWrapper__riSPf",card:"OtherServicesCard_card___cmaL",onHoverBgImage:"OtherServicesCard_onHoverBgImage__EoFE_",contentWrapper:"OtherServicesCard_contentWrapper__Y6eVI",cardOverlay:"OtherServicesCard_cardOverlay__i4rCb",cardTitle:"OtherServicesCard_cardTitle__NZV11",cardDescription:"OtherServicesCard_cardDescription__nBvkH",allServicesCardWrapper:"OtherServicesCard_allServicesCardWrapper__rq60h",allServicesCard:"OtherServicesCard_allServicesCard__GsbNw",embla:"OtherServicesCard_embla__MseRI",embla__viewport:"OtherServicesCard_embla__viewport__3b_Ri",embla__container:"OtherServicesCard_embla__container__kc9_n",embla__slide:"OtherServicesCard_embla__slide__s8Nth",embla__controls:"OtherServicesCard_embla__controls__4HcTH",submitButton:"OtherServicesCard_submitButton__Onvrt",emblaL3:"OtherServicesCard_emblaL3__TZl_X",embla__viewport__l3:"OtherServicesCard_embla__viewport__l3__0BdDC",embla__container__l3:"OtherServicesCard_embla__container__l3___pIs2",embla__slide__l3:"OtherServicesCard_embla__slide__l3__bXr8u",embla__dots__l3:"OtherServicesCard_embla__dots__l3__Q6Xxe"}},58090:(e,a,t)=>{var r=t(24640),s=t(70048);e.exports={variables:'"@styles/variables.module.css"',colorBlack:""+r.colorBlack,colorWhite:""+r.colorWhite,fifteenSpace:""+r.fifteenSpace,grayBorder:""+r.grayBorder,brandColorOne:""+r.brandColorOne,brandColorTwo:""+r.brandColorTwo,brandColorThree:""+r.brandColorThree,brandColorFour:""+r.brandColorFour,brandColorFive:""+r.brandColorFive,bodyTextXXXSSmall:""+r.bodyTextXXXSSmall,breakpoints:'"@styles/breakpoints.module.css"',"breakpoint-md":""+s["breakpoint-md"],"breakpoint-sm-427":""+s["breakpoint-sm-427"],"breakpoint-xl-1024":""+s["breakpoint-xl-1024"],"breakpoint-xl-1440":""+s["breakpoint-xl-1440"],"breakpoint-xl-2100":""+s["breakpoint-xl-2100"],"breakpoint-md-769":""+s["breakpoint-md-769"],"breakpoint-sm-430":""+s["breakpoint-sm-430"],"breakpoint-sm-390":""+s["breakpoint-sm-390"],"breakpoint-sm-326":""+s["breakpoint-sm-326"],"breakpoint-md-850":""+s["breakpoint-md-850"],"breakpoint-xl-2559":""+s["breakpoint-xl-2559"],"breakpoint-sm-450":""+s["breakpoint-sm-450"],"breakpoint-sm":""+s["breakpoint-sm"],testimonialWrapper:"Testimonial_testimonialWrapper__lUNmx",sliderContainer:"Testimonial_sliderContainer__NVrJs",slide:"Testimonial_slide__JXsAV",img:"Testimonial_img__E835_",clientTestiMonial:"Testimonial_clientTestiMonial__8jCUy",clientInfo:"Testimonial_clientInfo__GAB8E",clientName:"Testimonial_clientName__WebvK",clientDesignation:"Testimonial_clientDesignation__gozKK",testimonialTagline:"Testimonial_testimonialTagline__nvMvM",testimonialSection:"Testimonial_testimonialSection__DCq10",testimonialHeading:"Testimonial_testimonialHeading__5NM9p",title:"Testimonial_title__c1JnV",emblaWrapper:"Testimonial_emblaWrapper__WLJvU",bottomLeftGradient:"Testimonial_bottomLeftGradient__PhmeR",topRightGradient:"Testimonial_topRightGradient__e2zal",embla:"Testimonial_embla__EVnWd",embla__viewport:"Testimonial_embla__viewport__2MxwZ",embla__container:"Testimonial_embla__container__PDox4",embla__slide:"Testimonial_embla__slide__uHeep",embla__slide__number:"Testimonial_embla__slide__number__ycBDX",embla__controls:"Testimonial_embla__controls__E744_",mobileDots:"Testimonial_mobileDots__R1Hex",desktopDots:"Testimonial_desktopDots__CGll2"}},88491:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\CTA\Cta.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},52534:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\CaseStudyCard\CaseStudyCard.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},42083:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\ContactUsForm\ContactUsForm.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},53108:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\HeroSection\HeroSection.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},46218:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\Insights\Insights.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},26277:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\OtherServicesCard\OtherServicesCard.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},76771:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\ServicesCard\ServicesCard.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},77365:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\Testimonial\Testimonial.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default},4485:(e,a,t)=>{"use strict";t.d(a,{Z:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Documents\GitHub\mtl-nextjs-aws-site\Frontend\src\components\TrustedPartners\TrustedPartners.tsx`),{__esModule:s,$$typeof:i}=r,l=r.default}};